# Russian translation for lftp
# Copyright (C) 1998,2000-2016 <PERSON> <<EMAIL>>
# Copyright (C) 1999 Const <PERSON> <<EMAIL>>
# You may modify and redistribute this file according to GNU GPL (see COPYING).
#
msgid ""
msgstr ""
"Project-Id-Version: lftp 4.7.5\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2024-11-08 13:05+0300\n"
"PO-Revision-Date: 2016-11-30 13:40+0300\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Russian <<EMAIL>>\n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#: lib/argmatch.c:145
#, c-format
msgid "invalid argument %s for %s"
msgstr "неверный аргумент %s для %s"

#: lib/argmatch.c:146
#, c-format
msgid "ambiguous argument %s for %s"
msgstr "неоднозначный аргумент %s для %s"

#: lib/argmatch.c:165
msgid "Valid arguments are:"
msgstr "Допустимые аргументы:"

#: lib/error.c:208
msgid "Unknown system error"
msgstr "Неизвестная системная ошибка"

#: lib/getopt.c:282
#, c-format
msgid "%s: option '%s%s' is ambiguous\n"
msgstr "%s: опция `%s%s' неоднозначна\n"

#: lib/getopt.c:288
#, c-format
msgid "%s: option '%s%s' is ambiguous; possibilities:"
msgstr "%s: опция `%s%s' неоднозначна; варианты:"

#: lib/getopt.c:322
#, c-format
msgid "%s: unrecognized option '%s%s'\n"
msgstr "%s: неизвестная опция `%s%s'\n"

#: lib/getopt.c:348
#, c-format
msgid "%s: option '%s%s' doesn't allow an argument\n"
msgstr "%s: опция `%s%s' не позволяет указывать аргумент\n"

#: lib/getopt.c:363
#, c-format
msgid "%s: option '%s%s' requires an argument\n"
msgstr "%s: для опции `%s%s' требуется аргумент\n"

#: lib/getopt.c:624
#, c-format
msgid "%s: invalid option -- '%c'\n"
msgstr "%s: неверная опция -- `%c'\n"

#: lib/getopt.c:639 lib/getopt.c:685
#, c-format
msgid "%s: option requires an argument -- '%c'\n"
msgstr "%s: для опции требуется аргумент -- `%c'\n"

#. TRANSLATORS:
#. Get translations for open and closing quotation marks.
#. The message catalog should translate "`" to a left
#. quotation mark suitable for the locale, and similarly for
#. "'".  For example, a French Unicode local should translate
#. these to U+00AB (LEFT-POINTING DOUBLE ANGLE
#. QUOTATION MARK), and U+00BB (RIGHT-POINTING DOUBLE ANGLE
#. QUOTATION MARK), respectively.
#.
#. If the catalog has no translation, we will try to
#. use Unicode U+2018 (LEFT SINGLE QUOTATION MARK) and
#. Unicode U+2019 (RIGHT SINGLE QUOTATION MARK).  If the
#. current locale is not Unicode, locale_quoting_style
#. will quote 'like this', and clocale_quoting_style will
#. quote "like this".  You should always include translations
#. for "`" and "'" even if U+2018 and U+2019 are appropriate
#. for your locale.
#.
#. If you don't know what to put here, please see
#. <https://en.wikipedia.org/wiki/Quotation_marks_in_other_languages>
#. and use glyphs suitable for your language.
#: lib/quotearg.c:354
msgid "`"
msgstr "«"

#: lib/quotearg.c:355
msgid "'"
msgstr "»"

#: lib/xalloc-die.c:34
msgid "memory exhausted"
msgstr "память исчерпана"

#: src/ArgV.cc:107
msgid "option requires an argument"
msgstr "для этой опции требуется аргумент"

#: src/ArgV.cc:109 src/ArgV.cc:118
msgid "invalid option"
msgstr "неверная опция"

#: src/ArgV.cc:114
#, c-format
msgid "option `%s' requires an argument"
msgstr "для опции `%s' требуется аргумент"

#: src/ArgV.cc:116
#, c-format
msgid "unrecognized option `%s'"
msgstr "неизвестная опция `%s'"

#: src/attach.h:138
#, c-format
msgid "[%u] Attached to terminal %s. %s\n"
msgstr "[%u] Присоединился к терминалу %s. %s\n"

#: src/attach.h:150
#, c-format
msgid "[%u] Attached to terminal.\n"
msgstr "[%u] Присоединился к терминалу.\n"

#: src/buffer.cc:253 src/FileAccess.cc:866
msgid " [cached]"
msgstr " [из кэша]"

#: src/ChmodJob.cc:80
#, c-format
msgid "Failed to change mode of `%s' to %04o (%s).\n"
msgstr "Не удалось сменить права доступа `%s' на %04o (%s).\n"

#: src/ChmodJob.cc:83
#, c-format
msgid "Mode of `%s' changed to %04o (%s).\n"
msgstr "Права доступа `%s' изменены на %04o (%s).\n"

#: src/ChmodJob.cc:88
#, c-format
msgid "Failed to change mode of `%s' because no old mode is available.\n"
msgstr ""
"Не удалось сменить права доступа `%s', так как старые права недоступны.\n"

#: src/CmdExec.cc:105
#, c-format
msgid "Warning: chdir(%s) failed: %s\n"
msgstr "Предупреждение: ошибка chdir(%s): %s\n"

#: src/CmdExec.cc:207
#, c-format
msgid "Unknown command `%s'.\n"
msgstr "Неизвестная команда `%s'.\n"

#: src/CmdExec.cc:209
#, c-format
msgid "Ambiguous command `%s'.\n"
msgstr "Неоднозначное сокращение `%s'.\n"

#: src/CmdExec.cc:228
#, c-format
msgid "Module for command `%s' did not register the command.\n"
msgstr "Модуль для команды `%s' не зарегистрировал эту команду\n"

#: src/CmdExec.cc:384
#, c-format
msgid "cd ok, cwd=%s\n"
msgstr "cd ok, каталог=%s\n"

#: src/CmdExec.cc:405 src/MirrorJob.cc:736
#, c-format
msgid "%s: received redirection to `%s'\n"
msgstr "%s: получено перенаправление на `%s'\n"

#: src/CmdExec.cc:408 src/FileCopy.cc:1230
msgid "Too many redirections"
msgstr "Слишком много перенаправлений"

#: src/CmdExec.cc:517 src/CmdExec.cc:574
msgid "Interrupt"
msgstr "Прерывание"

#: src/CmdExec.cc:639
#, c-format
msgid "Warning: discarding incomplete command\n"
msgstr "Предупреждение: неполный остаток команды игнорируется\n"

#: src/CmdExec.cc:737
#, c-format
msgid "\tExecuting builtin `%s' [%s]\n"
msgstr "\tВыполнение примитива `%s' [%s]\n"

#: src/CmdExec.cc:742
msgid "Queue is stopped."
msgstr "Очередь остановлена."

#: src/CmdExec.cc:747
msgid "Now executing:"
msgstr "Сейчас исполняется:"

#: src/CmdExec.cc:758
#, c-format
msgid "\tWaiting for job [%d] to terminate\n"
msgstr "\tОжидание завершения задания [%d]\n"

#: src/CmdExec.cc:761
#, c-format
msgid "\tWaiting for termination of jobs: "
msgstr "\tОжидание завершения заданий: "

#: src/CmdExec.cc:770
msgid "\tRunning\n"
msgstr "\tВыполнение\n"

#: src/CmdExec.cc:772
msgid "\tWaiting for command\n"
msgstr "\tОжидание команды\n"

#: src/CmdExec.cc:1261
#, c-format
msgid "%s: command `%s' is not compiled in.\n"
msgstr "%s: команда `%s' не встроена при компиляции.\n"

#: src/CmdExec.cc:1278
#, c-format
msgid "Usage: %s cmd [args...]\n"
msgstr "Использование: %s команда [аргументы...]\n"

#: src/CmdExec.cc:1284
#, c-format
msgid "%s: cannot create local session\n"
msgstr "%s: не удалось создать локальный сеанс\n"

#: src/commands.cc:114
msgid "!<shell-command>"
msgstr "!<внешняя-команда>"

#: src/commands.cc:115
msgid "Launch shell or shell command\n"
msgstr "Запустить командный процессор или внешнюю команду\n"

#: src/commands.cc:116
msgid "(commands)"
msgstr "(команды)"

#: src/commands.cc:117
msgid ""
"Group commands together to be executed as one command\n"
"You can launch such a group in background\n"
msgstr ""
"Сгруппировать команды вместе, так что они могут быть выполнены как\n"
"единое целое. Такую группу команд можно запустить в фоновом режиме.\n"

#: src/commands.cc:120
msgid "alias [<name> [<value>]]"
msgstr "alias [<имя> [<значение>]]"

#: src/commands.cc:121
msgid ""
"Define or undefine alias <name>. If <value> omitted,\n"
"the alias is undefined, else is takes the value <value>.\n"
"If no argument is given the current aliases are listed.\n"
msgstr ""
"Определить или удалить псевдоним <имя>. Если <значение> не указано,\n"
"то псевдоним удаляется, иначе он принимает указанное значение.\n"
"Если аргументы не указаны, выводится текущий список псевдонимов.\n"

#: src/commands.cc:125
msgid "anon - login anonymously (by default)\n"
msgstr "anon - анонимная регистрация (используется по умолчанию)\n"

#: src/commands.cc:127
msgid "bookmark [SUBCMD]"
msgstr "bookmark [ДЕЙСТВИЕ]"

#: src/commands.cc:128
msgid ""
"bookmark command controls bookmarks\n"
"\n"
"The following subcommands are recognized:\n"
"  add <name> [<loc>] - add current place or given location to bookmarks\n"
"                       and bind to given name\n"
"  del <name>         - remove bookmark with the name\n"
"  edit               - start editor on bookmarks file\n"
"  import <type>      - import foreign bookmarks\n"
"  list               - list bookmarks (default)\n"
msgstr ""
"Команда bookmark предназначена для управления закладками\n"
"\n"
"Распознаются следующие ДЕЙСТВИЯ:\n"
"  add <имя> [<адрес>] - добавить текущий или указанный адрес к закладкам\n"
"  del <имя>           - удалить закладку с указанным именем\n"
"  edit                - правка файла закладок\n"
"  import <тип>        - импортировать закладки, созданные другими "
"программами\n"
"  list                - вывести список закладок (по умолчанию)\n"

#: src/commands.cc:137
msgid "cache [SUBCMD]"
msgstr "cache [ДЕЙСТВИЕ]"

#: src/commands.cc:138
msgid ""
"cache command controls local memory cache\n"
"\n"
"The following subcommands are recognized:\n"
"  stat        - print cache status (default)\n"
"  on|off      - turn on/off caching\n"
"  flush       - flush cache\n"
"  size <lim>  - set memory limit\n"
"  expire <Nx> - set cache expiration time to N seconds (x=s)\n"
"                minutes (x=m) hours (x=h) or days (x=d)\n"
msgstr ""
"Команда cache предназначена для управления локальным кэшем\n"
"\n"
"Распознаются следующие ДЕЙСТВИЯ:\n"
"  stat          - вывести состояние кэша (по умолчанию)\n"
"  on|off        - включить или выключить кэш\n"
"  flush         - очистить кэш\n"
"  size <предел> - ограничить объем памяти\n"
"  expire <Nx>   - установить время устаревания данных в кэше: N секунд "
"(x=s),\n"
"                  минут (x=m), часов (x=h) или дней (x=d)\n"

#: src/commands.cc:146
msgid "cat [-b] <files>"
msgstr "cat [-b] <файлы>"

#: src/commands.cc:147
msgid ""
"cat - output remote files to stdout (can be redirected)\n"
" -b  use binary mode (ascii is the default)\n"
msgstr ""
"cat - вывести содержимое файлов на сервере на стандартный вывод\n"
" -b  использовать бинарный режим (режим ascii по умолчанию)\n"

#: src/commands.cc:149
msgid "cd <rdir>"
msgstr "cd <каталог>"

#: src/commands.cc:150
msgid ""
"Change current remote directory to <rdir>. The previous remote directory\n"
"is stored as `-'. You can do `cd -' to change the directory back.\n"
"The previous directory for each site is also stored on disk, so you can\n"
"do `open site; cd -' even after lftp restart.\n"
msgstr ""
"Изменить текущий каталог на сервере. Предыдущий каталог на сервере\n"
"сохраняется под именем `-'. Вы можете выполнить `cd -', чтобы перейти\n"
"в предыдущий каталог. Последний каталог для каждого сервера\n"
"сохраняется на диске, поэтому можно сделать `open site; cd -' даже\n"
"после повторного запуска lftp.\n"

#: src/commands.cc:154
msgid "chmod [OPTS] mode file..."
msgstr "chmod [КЛЮЧИ] <режим> <файлы>"

#: src/commands.cc:155
msgid ""
"Change the mode of each FILE to MODE.\n"
"\n"
" -c, --changes        - like verbose but report only when a change is made\n"
" -f, --quiet          - suppress most error messages\n"
" -v, --verbose        - output a diagnostic for every file processed\n"
" -R, --recursive      - change files and directories recursively\n"
"\n"
"MODE can be an octal number or symbolic mode (see chmod(1))\n"
msgstr ""
"Изменить права доступа каждого указанного файла на указанный РЕЖИМ.\n"
"\n"
" -c, --changes        - как verbose, но сообщать только если сделано "
"изменение\n"
" -f, --quiet          - подавляет большинство сообщений об ошибках\n"
" -v, --verbose        - печатать сообщение о каждом обрабатываемом файле\n"
" -R, --recursive      - изменить права на файлах и каталогах рекурсивно\n"
"\n"
"РЕЖИМ может быть восьмеричным числом или символическим значением (см. "
"chmod(1))\n"

#: src/commands.cc:164
msgid ""
"Close idle connections. By default only with current server.\n"
" -a  close idle connections with all servers\n"
msgstr ""
"Закрыть неиспользуемые соединения с сервером. По умолчанию - только с\n"
"текущим сервером.\n"
" -a  закрыть неиспользуемые соединения со всеми серверами\n"

#: src/commands.cc:166
msgid "[re]cls [opts] [path/][pattern]"
msgstr ""

#: src/commands.cc:167
msgid ""
"List remote files. You can redirect output of this command to file\n"
"or via pipe to external command.\n"
"\n"
" -1                   - single-column output\n"
" -a, --all            - show dot files\n"
" -B, --basename       - show basename of files only\n"
"     --block-size=SIZ - use SIZ-byte blocks\n"
" -d, --directory      - list directory entries instead of contents\n"
" -F, --classify       - append indicator (one of /@) to entries\n"
" -h, --human-readable - print sizes in human readable format (e.g., 1K)\n"
"     --si             - likewise, but use powers of 1000 not 1024\n"
" -k, --kilobytes      - like --block-size=1024\n"
" -l, --long           - use a long listing format\n"
" -q, --quiet          - don't show status\n"
" -s, --size           - print size of each file\n"
"     --filesize       - if printing size, only print size for files\n"
" -i, --nocase         - case-insensitive pattern matching\n"
" -I, --sortnocase     - sort names case-insensitively\n"
" -D, --dirsfirst      - list directories first\n"
"     --sort=OPT       - \"name\", \"size\", \"date\"\n"
" -S                   - sort by file size\n"
" --user, --group, --perms, --date, --linkcount, --links\n"
"                      - show individual fields\n"
" --time-style=STYLE   - use specified time format\n"
"\n"
"By default, cls output is cached, to see new listing use `recls' or\n"
"`cache flush'.\n"
"\n"
"The variables cls-default and cls-completion-default can be used to\n"
"specify defaults for cls listings and completion listings, respectively.\n"
"For example, to make completion listings show file sizes, set\n"
"cls-completion-default to \"-s\".\n"
"\n"
"Tips: Use --filesize with -D to pack the listing better.  If you don't\n"
"always want to see file sizes, --filesize in cls-default will affect the\n"
"-s flag on the commandline as well.  Add `-i' to cls-completion-default\n"
"to make filename completion case-insensitive.\n"
msgstr ""

#: src/commands.cc:217
msgid "debug [OPTS] [<level>|off]"
msgstr "debug [КЛЮЧИ] [<уровень>|off]"

#: src/commands.cc:218
msgid ""
"Set debug level to given value or turn debug off completely.\n"
" -o <file>  redirect debug output to the file\n"
" -c  show message context\n"
" -p  show PID\n"
" -t  show timestamps\n"
msgstr ""
"Устанавливает уровень подробности отладочных сообщений или выключает\n"
"их полностью.\n"
" -o <файл>  направить отладочный вывод в указанный файл\n"
" -c  показывать контекст сообщений\n"
" -p  показывать идентификатор процесса\n"
" -t  показывать дату и время сообщений\n"

#: src/commands.cc:223
msgid "du [options] <dirs>"
msgstr "du [КЛЮЧИ] <каталоги>"

#: src/commands.cc:224
msgid ""
"Summarize disk usage.\n"
" -a, --all             write counts for all files, not just directories\n"
"     --block-size=SIZ  use SIZ-byte blocks\n"
" -b, --bytes           print size in bytes\n"
" -c, --total           produce a grand total\n"
" -d, --max-depth=N     print the total for a directory (or file, with --"
"all)\n"
"                       only if it is N or fewer levels below the command\n"
"                       line argument;  --max-depth=0 is the same as\n"
"                       --summarize\n"
" -F, --files           print number of files instead of sizes\n"
" -h, --human-readable  print sizes in human readable format (e.g., 1K 234M "
"2G)\n"
" -H, --si              likewise, but use powers of 1000 not 1024\n"
" -k, --kilobytes       like --block-size=1024\n"
" -m, --megabytes       like --block-size=1048576\n"
" -S, --separate-dirs   do not include size of subdirectories\n"
" -s, --summarize       display only a total for each argument\n"
"     --exclude=PAT     exclude files that match PAT\n"
msgstr ""

#: src/commands.cc:242
msgid "edit [OPTS] <file>"
msgstr "edit [КЛЮЧИ] <файл>"

#: src/commands.cc:243
msgid ""
"Retrieve remote file to a temporary location, run a local editor on it\n"
"and upload the file back if changed.\n"
" -k  keep the temporary file\n"
" -o <temp>  explicit temporary file location\n"
msgstr ""
"Скачать файл с сервера во временный локальный файл, запустить на нём\n"
"редактор и загрузить файл назад на сервер, если он был изменён.\n"
" -k  не удалять временный локальный файл\n"
" -o <temp>  использовать указанное имя для временного файла\n"

#: src/commands.cc:248
msgid "exit [<code>|bg]"
msgstr "exit [<код>|bg]"

#: src/commands.cc:249
msgid ""
"exit - exit from lftp or move to background if jobs are active\n"
"\n"
"If no jobs active, the code is passed to operating system as lftp\n"
"termination status. If omitted, exit code of last command is used.\n"
"`bg' forces moving to background if cmd:move-background is false.\n"
msgstr ""
"exit - выйти из lftp или, если есть задания, перейти в фоновый режим\n"
"\n"
"Если нет активных заданий, указанный код передается в систему в качестве "
"кода\n"
"завершения. Если код не указан, используется код завершения последней "
"команды.\n"
"`bg' заставляет lftp перейти в фоновый режим, даже если cmd:move-background\n"
" установлен в `off'.\n"

#: src/commands.cc:255
msgid ""
"Usage: find [OPTS] [directory]\n"
"Print contents of specified directory or current directory recursively.\n"
"Directories in the list are marked with trailing slash.\n"
"You can redirect output of this command.\n"
" -d, --maxdepth=LEVELS  Descend at most LEVELS of directories.\n"
msgstr ""
"Использование: find [КЛЮЧИ] [каталог]\n"
"Вывести содержимое указанного или текущего каталога рекурсивно.\n"
"Каталоги в списке помечаются дополнительной косой чертой в конце имени.\n"
"Вы можете перенаправить вывод этой команды.\n"
" -d, --maxdepth=УРОВНИ  Максимальная глубина рекурсивного спуска по "
"каталогам.\n"

#: src/commands.cc:260
msgid "get [OPTS] <rfile> [-o <lfile>]"
msgstr "get [КЛЮЧИ] <файл> [-o <лок.файл>]"

#: src/commands.cc:261
msgid ""
"Retrieve remote file <rfile> and store it to local file <lfile>.\n"
" -o <lfile> specifies local file name (default - basename of rfile)\n"
" -c  continue, resume transfer\n"
" -E  delete remote files after successful transfer\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Получить <файл> с сервера и поместить его в локальный файл.\n"
" -o <лок.файл> указывает имя локального файла\n"
"     (по умолчанию - имя файла на сервере без каталога)\n"
" -c  возобновить получение файла\n"
" -E  удалить файлы на сервере после успешного получения\n"
" -a  использовать режим ascii (бинарный режим по умолчанию)\n"
" -O <база> указывает базовый каталог или URL, куда класть полученные файлы\n"

#: src/commands.cc:268
msgid "glob [OPTS] <cmd> <args>"
msgstr "glob [КЛЮЧИ] <к-да> <арг-ты>"

#: src/commands.cc:270
msgid ""
"Expand wildcards and run specified command.\n"
"Options can be used to expand wildcards to list of files, directories,\n"
"or both types. Type selection is not very reliable and depends on server.\n"
"If entry type cannot be determined, it will be included in the list.\n"
" -f  plain files (default)\n"
" -d  directories\n"
" -a  all types\n"
" --exist      return zero exit code when the patterns expand to non-empty "
"list\n"
" --not-exist  return zero exit code when the patterns expand to an empty "
"list\n"
msgstr ""
"Заменить шаблоны имен файлов на список файлов и выполнить команду.\n"
"С помощью опций можно выбрать, нужно ли подставлять имена файлов, каталогов\n"
"или обоих типов. Этот выбор типа файлов не очень надежен и зависит от "
"сервера.\n"
"Если тип имени не может быть определен, то оно будет включено в список.\n"
" -f  обычные файлы (по умолчанию)\n"
" -d  только каталоги\n"
" -a  все типы\n"
" --exist      вернуть код 0, если список не будет пуст\n"
" --not-exist  вернуть код 0, если список будет пуст\n"

#: src/commands.cc:279
msgid "help [<cmd>]"
msgstr "help [<команда>]"

#: src/commands.cc:280
msgid "Print help for command <cmd>, or list of available commands\n"
msgstr "Вывести помощь для указанной <команды>, или список всех команд\n"

#: src/commands.cc:282
msgid ""
"List running jobs. -v means verbose, several -v can be specified.\n"
"If <job_no> is specified, only list a job with that number.\n"
msgstr ""
"Вывести список заданий. -v означает большую степень подробности,\n"
"допустимы несколько ключей -v.\n"
"Если указан номер задания, то выводится информация только об этом задании.\n"

#: src/commands.cc:284
msgid "kill all|<job_no>"
msgstr "kill all|<номер_задания>"

#: src/commands.cc:285
msgid "Delete specified job with <job_no> or all jobs\n"
msgstr "Удалить задание с указанным номером или все задания\n"

#: src/commands.cc:286
msgid "lcd <ldir>"
msgstr "lcd <локальный_каталог>"

#: src/commands.cc:287
msgid ""
"Change current local directory to <ldir>. The previous local directory\n"
"is stored as `-'. You can do `lcd -' to change the directory back.\n"
msgstr ""
"Сменить текущий локальный каталог. Предыдущий локальный каталог сохраняется\n"
"под именем `-'. Вы можете выполнить `lcd -', чтобы сменить каталог на "
"прежний.\n"

#: src/commands.cc:289
msgid "lftp [OPTS] <site>"
msgstr "lftp [КЛЮЧИ] <адрес>"

#: src/commands.cc:290
msgid ""
"`lftp' is the first command executed by lftp after rc files\n"
" -f <file>           execute commands from the file and exit\n"
" -c <cmd>            execute the commands and exit\n"
" --norc              don't execute rc files from the home directory\n"
" --help              print this help and exit\n"
" --version           print lftp version and exit\n"
"Other options are the same as in `open' command:\n"
" -e <cmd>            execute the command just after selecting\n"
" -u <user>[,<pass>]  use the user/password for authentication\n"
" -p <port>           use the port for connection\n"
" -s <slot>           assign the connection to this slot\n"
" -d                  switch on debugging mode\n"
" <site>              host name, URL or bookmark name\n"
msgstr ""
"`lftp' - первая команда, которую выполняет lftp после rc-файлов\n"
" -f <файл>           выполнить команды из указанного файла и выйти\n"
" -c <команда>        выполнить команду и выйти\n"
" --norc              не выполнять rc-файлы из домашнего каталога\n"
" --help              вывести данную подсказку и выйти\n"
" --version           вывести информацию о версии и выйти\n"
"Остальные ключи аналогичны ключам команды `open'\n"
" -e <команда>        выполнить команду после выбора сервера\n"
" -u <имя>[,<пароль>] использовать для аутентификации имя/пароль\n"
" -p <порт>           использовать для соединения указанный порт\n"
" -s <слот>           перейти в указанный слот\n"
" -d                  включить отладочный режим\n"
" <адрес>             имя сервера, URL или имя закладки\n"

#: src/commands.cc:303
msgid "ln [-s] <file1> <file2>"
msgstr "ln [-s] <файл1> <файл2>"

#: src/commands.cc:304
msgid "Link <file1> to <file2>\n"
msgstr "Привязать <файл1> к имени <файл2>\n"

#: src/commands.cc:308
msgid "ls [<args>]"
msgstr "ls [<аргументы>]"

#: src/commands.cc:309
msgid ""
"List remote files. You can redirect output of this command to file\n"
"or via pipe to external command.\n"
"By default, ls output is cached, to see new listing use `rels' or\n"
"`cache flush'.\n"
"See also `help cls'.\n"
msgstr ""
"Вывести список файлов на сервере. Вы можете направить вывод этой\n"
"команды в файл или через программный канал во внешнюю программу.\n"
"По-умолчанию, вывод команды ls кэшируется; чтобы увидеть обновленный\n"
"список файлов, используйте команды `rels' или `cache flush'.\n"
"Смотри также `help cls'.\n"

#: src/commands.cc:314
msgid "mget [OPTS] <files>"
msgstr "mget [КЛЮЧИ] <файлы>"

#: src/commands.cc:315
msgid ""
"Gets selected files with expanded wildcards\n"
" -c  continue, resume transfer\n"
" -d  create directories the same as in file names and get the\n"
"     files into them instead of current directory\n"
" -E  delete remote files after successful transfer\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Получить указанные файлы с поддержкой шаблонов в именах файлов\n"
" -c  возобновить получение файлов\n"
" -d  создавать каталоги в соответствии с именами файлов\n"
"     и сохранять файлы в этих каталогах вместо текущего\n"
" -E  удалить файлы на сервере после успешного получения\n"
" -a  использовать режим ascii (бинарный режим по умолчанию)\n"
" -O <база> указывает базовый каталог или URL, куда класть полученные файлы\n"

#: src/commands.cc:322
msgid "mirror [OPTS] [remote [local]]"
msgstr "mirror [КЛЮЧИ] [<откуда> [<куда>]]"

#: src/commands.cc:323
msgid "mkdir [OPTS] <dirs>"
msgstr "mkdir [КЛЮЧИ] <каталоги>"

#: src/commands.cc:324
msgid ""
"Make remote directories\n"
" -p  make all levels of path\n"
" -f  be quiet, suppress messages\n"
msgstr ""
"Создать каталоги на сервере\n"
" -p  создать все составляющие пути\n"
" -f  не выводить сообщения\n"

#: src/commands.cc:327
msgid "module name [args]"
msgstr "module <имя> [<аргументы>]"

#: src/commands.cc:328
msgid ""
"Load module (shared object). The module should contain function\n"
"   void module_init(int argc,const char *const *argv)\n"
"If name contains a slash, then the module is searched in current\n"
"directory, otherwise in directories specified by setting module:path.\n"
msgstr ""
"Загрузить модуль (разделяемый объект). Модуль должен предоставлять функцию\n"
"   void module_init(int argc,const char *const *argv)\n"
"Если имя содержит символ `/', то поиск модуля будет производиться в текущем\n"
"каталоге, в противном случае - в каталогах, указанных в переменной module:"
"path.\n"

#: src/commands.cc:332
msgid "more <files>"
msgstr "more <файлы>"

#: src/commands.cc:333
msgid "Same as `cat <files> | more'. if PAGER is set, it is used as filter\n"
msgstr ""
"То же, что и `cat <файлы> | more'. Если переменная среды PAGER\n"
"установлена, то в качестве фильтра используется ее значение.\n"

#: src/commands.cc:334
msgid "mput [OPTS] <files>"
msgstr "mput [КЛЮЧИ] <файлы>"

#: src/commands.cc:335
msgid ""
"Upload files with wildcard expansion\n"
" -c  continue, reput\n"
" -d  create directories the same as in file names and put the\n"
"     files into them instead of current directory\n"
" -E  delete local files after successful transfer (dangerous)\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Поместить файлы на сервер с поддержкой шаблонов в именах файлов\n"
" -c  возобновить передачу (reput)\n"
" -d  создавать каталоги в соответствии с именами файлов\n"
"     и сохранять файлы в этих каталогах вместо текущего\n"
" -E  удалить локальные файлы после успешной передачи (опасно)\n"
" -a  использовать режим ascii (бинарный режим по умолчанию)\n"
" -O <база> указывает базовый каталог или URL, куда класть переданные файлы\n"

#: src/commands.cc:342
msgid "mrm <files>"
msgstr "mrm <файлы>"

#: src/commands.cc:343
msgid "Removes specified files with wildcard expansion\n"
msgstr "Удалить указанные <файлы> с поддержкой шаблонов в именах файлов\n"

#: src/commands.cc:344
msgid "mv <file1> <file2>"
msgstr "mv <файл1> <файл2>"

#: src/commands.cc:345
msgid "Rename <file1> to <file2>\n"
msgstr "Переименовать <файл1> в <файл2>\n"

#: src/commands.cc:346
msgid "mmv [OPTS] <files> <target-dir>"
msgstr "mmv [КЛЮЧИ] <файлы> <конечный-каталог>"

#: src/commands.cc:347
msgid ""
"Move <files> to <target-directory> with wildcard expansion\n"
" -O <dir>  specifies the target directory (alternative way)\n"
msgstr ""

#: src/commands.cc:349
msgid "[re]nlist [<args>]"
msgstr "[re]nlist [<аргументы>]"

#: src/commands.cc:350
msgid ""
"List remote file names.\n"
"By default, nlist output is cached, to see new listing use `renlist' or\n"
"`cache flush'.\n"
msgstr ""
"Вывести список имен файлов на сервере.\n"
"По-умолчанию, вывод команды nlist кэшируется; чтобы увидеть обновленный\n"
"список файлов, используйте команды `renlist' или `cache flush'.\n"

#: src/commands.cc:353
msgid "open [OPTS] <site>"
msgstr "open [КЛЮЧИ] <адрес>"

#: src/commands.cc:354
msgid ""
"Select a server, URL or bookmark\n"
" -e <cmd>            execute the command just after selecting\n"
" -u <user>[,<pass>]  use the user/password for authentication\n"
" -p <port>           use the port for connection\n"
" -s <slot>           assign the connection to this slot\n"
" -d                  switch on debugging mode\n"
" <site>              host name, URL or bookmark name\n"
msgstr ""
"Выбрать сервер, URL или закладку\n"
" -e <команда>        выполнить команду после выбора сервера\n"
" -u <имя>[,<пароль>] использовать для регистрации указанные имя/пароль\n"
" -p <порт>           использовать для соединения указанный порт\n"
" -s <слот>           перейти в указанный слот\n"
" -d                  включить отладочный режим\n"
" <адрес>             имя сервера, URL или имя закладки\n"

#: src/commands.cc:361
msgid "pget [OPTS] <rfile> [-o <lfile>]"
msgstr "pget [КЛЮЧИ] <файл> [-o <лок.файл>]"

#: src/commands.cc:362
msgid ""
"Gets the specified file using several connections. This can speed up "
"transfer,\n"
"but loads the net heavily impacting other users. Use only if you really\n"
"have to transfer the file ASAP.\n"
"\n"
"Options:\n"
" -c  continue transfer. Requires <lfile>.lftp-pget-status file.\n"
" -n <maxconn>  set maximum number of connections (default is is taken from\n"
"     pget:default-n setting)\n"
" -O <base> specifies base directory where files should be placed\n"
msgstr ""
"Получить указанный файл с использованием нескольких соединений. Это может\n"
"ускорить прием, но загружает сеть сильнее обычного, а также влияет на "
"других\n"
"пользователей. Используйте эту возможность только в тех случаях, когда вам\n"
"действительно необходимо получить файл как можно скорее.\n"
"\n"
"Ключи:\n"
" -c  продолжить получение файла. Требуется файл <лок.файл>.lftp-pget-"
"status.\n"
" -n <кол-во>  максимальное количество одновременных соединений\n"
"     (по умолчанию берется настройка pget:default-n)\n"
" -O <база> указывает базовый каталог, куда класть полученные файлы\n"

#: src/commands.cc:370
msgid "put [OPTS] <lfile> [-o <rfile>]"
msgstr "put [КЛЮЧИ] <лок.файл> [-o <файл>]"

#: src/commands.cc:371
msgid ""
"Upload <lfile> with remote name <rfile>.\n"
" -o <rfile> specifies remote file name (default - basename of lfile)\n"
" -c  continue, reput\n"
"     it requires permission to overwrite remote files\n"
" -E  delete local files after successful transfer (dangerous)\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Поместить локальный файл на сервер под указанным именем.\n"
" -o <файл> указывает имя файла на сервере\n"
"     (по умолчанию - имя локального файла без каталога)\n"
" -c  возобновить пересылку (reput)\n"
"     этот ключ требует полномочий на замещение файлов на сервере\n"
" -E  удалить локальные файлы после успешной передачи (опасно)\n"
" -a  использовать режим ascii (бинарный режим по умолчанию)\n"
" -O <база> указывает базовый каталог или URL, куда класть переданные файлы\n"

#: src/commands.cc:379
msgid ""
"Print current remote URL.\n"
" -p  show password\n"
msgstr ""
"Вывести текущий URL сервера.\n"
" -p  показывать пароль\n"

#: src/commands.cc:381
msgid "queue [OPTS] [<cmd>]"
msgstr "queue [КЛЮЧИ] [<команда>]"

#: src/commands.cc:382
msgid ""
"\n"
"       queue [-n num] <command>\n"
"\n"
"Add the command to queue for current site. Each site has its own command\n"
"queue. `-n' adds the command before the given item in the queue. It is\n"
"possible to queue up a running job by using command `queue wait <jobno>'.\n"
"\n"
"       queue --delete|-d [index or wildcard expression]\n"
"\n"
"Delete one or more items from the queue. If no argument is given, the last\n"
"entry in the queue is deleted.\n"
"\n"
"       queue --move|-m <index or wildcard expression> [index]\n"
"\n"
"Move the given items before the given queue index, or to the end if no\n"
"destination is given.\n"
"\n"
"Options:\n"
" -q                  Be quiet.\n"
" -v                  Be verbose.\n"
" -Q                  Output in a format that can be used to re-queue.\n"
"                     Useful with --delete.\n"
msgstr ""

#: src/commands.cc:403
msgid "quote <cmd>"
msgstr "quote <команда>"

#: src/commands.cc:404
msgid ""
"Send the command uninterpreted. Use with caution - it can lead to\n"
"unknown remote state and thus will cause reconnect. You cannot\n"
"be sure that any change of remote state because of quoted command\n"
"is solid - it can be reset by reconnect at any time.\n"
msgstr ""
"Послать команду без обработки. Используйте с осторожностью, это может\n"
"привести к неизвестному состоянию сервера и вызвать рассоединение.\n"
"Вы не можете также полагаться на изменение состояния сервера, так как\n"
"повторное соединение может восстановить состояние в любой момент.\n"

#: src/commands.cc:409
msgid ""
"recls [<args>]\n"
"Same as `cls', but don't look in cache\n"
msgstr ""
"recls [<аргументы>]\n"
"То же, что и `cls', но не обращаться к кэшу\n"

#: src/commands.cc:412
msgid ""
"Usage: reget [OPTS] <rfile> [-o <lfile>]\n"
"Same as `get -c'\n"
msgstr ""
"reget [КЛЮЧИ] <файл> [-o <лок.файл>]\n"
"То же, что и `get -c'\n"

#: src/commands.cc:415
msgid ""
"Usage: rels [<args>]\n"
"Same as `ls', but don't look in cache\n"
msgstr ""
"Использование: rels [<аргументы>]\n"
"То же, что и `ls', но не обращаться к кэшу\n"

#: src/commands.cc:418
msgid ""
"Usage: renlist [<args>]\n"
"Same as `nlist', but don't look in cache\n"
msgstr ""
"Использование: [<аргументы>]\n"
"То же, что и `nlist', но не обращаться к кэшу\n"

#: src/commands.cc:420
msgid "repeat [OPTS] [delay] [command]"
msgstr "repeat [КЛЮЧИ] [задержка] [команда]"

#: src/commands.cc:422
msgid ""
"Usage: reput <lfile> [-o <rfile>]\n"
"Same as `put -c'\n"
msgstr ""
"reput <лок.файл> [-o <файл>]\n"
"То же, что и `put -c'\n"

#: src/commands.cc:424
msgid "rm [-r] [-f] <files>"
msgstr "rm [-r] [-f] <файлы>"

#: src/commands.cc:425
msgid ""
"Remove remote files\n"
" -r  recursive directory removal, be careful\n"
" -f  work quietly\n"
msgstr ""
"Удалить указанные <файлы> на сервере\n"
" -r  рекурсивное удаление каталогов (пользуйтесь с осторожностью)\n"
" -f  работать тихо\n"

#: src/commands.cc:428
msgid "rmdir [-f] <dirs>"
msgstr "rmdir [-f] <каталоги>"

#: src/commands.cc:429
msgid "Remove remote directories\n"
msgstr "Удалить указанные <каталоги> на сервере\n"

#: src/commands.cc:430
msgid "scache [<session_no>]"
msgstr "scache [<номер_сеанса>]"

#: src/commands.cc:431
msgid "List cached sessions or switch to specified session number\n"
msgstr ""
"Вывести список резервных сеансов или переключиться на сеанс\n"
"с указанным номером.\n"

#: src/commands.cc:432
msgid "set [OPT] [<var> [<val>]]"
msgstr "set [КЛЮЧИ] [<перем> [<значение>]]"

#: src/commands.cc:433
msgid ""
"Set variable to given value. If the value is omitted, unset the variable.\n"
"Variable name has format ``name/closure'', where closure can specify\n"
"exact application of the setting. See lftp(1) for details.\n"
"If set is called with no variable then only altered settings are listed.\n"
"It can be changed by options:\n"
" -a  list all settings, including default values\n"
" -d  list only default values, not necessary current ones\n"
msgstr ""
"Присвоить переменной указанное значение. Если значение не задано, удалить\n"
"переменную. Имя переменной имеет формат ``имя/адрес'', где адрес может\n"
"указывать точный контекст применения переменной. Для получения "
"дополнительной\n"
"информации смотрите lftp(1).\n"
"Если в вызове set не указано имя переменной, на экран будет выведен список\n"
"переменных с измененными значениями. Для вывода другой информации можно\n"
"использовать следующие ключи:\n"
" -a  вывести все переменные, включая значения по умолчанию\n"
" -d  вывести только значения по умолчанию, не обязательно текущие значения.\n"

#: src/commands.cc:441
msgid "site <site-cmd>"
msgstr "site <команда>"

#: src/commands.cc:442
msgid ""
"Execute site command <site_cmd> and output the result\n"
"You can redirect its output\n"
msgstr ""
"Исполнить site-команду на сервере и вывести ее результаты.\n"
"Вы можете перенаправить вывод этой команды.\n"

#: src/commands.cc:446
msgid ""
"Usage: slot [<label>]\n"
"List assigned slots.\n"
"If <label> is specified, switch to the slot named <label>.\n"
msgstr ""
"Использование: slot [<имя>]\n"
"Вывести список слотов.\n"
"Если указано имя, то переключиться в слот с таким именем.\n"

#: src/commands.cc:449
msgid "source <file>"
msgstr "source <файл>"

#: src/commands.cc:450
msgid "Execute commands recorded in file <file>\n"
msgstr "Выполнить команды из указанного файла\n"

#: src/commands.cc:452
msgid "torrent [OPTS] <file|URL>..."
msgstr "torrent [КЛЮЧИ] <файл|URL>"

#: src/commands.cc:453
msgid "user <user|URL> [<pass>]"
msgstr "user <имя|URL> [<пароль>]"

#: src/commands.cc:454
msgid ""
"Use specified info for remote login. If you specify URL, the password\n"
"will be cached for future usage.\n"
msgstr ""
"Использовать указанную информацию для авторизации. Если указан URL, то "
"пароль\n"
"будет запомнен для дальнейшего использования.\n"

#: src/commands.cc:457
msgid "Shows lftp version\n"
msgstr "Вывести версию lftp\n"

#: src/commands.cc:458
msgid "wait [<jobno>]"
msgstr "wait [<номер_задания>]"

#: src/commands.cc:459
msgid ""
"Wait for specified job to terminate. If jobno is omitted, wait\n"
"for last backgrounded job.\n"
msgstr ""
"Ждать завершения указанного задания. Если номер задания не указан,\n"
"ждать завершения последнего переведенного в фоновый режим задания.\n"

#: src/commands.cc:461
msgid "zcat <files>"
msgstr "zcat <файлы>"

#: src/commands.cc:462
msgid "Same as cat, but filter each file through zcat\n"
msgstr "То же, что и cat, но фильтровать каждый файл через программу zcat\n"

#: src/commands.cc:463
msgid "zmore <files>"
msgstr "zmore <файлы>"

#: src/commands.cc:464
msgid "Same as more, but filter each file through zcat\n"
msgstr "То же, что и more, но фильтровать каждый файл через программу zcat\n"

#: src/commands.cc:466
msgid "Same as cat, but filter each file through bzcat\n"
msgstr "То же, что и cat, но фильтровать каждый файл через программу bzcat\n"

#: src/commands.cc:468
msgid "Same as more, but filter each file through bzcat\n"
msgstr "То же, что и more, но фильтровать каждый файл через программу bzcat\n"

#: src/commands.cc:524
#, c-format
msgid "Usage: %s local-dir\n"
msgstr "Использование: %s <локальный_каталог>\n"

#: src/commands.cc:560
#, c-format
msgid "lcd ok, local cwd=%s\n"
msgstr "lcd ok, локальный каталог=%s\n"

#: src/commands.cc:576
#, c-format
msgid "Usage: cd remote-dir\n"
msgstr "Использование: cd <каталог_сервера>\n"

#: src/commands.cc:588
#, c-format
msgid "%s: no old directory for this site\n"
msgstr "%s: нет предыдущего каталога для этого сервера\n"

#: src/commands.cc:677
#, c-format
msgid "Usage: %s [<exit_code>]\n"
msgstr "Использование: %s [<код_завершения>]\n"

#: src/commands.cc:686
msgid ""
"There are running jobs and `cmd:move-background' is not set.\n"
"Use `exit bg' to force moving to background or `kill all' to terminate "
"jobs.\n"
msgstr ""
"Имеются незавершенные задания и `cmd:move-background' не включено.\n"
"Используйте `exit bg' чтобы переместить lftp в фоновый режим или `kill all'\n"
"чтобы завершить все задания.\n"

#: src/commands.cc:703
msgid ""
"\n"
"lftp now tricks the shell to move it to background process group.\n"
"lftp continues to run in the background despite the `Stopped' message.\n"
"lftp will automatically terminate when all jobs are finished.\n"
"Use `fg' shell command to return to lftp if it is still running.\n"
msgstr ""

#: src/commands.cc:837 src/commands.cc:3616
#, c-format
msgid "Try `%s --help' for more information\n"
msgstr "Введите `%s --help' для получения дополнительной информации\n"

#: src/commands.cc:856
#, c-format
msgid "%s: -c, -f, -v, -h conflict with other `open' options and arguments\n"
msgstr ""
"%s: -c, -f, -v, -h несовместимы с другими опциями и аргументами команды "
"`open'\n"

#: src/commands.cc:956
#, c-format
msgid "Usage: %s [-e cmd] [-p port] [-u user[,pass]] <host|url>\n"
msgstr ""
"Использование: %s [-e команда] [-p порт] [-u имя[,пароль]] <машина|url>\n"

#: src/commands.cc:1046 src/commands.cc:2276 src/DummyProto.cc:65
#: src/MirrorJob.cc:2172 src/MirrorJob.cc:2194
msgid " - not supported protocol"
msgstr " - протокол не поддерживается"

#: src/commands.cc:1078 src/commands.cc:2260
msgid "Password: "
msgstr "Пароль: "

#: src/commands.cc:1080
#, c-format
msgid "%s: GetPass() failed -- assume anonymous login\n"
msgstr "%s: ошибка GetPass() -- подразумевается анонимная регистрация\n"

#: src/commands.cc:1191 src/commands.cc:1284 src/commands.cc:1660
#: src/commands.cc:1691 src/commands.cc:1829 src/commands.cc:1914
#: src/commands.cc:2187 src/commands.cc:2381 src/commands.cc:2543
#: src/commands.cc:2588 src/commands.cc:2616 src/commands.cc:2623
#: src/commands.cc:2930 src/commands.cc:2957 src/commands.cc:2964
#: src/commands.cc:3293 src/lftp.cc:267 src/MirrorJob.cc:2136
#: src/SleepJob.cc:144 src/SleepJob.cc:200 src/Torrent.cc:4169
#, c-format
msgid "Try `help %s' for more information.\n"
msgstr "Запустите `help %s' для получения дополнительной информации.\n"

#: src/commands.cc:1201
#, c-format
msgid "Usage: %s [OPTS] command args...\n"
msgstr "Использование: %s [КЛЮЧИ] команда аргументы...\n"

#: src/commands.cc:1254
#, c-format
msgid "%s: -n: positive number expected. "
msgstr "%s: -n: Ожидается числовое значение больше нуля. "

#: src/commands.cc:1306
#, c-format
msgid "Created a stopped queue.\n"
msgstr "Создана остановленная очередь.\n"

#: src/commands.cc:1349 src/commands.cc:1377
#, c-format
msgid "%s: No queue is active.\n"
msgstr "%s: Нет активной очереди.\n"

#: src/commands.cc:1369
#, c-format
msgid "%s: -m: Number expected as second argument. "
msgstr "%s: -m: Ожидается числовое значение во втором аргументе. "

#: src/commands.cc:1421
#, c-format
msgid "Usage: %s <cmd>\n"
msgstr "Использование: %s <команда>\n"

#: src/commands.cc:1527
msgid "invalid argument for `--sort'"
msgstr "неверный аргумент для `--sort'"

#: src/commands.cc:1557
msgid "invalid block size"
msgstr "неверный размер блока"

#: src/commands.cc:1700
#, c-format
msgid "Usage: %s [OPTS] files...\n"
msgstr "Использование: %s [КЛЮЧИ] файлы...\n"

#: src/commands.cc:1785 src/commands.cc:1814
#, c-format
msgid "%s: %s: Number expected. "
msgstr "%s: %s: Ожидается числовое значение. "

#: src/commands.cc:1834
#, c-format
msgid "%s: --continue conflicts with --remove-target.\n"
msgstr "%s: --continue конфликтует с --remove-target.\n"

#: src/commands.cc:1844 src/commands.cc:1920
#, c-format
msgid "File name missed. "
msgstr "Пропущено имя файла. "

#: src/commands.cc:1989
#, c-format
msgid "Usage: %s %s[-f] files...\n"
msgstr "Использование: %s %s[-f] файлы...\n"

#: src/commands.cc:2032
#, c-format
msgid "Usage: %s [-e] <file|command>\n"
msgstr "Использование: %s [-e] <файл|команда>\n"

#: src/commands.cc:2079
#, c-format
msgid "Usage: %s [-v] [-v] ...\n"
msgstr "Использование: %s [-v] [-v] ...\n"

#: src/commands.cc:2093 src/commands.cc:2347 src/commands.cc:2469
#: src/commands.cc:2685 src/commands.cc:3101 src/commands.cc:3182
#: src/lftp.cc:279
#, c-format
msgid "%s: %s - not a number\n"
msgstr "%s: %s - не является числом\n"

#: src/commands.cc:2100 src/commands.cc:2326 src/commands.cc:2356
#: src/commands.cc:2487
#, c-format
msgid "%s: %d - no such job\n"
msgstr "%s: %d - указанное задание отсутствует\n"

#: src/commands.cc:2135
#, c-format
msgid "Usage: %s [-p]\n"
msgstr "Использование: %s [-p]\n"

#: src/commands.cc:2227
#, c-format
msgid "debug level is %d, output goes to %s\n"
msgstr "уровень отладки %d, вывод направлен в %s\n"

#: src/commands.cc:2230
#, c-format
msgid "debug is off\n"
msgstr "отладка выключена\n"

#: src/commands.cc:2241
#, c-format
msgid "Usage: %s <user|URL> [<pass>]\n"
msgstr "Использование: %s <имя|URL> [<пароль>]\n"

#: src/commands.cc:2316 src/commands.cc:2479
#, c-format
msgid "%s: no current job\n"
msgstr "%s: текущее задание отсутствует\n"

#: src/commands.cc:2328
#, c-format
msgid "Usage: %s <jobno> ... | all\n"
msgstr "Использование: %s <номер_задания> ... | all\n"

#: src/commands.cc:2409
#, c-format
msgid "%s: %s. Use `set -a' to look at all variables.\n"
msgstr "%s: %s. Используйте `set -a' для просмотра списка всех переменных.\n"

#: src/commands.cc:2453
#, c-format
msgid "Usage: %s [<jobno>]\n"
msgstr "Использование: %s [<номер_задания>]\n"

#: src/commands.cc:2492
#, c-format
msgid "%s: some other job waits for job %d\n"
msgstr "%s: другое задание ожидает завершения задания %d\n"

#: src/commands.cc:2497
#, c-format
msgid "%s: wait loop detected\n"
msgstr "%s: обнаружен цикл в цепочке ожидания\n"

#: src/commands.cc:2553
#, c-format
msgid "Usage: %s [OPTS] <files> <target-dir>\n"
msgstr "Использование: %s [КЛЮЧИ] <файлы> <конечный-каталог>\n"

#: src/commands.cc:2615 src/commands.cc:2956
#, c-format
msgid "Invalid command. "
msgstr "Недопустимая команда. "

#: src/commands.cc:2622 src/commands.cc:2963
#, c-format
msgid "Ambiguous command. "
msgstr "Неоднозначное сокращение. "

#: src/commands.cc:2641
#, c-format
msgid "%s: Operand missed for size\n"
msgstr "%s: Пропущен параметр размера\n"

#: src/commands.cc:2658
#, c-format
msgid "%s: Operand missed for `expire'\n"
msgstr "%s: Пропущен параметр `expire'\n"

#: src/commands.cc:2691
#, c-format
msgid ""
"%s: %s - no such cached session. Use `scache' to look at session list.\n"
msgstr ""
"%s: %s - нет такого резервного сеанса.\n"
"Для просмотра списка резервных сеансов используйте команду `scache'.\n"

#: src/commands.cc:2715
#, c-format
msgid "Sorry, no help for %s\n"
msgstr "Извините, нет помощи для %s\n"

#: src/commands.cc:2720
#, c-format
msgid "%s is a built-in alias for %s\n"
msgstr "%s является встроенным псевдонимом для %s\n"

#: src/commands.cc:2725
#, c-format
msgid "Usage: %s\n"
msgstr "Использование: %s\n"

#: src/commands.cc:2733
#, c-format
msgid "%s is an alias to `%s'\n"
msgstr "%s является псевдонимом для `%s'\n"

#: src/commands.cc:2737
#, c-format
msgid "No such command `%s'. Use `help' to see available commands.\n"
msgstr ""
"Команда `%s' отсутствует.\n"
"Введите `help' для получения списка доступных команд.\n"

#: src/commands.cc:2739
#, c-format
msgid "Ambiguous command `%s'. Use `help' to see available commands.\n"
msgstr ""
"Неоднозначное сокращение `%s'. Введите `help' для списка доступных команд.\n"

#: src/commands.cc:2805
#, c-format
msgid "LFTP | Version %s | Copyright (c) 1996-%d Alexander V. Lukyanov\n"
msgstr "LFTP | Версия %s | Copyright (c) 1996-%d Александр В. Лукьянов\n"

#: src/commands.cc:2808
#, c-format
msgid ""
"LFTP is free software: you can redistribute it and/or modify\n"
"it under the terms of the GNU General Public License as published by\n"
"the Free Software Foundation, either version 3 of the License, or\n"
"(at your option) any later version.\n"
"\n"
"This program is distributed in the hope that it will be useful,\n"
"but WITHOUT ANY WARRANTY; without even the implied warranty of\n"
"MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n"
"GNU General Public License for more details.\n"
"\n"
"You should have received a copy of the GNU General Public License\n"
"along with LFTP.  If not, see <http://www.gnu.org/licenses/>.\n"
msgstr ""

#: src/commands.cc:2822
#, c-format
msgid "Send bug reports and questions to the mailing list <%s>.\n"
msgstr "Сообщения об ошибках и вопросы посылайте в список рассылки <%s>.\n"

#: src/commands.cc:2828
msgid "Libraries used: "
msgstr "Загруженые библиотеки: "

#: src/commands.cc:2979 src/commands.cc:3007
#, c-format
msgid "%s: bookmark name required\n"
msgstr "%s: требуется указать имя закладки\n"

#: src/commands.cc:2996
#, c-format
msgid "%s: spaces in bookmark name are not allowed\n"
msgstr "%s: пробелы в имени закладки не разрешаются\n"

#: src/commands.cc:3009
#, c-format
msgid "%s: no such bookmark `%s'\n"
msgstr "%s: закладка `%s' отсутствует\n"

#: src/commands.cc:3032
#, c-format
msgid "%s: import type required (netscape,ncftp)\n"
msgstr "%s: требуется указание типа импорта (netscape,ncftp)\n"

#: src/commands.cc:3110
#, c-format
msgid "Usage: %s [-d #] dir\n"
msgstr "Использование: %s [-d #] dir\n"

#: src/commands.cc:3215
#, c-format
msgid "%s: invalid block size `%s'\n"
msgstr "%s: неверный размер блока `%s'\n"

#: src/commands.cc:3226
#, c-format
msgid "Usage: %s [options] <dirs>\n"
msgstr "Использование: %s [ключи] <каталоги>\n"

#: src/commands.cc:3232
#, c-format
msgid "%s: warning: summarizing is the same as using --max-depth=0\n"
msgstr ""

#: src/commands.cc:3236
#, c-format
msgid "%s: summarizing conflicts with --max-depth=%i\n"
msgstr ""

#: src/commands.cc:3280
#, c-format
msgid "Usage: %s command args...\n"
msgstr "Использование: %s команда аргументы...\n"

#: src/commands.cc:3292
#, c-format
msgid "Usage: %s module [args...]\n"
msgstr "Использование: %s модуль [аргументы...]\n"

#: src/commands.cc:3310 src/LocalAccess.cc:642
msgid "cannot get current directory"
msgstr "невозможно получить текущий каталог"

#: src/commands.cc:3374
#, c-format
msgid "Usage: %s [OPTS] mode file...\n"
msgstr "Использование: %s [КЛЮЧИ] права файлы...\n"

#: src/commands.cc:3394
#, c-format
msgid "invalid mode string: %s\n"
msgstr "неверная запись прав: %s\n"

#: src/commands.cc:3457 src/commands.cc:3466
msgid "Invalid range format. Format is min-max, e.g. 10-20."
msgstr "Неверный формат диапазона. Формат: минимум-максимум, например 10-20."

#: src/commands.cc:3478
#, c-format
msgid "Usage: %s [OPTS] file\n"
msgstr "Использование: %s [КЛЮЧИ] <файл>\n"

#: src/CopyJob.cc:82
#, c-format
msgid "`%s' at %lld %s%s%s%s"
msgstr "`%s' в позиции %lld %s%s%s%s"

#: src/CopyJob.cc:159
#, c-format
msgid "%lld $#ll#byte|bytes$ transferred in %ld $#l#second|seconds$"
msgstr ""
"%lld $#ll#байт перемещен|байта перемещены|байтов перемещено$ за %ld "
"$#l#секунду|секунды|секунд$"

#: src/CopyJob.cc:167
#, c-format
msgid "%lld $#ll#byte|bytes$ transferred"
msgstr "%lld $#ll#байт перемещен|байта перемещены|байтов перемещено$"

#: src/CopyJob.cc:283
#, c-format
msgid "Transfer of %d of %d $file|files$ failed\n"
msgstr "Пересылка %d из %d $файла|файлов|файлов$ завершилась неудачей\n"

#: src/CopyJob.cc:289
#, c-format
msgid "Total %d $file|files$ transferred\n"
msgstr "Всего перемещено: %d $файл|файла|файлов$\n"

#: src/FileAccess.cc:160
msgid "Access failed: "
msgstr "Ошибка доступа: "

#: src/FileAccess.cc:161
msgid "File cannot be accessed"
msgstr "Доступ к файлу невозможен"

#: src/FileAccess.cc:163 src/Fish.cc:982 src/ftpclass.cc:4599
#: src/ftpclass.cc:4608 src/SFtp.cc:1339 src/Torrent.cc:3533
msgid "Not connected"
msgstr "Нет соединения"

#: src/FileAccess.cc:166 src/FileAccess.cc:167
msgid "Fatal error"
msgstr "Фатальная ошибка"

#: src/FileAccess.cc:169
msgid "Store failed - you have to reput"
msgstr "Передача файла не выполнена - требуется возобновление (reput)"

#: src/FileAccess.cc:172 src/FileAccess.cc:173
msgid "Login failed"
msgstr "Ошибка регистрации"

#: src/FileAccess.cc:176 src/FileAccess.cc:177
msgid "Operation not supported"
msgstr "Операция не поддерживается"

#: src/FileAccess.cc:180
msgid "File moved"
msgstr "Файл переместился"

#: src/FileAccess.cc:182
msgid "File moved to `"
msgstr "Файл переместился в `"

#: src/FileCopy.cc:141
msgid "copy: destination file is already complete\n"
msgstr "копирование: файл назначения уже записан полностью\n"

#: src/FileCopy.cc:182
msgid "copy: put is broken\n"
msgstr "копирование: запись оборвалась\n"

#: src/FileCopy.cc:201
msgid "seek failed"
msgstr "позиционирование не удалось"

#: src/FileCopy.cc:207
msgid "no progress timeout"
msgstr "превышено время ожидания без продвижения вперёд"

#: src/FileCopy.cc:235
msgid "cannot seek on data source"
msgstr "невозможно позиционировать источник данных"

#: src/FileCopy.cc:238
#, c-format
msgid "copy: put rolled back to %lld, seeking get accordingly\n"
msgstr ""
"копирование: запись откатилась на позицию %lld, позиционируем чтение так же\n"

#: src/FileCopy.cc:251
msgid "copy: all data received, but get rolled back\n"
msgstr "копирование: все данные получены, но процесс чтения откатился\n"

#: src/FileCopy.cc:267
#, c-format
msgid "copy: get rolled back to %lld, seeking put accordingly\n"
msgstr ""
"копирование: чтение откатилось на позицию %lld, позиционируем запись так же\n"

#: src/FileCopy.cc:364
msgid "file size decreased during transfer"
msgstr "размер файла уменьшился во время пересылки"

#: src/FileCopy.cc:1227
#, c-format
msgid "copy: received redirection to `%s'\n"
msgstr "копирование: получено перенаправление на `%s'\n"

#: src/FileCopy.cc:1290
#, fuzzy
#| msgid "file size decreased during transfer"
msgid "file size increased during transfer"
msgstr "размер файла уменьшился во время пересылки"

#: src/FileCopy.cc:1390
msgid "file name missed in URL"
msgstr "пропущено имя файла в URL"

#: src/FileCopy.cc:2086
msgid "Verify command failed without a message"
msgstr "Команда проверки вернула статус ошибки без сообщения"

#: src/FileCopyFtp.cc:95
msgid "**** FXP: trying to reverse ftp:fxp-passive-source\n"
msgstr "**** FXP: попробуем изменить ftp:fxp-passive-source\n"

#: src/FileCopyFtp.cc:102
msgid "**** FXP: trying to reverse ftp:fxp-passive-sscn\n"
msgstr "**** FXP: попробуем изменить ftp:fxp-passive-sscn\n"

#: src/FileCopyFtp.cc:110
msgid "**** FXP: trying to reverse ftp:ssl-protect-fxp\n"
msgstr "**** FXP: попробуем изменить ftp:ssl-protect-fxp\n"

#: src/FileCopyFtp.cc:116
msgid "**** FXP: giving up, reverting to plain copy\n"
msgstr "**** FXP: все попытки неудачны, переходим к обычному копированию\n"

#: src/FileCopyFtp.cc:125
msgid "ftp:fxp-force is set but FXP is not available"
msgstr "ftp:fxp-force установлен, но FXP не удалось использовать"

#: src/FileCopy.h:285
msgid "Verifying..."
msgstr "Проверка..."

#: src/FileSetOutput.cc:230
msgid "non-option arguments found"
msgstr "обнаружены аргументы, не являющиеся опциями"

#: src/Filter.cc:166
msgid "pipe() failed: "
msgstr "функция pipe() не выполнена: "

#: src/Filter.cc:200 src/PtyShell.cc:135
#, c-format
msgid "chdir(%s) failed: %s\n"
msgstr "функция chdir(%s) не выполнена: %s\n"

#: src/Filter.cc:208
#, c-format
msgid "execvp(%s) failed: %s\n"
msgstr "функция execvp(%s) не выполнена: %s\n"

#: src/Filter.cc:213 src/PtyShell.cc:147
#, c-format
msgid "execl(/bin/sh) failed: %s\n"
msgstr "функция execl(/bin/sh) не выполнена: %s\n"

#: src/Filter.cc:415
msgid "file already exists and xfer:clobber is unset"
msgstr "файл уже существует и не задано xfer:clobber"

#: src/FindJobDu.cc:101
msgid "total"
msgstr "всего"

#: src/Fish.cc:75 src/ftpclass.cc:1292 src/Http.cc:1232 src/SFtp.cc:77
#, c-format
msgid "Closing idle connection"
msgstr "Закрытие неиспользуемого соединения"

#: src/Fish.cc:162 src/SFtp.cc:177
msgid "Running connect program"
msgstr "Выполняется соединяющая программа"

#: src/Fish.cc:588 src/ftpclass.cc:2887 src/ftpclass.cc:3107 src/Http.cc:1510
#: src/SFtp.cc:742 src/SFtp.cc:1083 src/SFtp.cc:1084 src/SSH_Access.cc:167
#, c-format
msgid "Peer closed connection"
msgstr "Соединение закрыто удаленной машиной"

#: src/Fish.cc:634 src/ftpclass.cc:3037 src/ftpclass.cc:4219 src/SFtp.cc:1108
#, c-format
msgid "extra server response"
msgstr "непредвиденный ответ сервера"

#: src/Fish.cc:987 src/ftpclass.cc:4611 src/Http.cc:2329 src/Http.cc:2336
#: src/SFtp.cc:1345 src/Torrent.cc:3536 src/TorrentTracker.cc:624
msgid "Connecting..."
msgstr "Установка соединения..."

#: src/Fish.cc:989 src/ftpclass.cc:4617 src/SFtp.cc:1347
msgid "Connected"
msgstr "Соединен"

#: src/Fish.cc:991 src/ftpclass.cc:4594 src/ftpclass.cc:4622
#: src/ftpclass.cc:4636 src/Http.cc:2338 src/SFtp.cc:1349
#: src/TorrentTracker.cc:626
msgid "Waiting for response..."
msgstr "Ожидание ответа..."

#: src/Fish.cc:993 src/ftpclass.cc:4656 src/Http.cc:2341 src/SFtp.cc:1351
msgid "Receiving data"
msgstr "Получение данных"

#: src/Fish.cc:995 src/ftpclass.cc:4654 src/Http.cc:2334 src/SFtp.cc:1353
msgid "Sending data"
msgstr "Отправка данных"

#: src/Fish.cc:997 src/SFtp.cc:1355
msgid "Done"
msgstr "Готово"

#: src/Fish.cc:1136 src/FtpDirList.cc:123 src/HttpDir.cc:1384 src/SFtp.cc:2200
#: src/SFtp.cc:2320
#, c-format
msgid "Getting file list (%lld) [%s]"
msgstr "Получение списка файлов (%lld) [%s]"

#: src/ftpclass.cc:197
#, c-format
msgid "Data connection peer has wrong port number"
msgstr "Канал данных имеет неверный удаленный номер порта"

#: src/ftpclass.cc:203
#, c-format
msgid "Data connection peer has mismatching address"
msgstr "Канал данных имеет удаленный адрес, не совпадающий с ожидаемым"

#: src/ftpclass.cc:225 src/ftpclass.cc:250
#, c-format
msgid "Switching to NOREST mode"
msgstr "Включение режима NOREST"

#: src/ftpclass.cc:425
#, c-format
msgid "Server reply matched ftp:retry-530, retrying"
msgstr "Ответ сервера совпал с ftp:retry-530, повтрная попытка"

#: src/ftpclass.cc:433
#, c-format
msgid "Server reply matched ftp:retry-530-anonymous, retrying"
msgstr "Ответ сервера совпал с ftp:retry-530-anonymous, повтрная попытка"

#: src/ftpclass.cc:466
msgid "Account is required, set ftp:acct variable"
msgstr "Требуется учетная запись, установите переменную ftp:acct"

#: src/ftpclass.cc:483
msgid "ftp:skey-force is set and server does not support OPIE nor S/KEY"
msgstr "ftp:skey-force включено, а сервер не поддерживает ни OPIE, ни S/KEY"

#: src/ftpclass.cc:501
#, c-format
msgid "assuming failed host name lookup"
msgstr "подразумевается ошибка преобразования адреса машины"

#: src/ftpclass.cc:809 src/ftpclass.cc:810
#, c-format
msgid "cannot parse EPSV response"
msgstr "невозможно разобрать ответ на EPSV"

#: src/ftpclass.cc:837 src/ftpclass.cc:838
#, c-format
msgid "cannot parse custom EPSV response"
msgstr "невозможно разобрать ответ на кастомный EPSV"

#: src/ftpclass.cc:1122
#, c-format
msgid "Closing control socket"
msgstr "Закрытие управляющего соединения"

#: src/ftpclass.cc:1341
msgid "MLSD is disabled by ftp:use-mlsd"
msgstr "MLSD запрещено настройкой ftp:use-mlsd"

#: src/ftpclass.cc:1411 src/Http.cc:1431 src/Torrent.cc:3204
#: src/Torrent.cc:3743 src/TorrentTracker.cc:395
#, c-format
msgid "cannot create socket of address family %d"
msgstr "невозможно создать сокет для семейства адресов %d"

#: src/ftpclass.cc:1442 src/Http.cc:1457
#, c-format
msgid "Socket error (%s) - reconnecting"
msgstr "Ошибка на сокете (%s) - повторное соединение"

#: src/ftpclass.cc:1715
msgid "MFF and SITE CHMOD are not supported by this site"
msgstr "MFF и SITE CHMOD не поддерживается этим сервером"

#: src/ftpclass.cc:1720
msgid "MLST and MLSD are not supported by this site"
msgstr "MLST и MLSD не поддерживаются этим сервером"

#: src/ftpclass.cc:2031
msgid "SITE SYMLINK is not supported by the server"
msgstr "SITE SYMLINK не поддерживается этим сервером"

#: src/ftpclass.cc:2204
msgid "unsupported network protocol"
msgstr "сетевой протокол не поддерживается"

#: src/ftpclass.cc:2253 src/ftpclass.cc:2355
#, c-format
msgid "Data socket error (%s) - reconnecting"
msgstr "Ошибка на сокете данных (%s) - повторное соединение"

#: src/ftpclass.cc:2281
#, c-format
msgid "Accepted data connection from (%s) port %u"
msgstr "Принято соединение данных с (%s), порт %u"

#: src/ftpclass.cc:2330
#, c-format
msgid "Connecting data socket to (%s) port %u"
msgstr "Соединение сокета данных с (%s), порт %u"

#: src/ftpclass.cc:2336
#, c-format
msgid "Connecting data socket to proxy %s (%s) port %u"
msgstr "Соединение сокета данных с прокси-сервером %s (%s), порт %u"

#: src/ftpclass.cc:2358 src/ftpclass.cc:4414
#, c-format
msgid "Switching passive mode off"
msgstr "Отключение пассивного режима"

#: src/ftpclass.cc:2366
#, c-format
msgid "Data connection established"
msgstr "Канал данных установлен"

#: src/ftpclass.cc:2688 src/ftpclass.cc:4548
msgid "ftp:ssl-force is set and server does not support or allow SSL"
msgstr ""
"ftp:ssl-force установлено, а сервер не поддерживает или не позволяет "
"использовать SSL"

#: src/ftpclass.cc:3053
#, c-format
msgid "Persist and retry"
msgstr "Назойливо попробуем еще раз"

#: src/ftpclass.cc:3321
#, c-format
msgid "Closing data socket"
msgstr "Закрытие соединения данных"

#: src/ftpclass.cc:3343
#, c-format
msgid "Closing aborted data socket"
msgstr "Закрытие соединения данных, оставшегося после ABOR"

#: src/ftpclass.cc:4193
#, c-format
msgid "saw file size in response"
msgstr "замечен размер файла в ответе сервера"

#: src/ftpclass.cc:4233 src/ftpclass.cc:4260
#, c-format
msgid "Turning on sync-mode"
msgstr "Включение синхронного режима"

#: src/ftpclass.cc:4434
#, c-format
msgid "Switching passive mode on"
msgstr "Включение пассивного режима"

#: src/ftpclass.cc:4585
msgid "FEAT negotiation..."
msgstr "Согласование FEAT..."

#: src/ftpclass.cc:4592
msgid "Sending commands..."
msgstr "Передача команд..."

#: src/ftpclass.cc:4596
msgid "Delaying before retry"
msgstr "Задержка перед повторной попыткой"

#: src/ftpclass.cc:4597 src/Http.cc:2331
msgid "Connection idle"
msgstr "Соединение не используется"

#: src/ftpclass.cc:4604 src/Http.cc:2323 src/Resolver.cc:172
#: src/Resolver.cc:217 src/TorrentTracker.cc:622
#, c-format
msgid "Resolving host address..."
msgstr "Преобразование адреса сервера..."

#: src/ftpclass.cc:4615
msgid "TLS negotiation..."
msgstr "Согласование TLS..."

#: src/ftpclass.cc:4619
msgid "Logging in..."
msgstr "Регистрация..."

#: src/ftpclass.cc:4623
msgid "Making data connection..."
msgstr "Установка соединения данных..."

#: src/ftpclass.cc:4626
msgid "Changing remote directory..."
msgstr "Смена каталога на сервере..."

#: src/ftpclass.cc:4632
msgid "Waiting for other copy peer..."
msgstr "Ожидание готовности другой стороны..."

#: src/ftpclass.cc:4634 src/ftpclass.cc:4658
msgid "Waiting for transfer to complete"
msgstr "Ожидание окончания пересылки"

#: src/ftpclass.cc:4638
msgid "Waiting for TLS shutdown..."
msgstr "Ожидание закрытия сеанса TLS..."

#: src/ftpclass.cc:4640
msgid "Waiting for data connection..."
msgstr "Ожидание установки соединения данных..."

#: src/ftpclass.cc:4646
msgid "Sending data/TLS"
msgstr "Отправка данных/TLS"

#: src/ftpclass.cc:4648
msgid "Receiving data/TLS"
msgstr "Получение данных/TLS"

#: src/Http.cc:230
#, c-format
msgid "Closing HTTP connection"
msgstr "Закрытие соединения HTTP"

#: src/Http.cc:240
msgid "POST method failed"
msgstr "Метод POST завершился неудачей"

#: src/Http.cc:1381
msgid "ftp over http cannot work without proxy, set hftp:proxy."
msgstr ""
"ftp через http прокси не может работать без прокси, установите hftp:proxy."

#: src/Http.cc:1537
#, c-format
msgid "Sending request..."
msgstr "Передача запроса..."

#: src/Http.cc:1575
#, c-format
msgid "Hit EOF while fetching headers"
msgstr "Получил конец файла (EOF) при чтении заголовков"

#: src/Http.cc:1695
#, c-format
msgid "Could not parse HTTP status line"
msgstr "Невозможно разобрать строку статуса HTTP"

#: src/Http.cc:1726
msgid "Object is not cached and http:cache-control has only-if-cached"
msgstr "Объект не в кэше, а http:cache-control содержит only-if-cached"

#: src/Http.cc:1891
#, c-format
msgid "Receiving body..."
msgstr "Получение тела ответа..."

#: src/Http.cc:2092
#, c-format
msgid "Hit EOF"
msgstr "Конец файла"

#: src/Http.cc:2095
#, c-format
msgid "Received not enough data, retrying"
msgstr "Получено не достаточно данных - повторная попытка."

#: src/Http.cc:2106
#, c-format
msgid "Received all"
msgstr "Все данные получены"

#: src/Http.cc:2111
#, c-format
msgid "Received all (total)"
msgstr "Все данные получены (совсем все)"

#: src/Http.cc:2135 src/Http.cc:2159
msgid "chunked format violated"
msgstr "формат данных `chunked' нарушен"

#: src/Http.cc:2145
#, c-format
msgid "Received last chunk"
msgstr "Получен последний фрагмент"

#: src/Http.cc:2339
msgid "Fetching headers..."
msgstr "Получение заголовков HTTP..."

#: src/Job.cc:293
#, c-format
msgid "[%d] Done (%s)"
msgstr "[%d] Завершено (%s)"

#: src/lftp.cc:370
#, c-format
msgid "[%u] Terminated by signal %d. %s\n"
msgstr "[%u] Выполнение прервано сигналом %d. %s\n"

#: src/lftp.cc:445
#, c-format
msgid "[%u] Started.  %s\n"
msgstr "[%u] Запуск.    %s\n"

#: src/lftp.cc:463
#, c-format
msgid "[%u] Detaching from the terminal to complete transfers...\n"
msgstr "[%u] Переход в фоновый режим для завершения работы заданий...\n"

#: src/lftp.cc:465
#, c-format
msgid "[%u] Exiting and detaching from the terminal.\n"
msgstr "[%u] Завершаю работу и отсоединяюсь от терминала.\n"

#: src/lftp.cc:470
#, c-format
msgid "[%u] Detached from terminal. %s\n"
msgstr "[%u] Отсоединился от терминала. %s\n"

#: src/lftp.cc:480
#, c-format
msgid "[%u] Finished. %s\n"
msgstr "[%u] Завершено. %s\n"

#: src/lftp.cc:484
#, c-format
msgid "[%u] Moving to background to complete transfers...\n"
msgstr "[%u] Переход в фоновый режим для завершения работы заданий...\n"

#: src/lftp.cc:553
msgid "history -w file|-r file|-c|-l [cnt]"
msgstr ""

#: src/lftp.cc:554
msgid ""
" -w <file> Write history to file.\n"
" -r <file> Read history from file; appends to current history.\n"
" -c  Clear the history.\n"
" -l  List the history (default).\n"
"Optional argument cnt specifies the number of history lines to list,\n"
"or \"all\" to list all entries.\n"
msgstr ""

#: src/lftp.cc:561
msgid "Attach the terminal to specified backgrounded lftp process.\n"
msgstr "Присоединить терминал к указанному фоновому процессу lftp.\n"

#: src/lftp_ssl.cc:1291
#, c-format
msgid "No certificate presented by %s.\n"
msgstr "Сервер %s не предъявил свой сертификат.\n"

#: src/LocalAccess.cc:604 src/NetAccess.cc:686
msgid "Getting directory contents"
msgstr "Получение содержимого каталога"

#: src/LocalAccess.cc:606 src/NetAccess.cc:690
msgid "Getting files information"
msgstr "Получение информации о файлах"

#: src/LsCache.cc:190
#, c-format
msgid "%ld $#l#byte|bytes$ cached"
msgstr "%ld $#l#байт|байта|байтов$ в кэше"

#: src/LsCache.cc:194
msgid ", no size limit"
msgstr ", нет ограничений по размеру"

#: src/LsCache.cc:196
#, c-format
msgid ", maximum size %ld\n"
msgstr ", максимальный размер %ld\n"

#: src/mgetJob.cc:78
#, c-format
msgid "%s: %s: no files found\n"
msgstr "%s: %s: файлы не найдены\n"

#: src/MirrorJob.cc:100
#, c-format
msgid "%sTotal: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
msgstr ""
"%sВсего: %d каталог$|а|ов$, %d файл$|а|ов$, %d $ссылка|ссылки|ссылок$\n"

#: src/MirrorJob.cc:104
#, c-format
msgid "%sNew: %d file$|s$, %d symlink$|s$\n"
msgstr "%sСозданы: %d файл$|а|ов$, %d $ссылка|ссылки|ссылок$\n"

#: src/MirrorJob.cc:108
#, c-format
msgid "%sModified: %d file$|s$, %d symlink$|s$\n"
msgstr "%sИзменены: %d $файл|файла|файлов$, %d $ссылка|ссылки|ссылок$\n"

#: src/MirrorJob.cc:115
#, c-format
msgid "%sRemoved: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
msgstr ""
"%sУдалены: %d каталог$|а|ов$, %d файл$|а|ов$, %d $ссылка|ссылки|ссылок$\n"

#: src/MirrorJob.cc:120
#, c-format
msgid "%s%d error$|s$ detected\n"
msgstr "%s%d ошиб$ка обнаружена|ки обнаружены|ок обнаружено$\n"

#: src/MirrorJob.cc:231
#, c-format
msgid "Finished %s"
msgstr "Завершено: %s"

#: src/MirrorJob.cc:344 src/MirrorJob.cc:519 src/MirrorJob.cc:1218
#, c-format
msgid "Removing old file `%s'"
msgstr "Удаление старого файла `%s'"

#: src/MirrorJob.cc:346
#, c-format
msgid "Overwriting old file `%s'"
msgstr "Перезапись старого файла `%s'"

#: src/MirrorJob.cc:355
#, c-format
msgid "Skipping file `%s' (only-existing)"
msgstr "Пропущен файл `%s' (only-existing)"

#: src/MirrorJob.cc:361
#, c-format
msgid "Transferring file `%s'"
msgstr "Копирование файла `%s'"

#: src/MirrorJob.cc:439
#, c-format
msgid "Skipping directory `%s' (only-existing)"
msgstr "Пропущен каталог `%s' (only-existing)"

#: src/MirrorJob.cc:461 src/MirrorJob.cc:550 src/MirrorJob.cc:902
#, c-format
msgid "Removing old local file `%s'"
msgstr "Удаление старого локального файла `%s'"

#: src/MirrorJob.cc:487
#, c-format
msgid "Scanning directory `%s'"
msgstr "Просмотр каталога `%s'"

#: src/MirrorJob.cc:489
#, c-format
msgid "Mirroring directory `%s'"
msgstr "Копирование каталога `%s'"

#: src/MirrorJob.cc:525 src/MirrorJob.cc:567
#, c-format
msgid "Making symbolic link `%s' to `%s'"
msgstr "Создание символьной ссылки `%s' на `%s'"

#: src/MirrorJob.cc:562
#, c-format
msgid "Skipping symlink `%s' (only-existing)"
msgstr "Пропущена символьная ссылка `%s' (only-existing)"

#: src/MirrorJob.cc:807
#, c-format
msgid "mirror: protocol `%s' is not suitable for mirror\n"
msgstr "mirror: протокол `%s' не подходит для зеркалирования\n"

#: src/MirrorJob.cc:923
#, c-format
msgid "Making directory `%s'"
msgstr "Создание каталога `%s'"

#: src/MirrorJob.cc:1181
#, c-format
msgid "Old directory `%s' is not removed"
msgstr "Старый каталог `%s' не удален"

#: src/MirrorJob.cc:1183
#, c-format
msgid "Old file `%s' is not removed"
msgstr "Старый файл `%s' не удален"

#: src/MirrorJob.cc:1216
#, c-format
msgid "Removing old directory `%s'"
msgstr "Удаление старого каталога `%s'"

#: src/MirrorJob.cc:1339
#, c-format
msgid "Removing source directory `%s'"
msgstr "Удаление исходного каталога `%s'"

#: src/MirrorJob.cc:1405
#, c-format
msgid "Removing source file `%s'"
msgstr "Удаление исходного файла `%s'"

#: src/MirrorJob.cc:1425
#, c-format
msgid "Retrying mirror...\n"
msgstr "Повторный проход...\n"

#: src/MirrorJob.cc:1694
msgid "pattern is empty"
msgstr ""

#: src/MirrorJob.cc:1779
#, c-format
msgid "%s must be one of: %s"
msgstr "%s должно быть одним из: %s"

#: src/MirrorJob.cc:2019
#, c-format
msgid ""
"%s: multiple --file or --directory options must have the same base "
"directory\n"
msgstr ""
"%s: при указании опций --file или --directory несколько раз базовый каталог "
"в них должен совпадать\n"

#: src/MirrorJob.cc:2160
#, c-format
msgid "%s: ambiguous source directory (`%s' or `%s'?)\n"
msgstr "%s: неоднозначен исходный каталог (`%s' или `%s'?)\n"

#: src/MirrorJob.cc:2182
#, c-format
msgid "%s: ambiguous target directory (`%s' or `%s'?)\n"
msgstr "%s: неоднозначен конечный каталог (`%s' или `%s'?)\n"

#: src/MirrorJob.cc:2223
#, c-format
msgid "%s: source directory is required (mirror:require-source is set)\n"
msgstr ""
"%s: требуется указать исходный каталог (mirror:require-source установлено)\n"

#: src/MirrorJob.cc:2343
msgid ""
"\n"
"Mirror specified remote directory to local directory\n"
"\n"
" -R, --reverse          reverse mirror (put files)\n"
"Lots of other options are documented in the man page lftp(1).\n"
"\n"
"When using -R, the first directory is local and the second is remote.\n"
"If the second directory is omitted, basename of the first directory is "
"used.\n"
"If both directories are omitted, current local and remote directories are "
"used.\n"
"\n"
"See the man page lftp(1) for a complete documentation.\n"
msgstr ""

#: src/mkdirJob.cc:128
#, c-format
msgid "%s ok, `%s' created\n"
msgstr "%s ok, `%s' создан\n"

#: src/mkdirJob.cc:130 src/rmJob.cc:51
#, c-format
msgid "%s failed for %d of %d director$y|ies$\n"
msgstr "Команда %s не выполнена для %d из %d каталог$а|ов|ов$\n"

#: src/mkdirJob.cc:133
#, c-format
msgid "%s ok, %d director$y|ies$ created\n"
msgstr "%s ok, %d каталог$|а|ов$ создано\n"

#: src/module.cc:190
#, c-format
msgid "depend module `%s': %s\n"
msgstr "модуль зависимости `%s': %s\n"

#: src/module.cc:210
msgid "modules are not supported on this system"
msgstr "модули на этой системе не поддерживаются"

#: src/mvJob.cc:92
#, c-format
msgid "rename successful\n"
msgstr "переименование выполнено успешно\n"

#: src/NetAccess.cc:168
#, c-format
msgid "Connecting to %s%s (%s) port %u"
msgstr "Соединение с %s%s (%s), порт %u"

#: src/NetAccess.cc:224 src/Torrent.cc:3326
#, c-format
msgid "Timeout - reconnecting"
msgstr "Превышение лимита времени ожидания - повторное соединение"

#: src/NetAccess.cc:323
msgid "Connection limit reached"
msgstr "Достигнут предел количества соединений"

#: src/NetAccess.cc:330
msgid "Delaying before reconnect"
msgstr "Задержка перед повторным соединением"

#: src/NetAccess.cc:354 src/NetAccess.cc:356
msgid "max-retries exceeded"
msgstr "превышен лимит количества попыток (max-retries)"

#: src/OutputJob.cc:145
#, c-format
msgid "%s (filter)"
msgstr "%s (фильтр)"

#: src/parsecmd.cc:290
msgid "parse: missing filter command\n"
msgstr "разбор: пропущена команда фильтра\n"

#: src/parsecmd.cc:292
msgid "parse: missing redirection filename\n"
msgstr "разбор: пропущено имя файла для перенаправления\n"

#: src/PatternSet.cc:110
#, c-format
msgid "regular expression `%s': %s"
msgstr "регулярное выражение `%s': %s"

#: src/pgetJob.cc:127
msgid "pget: falling back to plain get"
msgstr "pget: используется обычный get"

#: src/pgetJob.cc:131
msgid "the target file is remote"
msgstr "файл назначения не локальный"

#: src/pgetJob.cc:136
msgid "the source file size is unknown"
msgstr "размер исходного файла неизвестен"

#: src/pgetJob.cc:173
#, c-format
msgid "pget: warning: space allocation for %s (%lld bytes) failed: %s\n"
msgstr ""
"pget: предупреждение: выделение места для %s (%lld байт) не удалось: %s\n"

#: src/pgetJob.cc:234
#, c-format
msgid "`%s', got %lld of %lld (%d%%) %s%s"
msgstr "`%s', принято %lld из %lld (%d%%) %s%s"

#: src/plural.c:96
msgid "=1 =0|>1"
msgstr "%100!11%10=1 %100>1<5|%100>20%10>1<5"

#: src/PollVec.cc:69
#, c-format
msgid "%s: BUG - deadlock detected\n"
msgstr "%s: ОШИБКА - обнаружен тупик\n"

#: src/PtyShell.cc:68
msgid "pseudo-tty allocation failed: "
msgstr "выделение псевдо-терминала неудачно: "

#: src/QueueFeeder.cc:68
msgid "Added job$|s$"
msgstr "Задани$е добавлено|я добавлены|я добавлены$"

#: src/QueueFeeder.cc:164 src/QueueFeeder.cc:185
#, c-format
msgid "No queued jobs.\n"
msgstr "В очереди нет заданий.\n"

#: src/QueueFeeder.cc:166
#, c-format
msgid "No queued job #%i.\n"
msgstr "В очереди нет задания №%i.\n"

#: src/QueueFeeder.cc:171 src/QueueFeeder.cc:192
msgid "Deleted job$|s$"
msgstr "Задани$е удалено|я удалены|я удалены$"

#: src/QueueFeeder.cc:187
#, c-format
msgid "No queued jobs match \"%s\".\n"
msgstr "В очереди нет заданий, соответствующих \"%s\".\n"

#: src/QueueFeeder.cc:212 src/QueueFeeder.cc:230
msgid "Moved job$|s$"
msgstr "Задани$е перемещено|я перемещены|я перемещены$"

#: src/QueueFeeder.cc:354
msgid "Commands queued:"
msgstr "Очередь команд:"

#: src/ResMgr.cc:123
msgid "no such variable"
msgstr "нет переменной с таким именем"

#: src/ResMgr.cc:127
msgid "ambiguous variable name"
msgstr "неоднозначное сокращение имени переменной"

#: src/ResMgr.cc:335
msgid "invalid boolean value"
msgstr "неверное логическое значение"

#: src/ResMgr.cc:357
msgid "invalid boolean/auto value"
msgstr "неверное логическое значение, должно быть: true/false/auto"

#: src/ResMgr.cc:402 src/ResMgr.cc:713
msgid "invalid number"
msgstr "неверное число"

#: src/ResMgr.cc:415
msgid "invalid floating point number"
msgstr "неверное число с плавающей точкой"

#: src/ResMgr.cc:429
msgid "invalid unsigned number"
msgstr "неверное неотрицательное число"

#: src/ResMgr.cc:678
msgid "Invalid time unit letter, only [smhd] are allowed."
msgstr "Недопустимые единицы измерения времени, допускаются только [smhd]."

#: src/ResMgr.cc:686
msgid "Invalid time format. Format is <time><unit>, e.g. 2h30m."
msgstr "Неверный формат времени. Формат: <время><единицы>, например 2h30m."

#: src/ResMgr.cc:718
msgid "integer overflow"
msgstr "целочисленное переполнение"

#: src/ResMgr.cc:804
msgid "Invalid IPv4 numeric address"
msgstr "Некорректный цифровой IPv4 адрес"

#: src/ResMgr.cc:814
msgid "Invalid IPv6 numeric address"
msgstr "Некорректный цифровой IPv6 адрес"

#: src/ResMgr.cc:884 src/ResMgr.cc:888
msgid "this encoding is not supported"
msgstr "эта кодировка не поддерживается"

#: src/ResMgr.cc:894
msgid "no closure defined for this setting"
msgstr "для этой настройки контекст не имеет смысла"

#: src/ResMgr.cc:900
msgid "a closure is required for this setting"
msgstr "для этой настройки контекст обязателен"

#: src/Resolver.cc:236
msgid "host name resolve timeout"
msgstr "при преобразовании адреса сервера превышен лимит времени"

#: src/Resolver.cc:282
#, c-format
msgid "%d address$|es$ found"
msgstr "%d $адрес найден|адреса найдено|адресов найдено$"

#: src/Resolver.cc:327
msgid "Link-local IPv6 address should have a scope"
msgstr ""

#: src/Resolver.cc:765
msgid "DNS resolution not trusted."
msgstr ""

#: src/Resolver.cc:871
msgid "Host name lookup failure"
msgstr "Ошибка при преобразовании адреса сервера"

#: src/Resolver.cc:903
#, c-format
msgid "no such %s service"
msgstr "нет такого %s сервиса"

#: src/Resolver.cc:930
msgid "No address found"
msgstr "Адресов не найдено"

#: src/resource.cc:50 src/resource.cc:108
msgid "Proxy protocol unsupported"
msgstr "Данный протокол для прокси не поддерживается"

#: src/resource.cc:54
msgid "ftp:proxy password: "
msgstr "Пароль для ftp:proxy: "

#: src/resource.cc:70
#, c-format
msgid "%s must be one of: "
msgstr "%s должно быть одним из: "

#: src/resource.cc:72
msgid "must be one of: "
msgstr "должно быть одним из: "

#: src/resource.cc:84
msgid ", or empty"
msgstr ", или пустым"

#: src/resource.cc:116
msgid "only PUT and POST values allowed"
msgstr "разрешены только PUT и POST"

#: src/resource.cc:148
#, c-format
msgid "unknown address family `%s'"
msgstr "неизвестный тип адреса `%s'"

#: src/rmJob.cc:47
#, c-format
msgid "%s ok, `%s' removed\n"
msgstr "%s ok, `%s' удален\n"

#: src/rmJob.cc:54
#, c-format
msgid "%s failed for %d of %d file$|s$\n"
msgstr "Команда %s не выполнена для %d из %d файл$а|ов|ов$\n"

#: src/rmJob.cc:60
#, c-format
msgid "%s ok, %d director$y|ies$ removed\n"
msgstr "%s ok, %d каталог$|а|ов$ удалено\n"

#: src/rmJob.cc:63
#, c-format
msgid "%s ok, %d file$|s$ removed\n"
msgstr "%s ok, %d файл$|а|ов$ удалено\n"

#: src/SFtp.cc:1099 src/SFtp.cc:1100
#, c-format
msgid "invalid server response format"
msgstr "неверный формат ответа сервера"

#: src/SleepJob.cc:99
msgid "Sleeping forever"
msgstr "Бесконечное ожидание"

#: src/SleepJob.cc:100
msgid "Sleep time left: "
msgstr "Осталось подождать: "

#: src/SleepJob.cc:108
#, c-format
msgid "\tRepeat count: %d\n"
msgstr "\tСчетчик повторений: %d\n"

#: src/SleepJob.cc:142
#, c-format
msgid "%s: argument required. "
msgstr "%s: требуется аргумент. "

#: src/SleepJob.cc:262
#, c-format
msgid "%s: date-time specification missed\n"
msgstr "%s: не указана спецификация даты и времени\n"

#: src/SleepJob.cc:269
#, c-format
msgid "%s: date-time parse error\n"
msgstr "%s: ошибка разбора спецификации даты и времени\n"

#: src/SleepJob.cc:303
msgid ""
"Usage: sleep <time>[unit]\n"
"Sleep for given amount of time. The time argument can be optionally\n"
"followed by unit specifier: d - days, h - hours, m - minutes, s - seconds.\n"
"By default time is assumed to be seconds.\n"
msgstr ""
"Использование: sleep <время>[единицы_измерения]\n"
"Перейти в режим сна на заданный промежуток времени. После аргумента,\n"
"задающего время, допускается указание единиц измерения: d - дни, h - часы,\n"
"m - минуты, s - секунды. По умолчанию предполагается время в секундах.\n"

#: src/SleepJob.cc:310
msgid ""
"Repeat specified command with a delay between iterations.\n"
"Default delay is one second, default command is empty.\n"
" -c <count>  maximum number of iterations\n"
" -d <delay>  delay between iterations\n"
" --while-ok  stop when command exits with non-zero code\n"
" --until-ok  stop when command exits with zero code\n"
" --weak      stop when lftp moves to background.\n"
msgstr ""
"Повторять указанную команду с задержкой между повторами.\n"
"По-умолчанию задержка - 1 секунда, команды нет.\n"
" -c <кол-во>   количество повторений\n"
" -d <задержка> задержка между повторениями\n"
" --while-ok\tзавершить, когда команда вернёт ненулевой код\n"
" --until-ok\tзавершить, когда команда вернёт нулевой код\n"
" --weak\tзавершить, когда lftp переходит в фоновый режим.\n"

#: src/Speedometer.cc:90
#, c-format
msgid "%.0fb/s"
msgstr "%.0fб/с"

#: src/Speedometer.cc:93
#, c-format
msgid "%.1fK/s"
msgstr "%.1fКб/с"

#: src/Speedometer.cc:96
#, c-format
msgid "%.2fM/s"
msgstr "%.2fМб/с"

#: src/Speedometer.cc:103
#, c-format
msgid "%.0f B/s"
msgstr "%.0f б/с"

#: src/Speedometer.cc:105
#, c-format
msgid "%.1f KiB/s"
msgstr "%.1f Киб/с"

#: src/Speedometer.cc:107
#, c-format
msgid "%.2f MiB/s"
msgstr "%.2f Миб/с"

#: src/Speedometer.cc:129
msgid "eta:"
msgstr "овп:"

#: src/SSH_Access.cc:97
msgid "Password required"
msgstr "Требуется пароль"

#: src/SSH_Access.cc:102
msgid "Login incorrect"
msgstr "Login/пароль неверный"

#: src/SSH_Access.cc:196
#, c-format
msgid "Disconnecting"
msgstr "Закрытие соединения"

#: src/SysCmdJob.cc:73
#, c-format
msgid "execlp(%s) failed: %s\n"
msgstr "функция execlp(%s) не выполнена: %s\n"

#: src/TimeDate.cc:155
msgid "day"
msgstr "день"

#: src/TimeDate.cc:156
msgid "hour"
msgstr "час"

#: src/TimeDate.cc:157
msgid "minute"
msgstr "минута"

#: src/TimeDate.cc:158
msgid "second"
msgstr "секунда"

#: src/Torrent.cc:585
msgid "announced via "
msgstr "объявлений через "

#: src/Torrent.cc:599
#, c-format
msgid "next announce in %s"
msgstr "следующий анонс через %s"

#: src/Torrent.cc:1142
#, c-format
msgid "%d file$|s$ found, now scanning %s"
msgstr ""
"%d $файл найден|файла найдено|файлов найдено$, теперь просматривается %s"

#: src/Torrent.cc:1143
#, c-format
msgid "%d file$|s$ found"
msgstr "%d $файл найден|файла найдено|файлов найдено$"

#: src/Torrent.cc:2075 src/Torrent.cc:2085
#, c-format
msgid "Getting meta-data: %s"
msgstr "Получение мета-данных: %s"

#: src/Torrent.cc:2077
#, c-format
msgid "Validation: %u/%u (%u%%) %s%s"
msgstr "Проверка: %u/%u (%u%%) %s%s"

#: src/Torrent.cc:2088
msgid "Waiting for meta-data..."
msgstr "Ожидание метаданных..."

#: src/Torrent.cc:2096
msgid "Shutting down: "
msgstr "Останов: "

#: src/Torrent.cc:3207
#, c-format
msgid "Connecting to peer %s port %u"
msgstr "Соединение с узлом %s, порт %u"

#: src/Torrent.cc:3258 src/Torrent.cc:3375
#, c-format
msgid "peer unexpectedly closed connection after %s"
msgstr "удалённый узел неожиданно закрыл соединение после %s"

#: src/Torrent.cc:3259 src/Torrent.cc:3376
msgid "peer unexpectedly closed connection"
msgstr "удалённый узел неожиданно закрыл соединение"

#: src/Torrent.cc:3261 src/Torrent.cc:3262
#, c-format
msgid "peer closed connection (before handshake)"
msgstr "удалённый узел закрыл соединение (без обмена сообщениями)"

#: src/Torrent.cc:3265 src/Torrent.cc:3378 src/Torrent.cc:3379
#, c-format
msgid "invalid peer response format"
msgstr "неверный формат ответа удалённого узла"

#: src/Torrent.cc:3360 src/Torrent.cc:3361
#, c-format
msgid "peer closed connection"
msgstr "удалённый узел закрыл соединение"

#: src/Torrent.cc:3538
msgid "Handshaking..."
msgstr "Начало обмена сообщениями..."

#: src/Torrent.cc:3798
msgid "Cannot bind a socket for torrent:port-range"
msgstr "Не удалось выбрать порт из указанного диапазона torrent:port-range"

#: src/Torrent.cc:3858
#, c-format
msgid "Accepted connection from [%s]:%d"
msgstr "Принято соединение от [%s]:%d"

#: src/Torrent.cc:3924
#, c-format
msgid "peer sent unknown info_hash=%s in handshake"
msgstr ""
"удалённый узел прислал неизвестное значение info_hash=%s при начальном обмене"

#: src/Torrent.cc:3948
#, c-format
msgid "peer handshake timeout"
msgstr "превышено время ожидания при начальном обмене с удалённым узлом"

#: src/Torrent.cc:3960
#, c-format
msgid "peer short handshake"
msgstr "получено усечённое сообщение при начальном обмене с удалённым узлом"

#: src/Torrent.cc:3962
#, c-format
msgid "peer closed just accepted connection"
msgstr "удалённый узел закрыл только что принятое соединение"

#: src/Torrent.cc:4013
#, c-format
msgid "Seeding in background...\n"
msgstr "Раздача продолжится в фоновом режиме...\n"

#: src/Torrent.cc:4176
#, c-format
msgid "%s: --share conflicts with --output-directory.\n"
msgstr "%s: --share конфликтует с --output-directory.\n"

#: src/Torrent.cc:4180
#, c-format
msgid "%s: --share conflicts with --only-new.\n"
msgstr "%s: --share конфликтует с --only-new.\n"

#: src/Torrent.cc:4184
#, c-format
msgid "%s: --share conflicts with --only-incomplete.\n"
msgstr "%s: --share конфликтует с --only-incomplete.\n"

#: src/Torrent.cc:4226
#, c-format
msgid "%s: Please specify a file or directory to share.\n"
msgstr "%s: Пожалуйста, укажите файл или каталог для раздачи.\n"

#: src/Torrent.cc:4228
#, c-format
msgid "%s: Please specify meta-info file or URL.\n"
msgstr "%s: Пожалуйста, укажите torrent-файл или URL.\n"

#: src/Torrent.cc:4258
msgid ""
"Start BitTorrent job for the given torrent-files, which can be a local "
"file,\n"
"URL, magnet link or plain info_hash written in hex or base32. Local "
"wildcards\n"
"are expanded. Options:\n"
" -O <base>      specifies base directory where files should be placed\n"
" --force-valid  skip file validation\n"
" --dht-bootstrap=<node>  bootstrap DHT by sending a query to the node\n"
" --share        share specified file or directory\n"
msgstr ""
"Запустить задание BitTorrent жля указанного торрента, который может быть\n"
"указан как локальный torrent-файл, URL, magnet-ссылка или простой info_hash\n"
"записанный в формате hex или base32. Шаблоны заменяются на список локальных\n"
"файлов. Ключи:\n"
" -O <база>      указывает базовый каталог, куда класть полученные файлы\n"
" --force-valid  не проверять целостность локальных файлов при запуске\n"
" --dht-bootstrap=<узел>  инициализировать DHT посылкой запроса на <узел>\n"
" --share        опубликовать указанный файл или каталог\n"

#: src/TorrentTracker.cc:178
msgid "not started"
msgstr "ещё не запущен"

#: src/TorrentTracker.cc:181
#, c-format
msgid "next request in %s"
msgstr "следующий запрос через %s"

#: src/TorrentTracker.cc:284 src/TorrentTracker.cc:501
#, c-format
msgid "Received valid info about %d peer$|s$"
msgstr "Получил корректную информацию о %d узл$е|ах|ах$"

#: src/TorrentTracker.cc:298
#, c-format
msgid "Received valid info about %d IPv6 peer$|s$"
msgstr "Получил корректную информацию о %d узл$е|ах|ах$ с IPv6 адресами"

#~ msgid "%s: option '--%s' doesn't allow an argument\n"
#~ msgstr "%s: опция `--%s' не позволяет указывать аргумент\n"

#~ msgid "%s: unrecognized option '--%s'\n"
#~ msgstr "%s: неизвестная опция `--%s'\n"

#~ msgid "%s: option '-W %s' is ambiguous\n"
#~ msgstr "%s: опция `-W %s' неоднозначна\n"

#~ msgid "%s: option '-W %s' doesn't allow an argument\n"
#~ msgstr "%s: опция `-W %s' не позволяет указывать аргумент\n"

#~ msgid "%s: option '-W %s' requires an argument\n"
#~ msgstr "%s: для опции `-W %s' требуется аргумент\n"

#~ msgid "Usage: mv <file1> <file2>\n"
#~ msgstr "Использование: mv <файл1> <файл2>\n"

#~ msgid "number"
#~ msgstr "число"

#~ msgid "error: %s:%d\n"
#~ msgstr "ошибка: %s:%d\n"

#, fuzzy
#~ msgid ""
#~ "\n"
#~ "Mirror specified remote directory to local directory\n"
#~ "\n"
#~ " -c, --continue         continue a mirror job if possible\n"
#~ " -e, --delete           delete files not present at remote site\n"
#~ "     --delete-first     delete old files before transferring new ones\n"
#~ " -s, --allow-suid       set suid/sgid bits according to remote site\n"
#~ "     --allow-chown      try to set owner and group on files\n"
#~ "     --ignore-time      ignore time when deciding whether to download\n"
#~ " -n, --only-newer       download only newer files (-c won't work)\n"
#~ " -r, --no-recursion     don't go to subdirectories\n"
#~ " -p, --no-perms         don't set file permissions\n"
#~ "     --no-umask         don't apply umask to file modes\n"
#~ " -R, --reverse          reverse mirror (put files)\n"
#~ " -L, --dereference      download symbolic links as files\n"
#~ " -N, --newer-than=SPEC  download only files newer than specified time\n"
#~ " -P, --parallel[=N]     download N files in parallel\n"
#~ " -i RX, --include RX    include matching files\n"
#~ " -x RX, --exclude RX    exclude matching files\n"
#~ "                        RX is extended regular expression\n"
#~ " -v, --verbose[=N]      verbose operation\n"
#~ "     --log=FILE         write lftp commands being executed to FILE\n"
#~ "     --script=FILE      write lftp commands to FILE, but don't execute "
#~ "them\n"
#~ "     --just-print, --dry-run    same as --script=-\n"
#~ "\n"
#~ "When using -R, the first directory is local and the second is remote.\n"
#~ "If the second directory is omitted, basename of first directory is used.\n"
#~ "If both directories are omitted, current local and remote directories are "
#~ "used.\n"
#~ "See lftp(1) for a complete list of options.\n"
#~ msgstr ""
#~ "\n"
#~ "Выполнить \"зеркалирование\" указанного каталога с сервера в локальный "
#~ "каталог\n"
#~ "\n"
#~ " -c, --continue         продолжить прерванную команду `mirror', если "
#~ "возможно\n"
#~ " -e, --delete           удалять файлы, отсутствующие на сервере\n"
#~ "     --delete-first     удалять старые файлы до прекачки новых файлов\n"
#~ " -s, --allow-suid       сохранять значение битов suid/sgid\n"
#~ "     --allow-chown      попытаться сменить владельца и группу файлов\n"
#~ "     --ignore-time      игнорировать время при выборе файлов для "
#~ "перекачки\n"
#~ " -n, --only-newer       получать только более свежие файлы\n"
#~ "                        (указание ключа -c не будет иметь эффекта)\n"
#~ " -r, --no-recursion     не переходить в дочерние подкаталоги\n"
#~ " -p, --no-perms         не устанавливать режим доступа к файлам\n"
#~ "     --no-umask         не применять значение umask к режиму доступа "
#~ "файлов\n"
#~ " -R, --reverse          обратное зеркалирование (выполнять put)\n"
#~ " -L, --dereference      принимать символические ссылки как обычные файлы\n"
#~ " -N, --newer-than=SPEC  принимать только файлы новее указанного времени\n"
#~ " -P, --parallel[=N]     скачивать параллельно N файлов\n"
#~ " -i RX, --include RX    включить соответствующие файлы (только один "
#~ "ключ)\n"
#~ " -x RX, --exclude RX    исключить соответствующие файлы (только один "
#~ "ключ)\n"
#~ "                        RX - расширенное регулярное выражение\n"
#~ " -v, --verbose          выводить подробную информацию\n"
#~ "     --log=FILE         записать выполняемые комманды в файл FILE\n"
#~ "     --script=FILE      записать команды в FILE, но не выполнять их\n"
#~ "     --just-print, --dry-run    то же самое, что и --script=-\n"
#~ "\n"
#~ "При использовании -R первый каталог - локальный, второй - каталог на "
#~ "сервере.\n"
#~ "Если второй каталог на указан, то используется последний элемент "
#~ "первого.\n"
#~ "Если ни один из каталогов не указан, используются текущий локальный "
#~ "каталог\n"
#~ "и текущий каталог на сервере.\n"

#~ msgid "SITE CHMOD is disabled by ftp:use-site-chmod"
#~ msgstr "SITE CHMOD запрещено настройкой ftp:use-site-chmod"

#~ msgid ""
#~ "ftp:proxy-auth-type must be one of: user, joined, joined-acct, open, "
#~ "proxy-user@host"
#~ msgstr ""
#~ "значение ftp:proxy-auth-type должно быть одним из: user, joined, joined-"
#~ "acct, open, proxy-user@host"

#~ msgid "ftp:ssl-auth must be one of: SSL, TLS, TLS-P, TLS-C"
#~ msgstr "ftp:ssl-auth должен быть одним из: SSL, TLS, TLS-P, TLS-C"

#~ msgid "Invalid suffix. Valid suffixes are: k, M, G, T, P, E, Z, Y"
#~ msgstr "Неверный суффикс. Допустимыми являются: k, M, G, T, P, E, Z, Y"

#~ msgid "invalid pair of numbers"
#~ msgstr "неверно указана пара чисел"

#~ msgid "Saw `unknown', assume failed login"
#~ msgstr "Получена строка `unknown', подразумевается неудача регистрации"

#~ msgid "Can only edit plain queues.\n"
#~ msgstr "Можно редактировать только обычные очереди.\n"

#~ msgid "Couldn't create temporary file `%s': %s.\n"
#~ msgstr "Невозможно создать временный файл `%s': %s.\n"

#~ msgid "%s: error writing %s: %s\n"
#~ msgstr "%s: ошибка записи %s: %s\n"

#~ msgid "%s: illegal option -- %c\n"
#~ msgstr "%s: недопустимая опция -- %c\n"

#~ msgid ""
#~ "LFTP is free software, covered by the GNU General Public License, and you "
#~ "are\n"
#~ "welcome to change it and/or distribute copies of it under certain "
#~ "conditions.\n"
#~ "There is absolutely no warranty for LFTP.  See COPYING for details.\n"
#~ msgstr ""
#~ "LFTP - свободная программа под лицензией GNU General Public License,\n"
#~ "ее можно изменять и/или распространять при соблюдении некоторых условий.\n"
#~ "На LFTP не предоставляется никаких гарантий. Смотрите файл COPYING для\n"
#~ "дополнительной информации.\n"

#~ msgid "block size"
#~ msgstr "размер блока"

#~ msgid "%sTo be removed: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
#~ msgstr ""
#~ "%sПодлежат удалению: %d каталог$|а|ов$, %d файл$|а|ов$, %d $ссылка|ссылки|"
#~ "ссылок$\n"

#~ msgid "Usage: %s userid [pass]\n"
#~ msgstr "Использование: %s имя [пароль]\n"

#~ msgid "Cache is on"
#~ msgstr "Кэш включен"

#~ msgid "Cache is off"
#~ msgstr "Кэш выключен"

#~ msgid "Cache entries do not expire"
#~ msgstr "Содержимое кэша не устаревает"

#~ msgid "Cache entries expire in %ld $#l#second|seconds$\n"
#~ msgstr "Содержимое кэша устаревает через %ld $#l#секунду|секунды|секунд$\n"

#~ msgid "Cache entries expire in %ld $#l#minute|minutes$\n"
#~ msgstr "Содержимое кэша устаревает через %ld $#l#минуту|минуты|минут$\n"

#~ msgid ""
#~ "This is free software with ABSOLUTELY NO WARRANTY. See COPYING for "
#~ "details.\n"
#~ msgstr ""
#~ "Это свободное программное обеспечение, распространяемое без каких-либо\n"
#~ "гарантий. Смотрите файл COPYING для получения дополнительной информации.\n"

#~ msgid "%s: Invalid number for size\n"
#~ msgstr "%s: Неверное число для размера\n"

#~ msgid "Warning: getcwd() failed: %s\n"
#~ msgstr "Предупреждение: ошибка getcwd(): %s\n"

#~ msgid "Usage: %s mode file...\n"
#~ msgstr "Использование: %s режим файл...\n"

#~ msgid "%s: %s - not an octal number\n"
#~ msgstr "%s: %s - не является восьмеричным числом\n"

#~ msgid "Removing old remote file `%s'"
#~ msgstr "Удаление с сервера старого файла `%s'"

#~ msgid "Retrieving remote file `%s'"
#~ msgstr "Получение с сервера файла `%s'"

#~ msgid "nlist [<args>]"
#~ msgstr "nlist [<аргументы>]"

#~ msgid "List remote file names\n"
#~ msgstr "Вывести список файлов на сервере\n"

#~ msgid "Same as `get -c'\n"
#~ msgstr "То же, что и `get -c'\n"

#~ msgid "rels [<args>]"
#~ msgstr "rels [<аргументы>]"

#~ msgid "Same as `put -c'\n"
#~ msgstr "То же, что и `put -c'\n"

#~ msgid "bzcat <files>"
#~ msgstr "bzcat <файлы>"

#~ msgid "bzmore <files>"
#~ msgstr "bzmore <файлы>"

#~ msgid "child returned invalid data"
#~ msgstr "порожденный процесс возвратил некорректные данные"

#~ msgid "Saw `Login incorrect', assume failed login"
#~ msgstr ""
#~ "Получена строка `Login incorrect', подразумевается неудача регистрации"

#~ msgid "%s: cannot add empty bookmark\n"
#~ msgstr "%s: невозможно добавить пустую закладку\n"

#~ msgid "%s: cannot get current directory\n"
#~ msgstr "%s: невозможно получить текущий каталог\n"

#~ msgid "Fatal protocol error occured"
#~ msgstr "Произошла фатальная ошибка протокола"

#~ msgid "rmdir <dirs>"
#~ msgstr "rmdir <каталоги>"

#~ msgid "Usage: %s files...\n"
#~ msgstr "Использование: %s файлы...\n"

#~ msgid "Use specified info for remote login\n"
#~ msgstr "Использовать указанную информацию для регистрации на сервере\n"

#~ msgid "Sorry, %s can work with only ftp protocol\n"
#~ msgstr "Извините, %s поддерживает только протокол ftp\n"

#~ msgid "Usage: %s [-c] [-p] <source> <dest>\n"
#~ msgstr "Использование: %s [-c] [-p] <источник> <приемник>\n"

#~ msgid "Getting size of `%s' [%s]"
#~ msgstr "Получение размера файла `%s' [%s]"

#~ msgid "Copying of `%s' in progress (%c)"
#~ msgstr "Выполняется копирование файла `%s' (%c)"

#~ msgid " - not supported protocol\n"
#~ msgstr " - протокол не поддерживается\n"

#~ msgid "%s: %s - not supported protocol\n"
#~ msgstr "%s: %s - протокол не поддерживается\n"

#~ msgid "remote rm(%s) - %s\n"
#~ msgstr "удаление `%s' на сервере - %s\n"

#~ msgid "\tNo files transferred successfully :(\n"
#~ msgstr "\tНи одного файла не перемещено успешно :(\n"

#~ msgid "Average transfer rate %g bytes/s\n"
#~ msgstr "Средняя скорость пересылки: %g байт/с\n"

#~ msgid "%s: cannot write -- disk full?\n"
#~ msgstr "%s: не удается записать в файл -- диск переполнен?\n"

#~ msgid "mget [-c] [-d] [-e] <files>"
#~ msgstr "mget [-c] [-d] [-e] <файлы>"

#~ msgid ""
#~ "Upload files with wildcard expansion\n"
#~ " -c  continue, reput\n"
#~ " -d  create directories the same as in file names and put the\n"
#~ "     files into them instead of current directory\n"
#~ msgstr ""
#~ "Поместить <файлы> на сервер с поддержкой шаблонов в именах файлов\n"
#~ " -c  возобновить пересылку (reput)\n"
#~ " -d  создавать каталоги в соответствии с именами файлов\n"
#~ "     и помещать файлы в эти каталоги вместо текущего\n"

#~ msgid "Wait for specified job to terminate.\n"
#~ msgstr "Ожидать завершения работы указанного задания.\n"

#~ msgid "Usage: site <site_cmd>\n"
#~ msgstr "Использование: site <команда_сервера>\n"

#~ msgid ""
#~ "FtpGet | Version %s | Copyright (C) 1996-1999 Alexander V. Lukyanov\n"
#~ msgstr ""
#~ "FtpGet | Версия %s | Copyright (C) 1996-1999 Александр В. Лукьянов\n"

#~ msgid ""
#~ "Usage: ftpget [OPTIONS] host filename [-o local] [filename...]\n"
#~ "\n"
#~ "-p  --port         set port number\n"
#~ "-u  --user         login as user using pass as password\n"
#~ "-l  --list         get listing of specified directory(ies)\n"
#~ "-c  --continue     reget specified file(s)\n"
#~ "-q  --quiet        quiet (no output)\n"
#~ "-v  --verbose      verbose (lots of output)\n"
#~ "    --async-mode   use asynchronous mode (faster)\n"
#~ "    --sync-mode    use synchronous mode (compatible with bugs)\n"
#~ "\n"
#~ "-o  output to local file `local' (default - base name of filename)\n"
#~ msgstr ""
#~ "Использование: ftpget [КЛЮЧИ] машина файл [-o лок.файл] [файл...]\n"
#~ "\n"
#~ "-p  --port         использовать указанный номер порта\n"
#~ "-u  --user         использовать для регистрации указанные имя и пароль\n"
#~ "-l  --list         получить список файлов указанного каталога "
#~ "(каталогов)\n"
#~ "-c  --continue     возобновить получение указанных файлов (reget)\n"
#~ "-q  --quiet        подавлять вывод сообщений\n"
#~ "-v  --verbose      выводить максимум сообщений\n"
#~ "    --async-mode   работать в асинхронном режиме (высокая скорость)\n"
#~ "    --sync-mode    работать в синхронном режиме (высокая совместимость)\n"
#~ "\n"
#~ "-o  сохранять в локальный файл `лок.файл'\n"
#~ "    (по умолчанию - имя файла на сервере без каталога)\n"

#~ msgid "Usage: %s [-c] [-d] [-e] pattern ...\n"
#~ msgstr "Использование: %s [-c] [-d] [-e] шаблон ...\n"

#~ msgid "\tat %ld (%d%%) [%s]\n"
#~ msgstr "\tпозиция %ld (%d%%) [%s]\n"
