/* Opie (s/key) support for FTP.
   Copyright (C) 1998, 1999, 2000, 2001, 2002, 2003, 2004, 2005, 2006,
   2007, 2008, 2009, 2010, 2011 Free Software Foundation, Inc.

This file is part of GNU Wget.

GNU Wget is free software; you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation; either version 3 of the License, or
(at your option) any later version.

GNU Wget is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with Wget.  If not, see <http://www.gnu.org/licenses/>.

Additional permission under GNU GPL version 3 section 7

If you modify this program, or any covered work, by linking or
combining it with the OpenSSL project's OpenSSL library (or a
modified version of that library), containing parts covered by the
terms of the OpenSSL or SSLeay licenses, the Free Software Foundation
grants you additional permission to convey the resulting work.
Corresponding Source for a non-source form of such a combination
shall include the source code for the parts of OpenSSL used as well
as that of the covered work.  */

#include <config.h>

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include "md5.h"

/* Dictionary for integer-word translations.  Available in appendix D
   of rfc2289.  */
 static char Wp[2048][4] = {
  { 'A', '\0', '\0', '\0' },
  { 'A', 'B', 'E', '\0' },
  { 'A', 'C', 'E', '\0' },
  { 'A', 'C', 'T', '\0' },
  { 'A', 'D', '\0', '\0' },
  { 'A', 'D', 'A', '\0' },
  { 'A', 'D', 'D', '\0' },
  { 'A', 'G', 'O', '\0' },
  { 'A', 'I', 'D', '\0' },
  { 'A', 'I', 'M', '\0' },
  { 'A', 'I', 'R', '\0' },
  { 'A', 'L', 'L', '\0' },
  { 'A', 'L', 'P', '\0' },
  { 'A', 'M', '\0', '\0' },
  { 'A', 'M', 'Y', '\0' },
  { 'A', 'N', '\0', '\0' },
  { 'A', 'N', 'A', '\0' },
  { 'A', 'N', 'D', '\0' },
  { 'A', 'N', 'N', '\0' },
  { 'A', 'N', 'T', '\0' },
  { 'A', 'N', 'Y', '\0' },
  { 'A', 'P', 'E', '\0' },
  { 'A', 'P', 'S', '\0' },
  { 'A', 'P', 'T', '\0' },
  { 'A', 'R', 'C', '\0' },
  { 'A', 'R', 'E', '\0' },
  { 'A', 'R', 'K', '\0' },
  { 'A', 'R', 'M', '\0' },
  { 'A', 'R', 'T', '\0' },
  { 'A', 'S', '\0', '\0' },
  { 'A', 'S', 'H', '\0' },
  { 'A', 'S', 'K', '\0' },
  { 'A', 'T', '\0', '\0' },
  { 'A', 'T', 'E', '\0' },
  { 'A', 'U', 'G', '\0' },
  { 'A', 'U', 'K', '\0' },
  { 'A', 'V', 'E', '\0' },
  { 'A', 'W', 'E', '\0' },
  { 'A', 'W', 'K', '\0' },
  { 'A', 'W', 'L', '\0' },
  { 'A', 'W', 'N', '\0' },
  { 'A', 'X', '\0', '\0' },
  { 'A', 'Y', 'E', '\0' },
  { 'B', 'A', 'D', '\0' },
  { 'B', 'A', 'G', '\0' },
  { 'B', 'A', 'H', '\0' },
  { 'B', 'A', 'M', '\0' },
  { 'B', 'A', 'N', '\0' },
  { 'B', 'A', 'R', '\0' },
  { 'B', 'A', 'T', '\0' },
  { 'B', 'A', 'Y', '\0' },
  { 'B', 'E', '\0', '\0' },
  { 'B', 'E', 'D', '\0' },
  { 'B', 'E', 'E', '\0' },
  { 'B', 'E', 'G', '\0' },
  { 'B', 'E', 'N', '\0' },
  { 'B', 'E', 'T', '\0' },
  { 'B', 'E', 'Y', '\0' },
  { 'B', 'I', 'B', '\0' },
  { 'B', 'I', 'D', '\0' },
  { 'B', 'I', 'G', '\0' },
  { 'B', 'I', 'N', '\0' },
  { 'B', 'I', 'T', '\0' },
  { 'B', 'O', 'B', '\0' },
  { 'B', 'O', 'G', '\0' },
  { 'B', 'O', 'N', '\0' },
  { 'B', 'O', 'O', '\0' },
  { 'B', 'O', 'P', '\0' },
  { 'B', 'O', 'W', '\0' },
  { 'B', 'O', 'Y', '\0' },
  { 'B', 'U', 'B', '\0' },
  { 'B', 'U', 'D', '\0' },
  { 'B', 'U', 'G', '\0' },
  { 'B', 'U', 'M', '\0' },
  { 'B', 'U', 'N', '\0' },
  { 'B', 'U', 'S', '\0' },
  { 'B', 'U', 'T', '\0' },
  { 'B', 'U', 'Y', '\0' },
  { 'B', 'Y', '\0', '\0' },
  { 'B', 'Y', 'E', '\0' },
  { 'C', 'A', 'B', '\0' },
  { 'C', 'A', 'L', '\0' },
  { 'C', 'A', 'M', '\0' },
  { 'C', 'A', 'N', '\0' },
  { 'C', 'A', 'P', '\0' },
  { 'C', 'A', 'R', '\0' },
  { 'C', 'A', 'T', '\0' },
  { 'C', 'A', 'W', '\0' },
  { 'C', 'O', 'D', '\0' },
  { 'C', 'O', 'G', '\0' },
  { 'C', 'O', 'L', '\0' },
  { 'C', 'O', 'N', '\0' },
  { 'C', 'O', 'O', '\0' },
  { 'C', 'O', 'P', '\0' },
  { 'C', 'O', 'T', '\0' },
  { 'C', 'O', 'W', '\0' },
  { 'C', 'O', 'Y', '\0' },
  { 'C', 'R', 'Y', '\0' },
  { 'C', 'U', 'B', '\0' },
  { 'C', 'U', 'E', '\0' },
  { 'C', 'U', 'P', '\0' },
  { 'C', 'U', 'R', '\0' },
  { 'C', 'U', 'T', '\0' },
  { 'D', 'A', 'B', '\0' },
  { 'D', 'A', 'D', '\0' },
  { 'D', 'A', 'M', '\0' },
  { 'D', 'A', 'N', '\0' },
  { 'D', 'A', 'R', '\0' },
  { 'D', 'A', 'Y', '\0' },
  { 'D', 'E', 'E', '\0' },
  { 'D', 'E', 'L', '\0' },
  { 'D', 'E', 'N', '\0' },
  { 'D', 'E', 'S', '\0' },
  { 'D', 'E', 'W', '\0' },
  { 'D', 'I', 'D', '\0' },
  { 'D', 'I', 'E', '\0' },
  { 'D', 'I', 'G', '\0' },
  { 'D', 'I', 'N', '\0' },
  { 'D', 'I', 'P', '\0' },
  { 'D', 'O', '\0', '\0' },
  { 'D', 'O', 'E', '\0' },
  { 'D', 'O', 'G', '\0' },
  { 'D', 'O', 'N', '\0' },
  { 'D', 'O', 'T', '\0' },
  { 'D', 'O', 'W', '\0' },
  { 'D', 'R', 'Y', '\0' },
  { 'D', 'U', 'B', '\0' },
  { 'D', 'U', 'D', '\0' },
  { 'D', 'U', 'E', '\0' },
  { 'D', 'U', 'G', '\0' },
  { 'D', 'U', 'N', '\0' },
  { 'E', 'A', 'R', '\0' },
  { 'E', 'A', 'T', '\0' },
  { 'E', 'D', '\0', '\0' },
  { 'E', 'E', 'L', '\0' },
  { 'E', 'G', 'G', '\0' },
  { 'E', 'G', 'O', '\0' },
  { 'E', 'L', 'I', '\0' },
  { 'E', 'L', 'K', '\0' },
  { 'E', 'L', 'M', '\0' },
  { 'E', 'L', 'Y', '\0' },
  { 'E', 'M', '\0', '\0' },
  { 'E', 'N', 'D', '\0' },
  { 'E', 'S', 'T', '\0' },
  { 'E', 'T', 'C', '\0' },
  { 'E', 'V', 'A', '\0' },
  { 'E', 'V', 'E', '\0' },
  { 'E', 'W', 'E', '\0' },
  { 'E', 'Y', 'E', '\0' },
  { 'F', 'A', 'D', '\0' },
  { 'F', 'A', 'N', '\0' },
  { 'F', 'A', 'R', '\0' },
  { 'F', 'A', 'T', '\0' },
  { 'F', 'A', 'Y', '\0' },
  { 'F', 'E', 'D', '\0' },
  { 'F', 'E', 'E', '\0' },
  { 'F', 'E', 'W', '\0' },
  { 'F', 'I', 'B', '\0' },
  { 'F', 'I', 'G', '\0' },
  { 'F', 'I', 'N', '\0' },
  { 'F', 'I', 'R', '\0' },
  { 'F', 'I', 'T', '\0' },
  { 'F', 'L', 'O', '\0' },
  { 'F', 'L', 'Y', '\0' },
  { 'F', 'O', 'E', '\0' },
  { 'F', 'O', 'G', '\0' },
  { 'F', 'O', 'R', '\0' },
  { 'F', 'R', 'Y', '\0' },
  { 'F', 'U', 'M', '\0' },
  { 'F', 'U', 'N', '\0' },
  { 'F', 'U', 'R', '\0' },
  { 'G', 'A', 'B', '\0' },
  { 'G', 'A', 'D', '\0' },
  { 'G', 'A', 'G', '\0' },
  { 'G', 'A', 'L', '\0' },
  { 'G', 'A', 'M', '\0' },
  { 'G', 'A', 'P', '\0' },
  { 'G', 'A', 'S', '\0' },
  { 'G', 'A', 'Y', '\0' },
  { 'G', 'E', 'E', '\0' },
  { 'G', 'E', 'L', '\0' },
  { 'G', 'E', 'M', '\0' },
  { 'G', 'E', 'T', '\0' },
  { 'G', 'I', 'G', '\0' },
  { 'G', 'I', 'L', '\0' },
  { 'G', 'I', 'N', '\0' },
  { 'G', 'O', '\0', '\0' },
  { 'G', 'O', 'T', '\0' },
  { 'G', 'U', 'M', '\0' },
  { 'G', 'U', 'N', '\0' },
  { 'G', 'U', 'S', '\0' },
  { 'G', 'U', 'T', '\0' },
  { 'G', 'U', 'Y', '\0' },
  { 'G', 'Y', 'M', '\0' },
  { 'G', 'Y', 'P', '\0' },
  { 'H', 'A', '\0', '\0' },
  { 'H', 'A', 'D', '\0' },
  { 'H', 'A', 'L', '\0' },
  { 'H', 'A', 'M', '\0' },
  { 'H', 'A', 'N', '\0' },
  { 'H', 'A', 'P', '\0' },
  { 'H', 'A', 'S', '\0' },
  { 'H', 'A', 'T', '\0' },
  { 'H', 'A', 'W', '\0' },
  { 'H', 'A', 'Y', '\0' },
  { 'H', 'E', '\0', '\0' },
  { 'H', 'E', 'M', '\0' },
  { 'H', 'E', 'N', '\0' },
  { 'H', 'E', 'R', '\0' },
  { 'H', 'E', 'W', '\0' },
  { 'H', 'E', 'Y', '\0' },
  { 'H', 'I', '\0', '\0' },
  { 'H', 'I', 'D', '\0' },
  { 'H', 'I', 'M', '\0' },
  { 'H', 'I', 'P', '\0' },
  { 'H', 'I', 'S', '\0' },
  { 'H', 'I', 'T', '\0' },
  { 'H', 'O', '\0', '\0' },
  { 'H', 'O', 'B', '\0' },
  { 'H', 'O', 'C', '\0' },
  { 'H', 'O', 'E', '\0' },
  { 'H', 'O', 'G', '\0' },
  { 'H', 'O', 'P', '\0' },
  { 'H', 'O', 'T', '\0' },
  { 'H', 'O', 'W', '\0' },
  { 'H', 'U', 'B', '\0' },
  { 'H', 'U', 'E', '\0' },
  { 'H', 'U', 'G', '\0' },
  { 'H', 'U', 'H', '\0' },
  { 'H', 'U', 'M', '\0' },
  { 'H', 'U', 'T', '\0' },
  { 'I', '\0', '\0', '\0' },
  { 'I', 'C', 'Y', '\0' },
  { 'I', 'D', 'A', '\0' },
  { 'I', 'F', '\0', '\0' },
  { 'I', 'K', 'E', '\0' },
  { 'I', 'L', 'L', '\0' },
  { 'I', 'N', 'K', '\0' },
  { 'I', 'N', 'N', '\0' },
  { 'I', 'O', '\0', '\0' },
  { 'I', 'O', 'N', '\0' },
  { 'I', 'Q', '\0', '\0' },
  { 'I', 'R', 'A', '\0' },
  { 'I', 'R', 'E', '\0' },
  { 'I', 'R', 'K', '\0' },
  { 'I', 'S', '\0', '\0' },
  { 'I', 'T', '\0', '\0' },
  { 'I', 'T', 'S', '\0' },
  { 'I', 'V', 'Y', '\0' },
  { 'J', 'A', 'B', '\0' },
  { 'J', 'A', 'G', '\0' },
  { 'J', 'A', 'M', '\0' },
  { 'J', 'A', 'N', '\0' },
  { 'J', 'A', 'R', '\0' },
  { 'J', 'A', 'W', '\0' },
  { 'J', 'A', 'Y', '\0' },
  { 'J', 'E', 'T', '\0' },
  { 'J', 'I', 'G', '\0' },
  { 'J', 'I', 'M', '\0' },
  { 'J', 'O', '\0', '\0' },
  { 'J', 'O', 'B', '\0' },
  { 'J', 'O', 'E', '\0' },
  { 'J', 'O', 'G', '\0' },
  { 'J', 'O', 'T', '\0' },
  { 'J', 'O', 'Y', '\0' },
  { 'J', 'U', 'G', '\0' },
  { 'J', 'U', 'T', '\0' },
  { 'K', 'A', 'Y', '\0' },
  { 'K', 'E', 'G', '\0' },
  { 'K', 'E', 'N', '\0' },
  { 'K', 'E', 'Y', '\0' },
  { 'K', 'I', 'D', '\0' },
  { 'K', 'I', 'M', '\0' },
  { 'K', 'I', 'N', '\0' },
  { 'K', 'I', 'T', '\0' },
  { 'L', 'A', '\0', '\0' },
  { 'L', 'A', 'B', '\0' },
  { 'L', 'A', 'C', '\0' },
  { 'L', 'A', 'D', '\0' },
  { 'L', 'A', 'G', '\0' },
  { 'L', 'A', 'M', '\0' },
  { 'L', 'A', 'P', '\0' },
  { 'L', 'A', 'W', '\0' },
  { 'L', 'A', 'Y', '\0' },
  { 'L', 'E', 'A', '\0' },
  { 'L', 'E', 'D', '\0' },
  { 'L', 'E', 'E', '\0' },
  { 'L', 'E', 'G', '\0' },
  { 'L', 'E', 'N', '\0' },
  { 'L', 'E', 'O', '\0' },
  { 'L', 'E', 'T', '\0' },
  { 'L', 'E', 'W', '\0' },
  { 'L', 'I', 'D', '\0' },
  { 'L', 'I', 'E', '\0' },
  { 'L', 'I', 'N', '\0' },
  { 'L', 'I', 'P', '\0' },
  { 'L', 'I', 'T', '\0' },
  { 'L', 'O', '\0', '\0' },
  { 'L', 'O', 'B', '\0' },
  { 'L', 'O', 'G', '\0' },
  { 'L', 'O', 'P', '\0' },
  { 'L', 'O', 'S', '\0' },
  { 'L', 'O', 'T', '\0' },
  { 'L', 'O', 'U', '\0' },
  { 'L', 'O', 'W', '\0' },
  { 'L', 'O', 'Y', '\0' },
  { 'L', 'U', 'G', '\0' },
  { 'L', 'Y', 'E', '\0' },
  { 'M', 'A', '\0', '\0' },
  { 'M', 'A', 'C', '\0' },
  { 'M', 'A', 'D', '\0' },
  { 'M', 'A', 'E', '\0' },
  { 'M', 'A', 'N', '\0' },
  { 'M', 'A', 'O', '\0' },
  { 'M', 'A', 'P', '\0' },
  { 'M', 'A', 'T', '\0' },
  { 'M', 'A', 'W', '\0' },
  { 'M', 'A', 'Y', '\0' },
  { 'M', 'E', '\0', '\0' },
  { 'M', 'E', 'G', '\0' },
  { 'M', 'E', 'L', '\0' },
  { 'M', 'E', 'N', '\0' },
  { 'M', 'E', 'T', '\0' },
  { 'M', 'E', 'W', '\0' },
  { 'M', 'I', 'D', '\0' },
  { 'M', 'I', 'N', '\0' },
  { 'M', 'I', 'T', '\0' },
  { 'M', 'O', 'B', '\0' },
  { 'M', 'O', 'D', '\0' },
  { 'M', 'O', 'E', '\0' },
  { 'M', 'O', 'O', '\0' },
  { 'M', 'O', 'P', '\0' },
  { 'M', 'O', 'S', '\0' },
  { 'M', 'O', 'T', '\0' },
  { 'M', 'O', 'W', '\0' },
  { 'M', 'U', 'D', '\0' },
  { 'M', 'U', 'G', '\0' },
  { 'M', 'U', 'M', '\0' },
  { 'M', 'Y', '\0', '\0' },
  { 'N', 'A', 'B', '\0' },
  { 'N', 'A', 'G', '\0' },
  { 'N', 'A', 'N', '\0' },
  { 'N', 'A', 'P', '\0' },
  { 'N', 'A', 'T', '\0' },
  { 'N', 'A', 'Y', '\0' },
  { 'N', 'E', '\0', '\0' },
  { 'N', 'E', 'D', '\0' },
  { 'N', 'E', 'E', '\0' },
  { 'N', 'E', 'T', '\0' },
  { 'N', 'E', 'W', '\0' },
  { 'N', 'I', 'B', '\0' },
  { 'N', 'I', 'L', '\0' },
  { 'N', 'I', 'P', '\0' },
  { 'N', 'I', 'T', '\0' },
  { 'N', 'O', '\0', '\0' },
  { 'N', 'O', 'B', '\0' },
  { 'N', 'O', 'D', '\0' },
  { 'N', 'O', 'N', '\0' },
  { 'N', 'O', 'R', '\0' },
  { 'N', 'O', 'T', '\0' },
  { 'N', 'O', 'V', '\0' },
  { 'N', 'O', 'W', '\0' },
  { 'N', 'U', '\0', '\0' },
  { 'N', 'U', 'N', '\0' },
  { 'N', 'U', 'T', '\0' },
  { 'O', '\0', '\0', '\0' },
  { 'O', 'A', 'F', '\0' },
  { 'O', 'A', 'K', '\0' },
  { 'O', 'A', 'R', '\0' },
  { 'O', 'A', 'T', '\0' },
  { 'O', 'D', 'D', '\0' },
  { 'O', 'D', 'E', '\0' },
  { 'O', 'F', '\0', '\0' },
  { 'O', 'F', 'F', '\0' },
  { 'O', 'F', 'T', '\0' },
  { 'O', 'H', '\0', '\0' },
  { 'O', 'I', 'L', '\0' },
  { 'O', 'K', '\0', '\0' },
  { 'O', 'L', 'D', '\0' },
  { 'O', 'N', '\0', '\0' },
  { 'O', 'N', 'E', '\0' },
  { 'O', 'R', '\0', '\0' },
  { 'O', 'R', 'B', '\0' },
  { 'O', 'R', 'E', '\0' },
  { 'O', 'R', 'R', '\0' },
  { 'O', 'S', '\0', '\0' },
  { 'O', 'T', 'T', '\0' },
  { 'O', 'U', 'R', '\0' },
  { 'O', 'U', 'T', '\0' },
  { 'O', 'V', 'A', '\0' },
  { 'O', 'W', '\0', '\0' },
  { 'O', 'W', 'E', '\0' },
  { 'O', 'W', 'L', '\0' },
  { 'O', 'W', 'N', '\0' },
  { 'O', 'X', '\0', '\0' },
  { 'P', 'A', '\0', '\0' },
  { 'P', 'A', 'D', '\0' },
  { 'P', 'A', 'L', '\0' },
  { 'P', 'A', 'M', '\0' },
  { 'P', 'A', 'N', '\0' },
  { 'P', 'A', 'P', '\0' },
  { 'P', 'A', 'R', '\0' },
  { 'P', 'A', 'T', '\0' },
  { 'P', 'A', 'W', '\0' },
  { 'P', 'A', 'Y', '\0' },
  { 'P', 'E', 'A', '\0' },
  { 'P', 'E', 'G', '\0' },
  { 'P', 'E', 'N', '\0' },
  { 'P', 'E', 'P', '\0' },
  { 'P', 'E', 'R', '\0' },
  { 'P', 'E', 'T', '\0' },
  { 'P', 'E', 'W', '\0' },
  { 'P', 'H', 'I', '\0' },
  { 'P', 'I', '\0', '\0' },
  { 'P', 'I', 'E', '\0' },
  { 'P', 'I', 'N', '\0' },
  { 'P', 'I', 'T', '\0' },
  { 'P', 'L', 'Y', '\0' },
  { 'P', 'O', '\0', '\0' },
  { 'P', 'O', 'D', '\0' },
  { 'P', 'O', 'E', '\0' },
  { 'P', 'O', 'P', '\0' },
  { 'P', 'O', 'T', '\0' },
  { 'P', 'O', 'W', '\0' },
  { 'P', 'R', 'O', '\0' },
  { 'P', 'R', 'Y', '\0' },
  { 'P', 'U', 'B', '\0' },
  { 'P', 'U', 'G', '\0' },
  { 'P', 'U', 'N', '\0' },
  { 'P', 'U', 'P', '\0' },
  { 'P', 'U', 'T', '\0' },
  { 'Q', 'U', 'O', '\0' },
  { 'R', 'A', 'G', '\0' },
  { 'R', 'A', 'M', '\0' },
  { 'R', 'A', 'N', '\0' },
  { 'R', 'A', 'P', '\0' },
  { 'R', 'A', 'T', '\0' },
  { 'R', 'A', 'W', '\0' },
  { 'R', 'A', 'Y', '\0' },
  { 'R', 'E', 'B', '\0' },
  { 'R', 'E', 'D', '\0' },
  { 'R', 'E', 'P', '\0' },
  { 'R', 'E', 'T', '\0' },
  { 'R', 'I', 'B', '\0' },
  { 'R', 'I', 'D', '\0' },
  { 'R', 'I', 'G', '\0' },
  { 'R', 'I', 'M', '\0' },
  { 'R', 'I', 'O', '\0' },
  { 'R', 'I', 'P', '\0' },
  { 'R', 'O', 'B', '\0' },
  { 'R', 'O', 'D', '\0' },
  { 'R', 'O', 'E', '\0' },
  { 'R', 'O', 'N', '\0' },
  { 'R', 'O', 'T', '\0' },
  { 'R', 'O', 'W', '\0' },
  { 'R', 'O', 'Y', '\0' },
  { 'R', 'U', 'B', '\0' },
  { 'R', 'U', 'E', '\0' },
  { 'R', 'U', 'G', '\0' },
  { 'R', 'U', 'M', '\0' },
  { 'R', 'U', 'N', '\0' },
  { 'R', 'Y', 'E', '\0' },
  { 'S', 'A', 'C', '\0' },
  { 'S', 'A', 'D', '\0' },
  { 'S', 'A', 'G', '\0' },
  { 'S', 'A', 'L', '\0' },
  { 'S', 'A', 'M', '\0' },
  { 'S', 'A', 'N', '\0' },
  { 'S', 'A', 'P', '\0' },
  { 'S', 'A', 'T', '\0' },
  { 'S', 'A', 'W', '\0' },
  { 'S', 'A', 'Y', '\0' },
  { 'S', 'E', 'A', '\0' },
  { 'S', 'E', 'C', '\0' },
  { 'S', 'E', 'E', '\0' },
  { 'S', 'E', 'N', '\0' },
  { 'S', 'E', 'T', '\0' },
  { 'S', 'E', 'W', '\0' },
  { 'S', 'H', 'E', '\0' },
  { 'S', 'H', 'Y', '\0' },
  { 'S', 'I', 'N', '\0' },
  { 'S', 'I', 'P', '\0' },
  { 'S', 'I', 'R', '\0' },
  { 'S', 'I', 'S', '\0' },
  { 'S', 'I', 'T', '\0' },
  { 'S', 'K', 'I', '\0' },
  { 'S', 'K', 'Y', '\0' },
  { 'S', 'L', 'Y', '\0' },
  { 'S', 'O', '\0', '\0' },
  { 'S', 'O', 'B', '\0' },
  { 'S', 'O', 'D', '\0' },
  { 'S', 'O', 'N', '\0' },
  { 'S', 'O', 'P', '\0' },
  { 'S', 'O', 'W', '\0' },
  { 'S', 'O', 'Y', '\0' },
  { 'S', 'P', 'A', '\0' },
  { 'S', 'P', 'Y', '\0' },
  { 'S', 'U', 'B', '\0' },
  { 'S', 'U', 'D', '\0' },
  { 'S', 'U', 'E', '\0' },
  { 'S', 'U', 'M', '\0' },
  { 'S', 'U', 'N', '\0' },
  { 'S', 'U', 'P', '\0' },
  { 'T', 'A', 'B', '\0' },
  { 'T', 'A', 'D', '\0' },
  { 'T', 'A', 'G', '\0' },
  { 'T', 'A', 'N', '\0' },
  { 'T', 'A', 'P', '\0' },
  { 'T', 'A', 'R', '\0' },
  { 'T', 'E', 'A', '\0' },
  { 'T', 'E', 'D', '\0' },
  { 'T', 'E', 'E', '\0' },
  { 'T', 'E', 'N', '\0' },
  { 'T', 'H', 'E', '\0' },
  { 'T', 'H', 'Y', '\0' },
  { 'T', 'I', 'C', '\0' },
  { 'T', 'I', 'E', '\0' },
  { 'T', 'I', 'M', '\0' },
  { 'T', 'I', 'N', '\0' },
  { 'T', 'I', 'P', '\0' },
  { 'T', 'O', '\0', '\0' },
  { 'T', 'O', 'E', '\0' },
  { 'T', 'O', 'G', '\0' },
  { 'T', 'O', 'M', '\0' },
  { 'T', 'O', 'N', '\0' },
  { 'T', 'O', 'O', '\0' },
  { 'T', 'O', 'P', '\0' },
  { 'T', 'O', 'W', '\0' },
  { 'T', 'O', 'Y', '\0' },
  { 'T', 'R', 'Y', '\0' },
  { 'T', 'U', 'B', '\0' },
  { 'T', 'U', 'G', '\0' },
  { 'T', 'U', 'M', '\0' },
  { 'T', 'U', 'N', '\0' },
  { 'T', 'W', 'O', '\0' },
  { 'U', 'N', '\0', '\0' },
  { 'U', 'P', '\0', '\0' },
  { 'U', 'S', '\0', '\0' },
  { 'U', 'S', 'E', '\0' },
  { 'V', 'A', 'N', '\0' },
  { 'V', 'A', 'T', '\0' },
  { 'V', 'E', 'T', '\0' },
  { 'V', 'I', 'E', '\0' },
  { 'W', 'A', 'D', '\0' },
  { 'W', 'A', 'G', '\0' },
  { 'W', 'A', 'R', '\0' },
  { 'W', 'A', 'S', '\0' },
  { 'W', 'A', 'Y', '\0' },
  { 'W', 'E', '\0', '\0' },
  { 'W', 'E', 'B', '\0' },
  { 'W', 'E', 'D', '\0' },
  { 'W', 'E', 'E', '\0' },
  { 'W', 'E', 'T', '\0' },
  { 'W', 'H', 'O', '\0' },
  { 'W', 'H', 'Y', '\0' },
  { 'W', 'I', 'N', '\0' },
  { 'W', 'I', 'T', '\0' },
  { 'W', 'O', 'K', '\0' },
  { 'W', 'O', 'N', '\0' },
  { 'W', 'O', 'O', '\0' },
  { 'W', 'O', 'W', '\0' },
  { 'W', 'R', 'Y', '\0' },
  { 'W', 'U', '\0', '\0' },
  { 'Y', 'A', 'M', '\0' },
  { 'Y', 'A', 'P', '\0' },
  { 'Y', 'A', 'W', '\0' },
  { 'Y', 'E', '\0', '\0' },
  { 'Y', 'E', 'A', '\0' },
  { 'Y', 'E', 'S', '\0' },
  { 'Y', 'E', 'T', '\0' },
  { 'Y', 'O', 'U', '\0' },
  { 'A', 'B', 'E', 'D' },
  { 'A', 'B', 'E', 'L' },
  { 'A', 'B', 'E', 'T' },
  { 'A', 'B', 'L', 'E' },
  { 'A', 'B', 'U', 'T' },
  { 'A', 'C', 'H', 'E' },
  { 'A', 'C', 'I', 'D' },
  { 'A', 'C', 'M', 'E' },
  { 'A', 'C', 'R', 'E' },
  { 'A', 'C', 'T', 'A' },
  { 'A', 'C', 'T', 'S' },
  { 'A', 'D', 'A', 'M' },
  { 'A', 'D', 'D', 'S' },
  { 'A', 'D', 'E', 'N' },
  { 'A', 'F', 'A', 'R' },
  { 'A', 'F', 'R', 'O' },
  { 'A', 'G', 'E', 'E' },
  { 'A', 'H', 'E', 'M' },
  { 'A', 'H', 'O', 'Y' },
  { 'A', 'I', 'D', 'A' },
  { 'A', 'I', 'D', 'E' },
  { 'A', 'I', 'D', 'S' },
  { 'A', 'I', 'R', 'Y' },
  { 'A', 'J', 'A', 'R' },
  { 'A', 'K', 'I', 'N' },
  { 'A', 'L', 'A', 'N' },
  { 'A', 'L', 'E', 'C' },
  { 'A', 'L', 'G', 'A' },
  { 'A', 'L', 'I', 'A' },
  { 'A', 'L', 'L', 'Y' },
  { 'A', 'L', 'M', 'A' },
  { 'A', 'L', 'O', 'E' },
  { 'A', 'L', 'S', 'O' },
  { 'A', 'L', 'T', 'O' },
  { 'A', 'L', 'U', 'M' },
  { 'A', 'L', 'V', 'A' },
  { 'A', 'M', 'E', 'N' },
  { 'A', 'M', 'E', 'S' },
  { 'A', 'M', 'I', 'D' },
  { 'A', 'M', 'M', 'O' },
  { 'A', 'M', 'O', 'K' },
  { 'A', 'M', 'O', 'S' },
  { 'A', 'M', 'R', 'A' },
  { 'A', 'N', 'D', 'Y' },
  { 'A', 'N', 'E', 'W' },
  { 'A', 'N', 'N', 'A' },
  { 'A', 'N', 'N', 'E' },
  { 'A', 'N', 'T', 'E' },
  { 'A', 'N', 'T', 'I' },
  { 'A', 'Q', 'U', 'A' },
  { 'A', 'R', 'A', 'B' },
  { 'A', 'R', 'C', 'H' },
  { 'A', 'R', 'E', 'A' },
  { 'A', 'R', 'G', 'O' },
  { 'A', 'R', 'I', 'D' },
  { 'A', 'R', 'M', 'Y' },
  { 'A', 'R', 'T', 'S' },
  { 'A', 'R', 'T', 'Y' },
  { 'A', 'S', 'I', 'A' },
  { 'A', 'S', 'K', 'S' },
  { 'A', 'T', 'O', 'M' },
  { 'A', 'U', 'N', 'T' },
  { 'A', 'U', 'R', 'A' },
  { 'A', 'U', 'T', 'O' },
  { 'A', 'V', 'E', 'R' },
  { 'A', 'V', 'I', 'D' },
  { 'A', 'V', 'I', 'S' },
  { 'A', 'V', 'O', 'N' },
  { 'A', 'V', 'O', 'W' },
  { 'A', 'W', 'A', 'Y' },
  { 'A', 'W', 'R', 'Y' },
  { 'B', 'A', 'B', 'E' },
  { 'B', 'A', 'B', 'Y' },
  { 'B', 'A', 'C', 'H' },
  { 'B', 'A', 'C', 'K' },
  { 'B', 'A', 'D', 'E' },
  { 'B', 'A', 'I', 'L' },
  { 'B', 'A', 'I', 'T' },
  { 'B', 'A', 'K', 'E' },
  { 'B', 'A', 'L', 'D' },
  { 'B', 'A', 'L', 'E' },
  { 'B', 'A', 'L', 'I' },
  { 'B', 'A', 'L', 'K' },
  { 'B', 'A', 'L', 'L' },
  { 'B', 'A', 'L', 'M' },
  { 'B', 'A', 'N', 'D' },
  { 'B', 'A', 'N', 'E' },
  { 'B', 'A', 'N', 'G' },
  { 'B', 'A', 'N', 'K' },
  { 'B', 'A', 'R', 'B' },
  { 'B', 'A', 'R', 'D' },
  { 'B', 'A', 'R', 'E' },
  { 'B', 'A', 'R', 'K' },
  { 'B', 'A', 'R', 'N' },
  { 'B', 'A', 'R', 'R' },
  { 'B', 'A', 'S', 'E' },
  { 'B', 'A', 'S', 'H' },
  { 'B', 'A', 'S', 'K' },
  { 'B', 'A', 'S', 'S' },
  { 'B', 'A', 'T', 'E' },
  { 'B', 'A', 'T', 'H' },
  { 'B', 'A', 'W', 'D' },
  { 'B', 'A', 'W', 'L' },
  { 'B', 'E', 'A', 'D' },
  { 'B', 'E', 'A', 'K' },
  { 'B', 'E', 'A', 'M' },
  { 'B', 'E', 'A', 'N' },
  { 'B', 'E', 'A', 'R' },
  { 'B', 'E', 'A', 'T' },
  { 'B', 'E', 'A', 'U' },
  { 'B', 'E', 'C', 'K' },
  { 'B', 'E', 'E', 'F' },
  { 'B', 'E', 'E', 'N' },
  { 'B', 'E', 'E', 'R' },
  { 'B', 'E', 'E', 'T' },
  { 'B', 'E', 'L', 'A' },
  { 'B', 'E', 'L', 'L' },
  { 'B', 'E', 'L', 'T' },
  { 'B', 'E', 'N', 'D' },
  { 'B', 'E', 'N', 'T' },
  { 'B', 'E', 'R', 'G' },
  { 'B', 'E', 'R', 'N' },
  { 'B', 'E', 'R', 'T' },
  { 'B', 'E', 'S', 'S' },
  { 'B', 'E', 'S', 'T' },
  { 'B', 'E', 'T', 'A' },
  { 'B', 'E', 'T', 'H' },
  { 'B', 'H', 'O', 'Y' },
  { 'B', 'I', 'A', 'S' },
  { 'B', 'I', 'D', 'E' },
  { 'B', 'I', 'E', 'N' },
  { 'B', 'I', 'L', 'E' },
  { 'B', 'I', 'L', 'K' },
  { 'B', 'I', 'L', 'L' },
  { 'B', 'I', 'N', 'D' },
  { 'B', 'I', 'N', 'G' },
  { 'B', 'I', 'R', 'D' },
  { 'B', 'I', 'T', 'E' },
  { 'B', 'I', 'T', 'S' },
  { 'B', 'L', 'A', 'B' },
  { 'B', 'L', 'A', 'T' },
  { 'B', 'L', 'E', 'D' },
  { 'B', 'L', 'E', 'W' },
  { 'B', 'L', 'O', 'B' },
  { 'B', 'L', 'O', 'C' },
  { 'B', 'L', 'O', 'T' },
  { 'B', 'L', 'O', 'W' },
  { 'B', 'L', 'U', 'E' },
  { 'B', 'L', 'U', 'M' },
  { 'B', 'L', 'U', 'R' },
  { 'B', 'O', 'A', 'R' },
  { 'B', 'O', 'A', 'T' },
  { 'B', 'O', 'C', 'A' },
  { 'B', 'O', 'C', 'K' },
  { 'B', 'O', 'D', 'E' },
  { 'B', 'O', 'D', 'Y' },
  { 'B', 'O', 'G', 'Y' },
  { 'B', 'O', 'H', 'R' },
  { 'B', 'O', 'I', 'L' },
  { 'B', 'O', 'L', 'D' },
  { 'B', 'O', 'L', 'O' },
  { 'B', 'O', 'L', 'T' },
  { 'B', 'O', 'M', 'B' },
  { 'B', 'O', 'N', 'A' },
  { 'B', 'O', 'N', 'D' },
  { 'B', 'O', 'N', 'E' },
  { 'B', 'O', 'N', 'G' },
  { 'B', 'O', 'N', 'N' },
  { 'B', 'O', 'N', 'Y' },
  { 'B', 'O', 'O', 'K' },
  { 'B', 'O', 'O', 'M' },
  { 'B', 'O', 'O', 'N' },
  { 'B', 'O', 'O', 'T' },
  { 'B', 'O', 'R', 'E' },
  { 'B', 'O', 'R', 'G' },
  { 'B', 'O', 'R', 'N' },
  { 'B', 'O', 'S', 'E' },
  { 'B', 'O', 'S', 'S' },
  { 'B', 'O', 'T', 'H' },
  { 'B', 'O', 'U', 'T' },
  { 'B', 'O', 'W', 'L' },
  { 'B', 'O', 'Y', 'D' },
  { 'B', 'R', 'A', 'D' },
  { 'B', 'R', 'A', 'E' },
  { 'B', 'R', 'A', 'G' },
  { 'B', 'R', 'A', 'N' },
  { 'B', 'R', 'A', 'Y' },
  { 'B', 'R', 'E', 'D' },
  { 'B', 'R', 'E', 'W' },
  { 'B', 'R', 'I', 'G' },
  { 'B', 'R', 'I', 'M' },
  { 'B', 'R', 'O', 'W' },
  { 'B', 'U', 'C', 'K' },
  { 'B', 'U', 'D', 'D' },
  { 'B', 'U', 'F', 'F' },
  { 'B', 'U', 'L', 'B' },
  { 'B', 'U', 'L', 'K' },
  { 'B', 'U', 'L', 'L' },
  { 'B', 'U', 'N', 'K' },
  { 'B', 'U', 'N', 'T' },
  { 'B', 'U', 'O', 'Y' },
  { 'B', 'U', 'R', 'G' },
  { 'B', 'U', 'R', 'L' },
  { 'B', 'U', 'R', 'N' },
  { 'B', 'U', 'R', 'R' },
  { 'B', 'U', 'R', 'T' },
  { 'B', 'U', 'R', 'Y' },
  { 'B', 'U', 'S', 'H' },
  { 'B', 'U', 'S', 'S' },
  { 'B', 'U', 'S', 'T' },
  { 'B', 'U', 'S', 'Y' },
  { 'B', 'Y', 'T', 'E' },
  { 'C', 'A', 'D', 'Y' },
  { 'C', 'A', 'F', 'E' },
  { 'C', 'A', 'G', 'E' },
  { 'C', 'A', 'I', 'N' },
  { 'C', 'A', 'K', 'E' },
  { 'C', 'A', 'L', 'F' },
  { 'C', 'A', 'L', 'L' },
  { 'C', 'A', 'L', 'M' },
  { 'C', 'A', 'M', 'E' },
  { 'C', 'A', 'N', 'E' },
  { 'C', 'A', 'N', 'T' },
  { 'C', 'A', 'R', 'D' },
  { 'C', 'A', 'R', 'E' },
  { 'C', 'A', 'R', 'L' },
  { 'C', 'A', 'R', 'R' },
  { 'C', 'A', 'R', 'T' },
  { 'C', 'A', 'S', 'E' },
  { 'C', 'A', 'S', 'H' },
  { 'C', 'A', 'S', 'K' },
  { 'C', 'A', 'S', 'T' },
  { 'C', 'A', 'V', 'E' },
  { 'C', 'E', 'I', 'L' },
  { 'C', 'E', 'L', 'L' },
  { 'C', 'E', 'N', 'T' },
  { 'C', 'E', 'R', 'N' },
  { 'C', 'H', 'A', 'D' },
  { 'C', 'H', 'A', 'R' },
  { 'C', 'H', 'A', 'T' },
  { 'C', 'H', 'A', 'W' },
  { 'C', 'H', 'E', 'F' },
  { 'C', 'H', 'E', 'N' },
  { 'C', 'H', 'E', 'W' },
  { 'C', 'H', 'I', 'C' },
  { 'C', 'H', 'I', 'N' },
  { 'C', 'H', 'O', 'U' },
  { 'C', 'H', 'O', 'W' },
  { 'C', 'H', 'U', 'B' },
  { 'C', 'H', 'U', 'G' },
  { 'C', 'H', 'U', 'M' },
  { 'C', 'I', 'T', 'E' },
  { 'C', 'I', 'T', 'Y' },
  { 'C', 'L', 'A', 'D' },
  { 'C', 'L', 'A', 'M' },
  { 'C', 'L', 'A', 'N' },
  { 'C', 'L', 'A', 'W' },
  { 'C', 'L', 'A', 'Y' },
  { 'C', 'L', 'O', 'D' },
  { 'C', 'L', 'O', 'G' },
  { 'C', 'L', 'O', 'T' },
  { 'C', 'L', 'U', 'B' },
  { 'C', 'L', 'U', 'E' },
  { 'C', 'O', 'A', 'L' },
  { 'C', 'O', 'A', 'T' },
  { 'C', 'O', 'C', 'A' },
  { 'C', 'O', 'C', 'K' },
  { 'C', 'O', 'C', 'O' },
  { 'C', 'O', 'D', 'A' },
  { 'C', 'O', 'D', 'E' },
  { 'C', 'O', 'D', 'Y' },
  { 'C', 'O', 'E', 'D' },
  { 'C', 'O', 'I', 'L' },
  { 'C', 'O', 'I', 'N' },
  { 'C', 'O', 'K', 'E' },
  { 'C', 'O', 'L', 'A' },
  { 'C', 'O', 'L', 'D' },
  { 'C', 'O', 'L', 'T' },
  { 'C', 'O', 'M', 'A' },
  { 'C', 'O', 'M', 'B' },
  { 'C', 'O', 'M', 'E' },
  { 'C', 'O', 'O', 'K' },
  { 'C', 'O', 'O', 'L' },
  { 'C', 'O', 'O', 'N' },
  { 'C', 'O', 'O', 'T' },
  { 'C', 'O', 'R', 'D' },
  { 'C', 'O', 'R', 'E' },
  { 'C', 'O', 'R', 'K' },
  { 'C', 'O', 'R', 'N' },
  { 'C', 'O', 'S', 'T' },
  { 'C', 'O', 'V', 'E' },
  { 'C', 'O', 'W', 'L' },
  { 'C', 'R', 'A', 'B' },
  { 'C', 'R', 'A', 'G' },
  { 'C', 'R', 'A', 'M' },
  { 'C', 'R', 'A', 'Y' },
  { 'C', 'R', 'E', 'W' },
  { 'C', 'R', 'I', 'B' },
  { 'C', 'R', 'O', 'W' },
  { 'C', 'R', 'U', 'D' },
  { 'C', 'U', 'B', 'A' },
  { 'C', 'U', 'B', 'E' },
  { 'C', 'U', 'F', 'F' },
  { 'C', 'U', 'L', 'L' },
  { 'C', 'U', 'L', 'T' },
  { 'C', 'U', 'N', 'Y' },
  { 'C', 'U', 'R', 'B' },
  { 'C', 'U', 'R', 'D' },
  { 'C', 'U', 'R', 'E' },
  { 'C', 'U', 'R', 'L' },
  { 'C', 'U', 'R', 'T' },
  { 'C', 'U', 'T', 'S' },
  { 'D', 'A', 'D', 'E' },
  { 'D', 'A', 'L', 'E' },
  { 'D', 'A', 'M', 'E' },
  { 'D', 'A', 'N', 'A' },
  { 'D', 'A', 'N', 'E' },
  { 'D', 'A', 'N', 'G' },
  { 'D', 'A', 'N', 'K' },
  { 'D', 'A', 'R', 'E' },
  { 'D', 'A', 'R', 'K' },
  { 'D', 'A', 'R', 'N' },
  { 'D', 'A', 'R', 'T' },
  { 'D', 'A', 'S', 'H' },
  { 'D', 'A', 'T', 'A' },
  { 'D', 'A', 'T', 'E' },
  { 'D', 'A', 'V', 'E' },
  { 'D', 'A', 'V', 'Y' },
  { 'D', 'A', 'W', 'N' },
  { 'D', 'A', 'Y', 'S' },
  { 'D', 'E', 'A', 'D' },
  { 'D', 'E', 'A', 'F' },
  { 'D', 'E', 'A', 'L' },
  { 'D', 'E', 'A', 'N' },
  { 'D', 'E', 'A', 'R' },
  { 'D', 'E', 'B', 'T' },
  { 'D', 'E', 'C', 'K' },
  { 'D', 'E', 'E', 'D' },
  { 'D', 'E', 'E', 'M' },
  { 'D', 'E', 'E', 'R' },
  { 'D', 'E', 'F', 'T' },
  { 'D', 'E', 'F', 'Y' },
  { 'D', 'E', 'L', 'L' },
  { 'D', 'E', 'N', 'T' },
  { 'D', 'E', 'N', 'Y' },
  { 'D', 'E', 'S', 'K' },
  { 'D', 'I', 'A', 'L' },
  { 'D', 'I', 'C', 'E' },
  { 'D', 'I', 'E', 'D' },
  { 'D', 'I', 'E', 'T' },
  { 'D', 'I', 'M', 'E' },
  { 'D', 'I', 'N', 'E' },
  { 'D', 'I', 'N', 'G' },
  { 'D', 'I', 'N', 'T' },
  { 'D', 'I', 'R', 'E' },
  { 'D', 'I', 'R', 'T' },
  { 'D', 'I', 'S', 'C' },
  { 'D', 'I', 'S', 'H' },
  { 'D', 'I', 'S', 'K' },
  { 'D', 'I', 'V', 'E' },
  { 'D', 'O', 'C', 'K' },
  { 'D', 'O', 'E', 'S' },
  { 'D', 'O', 'L', 'E' },
  { 'D', 'O', 'L', 'L' },
  { 'D', 'O', 'L', 'T' },
  { 'D', 'O', 'M', 'E' },
  { 'D', 'O', 'N', 'E' },
  { 'D', 'O', 'O', 'M' },
  { 'D', 'O', 'O', 'R' },
  { 'D', 'O', 'R', 'A' },
  { 'D', 'O', 'S', 'E' },
  { 'D', 'O', 'T', 'E' },
  { 'D', 'O', 'U', 'G' },
  { 'D', 'O', 'U', 'R' },
  { 'D', 'O', 'V', 'E' },
  { 'D', 'O', 'W', 'N' },
  { 'D', 'R', 'A', 'B' },
  { 'D', 'R', 'A', 'G' },
  { 'D', 'R', 'A', 'M' },
  { 'D', 'R', 'A', 'W' },
  { 'D', 'R', 'E', 'W' },
  { 'D', 'R', 'U', 'B' },
  { 'D', 'R', 'U', 'G' },
  { 'D', 'R', 'U', 'M' },
  { 'D', 'U', 'A', 'L' },
  { 'D', 'U', 'C', 'K' },
  { 'D', 'U', 'C', 'T' },
  { 'D', 'U', 'E', 'L' },
  { 'D', 'U', 'E', 'T' },
  { 'D', 'U', 'K', 'E' },
  { 'D', 'U', 'L', 'L' },
  { 'D', 'U', 'M', 'B' },
  { 'D', 'U', 'N', 'E' },
  { 'D', 'U', 'N', 'K' },
  { 'D', 'U', 'S', 'K' },
  { 'D', 'U', 'S', 'T' },
  { 'D', 'U', 'T', 'Y' },
  { 'E', 'A', 'C', 'H' },
  { 'E', 'A', 'R', 'L' },
  { 'E', 'A', 'R', 'N' },
  { 'E', 'A', 'S', 'E' },
  { 'E', 'A', 'S', 'T' },
  { 'E', 'A', 'S', 'Y' },
  { 'E', 'B', 'E', 'N' },
  { 'E', 'C', 'H', 'O' },
  { 'E', 'D', 'D', 'Y' },
  { 'E', 'D', 'E', 'N' },
  { 'E', 'D', 'G', 'E' },
  { 'E', 'D', 'G', 'Y' },
  { 'E', 'D', 'I', 'T' },
  { 'E', 'D', 'N', 'A' },
  { 'E', 'G', 'A', 'N' },
  { 'E', 'L', 'A', 'N' },
  { 'E', 'L', 'B', 'A' },
  { 'E', 'L', 'L', 'A' },
  { 'E', 'L', 'S', 'E' },
  { 'E', 'M', 'I', 'L' },
  { 'E', 'M', 'I', 'T' },
  { 'E', 'M', 'M', 'A' },
  { 'E', 'N', 'D', 'S' },
  { 'E', 'R', 'I', 'C' },
  { 'E', 'R', 'O', 'S' },
  { 'E', 'V', 'E', 'N' },
  { 'E', 'V', 'E', 'R' },
  { 'E', 'V', 'I', 'L' },
  { 'E', 'Y', 'E', 'D' },
  { 'F', 'A', 'C', 'E' },
  { 'F', 'A', 'C', 'T' },
  { 'F', 'A', 'D', 'E' },
  { 'F', 'A', 'I', 'L' },
  { 'F', 'A', 'I', 'N' },
  { 'F', 'A', 'I', 'R' },
  { 'F', 'A', 'K', 'E' },
  { 'F', 'A', 'L', 'L' },
  { 'F', 'A', 'M', 'E' },
  { 'F', 'A', 'N', 'G' },
  { 'F', 'A', 'R', 'M' },
  { 'F', 'A', 'S', 'T' },
  { 'F', 'A', 'T', 'E' },
  { 'F', 'A', 'W', 'N' },
  { 'F', 'E', 'A', 'R' },
  { 'F', 'E', 'A', 'T' },
  { 'F', 'E', 'E', 'D' },
  { 'F', 'E', 'E', 'L' },
  { 'F', 'E', 'E', 'T' },
  { 'F', 'E', 'L', 'L' },
  { 'F', 'E', 'L', 'T' },
  { 'F', 'E', 'N', 'D' },
  { 'F', 'E', 'R', 'N' },
  { 'F', 'E', 'S', 'T' },
  { 'F', 'E', 'U', 'D' },
  { 'F', 'I', 'E', 'F' },
  { 'F', 'I', 'G', 'S' },
  { 'F', 'I', 'L', 'E' },
  { 'F', 'I', 'L', 'L' },
  { 'F', 'I', 'L', 'M' },
  { 'F', 'I', 'N', 'D' },
  { 'F', 'I', 'N', 'E' },
  { 'F', 'I', 'N', 'K' },
  { 'F', 'I', 'R', 'E' },
  { 'F', 'I', 'R', 'M' },
  { 'F', 'I', 'S', 'H' },
  { 'F', 'I', 'S', 'K' },
  { 'F', 'I', 'S', 'T' },
  { 'F', 'I', 'T', 'S' },
  { 'F', 'I', 'V', 'E' },
  { 'F', 'L', 'A', 'G' },
  { 'F', 'L', 'A', 'K' },
  { 'F', 'L', 'A', 'M' },
  { 'F', 'L', 'A', 'T' },
  { 'F', 'L', 'A', 'W' },
  { 'F', 'L', 'E', 'A' },
  { 'F', 'L', 'E', 'D' },
  { 'F', 'L', 'E', 'W' },
  { 'F', 'L', 'I', 'T' },
  { 'F', 'L', 'O', 'C' },
  { 'F', 'L', 'O', 'G' },
  { 'F', 'L', 'O', 'W' },
  { 'F', 'L', 'U', 'B' },
  { 'F', 'L', 'U', 'E' },
  { 'F', 'O', 'A', 'L' },
  { 'F', 'O', 'A', 'M' },
  { 'F', 'O', 'G', 'Y' },
  { 'F', 'O', 'I', 'L' },
  { 'F', 'O', 'L', 'D' },
  { 'F', 'O', 'L', 'K' },
  { 'F', 'O', 'N', 'D' },
  { 'F', 'O', 'N', 'T' },
  { 'F', 'O', 'O', 'D' },
  { 'F', 'O', 'O', 'L' },
  { 'F', 'O', 'O', 'T' },
  { 'F', 'O', 'R', 'D' },
  { 'F', 'O', 'R', 'E' },
  { 'F', 'O', 'R', 'K' },
  { 'F', 'O', 'R', 'M' },
  { 'F', 'O', 'R', 'T' },
  { 'F', 'O', 'S', 'S' },
  { 'F', 'O', 'U', 'L' },
  { 'F', 'O', 'U', 'R' },
  { 'F', 'O', 'W', 'L' },
  { 'F', 'R', 'A', 'U' },
  { 'F', 'R', 'A', 'Y' },
  { 'F', 'R', 'E', 'D' },
  { 'F', 'R', 'E', 'E' },
  { 'F', 'R', 'E', 'T' },
  { 'F', 'R', 'E', 'Y' },
  { 'F', 'R', 'O', 'G' },
  { 'F', 'R', 'O', 'M' },
  { 'F', 'U', 'E', 'L' },
  { 'F', 'U', 'L', 'L' },
  { 'F', 'U', 'M', 'E' },
  { 'F', 'U', 'N', 'D' },
  { 'F', 'U', 'N', 'K' },
  { 'F', 'U', 'R', 'Y' },
  { 'F', 'U', 'S', 'E' },
  { 'F', 'U', 'S', 'S' },
  { 'G', 'A', 'F', 'F' },
  { 'G', 'A', 'G', 'E' },
  { 'G', 'A', 'I', 'L' },
  { 'G', 'A', 'I', 'N' },
  { 'G', 'A', 'I', 'T' },
  { 'G', 'A', 'L', 'A' },
  { 'G', 'A', 'L', 'E' },
  { 'G', 'A', 'L', 'L' },
  { 'G', 'A', 'L', 'T' },
  { 'G', 'A', 'M', 'E' },
  { 'G', 'A', 'N', 'G' },
  { 'G', 'A', 'R', 'B' },
  { 'G', 'A', 'R', 'Y' },
  { 'G', 'A', 'S', 'H' },
  { 'G', 'A', 'T', 'E' },
  { 'G', 'A', 'U', 'L' },
  { 'G', 'A', 'U', 'R' },
  { 'G', 'A', 'V', 'E' },
  { 'G', 'A', 'W', 'K' },
  { 'G', 'E', 'A', 'R' },
  { 'G', 'E', 'L', 'D' },
  { 'G', 'E', 'N', 'E' },
  { 'G', 'E', 'N', 'T' },
  { 'G', 'E', 'R', 'M' },
  { 'G', 'E', 'T', 'S' },
  { 'G', 'I', 'B', 'E' },
  { 'G', 'I', 'F', 'T' },
  { 'G', 'I', 'L', 'D' },
  { 'G', 'I', 'L', 'L' },
  { 'G', 'I', 'L', 'T' },
  { 'G', 'I', 'N', 'A' },
  { 'G', 'I', 'R', 'D' },
  { 'G', 'I', 'R', 'L' },
  { 'G', 'I', 'S', 'T' },
  { 'G', 'I', 'V', 'E' },
  { 'G', 'L', 'A', 'D' },
  { 'G', 'L', 'E', 'E' },
  { 'G', 'L', 'E', 'N' },
  { 'G', 'L', 'I', 'B' },
  { 'G', 'L', 'O', 'B' },
  { 'G', 'L', 'O', 'M' },
  { 'G', 'L', 'O', 'W' },
  { 'G', 'L', 'U', 'E' },
  { 'G', 'L', 'U', 'M' },
  { 'G', 'L', 'U', 'T' },
  { 'G', 'O', 'A', 'D' },
  { 'G', 'O', 'A', 'L' },
  { 'G', 'O', 'A', 'T' },
  { 'G', 'O', 'E', 'R' },
  { 'G', 'O', 'E', 'S' },
  { 'G', 'O', 'L', 'D' },
  { 'G', 'O', 'L', 'F' },
  { 'G', 'O', 'N', 'E' },
  { 'G', 'O', 'N', 'G' },
  { 'G', 'O', 'O', 'D' },
  { 'G', 'O', 'O', 'F' },
  { 'G', 'O', 'R', 'E' },
  { 'G', 'O', 'R', 'Y' },
  { 'G', 'O', 'S', 'H' },
  { 'G', 'O', 'U', 'T' },
  { 'G', 'O', 'W', 'N' },
  { 'G', 'R', 'A', 'B' },
  { 'G', 'R', 'A', 'D' },
  { 'G', 'R', 'A', 'Y' },
  { 'G', 'R', 'E', 'G' },
  { 'G', 'R', 'E', 'W' },
  { 'G', 'R', 'E', 'Y' },
  { 'G', 'R', 'I', 'D' },
  { 'G', 'R', 'I', 'M' },
  { 'G', 'R', 'I', 'N' },
  { 'G', 'R', 'I', 'T' },
  { 'G', 'R', 'O', 'W' },
  { 'G', 'R', 'U', 'B' },
  { 'G', 'U', 'L', 'F' },
  { 'G', 'U', 'L', 'L' },
  { 'G', 'U', 'N', 'K' },
  { 'G', 'U', 'R', 'U' },
  { 'G', 'U', 'S', 'H' },
  { 'G', 'U', 'S', 'T' },
  { 'G', 'W', 'E', 'N' },
  { 'G', 'W', 'Y', 'N' },
  { 'H', 'A', 'A', 'G' },
  { 'H', 'A', 'A', 'S' },
  { 'H', 'A', 'C', 'K' },
  { 'H', 'A', 'I', 'L' },
  { 'H', 'A', 'I', 'R' },
  { 'H', 'A', 'L', 'E' },
  { 'H', 'A', 'L', 'F' },
  { 'H', 'A', 'L', 'L' },
  { 'H', 'A', 'L', 'O' },
  { 'H', 'A', 'L', 'T' },
  { 'H', 'A', 'N', 'D' },
  { 'H', 'A', 'N', 'G' },
  { 'H', 'A', 'N', 'K' },
  { 'H', 'A', 'N', 'S' },
  { 'H', 'A', 'R', 'D' },
  { 'H', 'A', 'R', 'K' },
  { 'H', 'A', 'R', 'M' },
  { 'H', 'A', 'R', 'T' },
  { 'H', 'A', 'S', 'H' },
  { 'H', 'A', 'S', 'T' },
  { 'H', 'A', 'T', 'E' },
  { 'H', 'A', 'T', 'H' },
  { 'H', 'A', 'U', 'L' },
  { 'H', 'A', 'V', 'E' },
  { 'H', 'A', 'W', 'K' },
  { 'H', 'A', 'Y', 'S' },
  { 'H', 'E', 'A', 'D' },
  { 'H', 'E', 'A', 'L' },
  { 'H', 'E', 'A', 'R' },
  { 'H', 'E', 'A', 'T' },
  { 'H', 'E', 'B', 'E' },
  { 'H', 'E', 'C', 'K' },
  { 'H', 'E', 'E', 'D' },
  { 'H', 'E', 'E', 'L' },
  { 'H', 'E', 'F', 'T' },
  { 'H', 'E', 'L', 'D' },
  { 'H', 'E', 'L', 'L' },
  { 'H', 'E', 'L', 'M' },
  { 'H', 'E', 'R', 'B' },
  { 'H', 'E', 'R', 'D' },
  { 'H', 'E', 'R', 'E' },
  { 'H', 'E', 'R', 'O' },
  { 'H', 'E', 'R', 'S' },
  { 'H', 'E', 'S', 'S' },
  { 'H', 'E', 'W', 'N' },
  { 'H', 'I', 'C', 'K' },
  { 'H', 'I', 'D', 'E' },
  { 'H', 'I', 'G', 'H' },
  { 'H', 'I', 'K', 'E' },
  { 'H', 'I', 'L', 'L' },
  { 'H', 'I', 'L', 'T' },
  { 'H', 'I', 'N', 'D' },
  { 'H', 'I', 'N', 'T' },
  { 'H', 'I', 'R', 'E' },
  { 'H', 'I', 'S', 'S' },
  { 'H', 'I', 'V', 'E' },
  { 'H', 'O', 'B', 'O' },
  { 'H', 'O', 'C', 'K' },
  { 'H', 'O', 'F', 'F' },
  { 'H', 'O', 'L', 'D' },
  { 'H', 'O', 'L', 'E' },
  { 'H', 'O', 'L', 'M' },
  { 'H', 'O', 'L', 'T' },
  { 'H', 'O', 'M', 'E' },
  { 'H', 'O', 'N', 'E' },
  { 'H', 'O', 'N', 'K' },
  { 'H', 'O', 'O', 'D' },
  { 'H', 'O', 'O', 'F' },
  { 'H', 'O', 'O', 'K' },
  { 'H', 'O', 'O', 'T' },
  { 'H', 'O', 'R', 'N' },
  { 'H', 'O', 'S', 'E' },
  { 'H', 'O', 'S', 'T' },
  { 'H', 'O', 'U', 'R' },
  { 'H', 'O', 'V', 'E' },
  { 'H', 'O', 'W', 'E' },
  { 'H', 'O', 'W', 'L' },
  { 'H', 'O', 'Y', 'T' },
  { 'H', 'U', 'C', 'K' },
  { 'H', 'U', 'E', 'D' },
  { 'H', 'U', 'F', 'F' },
  { 'H', 'U', 'G', 'E' },
  { 'H', 'U', 'G', 'H' },
  { 'H', 'U', 'G', 'O' },
  { 'H', 'U', 'L', 'K' },
  { 'H', 'U', 'L', 'L' },
  { 'H', 'U', 'N', 'K' },
  { 'H', 'U', 'N', 'T' },
  { 'H', 'U', 'R', 'D' },
  { 'H', 'U', 'R', 'L' },
  { 'H', 'U', 'R', 'T' },
  { 'H', 'U', 'S', 'H' },
  { 'H', 'Y', 'D', 'E' },
  { 'H', 'Y', 'M', 'N' },
  { 'I', 'B', 'I', 'S' },
  { 'I', 'C', 'O', 'N' },
  { 'I', 'D', 'E', 'A' },
  { 'I', 'D', 'L', 'E' },
  { 'I', 'F', 'F', 'Y' },
  { 'I', 'N', 'C', 'A' },
  { 'I', 'N', 'C', 'H' },
  { 'I', 'N', 'T', 'O' },
  { 'I', 'O', 'N', 'S' },
  { 'I', 'O', 'T', 'A' },
  { 'I', 'O', 'W', 'A' },
  { 'I', 'R', 'I', 'S' },
  { 'I', 'R', 'M', 'A' },
  { 'I', 'R', 'O', 'N' },
  { 'I', 'S', 'L', 'E' },
  { 'I', 'T', 'C', 'H' },
  { 'I', 'T', 'E', 'M' },
  { 'I', 'V', 'A', 'N' },
  { 'J', 'A', 'C', 'K' },
  { 'J', 'A', 'D', 'E' },
  { 'J', 'A', 'I', 'L' },
  { 'J', 'A', 'K', 'E' },
  { 'J', 'A', 'N', 'E' },
  { 'J', 'A', 'V', 'A' },
  { 'J', 'E', 'A', 'N' },
  { 'J', 'E', 'F', 'F' },
  { 'J', 'E', 'R', 'K' },
  { 'J', 'E', 'S', 'S' },
  { 'J', 'E', 'S', 'T' },
  { 'J', 'I', 'B', 'E' },
  { 'J', 'I', 'L', 'L' },
  { 'J', 'I', 'L', 'T' },
  { 'J', 'I', 'V', 'E' },
  { 'J', 'O', 'A', 'N' },
  { 'J', 'O', 'B', 'S' },
  { 'J', 'O', 'C', 'K' },
  { 'J', 'O', 'E', 'L' },
  { 'J', 'O', 'E', 'Y' },
  { 'J', 'O', 'H', 'N' },
  { 'J', 'O', 'I', 'N' },
  { 'J', 'O', 'K', 'E' },
  { 'J', 'O', 'L', 'T' },
  { 'J', 'O', 'V', 'E' },
  { 'J', 'U', 'D', 'D' },
  { 'J', 'U', 'D', 'E' },
  { 'J', 'U', 'D', 'O' },
  { 'J', 'U', 'D', 'Y' },
  { 'J', 'U', 'J', 'U' },
  { 'J', 'U', 'K', 'E' },
  { 'J', 'U', 'L', 'Y' },
  { 'J', 'U', 'N', 'E' },
  { 'J', 'U', 'N', 'K' },
  { 'J', 'U', 'N', 'O' },
  { 'J', 'U', 'R', 'Y' },
  { 'J', 'U', 'S', 'T' },
  { 'J', 'U', 'T', 'E' },
  { 'K', 'A', 'H', 'N' },
  { 'K', 'A', 'L', 'E' },
  { 'K', 'A', 'N', 'E' },
  { 'K', 'A', 'N', 'T' },
  { 'K', 'A', 'R', 'L' },
  { 'K', 'A', 'T', 'E' },
  { 'K', 'E', 'E', 'L' },
  { 'K', 'E', 'E', 'N' },
  { 'K', 'E', 'N', 'O' },
  { 'K', 'E', 'N', 'T' },
  { 'K', 'E', 'R', 'N' },
  { 'K', 'E', 'R', 'R' },
  { 'K', 'E', 'Y', 'S' },
  { 'K', 'I', 'C', 'K' },
  { 'K', 'I', 'L', 'L' },
  { 'K', 'I', 'N', 'D' },
  { 'K', 'I', 'N', 'G' },
  { 'K', 'I', 'R', 'K' },
  { 'K', 'I', 'S', 'S' },
  { 'K', 'I', 'T', 'E' },
  { 'K', 'L', 'A', 'N' },
  { 'K', 'N', 'E', 'E' },
  { 'K', 'N', 'E', 'W' },
  { 'K', 'N', 'I', 'T' },
  { 'K', 'N', 'O', 'B' },
  { 'K', 'N', 'O', 'T' },
  { 'K', 'N', 'O', 'W' },
  { 'K', 'O', 'C', 'H' },
  { 'K', 'O', 'N', 'G' },
  { 'K', 'U', 'D', 'O' },
  { 'K', 'U', 'R', 'D' },
  { 'K', 'U', 'R', 'T' },
  { 'K', 'Y', 'L', 'E' },
  { 'L', 'A', 'C', 'E' },
  { 'L', 'A', 'C', 'K' },
  { 'L', 'A', 'C', 'Y' },
  { 'L', 'A', 'D', 'Y' },
  { 'L', 'A', 'I', 'D' },
  { 'L', 'A', 'I', 'N' },
  { 'L', 'A', 'I', 'R' },
  { 'L', 'A', 'K', 'E' },
  { 'L', 'A', 'M', 'B' },
  { 'L', 'A', 'M', 'E' },
  { 'L', 'A', 'N', 'D' },
  { 'L', 'A', 'N', 'E' },
  { 'L', 'A', 'N', 'G' },
  { 'L', 'A', 'R', 'D' },
  { 'L', 'A', 'R', 'K' },
  { 'L', 'A', 'S', 'S' },
  { 'L', 'A', 'S', 'T' },
  { 'L', 'A', 'T', 'E' },
  { 'L', 'A', 'U', 'D' },
  { 'L', 'A', 'V', 'A' },
  { 'L', 'A', 'W', 'N' },
  { 'L', 'A', 'W', 'S' },
  { 'L', 'A', 'Y', 'S' },
  { 'L', 'E', 'A', 'D' },
  { 'L', 'E', 'A', 'F' },
  { 'L', 'E', 'A', 'K' },
  { 'L', 'E', 'A', 'N' },
  { 'L', 'E', 'A', 'R' },
  { 'L', 'E', 'E', 'K' },
  { 'L', 'E', 'E', 'R' },
  { 'L', 'E', 'F', 'T' },
  { 'L', 'E', 'N', 'D' },
  { 'L', 'E', 'N', 'S' },
  { 'L', 'E', 'N', 'T' },
  { 'L', 'E', 'O', 'N' },
  { 'L', 'E', 'S', 'K' },
  { 'L', 'E', 'S', 'S' },
  { 'L', 'E', 'S', 'T' },
  { 'L', 'E', 'T', 'S' },
  { 'L', 'I', 'A', 'R' },
  { 'L', 'I', 'C', 'E' },
  { 'L', 'I', 'C', 'K' },
  { 'L', 'I', 'E', 'D' },
  { 'L', 'I', 'E', 'N' },
  { 'L', 'I', 'E', 'S' },
  { 'L', 'I', 'E', 'U' },
  { 'L', 'I', 'F', 'E' },
  { 'L', 'I', 'F', 'T' },
  { 'L', 'I', 'K', 'E' },
  { 'L', 'I', 'L', 'A' },
  { 'L', 'I', 'L', 'T' },
  { 'L', 'I', 'L', 'Y' },
  { 'L', 'I', 'M', 'A' },
  { 'L', 'I', 'M', 'B' },
  { 'L', 'I', 'M', 'E' },
  { 'L', 'I', 'N', 'D' },
  { 'L', 'I', 'N', 'E' },
  { 'L', 'I', 'N', 'K' },
  { 'L', 'I', 'N', 'T' },
  { 'L', 'I', 'O', 'N' },
  { 'L', 'I', 'S', 'A' },
  { 'L', 'I', 'S', 'T' },
  { 'L', 'I', 'V', 'E' },
  { 'L', 'O', 'A', 'D' },
  { 'L', 'O', 'A', 'F' },
  { 'L', 'O', 'A', 'M' },
  { 'L', 'O', 'A', 'N' },
  { 'L', 'O', 'C', 'K' },
  { 'L', 'O', 'F', 'T' },
  { 'L', 'O', 'G', 'E' },
  { 'L', 'O', 'I', 'S' },
  { 'L', 'O', 'L', 'A' },
  { 'L', 'O', 'N', 'E' },
  { 'L', 'O', 'N', 'G' },
  { 'L', 'O', 'O', 'K' },
  { 'L', 'O', 'O', 'N' },
  { 'L', 'O', 'O', 'T' },
  { 'L', 'O', 'R', 'D' },
  { 'L', 'O', 'R', 'E' },
  { 'L', 'O', 'S', 'E' },
  { 'L', 'O', 'S', 'S' },
  { 'L', 'O', 'S', 'T' },
  { 'L', 'O', 'U', 'D' },
  { 'L', 'O', 'V', 'E' },
  { 'L', 'O', 'W', 'E' },
  { 'L', 'U', 'C', 'K' },
  { 'L', 'U', 'C', 'Y' },
  { 'L', 'U', 'G', 'E' },
  { 'L', 'U', 'K', 'E' },
  { 'L', 'U', 'L', 'U' },
  { 'L', 'U', 'N', 'D' },
  { 'L', 'U', 'N', 'G' },
  { 'L', 'U', 'R', 'A' },
  { 'L', 'U', 'R', 'E' },
  { 'L', 'U', 'R', 'K' },
  { 'L', 'U', 'S', 'H' },
  { 'L', 'U', 'S', 'T' },
  { 'L', 'Y', 'L', 'E' },
  { 'L', 'Y', 'N', 'N' },
  { 'L', 'Y', 'O', 'N' },
  { 'L', 'Y', 'R', 'A' },
  { 'M', 'A', 'C', 'E' },
  { 'M', 'A', 'D', 'E' },
  { 'M', 'A', 'G', 'I' },
  { 'M', 'A', 'I', 'D' },
  { 'M', 'A', 'I', 'L' },
  { 'M', 'A', 'I', 'N' },
  { 'M', 'A', 'K', 'E' },
  { 'M', 'A', 'L', 'E' },
  { 'M', 'A', 'L', 'I' },
  { 'M', 'A', 'L', 'L' },
  { 'M', 'A', 'L', 'T' },
  { 'M', 'A', 'N', 'A' },
  { 'M', 'A', 'N', 'N' },
  { 'M', 'A', 'N', 'Y' },
  { 'M', 'A', 'R', 'C' },
  { 'M', 'A', 'R', 'E' },
  { 'M', 'A', 'R', 'K' },
  { 'M', 'A', 'R', 'S' },
  { 'M', 'A', 'R', 'T' },
  { 'M', 'A', 'R', 'Y' },
  { 'M', 'A', 'S', 'H' },
  { 'M', 'A', 'S', 'K' },
  { 'M', 'A', 'S', 'S' },
  { 'M', 'A', 'S', 'T' },
  { 'M', 'A', 'T', 'E' },
  { 'M', 'A', 'T', 'H' },
  { 'M', 'A', 'U', 'L' },
  { 'M', 'A', 'Y', 'O' },
  { 'M', 'E', 'A', 'D' },
  { 'M', 'E', 'A', 'L' },
  { 'M', 'E', 'A', 'N' },
  { 'M', 'E', 'A', 'T' },
  { 'M', 'E', 'E', 'K' },
  { 'M', 'E', 'E', 'T' },
  { 'M', 'E', 'L', 'D' },
  { 'M', 'E', 'L', 'T' },
  { 'M', 'E', 'M', 'O' },
  { 'M', 'E', 'N', 'D' },
  { 'M', 'E', 'N', 'U' },
  { 'M', 'E', 'R', 'T' },
  { 'M', 'E', 'S', 'H' },
  { 'M', 'E', 'S', 'S' },
  { 'M', 'I', 'C', 'E' },
  { 'M', 'I', 'K', 'E' },
  { 'M', 'I', 'L', 'D' },
  { 'M', 'I', 'L', 'E' },
  { 'M', 'I', 'L', 'K' },
  { 'M', 'I', 'L', 'L' },
  { 'M', 'I', 'L', 'T' },
  { 'M', 'I', 'M', 'I' },
  { 'M', 'I', 'N', 'D' },
  { 'M', 'I', 'N', 'E' },
  { 'M', 'I', 'N', 'I' },
  { 'M', 'I', 'N', 'K' },
  { 'M', 'I', 'N', 'T' },
  { 'M', 'I', 'R', 'E' },
  { 'M', 'I', 'S', 'S' },
  { 'M', 'I', 'S', 'T' },
  { 'M', 'I', 'T', 'E' },
  { 'M', 'I', 'T', 'T' },
  { 'M', 'O', 'A', 'N' },
  { 'M', 'O', 'A', 'T' },
  { 'M', 'O', 'C', 'K' },
  { 'M', 'O', 'D', 'E' },
  { 'M', 'O', 'L', 'D' },
  { 'M', 'O', 'L', 'E' },
  { 'M', 'O', 'L', 'L' },
  { 'M', 'O', 'L', 'T' },
  { 'M', 'O', 'N', 'A' },
  { 'M', 'O', 'N', 'K' },
  { 'M', 'O', 'N', 'T' },
  { 'M', 'O', 'O', 'D' },
  { 'M', 'O', 'O', 'N' },
  { 'M', 'O', 'O', 'R' },
  { 'M', 'O', 'O', 'T' },
  { 'M', 'O', 'R', 'E' },
  { 'M', 'O', 'R', 'N' },
  { 'M', 'O', 'R', 'T' },
  { 'M', 'O', 'S', 'S' },
  { 'M', 'O', 'S', 'T' },
  { 'M', 'O', 'T', 'H' },
  { 'M', 'O', 'V', 'E' },
  { 'M', 'U', 'C', 'H' },
  { 'M', 'U', 'C', 'K' },
  { 'M', 'U', 'D', 'D' },
  { 'M', 'U', 'F', 'F' },
  { 'M', 'U', 'L', 'E' },
  { 'M', 'U', 'L', 'L' },
  { 'M', 'U', 'R', 'K' },
  { 'M', 'U', 'S', 'H' },
  { 'M', 'U', 'S', 'T' },
  { 'M', 'U', 'T', 'E' },
  { 'M', 'U', 'T', 'T' },
  { 'M', 'Y', 'R', 'A' },
  { 'M', 'Y', 'T', 'H' },
  { 'N', 'A', 'G', 'Y' },
  { 'N', 'A', 'I', 'L' },
  { 'N', 'A', 'I', 'R' },
  { 'N', 'A', 'M', 'E' },
  { 'N', 'A', 'R', 'Y' },
  { 'N', 'A', 'S', 'H' },
  { 'N', 'A', 'V', 'E' },
  { 'N', 'A', 'V', 'Y' },
  { 'N', 'E', 'A', 'L' },
  { 'N', 'E', 'A', 'R' },
  { 'N', 'E', 'A', 'T' },
  { 'N', 'E', 'C', 'K' },
  { 'N', 'E', 'E', 'D' },
  { 'N', 'E', 'I', 'L' },
  { 'N', 'E', 'L', 'L' },
  { 'N', 'E', 'O', 'N' },
  { 'N', 'E', 'R', 'O' },
  { 'N', 'E', 'S', 'S' },
  { 'N', 'E', 'S', 'T' },
  { 'N', 'E', 'W', 'S' },
  { 'N', 'E', 'W', 'T' },
  { 'N', 'I', 'B', 'S' },
  { 'N', 'I', 'C', 'E' },
  { 'N', 'I', 'C', 'K' },
  { 'N', 'I', 'L', 'E' },
  { 'N', 'I', 'N', 'A' },
  { 'N', 'I', 'N', 'E' },
  { 'N', 'O', 'A', 'H' },
  { 'N', 'O', 'D', 'E' },
  { 'N', 'O', 'E', 'L' },
  { 'N', 'O', 'L', 'L' },
  { 'N', 'O', 'N', 'E' },
  { 'N', 'O', 'O', 'K' },
  { 'N', 'O', 'O', 'N' },
  { 'N', 'O', 'R', 'M' },
  { 'N', 'O', 'S', 'E' },
  { 'N', 'O', 'T', 'E' },
  { 'N', 'O', 'U', 'N' },
  { 'N', 'O', 'V', 'A' },
  { 'N', 'U', 'D', 'E' },
  { 'N', 'U', 'L', 'L' },
  { 'N', 'U', 'M', 'B' },
  { 'O', 'A', 'T', 'H' },
  { 'O', 'B', 'E', 'Y' },
  { 'O', 'B', 'O', 'E' },
  { 'O', 'D', 'I', 'N' },
  { 'O', 'H', 'I', 'O' },
  { 'O', 'I', 'L', 'Y' },
  { 'O', 'I', 'N', 'T' },
  { 'O', 'K', 'A', 'Y' },
  { 'O', 'L', 'A', 'F' },
  { 'O', 'L', 'D', 'Y' },
  { 'O', 'L', 'G', 'A' },
  { 'O', 'L', 'I', 'N' },
  { 'O', 'M', 'A', 'N' },
  { 'O', 'M', 'E', 'N' },
  { 'O', 'M', 'I', 'T' },
  { 'O', 'N', 'C', 'E' },
  { 'O', 'N', 'E', 'S' },
  { 'O', 'N', 'L', 'Y' },
  { 'O', 'N', 'T', 'O' },
  { 'O', 'N', 'U', 'S' },
  { 'O', 'R', 'A', 'L' },
  { 'O', 'R', 'G', 'Y' },
  { 'O', 'S', 'L', 'O' },
  { 'O', 'T', 'I', 'S' },
  { 'O', 'T', 'T', 'O' },
  { 'O', 'U', 'C', 'H' },
  { 'O', 'U', 'S', 'T' },
  { 'O', 'U', 'T', 'S' },
  { 'O', 'V', 'A', 'L' },
  { 'O', 'V', 'E', 'N' },
  { 'O', 'V', 'E', 'R' },
  { 'O', 'W', 'L', 'Y' },
  { 'O', 'W', 'N', 'S' },
  { 'Q', 'U', 'A', 'D' },
  { 'Q', 'U', 'I', 'T' },
  { 'Q', 'U', 'O', 'D' },
  { 'R', 'A', 'C', 'E' },
  { 'R', 'A', 'C', 'K' },
  { 'R', 'A', 'C', 'Y' },
  { 'R', 'A', 'F', 'T' },
  { 'R', 'A', 'G', 'E' },
  { 'R', 'A', 'I', 'D' },
  { 'R', 'A', 'I', 'L' },
  { 'R', 'A', 'I', 'N' },
  { 'R', 'A', 'K', 'E' },
  { 'R', 'A', 'N', 'K' },
  { 'R', 'A', 'N', 'T' },
  { 'R', 'A', 'R', 'E' },
  { 'R', 'A', 'S', 'H' },
  { 'R', 'A', 'T', 'E' },
  { 'R', 'A', 'V', 'E' },
  { 'R', 'A', 'Y', 'S' },
  { 'R', 'E', 'A', 'D' },
  { 'R', 'E', 'A', 'L' },
  { 'R', 'E', 'A', 'M' },
  { 'R', 'E', 'A', 'R' },
  { 'R', 'E', 'C', 'K' },
  { 'R', 'E', 'E', 'D' },
  { 'R', 'E', 'E', 'F' },
  { 'R', 'E', 'E', 'K' },
  { 'R', 'E', 'E', 'L' },
  { 'R', 'E', 'I', 'D' },
  { 'R', 'E', 'I', 'N' },
  { 'R', 'E', 'N', 'A' },
  { 'R', 'E', 'N', 'D' },
  { 'R', 'E', 'N', 'T' },
  { 'R', 'E', 'S', 'T' },
  { 'R', 'I', 'C', 'E' },
  { 'R', 'I', 'C', 'H' },
  { 'R', 'I', 'C', 'K' },
  { 'R', 'I', 'D', 'E' },
  { 'R', 'I', 'F', 'T' },
  { 'R', 'I', 'L', 'L' },
  { 'R', 'I', 'M', 'E' },
  { 'R', 'I', 'N', 'G' },
  { 'R', 'I', 'N', 'K' },
  { 'R', 'I', 'S', 'E' },
  { 'R', 'I', 'S', 'K' },
  { 'R', 'I', 'T', 'E' },
  { 'R', 'O', 'A', 'D' },
  { 'R', 'O', 'A', 'M' },
  { 'R', 'O', 'A', 'R' },
  { 'R', 'O', 'B', 'E' },
  { 'R', 'O', 'C', 'K' },
  { 'R', 'O', 'D', 'E' },
  { 'R', 'O', 'I', 'L' },
  { 'R', 'O', 'L', 'L' },
  { 'R', 'O', 'M', 'E' },
  { 'R', 'O', 'O', 'D' },
  { 'R', 'O', 'O', 'F' },
  { 'R', 'O', 'O', 'K' },
  { 'R', 'O', 'O', 'M' },
  { 'R', 'O', 'O', 'T' },
  { 'R', 'O', 'S', 'A' },
  { 'R', 'O', 'S', 'E' },
  { 'R', 'O', 'S', 'S' },
  { 'R', 'O', 'S', 'Y' },
  { 'R', 'O', 'T', 'H' },
  { 'R', 'O', 'U', 'T' },
  { 'R', 'O', 'V', 'E' },
  { 'R', 'O', 'W', 'E' },
  { 'R', 'O', 'W', 'S' },
  { 'R', 'U', 'B', 'E' },
  { 'R', 'U', 'B', 'Y' },
  { 'R', 'U', 'D', 'E' },
  { 'R', 'U', 'D', 'Y' },
  { 'R', 'U', 'I', 'N' },
  { 'R', 'U', 'L', 'E' },
  { 'R', 'U', 'N', 'G' },
  { 'R', 'U', 'N', 'S' },
  { 'R', 'U', 'N', 'T' },
  { 'R', 'U', 'S', 'E' },
  { 'R', 'U', 'S', 'H' },
  { 'R', 'U', 'S', 'K' },
  { 'R', 'U', 'S', 'S' },
  { 'R', 'U', 'S', 'T' },
  { 'R', 'U', 'T', 'H' },
  { 'S', 'A', 'C', 'K' },
  { 'S', 'A', 'F', 'E' },
  { 'S', 'A', 'G', 'E' },
  { 'S', 'A', 'I', 'D' },
  { 'S', 'A', 'I', 'L' },
  { 'S', 'A', 'L', 'E' },
  { 'S', 'A', 'L', 'K' },
  { 'S', 'A', 'L', 'T' },
  { 'S', 'A', 'M', 'E' },
  { 'S', 'A', 'N', 'D' },
  { 'S', 'A', 'N', 'E' },
  { 'S', 'A', 'N', 'G' },
  { 'S', 'A', 'N', 'K' },
  { 'S', 'A', 'R', 'A' },
  { 'S', 'A', 'U', 'L' },
  { 'S', 'A', 'V', 'E' },
  { 'S', 'A', 'Y', 'S' },
  { 'S', 'C', 'A', 'N' },
  { 'S', 'C', 'A', 'R' },
  { 'S', 'C', 'A', 'T' },
  { 'S', 'C', 'O', 'T' },
  { 'S', 'E', 'A', 'L' },
  { 'S', 'E', 'A', 'M' },
  { 'S', 'E', 'A', 'R' },
  { 'S', 'E', 'A', 'T' },
  { 'S', 'E', 'E', 'D' },
  { 'S', 'E', 'E', 'K' },
  { 'S', 'E', 'E', 'M' },
  { 'S', 'E', 'E', 'N' },
  { 'S', 'E', 'E', 'S' },
  { 'S', 'E', 'L', 'F' },
  { 'S', 'E', 'L', 'L' },
  { 'S', 'E', 'N', 'D' },
  { 'S', 'E', 'N', 'T' },
  { 'S', 'E', 'T', 'S' },
  { 'S', 'E', 'W', 'N' },
  { 'S', 'H', 'A', 'G' },
  { 'S', 'H', 'A', 'M' },
  { 'S', 'H', 'A', 'W' },
  { 'S', 'H', 'A', 'Y' },
  { 'S', 'H', 'E', 'D' },
  { 'S', 'H', 'I', 'M' },
  { 'S', 'H', 'I', 'N' },
  { 'S', 'H', 'O', 'D' },
  { 'S', 'H', 'O', 'E' },
  { 'S', 'H', 'O', 'T' },
  { 'S', 'H', 'O', 'W' },
  { 'S', 'H', 'U', 'N' },
  { 'S', 'H', 'U', 'T' },
  { 'S', 'I', 'C', 'K' },
  { 'S', 'I', 'D', 'E' },
  { 'S', 'I', 'F', 'T' },
  { 'S', 'I', 'G', 'H' },
  { 'S', 'I', 'G', 'N' },
  { 'S', 'I', 'L', 'K' },
  { 'S', 'I', 'L', 'L' },
  { 'S', 'I', 'L', 'O' },
  { 'S', 'I', 'L', 'T' },
  { 'S', 'I', 'N', 'E' },
  { 'S', 'I', 'N', 'G' },
  { 'S', 'I', 'N', 'K' },
  { 'S', 'I', 'R', 'E' },
  { 'S', 'I', 'T', 'E' },
  { 'S', 'I', 'T', 'S' },
  { 'S', 'I', 'T', 'U' },
  { 'S', 'K', 'A', 'T' },
  { 'S', 'K', 'E', 'W' },
  { 'S', 'K', 'I', 'D' },
  { 'S', 'K', 'I', 'M' },
  { 'S', 'K', 'I', 'N' },
  { 'S', 'K', 'I', 'T' },
  { 'S', 'L', 'A', 'B' },
  { 'S', 'L', 'A', 'M' },
  { 'S', 'L', 'A', 'T' },
  { 'S', 'L', 'A', 'Y' },
  { 'S', 'L', 'E', 'D' },
  { 'S', 'L', 'E', 'W' },
  { 'S', 'L', 'I', 'D' },
  { 'S', 'L', 'I', 'M' },
  { 'S', 'L', 'I', 'T' },
  { 'S', 'L', 'O', 'B' },
  { 'S', 'L', 'O', 'G' },
  { 'S', 'L', 'O', 'T' },
  { 'S', 'L', 'O', 'W' },
  { 'S', 'L', 'U', 'G' },
  { 'S', 'L', 'U', 'M' },
  { 'S', 'L', 'U', 'R' },
  { 'S', 'M', 'O', 'G' },
  { 'S', 'M', 'U', 'G' },
  { 'S', 'N', 'A', 'G' },
  { 'S', 'N', 'O', 'B' },
  { 'S', 'N', 'O', 'W' },
  { 'S', 'N', 'U', 'B' },
  { 'S', 'N', 'U', 'G' },
  { 'S', 'O', 'A', 'K' },
  { 'S', 'O', 'A', 'R' },
  { 'S', 'O', 'C', 'K' },
  { 'S', 'O', 'D', 'A' },
  { 'S', 'O', 'F', 'A' },
  { 'S', 'O', 'F', 'T' },
  { 'S', 'O', 'I', 'L' },
  { 'S', 'O', 'L', 'D' },
  { 'S', 'O', 'M', 'E' },
  { 'S', 'O', 'N', 'G' },
  { 'S', 'O', 'O', 'N' },
  { 'S', 'O', 'O', 'T' },
  { 'S', 'O', 'R', 'E' },
  { 'S', 'O', 'R', 'T' },
  { 'S', 'O', 'U', 'L' },
  { 'S', 'O', 'U', 'R' },
  { 'S', 'O', 'W', 'N' },
  { 'S', 'T', 'A', 'B' },
  { 'S', 'T', 'A', 'G' },
  { 'S', 'T', 'A', 'N' },
  { 'S', 'T', 'A', 'R' },
  { 'S', 'T', 'A', 'Y' },
  { 'S', 'T', 'E', 'M' },
  { 'S', 'T', 'E', 'W' },
  { 'S', 'T', 'I', 'R' },
  { 'S', 'T', 'O', 'W' },
  { 'S', 'T', 'U', 'B' },
  { 'S', 'T', 'U', 'N' },
  { 'S', 'U', 'C', 'H' },
  { 'S', 'U', 'D', 'S' },
  { 'S', 'U', 'I', 'T' },
  { 'S', 'U', 'L', 'K' },
  { 'S', 'U', 'M', 'S' },
  { 'S', 'U', 'N', 'G' },
  { 'S', 'U', 'N', 'K' },
  { 'S', 'U', 'R', 'E' },
  { 'S', 'U', 'R', 'F' },
  { 'S', 'W', 'A', 'B' },
  { 'S', 'W', 'A', 'G' },
  { 'S', 'W', 'A', 'M' },
  { 'S', 'W', 'A', 'N' },
  { 'S', 'W', 'A', 'T' },
  { 'S', 'W', 'A', 'Y' },
  { 'S', 'W', 'I', 'M' },
  { 'S', 'W', 'U', 'M' },
  { 'T', 'A', 'C', 'K' },
  { 'T', 'A', 'C', 'T' },
  { 'T', 'A', 'I', 'L' },
  { 'T', 'A', 'K', 'E' },
  { 'T', 'A', 'L', 'E' },
  { 'T', 'A', 'L', 'K' },
  { 'T', 'A', 'L', 'L' },
  { 'T', 'A', 'N', 'K' },
  { 'T', 'A', 'S', 'K' },
  { 'T', 'A', 'T', 'E' },
  { 'T', 'A', 'U', 'T' },
  { 'T', 'E', 'A', 'L' },
  { 'T', 'E', 'A', 'M' },
  { 'T', 'E', 'A', 'R' },
  { 'T', 'E', 'C', 'H' },
  { 'T', 'E', 'E', 'M' },
  { 'T', 'E', 'E', 'N' },
  { 'T', 'E', 'E', 'T' },
  { 'T', 'E', 'L', 'L' },
  { 'T', 'E', 'N', 'D' },
  { 'T', 'E', 'N', 'T' },
  { 'T', 'E', 'R', 'M' },
  { 'T', 'E', 'R', 'N' },
  { 'T', 'E', 'S', 'S' },
  { 'T', 'E', 'S', 'T' },
  { 'T', 'H', 'A', 'N' },
  { 'T', 'H', 'A', 'T' },
  { 'T', 'H', 'E', 'E' },
  { 'T', 'H', 'E', 'M' },
  { 'T', 'H', 'E', 'N' },
  { 'T', 'H', 'E', 'Y' },
  { 'T', 'H', 'I', 'N' },
  { 'T', 'H', 'I', 'S' },
  { 'T', 'H', 'U', 'D' },
  { 'T', 'H', 'U', 'G' },
  { 'T', 'I', 'C', 'K' },
  { 'T', 'I', 'D', 'E' },
  { 'T', 'I', 'D', 'Y' },
  { 'T', 'I', 'E', 'D' },
  { 'T', 'I', 'E', 'R' },
  { 'T', 'I', 'L', 'E' },
  { 'T', 'I', 'L', 'L' },
  { 'T', 'I', 'L', 'T' },
  { 'T', 'I', 'M', 'E' },
  { 'T', 'I', 'N', 'A' },
  { 'T', 'I', 'N', 'E' },
  { 'T', 'I', 'N', 'T' },
  { 'T', 'I', 'N', 'Y' },
  { 'T', 'I', 'R', 'E' },
  { 'T', 'O', 'A', 'D' },
  { 'T', 'O', 'G', 'O' },
  { 'T', 'O', 'I', 'L' },
  { 'T', 'O', 'L', 'D' },
  { 'T', 'O', 'L', 'L' },
  { 'T', 'O', 'N', 'E' },
  { 'T', 'O', 'N', 'G' },
  { 'T', 'O', 'N', 'Y' },
  { 'T', 'O', 'O', 'K' },
  { 'T', 'O', 'O', 'L' },
  { 'T', 'O', 'O', 'T' },
  { 'T', 'O', 'R', 'E' },
  { 'T', 'O', 'R', 'N' },
  { 'T', 'O', 'T', 'E' },
  { 'T', 'O', 'U', 'R' },
  { 'T', 'O', 'U', 'T' },
  { 'T', 'O', 'W', 'N' },
  { 'T', 'R', 'A', 'G' },
  { 'T', 'R', 'A', 'M' },
  { 'T', 'R', 'A', 'Y' },
  { 'T', 'R', 'E', 'E' },
  { 'T', 'R', 'E', 'K' },
  { 'T', 'R', 'I', 'G' },
  { 'T', 'R', 'I', 'M' },
  { 'T', 'R', 'I', 'O' },
  { 'T', 'R', 'O', 'D' },
  { 'T', 'R', 'O', 'T' },
  { 'T', 'R', 'O', 'Y' },
  { 'T', 'R', 'U', 'E' },
  { 'T', 'U', 'B', 'A' },
  { 'T', 'U', 'B', 'E' },
  { 'T', 'U', 'C', 'K' },
  { 'T', 'U', 'F', 'T' },
  { 'T', 'U', 'N', 'A' },
  { 'T', 'U', 'N', 'E' },
  { 'T', 'U', 'N', 'G' },
  { 'T', 'U', 'R', 'F' },
  { 'T', 'U', 'R', 'N' },
  { 'T', 'U', 'S', 'K' },
  { 'T', 'W', 'I', 'G' },
  { 'T', 'W', 'I', 'N' },
  { 'T', 'W', 'I', 'T' },
  { 'U', 'L', 'A', 'N' },
  { 'U', 'N', 'I', 'T' },
  { 'U', 'R', 'G', 'E' },
  { 'U', 'S', 'E', 'D' },
  { 'U', 'S', 'E', 'R' },
  { 'U', 'S', 'E', 'S' },
  { 'U', 'T', 'A', 'H' },
  { 'V', 'A', 'I', 'L' },
  { 'V', 'A', 'I', 'N' },
  { 'V', 'A', 'L', 'E' },
  { 'V', 'A', 'R', 'Y' },
  { 'V', 'A', 'S', 'E' },
  { 'V', 'A', 'S', 'T' },
  { 'V', 'E', 'A', 'L' },
  { 'V', 'E', 'D', 'A' },
  { 'V', 'E', 'I', 'L' },
  { 'V', 'E', 'I', 'N' },
  { 'V', 'E', 'N', 'D' },
  { 'V', 'E', 'N', 'T' },
  { 'V', 'E', 'R', 'B' },
  { 'V', 'E', 'R', 'Y' },
  { 'V', 'E', 'T', 'O' },
  { 'V', 'I', 'C', 'E' },
  { 'V', 'I', 'E', 'W' },
  { 'V', 'I', 'N', 'E' },
  { 'V', 'I', 'S', 'E' },
  { 'V', 'O', 'I', 'D' },
  { 'V', 'O', 'L', 'T' },
  { 'V', 'O', 'T', 'E' },
  { 'W', 'A', 'C', 'K' },
  { 'W', 'A', 'D', 'E' },
  { 'W', 'A', 'G', 'E' },
  { 'W', 'A', 'I', 'L' },
  { 'W', 'A', 'I', 'T' },
  { 'W', 'A', 'K', 'E' },
  { 'W', 'A', 'L', 'E' },
  { 'W', 'A', 'L', 'K' },
  { 'W', 'A', 'L', 'L' },
  { 'W', 'A', 'L', 'T' },
  { 'W', 'A', 'N', 'D' },
  { 'W', 'A', 'N', 'E' },
  { 'W', 'A', 'N', 'G' },
  { 'W', 'A', 'N', 'T' },
  { 'W', 'A', 'R', 'D' },
  { 'W', 'A', 'R', 'M' },
  { 'W', 'A', 'R', 'N' },
  { 'W', 'A', 'R', 'T' },
  { 'W', 'A', 'S', 'H' },
  { 'W', 'A', 'S', 'T' },
  { 'W', 'A', 'T', 'S' },
  { 'W', 'A', 'T', 'T' },
  { 'W', 'A', 'V', 'E' },
  { 'W', 'A', 'V', 'Y' },
  { 'W', 'A', 'Y', 'S' },
  { 'W', 'E', 'A', 'K' },
  { 'W', 'E', 'A', 'L' },
  { 'W', 'E', 'A', 'N' },
  { 'W', 'E', 'A', 'R' },
  { 'W', 'E', 'E', 'D' },
  { 'W', 'E', 'E', 'K' },
  { 'W', 'E', 'I', 'R' },
  { 'W', 'E', 'L', 'D' },
  { 'W', 'E', 'L', 'L' },
  { 'W', 'E', 'L', 'T' },
  { 'W', 'E', 'N', 'T' },
  { 'W', 'E', 'R', 'E' },
  { 'W', 'E', 'R', 'T' },
  { 'W', 'E', 'S', 'T' },
  { 'W', 'H', 'A', 'M' },
  { 'W', 'H', 'A', 'T' },
  { 'W', 'H', 'E', 'E' },
  { 'W', 'H', 'E', 'N' },
  { 'W', 'H', 'E', 'T' },
  { 'W', 'H', 'O', 'A' },
  { 'W', 'H', 'O', 'M' },
  { 'W', 'I', 'C', 'K' },
  { 'W', 'I', 'F', 'E' },
  { 'W', 'I', 'L', 'D' },
  { 'W', 'I', 'L', 'L' },
  { 'W', 'I', 'N', 'D' },
  { 'W', 'I', 'N', 'E' },
  { 'W', 'I', 'N', 'G' },
  { 'W', 'I', 'N', 'K' },
  { 'W', 'I', 'N', 'O' },
  { 'W', 'I', 'R', 'E' },
  { 'W', 'I', 'S', 'E' },
  { 'W', 'I', 'S', 'H' },
  { 'W', 'I', 'T', 'H' },
  { 'W', 'O', 'L', 'F' },
  { 'W', 'O', 'N', 'T' },
  { 'W', 'O', 'O', 'D' },
  { 'W', 'O', 'O', 'L' },
  { 'W', 'O', 'R', 'D' },
  { 'W', 'O', 'R', 'E' },
  { 'W', 'O', 'R', 'K' },
  { 'W', 'O', 'R', 'M' },
  { 'W', 'O', 'R', 'N' },
  { 'W', 'O', 'V', 'E' },
  { 'W', 'R', 'I', 'T' },
  { 'W', 'Y', 'N', 'N' },
  { 'Y', 'A', 'L', 'E' },
  { 'Y', 'A', 'N', 'G' },
  { 'Y', 'A', 'N', 'K' },
  { 'Y', 'A', 'R', 'D' },
  { 'Y', 'A', 'R', 'N' },
  { 'Y', 'A', 'W', 'L' },
  { 'Y', 'A', 'W', 'N' },
  { 'Y', 'E', 'A', 'H' },
  { 'Y', 'E', 'A', 'R' },
  { 'Y', 'E', 'L', 'L' },
  { 'Y', 'O', 'G', 'A' },
  { 'Y', 'O', 'K', 'E' }
};

/* Extract LENGTH bits from the char array S starting with bit number
   START.  It always reads three consecutive octects, which means it
   can read past end of data when START is at the edge of the region. */

static uint32_t
extract (const unsigned char *s, int start, int length)
{
  unsigned char cl = s[start / 8];
  unsigned char cc = s[start / 8 + 1];
  unsigned char cr = s[start / 8 + 2];
  uint32_t x;
  x   = (uint32_t)(cl << 8 | cc) << 8 | cr;
  x >>= 24 - (length + (start % 8));
  x  &= (0xffff >> (16 - length));
  return x;
}

/* Length of a string known to be at least 1 and at most 4 chars
   long.  */

#define STRLEN_1_4(s) (!(s)[1] ? 1 : !(s)[2] ? 2 : !(s)[3] ? 3 : 4)

/* Encode 8 bytes in C as a string of English words and store them to
   STORE.  Returns STORE.  */

static char *
btoe (char *store, const unsigned char *c)
{
  unsigned char cp[10];         /* add in room for the parity 2 bits +
                                   extract() slop.  */
  int p, i;
  char *store_beg = store;

  *store = '\0';

  /* Workaround for extract() reads beyond end of data */
  memset (cp, 0, sizeof(cp));
  memcpy (cp, c, 8);

  /* Compute parity and append it to CP.  */
  for (p = 0, i = 0; i < 64; i += 2)
    p += extract (cp, i, 2);
  cp[8] = (char)p << 6;

  /* The 64 bits of input and the two parity bits comprise 66 bits of
     data that are now in CP.  We convert that information, 11 bits at
     a time, to English words indexed from Wp.  Since there are 2048
     (2^11) words in Wp, every 11-bit combination corresponds to a
     distinct word.  */
  memcpy (store, &Wp[extract (cp,  0, 11)][0], 4);
  store += STRLEN_1_4 (store);
  *store++ = ' ';
  memcpy (store, &Wp[extract (cp, 11, 11)][0], 4);
  store += STRLEN_1_4 (store);
  *store++ = ' ';
  memcpy (store, &Wp[extract (cp, 22, 11)][0], 4);
  store += STRLEN_1_4 (store);
  *store++ = ' ';
  memcpy (store, &Wp[extract (cp, 33, 11)][0], 4);
  store += STRLEN_1_4 (store);
  *store++ = ' ';
  memcpy (store, &Wp[extract (cp, 44, 11)][0], 4);
  store += STRLEN_1_4 (store);
  *store++ = ' ';
  memcpy (store, &Wp[extract (cp, 55, 11)][0], 4);
  store[4] = '\0';              /* make sure the string is terminated */

/*  DEBUGP (("wrote %s to STORE\n", quote (store_beg)));*/
  return store_beg;
}

/* Calculate the SKEY response, based on the sequence, seed
   (challenge), and the secret password.  The calculated response is
   used instead of the real password when logging in to SKEY-enabled
   servers.

   The result is calculated like this:

   + Concatenate SEED and PASS and calculate the 16-byte MD5 checksum.

   + Shorten the checksum to eight bytes by folding the second eight
     bytes onto the first eight using XOR.  The resulting eight-byte
     sequence is the key.

   + MD5-process the key, fold the checksum to eight bytes and store
     it back to the key.  Repeat this crunching SEQUENCE times.
     (Sequence is a number that gets decremented every time the user
     logs in to the server.  Therefore an eavesdropper would have to
     invert the hash function in order to guess the next one-time
     password.)

   + Convert the resulting 64-bit key to 6 English words separated by
     spaces (see btoe for details) and return the resulting ASCII
     string.

   All this is described in section 6 of rfc2289 in more detail.  */

const char *
calculate_skey_response (int sequence, const char *seed, const char *pass)
{
  unsigned char key[8];

  /* Room to hold 6 four-letter words (heh), 5 space separators, and
     the terminating \0.  24+5+1 == 30  */
  static char english[30];

  struct md5_ctx ctx;
  uint32_t checksum[4];

  md5_init_ctx (&ctx);
  md5_process_bytes ((const unsigned char *) seed, strlen (seed), &ctx);
  md5_process_bytes ((const unsigned char *) pass, strlen (pass), &ctx);
  md5_finish_ctx (&ctx, (unsigned char *) checksum);
  checksum[0] ^= checksum[2];
  checksum[1] ^= checksum[3];
  memcpy (key, checksum, 8);

  while (sequence-- > 0)
    {
      md5_init_ctx (&ctx);
      md5_process_bytes ((unsigned char *) key, 8, &ctx);
      md5_finish_ctx (&ctx, (unsigned char *) checksum);
      checksum[0] ^= checksum[2];
      checksum[1] ^= checksum[3];
      memcpy (key, checksum, 8);
    }
  return btoe (english, key);
}
