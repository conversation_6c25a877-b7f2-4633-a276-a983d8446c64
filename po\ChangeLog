2015-09-15	<PERSON> <<EMAIL>>

	* zh_TW.po: update.

2013-05-29	<PERSON><PERSON> <<EMAIL>>

	* cs.po: update.

2013-02-20	<PERSON> <<EMAIL>>

	* ru.po: update.

2013-01-29	<PERSON> <<EMAIL>>

	* ru.po: update.

2012-10-08	<PERSON><PERSON><PERSON> <<EMAIL>>

	* pl.po: update.

2012-10-03	<PERSON><PERSON> <<EMAIL>>

	* cs.po: update.

2012-01-26	<PERSON><PERSON> <<EMAIL>>

	* cs.po: update.

2011-09-22	<PERSON><PERSON> <<EMAIL>>

	* cs.po: update.

2011-09-19	<PERSON><PERSON><PERSON> <<EMAIL>>

	* pl.po: update.

2011-04-11	<PERSON> <<EMAIL>>

	* ru.po: update.

2011-04-11	<PERSON><PERSON> <<EMAIL>>

	* cs.po: update.

2010-11-26	<PERSON><PERSON> <<EMAIL>>

	* cs.po: update.

2010-08-31	<PERSON> V. Lukyanov <<EMAIL>>

	* ru.po: update.

2010-08-17	Noèl Köthe <<EMAIL>>

	* de.po: add missed comma; fix some minor translation.

2010-06-23	Petr Pisar <<EMAIL>>

	* cs.po: update.

2008-05-12	Alexander V. Lukyanov <<EMAIL>>

	* ru.po: update.

2008-05-12	Alexander V. Lukyanov <<EMAIL>>

	* de.po: fixed encoding (reported by Gerfried Fuchs).

2008-05-07	Jakub Bogusz <<EMAIL>>

	* pl.po: update.

2008-04-18	Alain PORTAL <<EMAIL>>

	* fr.po: update.

2008-03-31	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update.

2008-03-07	Alexander V. Lukyanov <<EMAIL>>

	* ru.po: update.

2008-02-05	Abel Cheung <<EMAIL>>

	* zh_HK.po: new translation.
	* zh_TW.po: update.

2007-11-01	Jakub Bogusz <<EMAIL>>

	* pl.po: update.

2007-10-24	Alain PORTAL <<EMAIL>>

	* fr.po: update.

2007-10-15	Alexander V. Lukyanov <<EMAIL>>

	* ru.po: update.

2007-08-01	Alexander V. Lukyanov <<EMAIL>>

	* ru.po: fixed mput description (reported by <<EMAIL>>).

2007-07-02	Alain PORTAL <<EMAIL>>

	* fr.po: update.

2007-04-11	Alexander V. Lukyanov <<EMAIL>>

        * ru.po: update.

2007-01-29	Jakub Bogusz <<EMAIL>>

	* pl.po: update.

2006-08-27	Jakub Bogusz <<EMAIL>>

	* pl.po: update.

2005-11-08	Nicolas Noble <<EMAIL>>

	* fr.po: update.

2005-09-05	Jakub Bogusz <<EMAIL>>

	* pl.po: update.

2005-08-04	Alexander V. Lukyanov <<EMAIL>>

	* ru.po: update.

2005-08-04	Abel Cheung <<EMAIL>>

	* zh_TW.po: update.

2005-06-27	Alexander V. Lukyanov <<EMAIL>>

	* ru.po: update.

2005-06-27	Wei-Lun Chao <<EMAIL>>

	* zh_TW.po: update.

2005-05-23	Jakub Bogusz <<EMAIL>>

	* pl.po: update.

2005-04-16	Jakub Bogusz <<EMAIL>>

	* pl.po: update.
	
2004-12-06	Jakub Bogusz <<EMAIL>>

	* pl.po: update.

2004-11-26	Jakub Bogusz <<EMAIL>>

	* pl.po: update.

2004-06-02	Jakub Bogusz <<EMAIL>>

	* pl.po: a fix.

2004-05-26	Jakub Bogusz <<EMAIL>>

	* pl.po: update.

2004-05-21	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update.

2004-04-08	Jakub Bogusz <<EMAIL>>

	* pl.po: update.

2004-03-29	Arkadiusz Miskiewicz <<EMAIL>>

	* pl.po: update.

2003-12-19	Alexander V. Lukyanov <<EMAIL>>

	* ru.po: update.

2003-12-18	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update.

2003-11-21	Jakub Bogusz <<EMAIL>>

	* pl.po: update.

2003-10-10	Alexander V. Lukyanov <<EMAIL>>

	* ru.po: update.

2003-06-16	Jakub Bogusz <<EMAIL>>

	* pl.po: update.

2003-06-09	Nicolas Noble <<EMAIL>>

	* fr.po: update.

2003-03-03	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update.

2003-02-20	Nicolas Noble <<EMAIL>>

	* fr.po: update.

2003-02-06	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update.

2002-09-16	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update.

2002-08-09	Eduardo <<EMAIL>>

	* es.po: update.

2002-07-09	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update.
	
2002-05-17	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update.

2002-05-14  gettextize  <<EMAIL>>

	* Makefile.in.in: Upgrade to gettext-0.11.2.
	* Rules-quot: New file, from gettext-0.11.2.
	* boldquot.sed: New file, from gettext-0.11.2.
	* <EMAIL>: New file, from gettext-0.11.2.
	* <EMAIL>: New file, from gettext-0.11.2.
	* insert-header.sin: New file, from gettext-0.11.2.
	* quot.sed: New file, from gettext-0.11.2.
	* remove-potcdate.sin: New file, from gettext-0.11.2.

2002-04-12	Jun Nakajima <<EMAIL>>

	* ja.po: don't translate ETA time abbreviations because of two-byte
	  character encoding.

2002-03-13	Arkadiusz Miskiewicz <<EMAIL>>

	* pl.po: update

2002-03-04	Nicolas Noble <<EMAIL>>

	* fr.po: update.

2002-03-03	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update.

2001-10-19	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update.

2001-10-19	Nicolas Noble <<EMAIL>>

	* fr.po: update.

2001-10-02	Masayuki Hatta <<EMAIL>>

	* ja.po: update.

2001-10-01	Nicolas Noble <<EMAIL>>

	* fr.po: update.

2001-08-06	Nicolas Noble <<EMAIL>>

	* fr.po: update.

2001-07-31	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update.

2001-07-27	Martin Michlmayr <<EMAIL>>

	* fr.po: use libre instead of gratuit.

2001-07-06	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update.

2001-06-22  gettextize  <<EMAIL>>

	* Makefile.in.in: Upgrade to gettext-0.10.38.

2001-06-01	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update.

2001-05-29	Nicolas Noble <<EMAIL>>

	* fr.po: update.

2001-05-28	Dojip Kim <<EMAIL>>

	* ko.po: update.

2001-05-22	Nicolas Noble <<EMAIL>>

	* fr.po: update.

2001-04-24	R.I.P. Deaddog <<EMAIL>>

	* zh_TW.po: new file.

2001-04-04	Nicolas Noble <<EMAIL>>

	* fr.po: update

2001-04-03	Alexander V. Lukyanov <<EMAIL>>

	* ru.po: fix plural rule for russian. What a shame...

2001-03-02	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update

2001-02-16	Albert Chin-A-Young <<EMAIL>>

	* Makefile.in.in: Add --with-locale-dir=DIR and 
	  --with-gnu-locale-dir=DIR for more precise placement of GNU locale
	  files.

2001-01-04	Nicolas Noble <<EMAIL>>

	* fr.po: update

2000-11-23	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update

2000-10-31	Arkadiusz Miskiewicz <<EMAIL>>

	* pl.po: update

2000-10-02	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update

2000-09-30	Masayuki Hatta <<EMAIL>>

	* ja.po: new file.

2000-09-25	Nicolas Lichtmaier <<EMAIL>>

	* es.po: update.

2000-09-22	<EMAIL>

	* fr.po: update

2000-09-07	Alexander V. Lukyanov <<EMAIL>>

	* ru.po: update.

2000-08-27	Nicolas Noble <<EMAIL>>

	* fr.po: new file.

2000-08-26	Alexander V. Lukyanov <<EMAIL>>

	* POTFILES.in: add Speedometer.cc (reported by Nicolas Noble)

2000-08-25	Nicolás Lichtmaier <<EMAIL>>

	* es.po: update.

2000-08-22	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update

2000-08-06	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update

2000-07-17	Nicolás Lichtmaier <<EMAIL>>

	* es.po: update.

2000-06-29	Manoj Kasichainula <<EMAIL>>

	* Makefile.in.in: use DESTDIR.

2000-05-24	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update

2000-04-27	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update.

2000-04-24	Giovanni Bortolozzo <<EMAIL>>

	* it.po: update.

2000-03-22	Nicolas Lichtmaier <<EMAIL>>

	* es.po: update.

2000-02-25      Giovanni Bortolozzo <<EMAIL>>

        * it.po: update.

2000-02-15	Alexander V. Lukyanov <<EMAIL>>

	* ru.po: some updates.

2000-01-02	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update.

1999-10-25	Giovanni Bortolozzo <<EMAIL>>

	* it.po: update for 2.1.4

1999-10-21	Dojip Kim <<EMAIL>>

	* ko.po: update

1999-10-20	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update

1999-10-11	Nicolas Lichtmaier <<EMAIL>>

	* es.po: update

1999-10-05	Dojip Kim <<EMAIL>>

	* ko.po: new file.

1999-09-30	Const Kaplinsky <<EMAIL>>

	* ru.po: update

1999-09-12	Wang Jian <<EMAIL>>

	* zh_CN.po: new file.

1999-08-24	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update

1999-08-15	Arkadiusz Miskiewicz <<EMAIL>>

	* pl.po: update

1999-07-25	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update

1999-07-15	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update for 2.0.2

1999-07-12	Alexander V. Lukyanov <<EMAIL>>

	* de.po, es.po, it.po, pl.po, pt_BR.po, ru.po: remove \n in Done
	  message.

1999-07-11	Giovanni Bortolozzo <<EMAIL>>

	* it.po: update for 2.0.1

1999-07-01	Const Kaplinsky <<EMAIL>>

	* ru.po: update for 2.0.1

1999-06-27	Arkadiusz Miśkiewicz <<EMAIL>>

	* pl.po: update

1999-06-25	Alexander V. Lukyanov <<EMAIL>>

	* de.po, es.po, pl.po, ru.po: translate "eta:"

1999-06-25	Arkadiusz Miśkiewicz <<EMAIL>>

	* pl.po: update

1999-06-11	Alexander V. Lukyanov <<EMAIL>>

	* it.po, pt_BR.po: few obvious to me corrections.

1999-06-11	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update

1999-06-09	Nicolas Lichtmaier <<EMAIL>>

	* es.po: update

1999-06-04	Alexander V. Lukyanov <<EMAIL>>

	* ru.po: few corrections.

1999-06-04	Const Kaplinsky <<EMAIL>>

	* ru.po: better translation.

1999-06-03	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update for 990602

1999-06-02	Alexander V. Lukyanov <<EMAIL>>

	* ru.po: update for 990602 snapshot

1999-05-26	Alexander V. Lukyanov <<EMAIL>>

	* ru.po: update for lftp-990526
	* POTFILES.in: add ChmodJob.cc, TreatFileJob.cc; remove mrmJob.cc.

1999-05-20	Alexander V. Lukyanov <<EMAIL>>

	* POTFILES.in: add HttpDir.cc

1999-05-17	Alexander V. Lukyanov <<EMAIL>>

	* ru.po: update for lftp-990517
	* POTFILES.in: add FtpGlob.cc

1999-04-30	Alexander V. Lukyanov <<EMAIL>>

	* ru.po: update for lftp-990430

1999-04-16	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update for lftp-990415

1999-04-15	Alexander V. Lukyanov <<EMAIL>>

	* ru.po: update for lftp-990415

1999-04-15	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: update

1999-02-19	Arkadiusz Miśkiewicz <<EMAIL>>

	* pl.po: updated for lftp-990204

1999-01-29	Moritz Moeller-Herrmann <<EMAIL>>

	* de.po: new file

1999-01-03	Alexander V. Lukyanov <<EMAIL>>

	* es.po, it.po, pl.po, ru.po: updated copyright translation for
	  1999 year

1998-12-13	Giovanni Bortolozzo <<EMAIL>>

	* it.po: update for 1.2

1998-12-10	Arkadiusz Miśkiewicz <<EMAIL>>

	* pl.po: update

1998-11-27	Alexander V. Lukyanov <<EMAIL>>

	* ru.po: update

1998-10-27	Giovanni Bortolozzo <<EMAIL>>

	* it.po: update

1998-09-27	Nicolás Lichtmaier <<EMAIL>>

	* es.po: update

1998-09-14	Arkadiusz Miśkiewicz <<EMAIL>>

	* pl.po: fixed typo (',' -> '|')

1998-09-08	Alexander V. Lukyanov <<EMAIL>>

	* pl.po: added #l# for some strings, add %s for eta

1998-09-08	Arkadiusz Miśkiewicz <<EMAIL>>

	* new pl.po

1998-09-04	Alexander V. Lukyanov <<EMAIL>>

	* es.po, ru.po, it.po, pt_BR.po: ajusted some strings due to
	  introduction of #l# option for plural; also add %s to some
	  strings for new eta in get and pget.

(older events were not logged, unfortunately)
