# Ukrainian translation of lftp interface.
# Copyright (C) 2013 <PERSON> <<EMAIL>>
# This file is distributed under the same license as the lftp package.
#
# <PERSON> <<EMAIL>>, 2013, 2014, 2015, 2018.
msgid ""
msgstr ""
"Project-Id-Version: lftp 4.4.8\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2024-11-08 13:05+0300\n"
"PO-Revision-Date: 2018-01-03 13:01+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Ukrainian <<EMAIL>>\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"X-Generator: Lokalize 2.0\n"

#: lib/argmatch.c:145
#, c-format
msgid "invalid argument %s for %s"
msgstr "некоректний аргумент %s для %s"

#: lib/argmatch.c:146
#, c-format
msgid "ambiguous argument %s for %s"
msgstr "неоднозначний аргумент %s для %s"

#: lib/argmatch.c:165
msgid "Valid arguments are:"
msgstr "Допустимі аргументи:"

#: lib/error.c:208
msgid "Unknown system error"
msgstr "Невідома системна помилка"

#: lib/getopt.c:282
#, c-format
msgid "%s: option '%s%s' is ambiguous\n"
msgstr "%s: параметр «%s%s» не є однозначним\n"

#: lib/getopt.c:288
#, c-format
msgid "%s: option '%s%s' is ambiguous; possibilities:"
msgstr "%s: неоднозначний параметр «%s%s»; можливі варіанти:"

#: lib/getopt.c:322
#, c-format
msgid "%s: unrecognized option '%s%s'\n"
msgstr "%s: невідомий параметр «%s%s»\n"

#: lib/getopt.c:348
#, c-format
msgid "%s: option '%s%s' doesn't allow an argument\n"
msgstr "%s: додавання аргументів до параметра «%s%s» не передбачено\n"

#: lib/getopt.c:363
#, c-format
msgid "%s: option '%s%s' requires an argument\n"
msgstr "%s: до параметра «%s%s» слід додати аргумент\n"

#: lib/getopt.c:624
#, c-format
msgid "%s: invalid option -- '%c'\n"
msgstr "%s: некоректний параметр — «%c»\n"

#: lib/getopt.c:639 lib/getopt.c:685
#, c-format
msgid "%s: option requires an argument -- '%c'\n"
msgstr "%s: до параметра слід додати аргумент — «%c»\n"

#. TRANSLATORS:
#. Get translations for open and closing quotation marks.
#. The message catalog should translate "`" to a left
#. quotation mark suitable for the locale, and similarly for
#. "'".  For example, a French Unicode local should translate
#. these to U+00AB (LEFT-POINTING DOUBLE ANGLE
#. QUOTATION MARK), and U+00BB (RIGHT-POINTING DOUBLE ANGLE
#. QUOTATION MARK), respectively.
#.
#. If the catalog has no translation, we will try to
#. use Unicode U+2018 (LEFT SINGLE QUOTATION MARK) and
#. Unicode U+2019 (RIGHT SINGLE QUOTATION MARK).  If the
#. current locale is not Unicode, locale_quoting_style
#. will quote 'like this', and clocale_quoting_style will
#. quote "like this".  You should always include translations
#. for "`" and "'" even if U+2018 and U+2019 are appropriate
#. for your locale.
#.
#. If you don't know what to put here, please see
#. <https://en.wikipedia.org/wiki/Quotation_marks_in_other_languages>
#. and use glyphs suitable for your language.
#: lib/quotearg.c:354
msgid "`"
msgstr "«"

#: lib/quotearg.c:355
msgid "'"
msgstr "»"

#: lib/xalloc-die.c:34
msgid "memory exhausted"
msgstr "пам'ять вичерпано"

#: src/ArgV.cc:107
msgid "option requires an argument"
msgstr "параметр потребує вказування аргументу"

#: src/ArgV.cc:109 src/ArgV.cc:118
msgid "invalid option"
msgstr "некоректний параметр"

#: src/ArgV.cc:114
#, c-format
msgid "option `%s' requires an argument"
msgstr "разом з параметром «%s» має бути вказано аргумент"

#: src/ArgV.cc:116
#, c-format
msgid "unrecognized option `%s'"
msgstr "невідомий параметр «%s»"

#: src/attach.h:138
#, c-format
msgid "[%u] Attached to terminal %s. %s\n"
msgstr "[%u] Приєднано до термінала %s. %s\n"

#: src/attach.h:150
#, c-format
msgid "[%u] Attached to terminal.\n"
msgstr "[%u] Приєднано до термінала.\n"

#: src/buffer.cc:253 src/FileAccess.cc:866
msgid " [cached]"
msgstr " [кешовано]"

#: src/ChmodJob.cc:80
#, c-format
msgid "Failed to change mode of `%s' to %04o (%s).\n"
msgstr "Не вдалося змінити режим доступу до «%s» на %04o (%s).\n"

#: src/ChmodJob.cc:83
#, c-format
msgid "Mode of `%s' changed to %04o (%s).\n"
msgstr "Режим доступу до «%s» змінено на %04o (%s).\n"

#: src/ChmodJob.cc:88
#, c-format
msgid "Failed to change mode of `%s' because no old mode is available.\n"
msgstr ""
"Не вдалося змінити режим доступу до «%s» оскільки попередній режим доступу "
"недоступний.\n"

#: src/CmdExec.cc:105
#, c-format
msgid "Warning: chdir(%s) failed: %s\n"
msgstr "Попередження: помилка під час спроби виконати chdir(%s): %s\n"

#: src/CmdExec.cc:207
#, c-format
msgid "Unknown command `%s'.\n"
msgstr "Невідома команда «%s».\n"

#: src/CmdExec.cc:209
#, c-format
msgid "Ambiguous command `%s'.\n"
msgstr "Неоднозначна команда «%s».\n"

#: src/CmdExec.cc:228
#, c-format
msgid "Module for command `%s' did not register the command.\n"
msgstr "Модулем для команди «%s» не зареєстровано команди.\n"

#: src/CmdExec.cc:384
#, c-format
msgid "cd ok, cwd=%s\n"
msgstr "cd виконано, cwd=%s\n"

#: src/CmdExec.cc:405 src/MirrorJob.cc:736
#, c-format
msgid "%s: received redirection to `%s'\n"
msgstr "%s: отримано переспрямовування до «%s»\n"

#: src/CmdExec.cc:408 src/FileCopy.cc:1230
msgid "Too many redirections"
msgstr "Занадто багато переспрямувань"

#: src/CmdExec.cc:517 src/CmdExec.cc:574
msgid "Interrupt"
msgstr "Перервати"

#: src/CmdExec.cc:639
#, c-format
msgid "Warning: discarding incomplete command\n"
msgstr "Попередження: відкидаємо неповну команду\n"

#: src/CmdExec.cc:737
#, c-format
msgid "\tExecuting builtin `%s' [%s]\n"
msgstr "\tВиконуємо вбудоване «%s» [%s]\n"

#: src/CmdExec.cc:742
msgid "Queue is stopped."
msgstr "Чергу виконання зупинено."

#: src/CmdExec.cc:747
msgid "Now executing:"
msgstr "Зараз виконуємо:"

#: src/CmdExec.cc:758
#, c-format
msgid "\tWaiting for job [%d] to terminate\n"
msgstr "\tОчікуємо на переривання роботи над завданням [%d]\n"

#: src/CmdExec.cc:761
#, c-format
msgid "\tWaiting for termination of jobs: "
msgstr "\tОчікуємо на переривання роботи над завданнями: "

#: src/CmdExec.cc:770
msgid "\tRunning\n"
msgstr "\tВиконуємо\n"

#: src/CmdExec.cc:772
msgid "\tWaiting for command\n"
msgstr "\tОчікуємо на команду\n"

#: src/CmdExec.cc:1261
#, c-format
msgid "%s: command `%s' is not compiled in.\n"
msgstr "%s: команду «%s» не вбудовано до програми.\n"

#: src/CmdExec.cc:1278
#, c-format
msgid "Usage: %s cmd [args...]\n"
msgstr "Користування: %s команда [аргументи...]\n"

#: src/CmdExec.cc:1284
#, c-format
msgid "%s: cannot create local session\n"
msgstr "%s: не вдалося створити локальний сеанс\n"

#: src/commands.cc:114
msgid "!<shell-command>"
msgstr "!<команда оболонки>"

#: src/commands.cc:115
msgid "Launch shell or shell command\n"
msgstr "Запустити командну оболонку або команду у оболонці\n"

#: src/commands.cc:116
msgid "(commands)"
msgstr "(команди)"

#: src/commands.cc:117
msgid ""
"Group commands together to be executed as one command\n"
"You can launch such a group in background\n"
msgstr ""
"Групові команди можна виконувати разом як одну команду.\n"
"Таку групу можна запускати у фоновому режимі.\n"

#: src/commands.cc:120
msgid "alias [<name> [<value>]]"
msgstr "alias [<назва> [<значення>]]"

#: src/commands.cc:121
msgid ""
"Define or undefine alias <name>. If <value> omitted,\n"
"the alias is undefined, else is takes the value <value>.\n"
"If no argument is given the current aliases are listed.\n"
msgstr ""
"Визначити або скасувати визначення псевдоніма <назва>. Якщо\n"
"пропущено <значення>, визначення псевдоніма скасовується,\n"
"інакше призначається значення <значення>.\n"
"Без аргументів команда показує список поточних псевдонімів.\n"

#: src/commands.cc:125
msgid "anon - login anonymously (by default)\n"
msgstr "anon — увійти анонімно (типовий варіант)\n"

#: src/commands.cc:127
msgid "bookmark [SUBCMD]"
msgstr "bookmark [ПІДКОМАНДА]"

#: src/commands.cc:128
msgid ""
"bookmark command controls bookmarks\n"
"\n"
"The following subcommands are recognized:\n"
"  add <name> [<loc>] - add current place or given location to bookmarks\n"
"                       and bind to given name\n"
"  del <name>         - remove bookmark with the name\n"
"  edit               - start editor on bookmarks file\n"
"  import <type>      - import foreign bookmarks\n"
"  list               - list bookmarks (default)\n"
msgstr ""
"Команда bookmark керує закладками.\n"
"\n"
"Передбачено такі підкоманди:\n"
"  add <назва> [<адреса>] - додати поточну або вказану адресу до закладок\n"
"                       і пов’язати її з вказаною назвою\n"
"  del <назва>            - вилучити закладку з вказаною назвою\n"
"  edit                   - запустити редактор файла закладок\n"
"  import <тип>           - імпортувати сторонні закладки\n"
"  list                   - показати список закладок (типова команда)\n"

#: src/commands.cc:137
msgid "cache [SUBCMD]"
msgstr "cache [ПІДКОМАНДА]"

#: src/commands.cc:138
msgid ""
"cache command controls local memory cache\n"
"\n"
"The following subcommands are recognized:\n"
"  stat        - print cache status (default)\n"
"  on|off      - turn on/off caching\n"
"  flush       - flush cache\n"
"  size <lim>  - set memory limit\n"
"  expire <Nx> - set cache expiration time to N seconds (x=s)\n"
"                minutes (x=m) hours (x=h) or days (x=d)\n"
msgstr ""
"Команда cache керує локальним кешем у пам’яті.\n"
"\n"
"Передбачено розпізнавання таких підкоманд:\n"
"  stat        - вивести дані щодо стану кешу (типова)\n"
"  on|off      - увімкнути або вимкнути кешування\n"
"  flush       - спорожнити кеш\n"
"  size <межа> - обмежити кешування у пам’яті\n"
"  expire <Nx> - встановити строк дії кешу у секундах (x=s),\n"
"                хвилинах (x=m), годинах (x=h) або днях (x=d)\n"

#: src/commands.cc:146
msgid "cat [-b] <files>"
msgstr "cat [-b] <файли>"

#: src/commands.cc:147
msgid ""
"cat - output remote files to stdout (can be redirected)\n"
" -b  use binary mode (ascii is the default)\n"
msgstr ""
"cat - вивести віддалені файли до stdout (можна переспрямовувати)\n"
" -b  використовувати двійковий режим (типовим є ascii)\n"

#: src/commands.cc:149
msgid "cd <rdir>"
msgstr "cd <каталог>"

#: src/commands.cc:150
msgid ""
"Change current remote directory to <rdir>. The previous remote directory\n"
"is stored as `-'. You can do `cd -' to change the directory back.\n"
"The previous directory for each site is also stored on disk, so you can\n"
"do `open site; cd -' even after lftp restart.\n"
msgstr ""
"Змінити поточний віддалений каталог на <віддалений_каталог>. Попередній "
"віддалений\n"
"каталог буде збережено як «-». Ви зможете скористатися командою «cd -» для "
"повернення.\n"
"Також на диску буде збережено попередній каталог для кожного з сайтів. Отже, "
"ви зможете\n"
"віддати команду «open сайт; cd -» навіть після перезапуску lftp.\n"

#: src/commands.cc:154
msgid "chmod [OPTS] mode file..."
msgstr "chmod [ПАРАМЕТРИ] режим файл..."

#: src/commands.cc:155
msgid ""
"Change the mode of each FILE to MODE.\n"
"\n"
" -c, --changes        - like verbose but report only when a change is made\n"
" -f, --quiet          - suppress most error messages\n"
" -v, --verbose        - output a diagnostic for every file processed\n"
" -R, --recursive      - change files and directories recursively\n"
"\n"
"MODE can be an octal number or symbolic mode (see chmod(1))\n"
msgstr ""
"Змінити режим доступу до кожного з файлів ФАЙЛ на РЕЖИМ.\n"
"\n"
" -c, --changes        - подібно до verbose, але повідомляти лише про внесені "
"зміни\n"
" -f, --quiet          - придушити більшість повідомлень про помилки\n"
" -v, --verbose        - вивести діагностичні повідомлення для кожного "
"оброблених файлів\n"
" -R, --recursive      - змінювати файли і каталоги рекурсивно\n"
"\n"
"РЕЖИМ вказується у форматі вісімкового числа або символічного режиму (див. "
"chmod(1))\n"

#: src/commands.cc:164
msgid ""
"Close idle connections. By default only with current server.\n"
" -a  close idle connections with all servers\n"
msgstr ""
"Розірвати неактивні з’єднання. Типово, лише з поточним сервером.\n"
" -a  розірвати неактивні з’єднання з усіма серверами\n"

#: src/commands.cc:166
msgid "[re]cls [opts] [path/][pattern]"
msgstr "[re]cls [параметри] [шлях/][взірець]"

#: src/commands.cc:167
msgid ""
"List remote files. You can redirect output of this command to file\n"
"or via pipe to external command.\n"
"\n"
" -1                   - single-column output\n"
" -a, --all            - show dot files\n"
" -B, --basename       - show basename of files only\n"
"     --block-size=SIZ - use SIZ-byte blocks\n"
" -d, --directory      - list directory entries instead of contents\n"
" -F, --classify       - append indicator (one of /@) to entries\n"
" -h, --human-readable - print sizes in human readable format (e.g., 1K)\n"
"     --si             - likewise, but use powers of 1000 not 1024\n"
" -k, --kilobytes      - like --block-size=1024\n"
" -l, --long           - use a long listing format\n"
" -q, --quiet          - don't show status\n"
" -s, --size           - print size of each file\n"
"     --filesize       - if printing size, only print size for files\n"
" -i, --nocase         - case-insensitive pattern matching\n"
" -I, --sortnocase     - sort names case-insensitively\n"
" -D, --dirsfirst      - list directories first\n"
"     --sort=OPT       - \"name\", \"size\", \"date\"\n"
" -S                   - sort by file size\n"
" --user, --group, --perms, --date, --linkcount, --links\n"
"                      - show individual fields\n"
" --time-style=STYLE   - use specified time format\n"
"\n"
"By default, cls output is cached, to see new listing use `recls' or\n"
"`cache flush'.\n"
"\n"
"The variables cls-default and cls-completion-default can be used to\n"
"specify defaults for cls listings and completion listings, respectively.\n"
"For example, to make completion listings show file sizes, set\n"
"cls-completion-default to \"-s\".\n"
"\n"
"Tips: Use --filesize with -D to pack the listing better.  If you don't\n"
"always want to see file sizes, --filesize in cls-default will affect the\n"
"-s flag on the commandline as well.  Add `-i' to cls-completion-default\n"
"to make filename completion case-insensitive.\n"
msgstr ""
"Показати список файлів на віддаленому вузлі. Ви можете переспрямувати дані,\n"
"виведені цією командою до фала або, за допомогою каналу, до зовнішньої "
"команди.\n"
" -1                   - виведення даних у один стовпчик\n"
" -a, --all            - показувати файли, назви яких починаються з крапки\n"
" -B, --basename       - показувати лише базові назви файлів\n"
"     --block-size=SIZ - використовувати SIZ-байтові блоки\n"
" -d, --directory      - вивести список записів каталогів замість вмісту\n"
" -F, --classify       - дописати індикатор (один з /@) до записів\n"
" -h, --human-readable - вивести дані щодо розміру у зручному для читання "
"форматі\n"
"                        (приклад: 1K)\n"
"     --si             - не саме, але за основою 1000, а не 1024\n"
" -k, --kilobytes      - те саме, що --block-size=1024\n"
" -l, --long           - використовувати формат довгого списку\n"
" -q, --quiet          - не показувати дані щодо стану\n"
" -s, --size           - вивести дані щодо розміру кожного з файлів\n"
"     --filesize       - виводити розмір лише для файлів\n"
" -i, --nocase         - не враховувати регістр символів під час порівнянь\n"
" -I, --sortnocase     - впорядковувати назви без врахування регістру "
"символів\n"
" -D, --dirsfirst      - вивести каталоги на початку списку\n"
"     --sort=OPT       - \"name\", \"size\", \"date\"\n"
" -S                   - впорядкувати файли за розміром\n"
" --user, --group, --perms, --date, --linkcount, --links\n"
"                      - показати окремі поля\n"
" --time-style=СТИЛЬ   - використати вказаний формат показу часу\n"
"\n"
"Типово, виведені cls дані кешуються. Щоб переглянути нові дані,\n"
"скористайтеся «recls» або «cache flush».\n"
"\n"
"Для визначення типових параметрів списків cls та доповнення можна "
"скористатися\n"
"змінними cls-default і cls-completion-default, відповідно.\n"
"Наприклад, щоб у списках доповнення було показано розміри файлів, "
"встановіть\n"
"для cls-completion-default значення \"-s\".\n"
"\n"
"Підказки: скористайтеся --filesize з -D для отримання компактніших списків.\n"
"Якщо вам не завжди потрібні дані щодо розмірів файлів, --filesize у cls-"
"default\n"
"вплине і на прапорець -s командного рядка. Додайте «-i» до cls-completion-"
"default,\n"
"щоб зробити доповнення назв файлів незалежним від регістру символів.\n"

#: src/commands.cc:217
msgid "debug [OPTS] [<level>|off]"
msgstr "debug [ПАРАМЕТРИ] [<рівень>|off]"

#: src/commands.cc:218
msgid ""
"Set debug level to given value or turn debug off completely.\n"
" -o <file>  redirect debug output to the file\n"
" -c  show message context\n"
" -p  show PID\n"
" -t  show timestamps\n"
msgstr ""
"Встановити для рівня діагностики вказане значення або вимкнути діагностику.\n"
" -o <файл>  спрямувати виведені дані до вказаного файла\n"
" -c  виводити контекст повідомлення\n"
" -p  виводити PID\n"
" -t  виводити дані щодо часових позначок\n"

#: src/commands.cc:223
msgid "du [options] <dirs>"
msgstr "du [параметри] <каталоги>"

#: src/commands.cc:224
msgid ""
"Summarize disk usage.\n"
" -a, --all             write counts for all files, not just directories\n"
"     --block-size=SIZ  use SIZ-byte blocks\n"
" -b, --bytes           print size in bytes\n"
" -c, --total           produce a grand total\n"
" -d, --max-depth=N     print the total for a directory (or file, with --"
"all)\n"
"                       only if it is N or fewer levels below the command\n"
"                       line argument;  --max-depth=0 is the same as\n"
"                       --summarize\n"
" -F, --files           print number of files instead of sizes\n"
" -h, --human-readable  print sizes in human readable format (e.g., 1K 234M "
"2G)\n"
" -H, --si              likewise, but use powers of 1000 not 1024\n"
" -k, --kilobytes       like --block-size=1024\n"
" -m, --megabytes       like --block-size=1048576\n"
" -S, --separate-dirs   do not include size of subdirectories\n"
" -s, --summarize       display only a total for each argument\n"
"     --exclude=PAT     exclude files that match PAT\n"
msgstr ""
"Отримати резюме щодо використання диска.\n"
" -a, --all             показати дані щодо усіх файлів, не лише каталогів\n"
"     --block-size=SIZ  використовувати SIZ-байтові блоки\n"
" -b, --bytes           вивести дані у байтах\n"
" -c, --total           вивести загальні дані\n"
" -d, --max-depth=N     вивести загальні дані щодо каталогу (або файла з --"
"all),\n"
"                       лише, якщо він зберігається на N або менше рівнів\n"
"                       нижче за каталог у аргументі командного рядка;\n"
"                       --max-depth=0 є тим самим, що і --summarize\n"
" -F, --files           вивести дані щодо кількості файлів замість розмірів\n"
" -h, --human-readable  вивести розміри у зручному для читання форматі "
"(приклади: 1K 234M 2G)\n"
" -H, --si              те саме, але з використанням основи 1000, а не 1024\n"
" -k, --kilobytes       те саме, що і --block-size=1024\n"
" -m, --megabytes       те саме, що і --block-size=1048576\n"
" -S, --separate-dirs   не включати розмір підкаталогів\n"
" -s, --summarize       показати лише загальні дані щодо усіх аргументів\n"
"     --exclude=PAT     виключити усі файли, які відповідають взірцеві PAT\n"

#: src/commands.cc:242
msgid "edit [OPTS] <file>"
msgstr "edit [ПАРАМЕТРИ] <файл>"

#: src/commands.cc:243
msgid ""
"Retrieve remote file to a temporary location, run a local editor on it\n"
"and upload the file back if changed.\n"
" -k  keep the temporary file\n"
" -o <temp>  explicit temporary file location\n"
msgstr ""
"Отримати віддалений файл до тимчасового каталогу, запустити локальний "
"редактор\n"
"для його редагування і вивантажити файл назад, якщо його буде змінено.\n"
" -k  зберегти тимчасовий файл\n"
" -o <каталог>  явним чином вказати місце тимчасового файла\n"

#: src/commands.cc:248
msgid "exit [<code>|bg]"
msgstr "exit [<код>|bg]"

#: src/commands.cc:249
msgid ""
"exit - exit from lftp or move to background if jobs are active\n"
"\n"
"If no jobs active, the code is passed to operating system as lftp\n"
"termination status. If omitted, exit code of last command is used.\n"
"`bg' forces moving to background if cmd:move-background is false.\n"
msgstr ""
"exit - вийти з lftp або перейти у фоновий режим, якщо виконуються завдання.\n"
"\n"
"Якщо завдань не виконується, операційній системі буде передано вказаний код\n"
"стану переривання роботи lftp. Якщо код не вказано, буде передано код\n"
"завершення останньої з команд. «bg» виконує примусове переведення у фоновий\n"
"режим, якщо значенням cmd:move-background є false.\n"

#: src/commands.cc:255
msgid ""
"Usage: find [OPTS] [directory]\n"
"Print contents of specified directory or current directory recursively.\n"
"Directories in the list are marked with trailing slash.\n"
"You can redirect output of this command.\n"
" -d, --maxdepth=LEVELS  Descend at most LEVELS of directories.\n"
msgstr ""
"Користування: find [ПАРАМЕТРИ] [каталог]\n"
"Вивести рекурсивно вміст вказаного або поточного каталогу.\n"
"Каталоги у списку буде позначено кінцевим символом похилої риски.\n"
"Виведені дані можна переспрямовувати.\n"
" -d, --maxdepth=РІВЕНЬ  спускатися лише до вказаного рівня вкладеності.\n"

#: src/commands.cc:260
msgid "get [OPTS] <rfile> [-o <lfile>]"
msgstr "get [ПАРАМЕТРИ] <віддалений файл> [-o <локальний файл>]"

#: src/commands.cc:261
msgid ""
"Retrieve remote file <rfile> and store it to local file <lfile>.\n"
" -o <lfile> specifies local file name (default - basename of rfile)\n"
" -c  continue, resume transfer\n"
" -E  delete remote files after successful transfer\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Отримати віддалений файл <віддалений файл> і зберегти його до файла "
"<локальний файл>.\n"
" -o <локальний файл> визначає назву локального файла (типово, основна назва "
"віддаленого файла)\n"
" -c  продовжити, поновити перенесення даних\n"
" -E  вилучити віддалений файл після успішного перенесення даних\n"
" -a  використовувати режим ascii (типовим є двійковий режим)\n"
" -O <основа> визначає базовий каталог або адресу, куди слід зберігати файли\n"

#: src/commands.cc:268
msgid "glob [OPTS] <cmd> <args>"
msgstr "glob [ПАРАМЕТРИ] <команда> <аргументи>"

#: src/commands.cc:270
msgid ""
"Expand wildcards and run specified command.\n"
"Options can be used to expand wildcards to list of files, directories,\n"
"or both types. Type selection is not very reliable and depends on server.\n"
"If entry type cannot be determined, it will be included in the list.\n"
" -f  plain files (default)\n"
" -d  directories\n"
" -a  all types\n"
" --exist      return zero exit code when the patterns expand to non-empty "
"list\n"
" --not-exist  return zero exit code when the patterns expand to an empty "
"list\n"
msgstr ""
"Обробити шаблони заміни і виконати вказану команду.\n"
"Параметри визначають режим обробки шаблонів заміни: список файлів, "
"каталогів\n"
"або файлів і каталогів. Вибір типу працює лише на деяких серверах.\n"
"Якщо тип запису не можна визначити, його буде включено до списку.\n"
" -f  звичайні файли (типова поведінка)\n"
" -d  каталоги\n"
" -a  записи усіх типів\n"
" --exist      повернути нульовий код виходу, якщо взірці розгортаються у "
"непорожній список\n"
" --not-exist  повернути нульовий код виходу, якщо взірці розгортаються у "
"порожній список\n"

#: src/commands.cc:279
msgid "help [<cmd>]"
msgstr "help [<команда>]"

#: src/commands.cc:280
msgid "Print help for command <cmd>, or list of available commands\n"
msgstr "Вивести довідку щодо команди <команда> або список доступних команд.\n"

#: src/commands.cc:282
msgid ""
"List running jobs. -v means verbose, several -v can be specified.\n"
"If <job_no> is specified, only list a job with that number.\n"
msgstr ""
"Показати список завдань, які виконуються -v означає «докладно», можна "
"вказати декілька -v.\n"
"Якщо вказано <номер завдання>, вивести дані лише щодо завдання з вказаним "
"номером.\n"

#: src/commands.cc:284
msgid "kill all|<job_no>"
msgstr "kill all|<номер завдання>"

#: src/commands.cc:285
msgid "Delete specified job with <job_no> or all jobs\n"
msgstr ""
"Вилучити вказане завдання з номером <номер завдання> або усі завдання.\n"

#: src/commands.cc:286
msgid "lcd <ldir>"
msgstr "lcd <каталог>"

#: src/commands.cc:287
msgid ""
"Change current local directory to <ldir>. The previous local directory\n"
"is stored as `-'. You can do `lcd -' to change the directory back.\n"
msgstr ""
"Змірити поточний локальний каталог на <локальний_каталог>. Попередній "
"локальний\n"
"каталог буде збережено як «-». Ви можете скористатися командою «lcd -»,\n"
"щоб повернутися до нього.\n"

#: src/commands.cc:289
msgid "lftp [OPTS] <site>"
msgstr "lftp [ПАРАМЕТРИ] <сайт>"

#: src/commands.cc:290
msgid ""
"`lftp' is the first command executed by lftp after rc files\n"
" -f <file>           execute commands from the file and exit\n"
" -c <cmd>            execute the commands and exit\n"
" --norc              don't execute rc files from the home directory\n"
" --help              print this help and exit\n"
" --version           print lftp version and exit\n"
"Other options are the same as in `open' command:\n"
" -e <cmd>            execute the command just after selecting\n"
" -u <user>[,<pass>]  use the user/password for authentication\n"
" -p <port>           use the port for connection\n"
" -s <slot>           assign the connection to this slot\n"
" -d                  switch on debugging mode\n"
" <site>              host name, URL or bookmark name\n"
msgstr ""
"«lftp» є першою командою, яку буде виконано після файлів ресурсів.\n"
" -f <файл>           виконати команди з вказаного файла і завершити роботу\n"
" -c <команда>        виконати команди і завершити роботу\n"
" --norc              не виконувати файли rc з домашнього каталогу\n"
" --help              вивести це довідкове повідомлення і завершити роботу\n"
" --version           вивести дані щодо версії lftp і завершити роботу\n"
"Інші параметри є тими самими, що і у команди «open»\n"
" -e <команда>        виконати команду одразу після вибору\n"
" -u <користувач>[,<пароль>] використати для розпізнавання вказану "
"комбінацію\n"
" -p <порт>           використати для встановлення з’єднання вказаний порт\n"
" -s <слот>           пов'язати з'єднання із вказаним слотом\n"
" -d                  перемкнутися на діагностичний режим\n"
" <сайт>              назва вузла, адреса бо закладка\n"

#: src/commands.cc:303
msgid "ln [-s] <file1> <file2>"
msgstr "ln [-s] <файл1> <файл2>"

#: src/commands.cc:304
msgid "Link <file1> to <file2>\n"
msgstr "Створити символічне посилання <файл1> та <файл2>\n"

#: src/commands.cc:308
msgid "ls [<args>]"
msgstr "ls [<аргументи>]"

#: src/commands.cc:309
msgid ""
"List remote files. You can redirect output of this command to file\n"
"or via pipe to external command.\n"
"By default, ls output is cached, to see new listing use `rels' or\n"
"`cache flush'.\n"
"See also `help cls'.\n"
msgstr ""
"Показати список віддалених файлів. Ви можете переспрямовувати виведені\n"
"цією командою дані до файла або за допомогою «|» до зовнішньої команди.\n"
"Типово, виведені ls дані кешуються, щоб переглянути оновлені дані,\n"
"скористайтеся «rels» або «cache flush».\n"
"Див. також «help cls».\n"

#: src/commands.cc:314
msgid "mget [OPTS] <files>"
msgstr "mget [ПАРАМЕТРИ] <файли>"

#: src/commands.cc:315
msgid ""
"Gets selected files with expanded wildcards\n"
" -c  continue, resume transfer\n"
" -d  create directories the same as in file names and get the\n"
"     files into them instead of current directory\n"
" -E  delete remote files after successful transfer\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Отримати вказані файли з розгортанням шаблонів заміни.\n"
" -c  продовжити, поновити перенесення даних\n"
" -d  створити каталоги зі шляхів до файлів і зберегти файли у них,\n"
"     а не у поточному каталозі\n"
" -E  вилучити віддалений файл після успішного перенесення даних\n"
" -a  використовувати режим ascii (типовим є двійковий режим)\n"
" -O <основа> визначає базовий каталог або адресу, куди слід зберігати файли\n"

#: src/commands.cc:322
msgid "mirror [OPTS] [remote [local]]"
msgstr "mirror [ПАРАМЕТРИ] [віддалена [локальна]]"

#: src/commands.cc:323
msgid "mkdir [OPTS] <dirs>"
msgstr "mkdir [ПАРАМЕТРИ] <каталоги>"

#: src/commands.cc:324
msgid ""
"Make remote directories\n"
" -p  make all levels of path\n"
" -f  be quiet, suppress messages\n"
msgstr ""
"Створення каталогів на віддаленому вузлі\n"
" -p  створити усі рівні вказаного шляху\n"
" -f  не виводити повідомлень\n"

#: src/commands.cc:327
msgid "module name [args]"
msgstr "module назва [аргументи]"

#: src/commands.cc:328
msgid ""
"Load module (shared object). The module should contain function\n"
"   void module_init(int argc,const char *const *argv)\n"
"If name contains a slash, then the module is searched in current\n"
"directory, otherwise in directories specified by setting module:path.\n"
msgstr ""
"Завантажити модуль (об’єкт спільного використання). У модулі має міститися "
"функція\n"
"   void module_init(int argc,const char *const *argv)\n"
"Якщо у назві модуля міститься символ похилої риски, пошук його коду буде "
"виконано\n"
"у поточному каталозі. Інакше, програм шукатиме модулі у каталогах, "
"визначених\n"
"змінною module:path.\n"

#: src/commands.cc:332
msgid "more <files>"
msgstr "more <файли>"

#: src/commands.cc:333
msgid "Same as `cat <files> | more'. if PAGER is set, it is used as filter\n"
msgstr ""
"Те саме, що і «cat <файли> | more». Якщо визначено змінну PAGER, відповідну "
"програму буде використано як фільтр даних.\n"

#: src/commands.cc:334
msgid "mput [OPTS] <files>"
msgstr "mput [ПАРАМЕТРИ] <файли>"

#: src/commands.cc:335
msgid ""
"Upload files with wildcard expansion\n"
" -c  continue, reput\n"
" -d  create directories the same as in file names and put the\n"
"     files into them instead of current directory\n"
" -E  delete local files after successful transfer (dangerous)\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Вивантажити вказані файли з розгортанням шаблонів заміни.\n"
" -c  продовжити, повторно вивантажити\n"
" -d  створити каталоги зі шляхів до файлів і зберегти файли у них,\n"
"     а не у поточному каталозі\n"
" -E  вилучити локальний файл після успішного перенесення даних\n"
" -a  використовувати режим ascii (типовим є двійковий режим)\n"
" -O <основа> визначає базовий каталог або адресу, куди слід зберігати файли\n"

#: src/commands.cc:342
msgid "mrm <files>"
msgstr "mrm <файли>"

#: src/commands.cc:343
msgid "Removes specified files with wildcard expansion\n"
msgstr "Вилучає вказані файли з розгортанням шаблонів заміни\n"

#: src/commands.cc:344
msgid "mv <file1> <file2>"
msgstr "mv <файл1> <файл2>"

#: src/commands.cc:345
msgid "Rename <file1> to <file2>\n"
msgstr "Перейменувати <файл1> на <файл2>\n"

#: src/commands.cc:346
msgid "mmv [OPTS] <files> <target-dir>"
msgstr "mmv [ПАРАМЕТРИ] <файли> <каталог_призначення>"

#: src/commands.cc:347
msgid ""
"Move <files> to <target-directory> with wildcard expansion\n"
" -O <dir>  specifies the target directory (alternative way)\n"
msgstr ""
"Пересунути <файли> до каталогу <каталог_призначення> із розгортанням "
"замінників\n"
" -O <каталог>  визначає каталог призначення (альтернативний спосіб)\n"

#: src/commands.cc:349
msgid "[re]nlist [<args>]"
msgstr "[re]nlist [<аргументи>]"

#: src/commands.cc:350
msgid ""
"List remote file names.\n"
"By default, nlist output is cached, to see new listing use `renlist' or\n"
"`cache flush'.\n"
msgstr ""
"Вивести список назв віддалених файлів.\n"
"Типово, дані, виведені nlist, кешуються. Щоб переглянути оновлений список\n"
"скористайтеся командою «renlist» або «cache flush».\n"

#: src/commands.cc:353
msgid "open [OPTS] <site>"
msgstr "open [ПАРАМЕТРИ] <сайт>"

#: src/commands.cc:354
msgid ""
"Select a server, URL or bookmark\n"
" -e <cmd>            execute the command just after selecting\n"
" -u <user>[,<pass>]  use the user/password for authentication\n"
" -p <port>           use the port for connection\n"
" -s <slot>           assign the connection to this slot\n"
" -d                  switch on debugging mode\n"
" <site>              host name, URL or bookmark name\n"
msgstr ""
"Вибрати сервер, адресу або закладку\n"
" -e <команда>        виконати команду одразу після здійснення вибору\n"
" -u <користувач>[,<пароль>] скористатися для розпізнавання вказаними "
"значеннями\n"
" -p <порт>           використовувати для з’єднання вказаний порт\n"
" -s <слот>           пов’язати з’єднання з вказаним слотом\n"
" -d                  увімкнути режим діагностики\n"
" <сайт>              назва вузла, адреса або назва закладки\n"

#: src/commands.cc:361
msgid "pget [OPTS] <rfile> [-o <lfile>]"
msgstr "pget [ПАРАМЕТРИ] <віддалений файл> [-o <локальний файл>]"

#: src/commands.cc:362
msgid ""
"Gets the specified file using several connections. This can speed up "
"transfer,\n"
"but loads the net heavily impacting other users. Use only if you really\n"
"have to transfer the file ASAP.\n"
"\n"
"Options:\n"
" -c  continue transfer. Requires <lfile>.lftp-pget-status file.\n"
" -n <maxconn>  set maximum number of connections (default is is taken from\n"
"     pget:default-n setting)\n"
" -O <base> specifies base directory where files should be placed\n"
msgstr ""
"Отримати вказаний файл за допомогою декількох з’єднань. Може пришвидшити "
"передавання\n"
"даних, але значно навантажує канал зв’язку і може завадити роботі інших "
"користувачів.\n"
"Користуйтеся, лише якщо файл обов’язково слід передати з максимальною "
"швидкістю.\n"
"\n"
"Параметри:\n"
" -c  продовжити передавання даних. Потрібен файл <локальний файл>.lftp-pget-"
"status.\n"
" -n <кількість> встановити максимальну кількість з’єднань (типово "
"визначається\n"
"     параметром pget:default-n)\n"
" -O <основа> визначає основний каталог, де слід зберігати файли\n"

#: src/commands.cc:370
msgid "put [OPTS] <lfile> [-o <rfile>]"
msgstr "put [ПАРАМЕТРИ] <локальний файл> [-o <віддалений файл>]"

#: src/commands.cc:371
msgid ""
"Upload <lfile> with remote name <rfile>.\n"
" -o <rfile> specifies remote file name (default - basename of lfile)\n"
" -c  continue, reput\n"
"     it requires permission to overwrite remote files\n"
" -E  delete local files after successful transfer (dangerous)\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Вивантажити <локальний файл> до віддаленого файла <віддалений файл>.\n"
" -o <віддалений файл> визначає назву віддаленого файла (типовою є\n"
"     назва локального файла)\n"
" -c  продовжити, повторно вивантажити\n"
"     потребує прав доступу на перезапис віддалених файлів\n"
" -E  вилучити локальний файл після успішного перенесення даних\n"
" -a  використовувати режим ascii (типовим є двійковий режим)\n"
" -O <основа> визначає базовий каталог або адресу, куди слід зберігати файли\n"

#: src/commands.cc:379
msgid ""
"Print current remote URL.\n"
" -p  show password\n"
msgstr ""
"Вивести поточну віддалену адресу.\n"
" -p  показати пароль\n"

#: src/commands.cc:381
msgid "queue [OPTS] [<cmd>]"
msgstr "queue [ПАРАМЕТРИ] [<команда>]"

#: src/commands.cc:382
msgid ""
"\n"
"       queue [-n num] <command>\n"
"\n"
"Add the command to queue for current site. Each site has its own command\n"
"queue. `-n' adds the command before the given item in the queue. It is\n"
"possible to queue up a running job by using command `queue wait <jobno>'.\n"
"\n"
"       queue --delete|-d [index or wildcard expression]\n"
"\n"
"Delete one or more items from the queue. If no argument is given, the last\n"
"entry in the queue is deleted.\n"
"\n"
"       queue --move|-m <index or wildcard expression> [index]\n"
"\n"
"Move the given items before the given queue index, or to the end if no\n"
"destination is given.\n"
"\n"
"Options:\n"
" -q                  Be quiet.\n"
" -v                  Be verbose.\n"
" -Q                  Output in a format that can be used to re-queue.\n"
"                     Useful with --delete.\n"
msgstr ""
"\n"
"       queue [-n номер] <команда>\n"
"\n"
"Додати команду до черги для поточного сайта. У кожного з сайтів є власна\n"
"черга команд. «-n» додає команду перед записом черги з вказаним номером.\n"
"Можна підняти у черзі завдання, що виконується за допомогою команди\n"
"«queue wait <номер завдання>».\n"
"\n"
"       queue --delete|-d [індекс або вираз-замінник]\n"
"\n"
"Вилучити один або декілька записів з черги. Якщо аргументів не вказано, "
"буде\n"
"вилучено останній з записів у черзі.\n"
"\n"
"       queue --move|-m <індекс або вираз-замінник> [індекс]\n"
"\n"
"Пересунути вказані записи у позицію до вказаного індексу у черзі або у "
"кінець\n"
"списку, якщо призначення не вказано.\n"
"\n"
"Параметри:\n"
" -q                  не виводити ніяких повідомлень.\n"
" -v                  режим докладних повідомлень.\n"
" -Q                  виводити повідомлення у форматі, яким можна "
"скористатися\n"
"                     для повторного позиціювання у черзі, корисно для --"
"delete.\n"

#: src/commands.cc:403
msgid "quote <cmd>"
msgstr "quote <команда>"

#: src/commands.cc:404
msgid ""
"Send the command uninterpreted. Use with caution - it can lead to\n"
"unknown remote state and thus will cause reconnect. You cannot\n"
"be sure that any change of remote state because of quoted command\n"
"is solid - it can be reset by reconnect at any time.\n"
msgstr ""
"Надіслати команду без обробки. Будьте обережні: може призвести\n"
"до невизначеного стану віддаленого вузла і, отже, до розірвання\n"
"з’єднання. Ви не можете бути певні щодо будь-якої зміни стану\n"
"віддаленого вузла, оскільки повторне з’єднання може відновити\n"
"початковий стан у будь-який момент часу.\n"

#: src/commands.cc:409
msgid ""
"recls [<args>]\n"
"Same as `cls', but don't look in cache\n"
msgstr ""
"recls [<аргументи>]\n"
"Те саме, що і «cls», але без використання даних з кешу.\n"

#: src/commands.cc:412
msgid ""
"Usage: reget [OPTS] <rfile> [-o <lfile>]\n"
"Same as `get -c'\n"
msgstr ""
"Користування: reget [ПАРАМЕТРИ] <віддалений файл> [-o <локальний файл>]\n"
"Те саме, що і «get -c»\n"

#: src/commands.cc:415
msgid ""
"Usage: rels [<args>]\n"
"Same as `ls', but don't look in cache\n"
msgstr ""
"Користування: rels [<аргументи>]\n"
"Те саме, що і ls, але без пошуку у кеші.\n"

#: src/commands.cc:418
msgid ""
"Usage: renlist [<args>]\n"
"Same as `nlist', but don't look in cache\n"
msgstr ""
"Користування: renlist [<аргументи>]\n"
"Те саме, що і nlist, але без пошуку у кеші\n"

#: src/commands.cc:420
msgid "repeat [OPTS] [delay] [command]"
msgstr "repeat [ПАРАМЕТРИ] [затримка] [команда]"

#: src/commands.cc:422
msgid ""
"Usage: reput <lfile> [-o <rfile>]\n"
"Same as `put -c'\n"
msgstr ""
"Користування: reput <локальний_файл> [-o <віддалений_файл>]\n"
"Те саме, що і «put -c»\n"

#: src/commands.cc:424
msgid "rm [-r] [-f] <files>"
msgstr "rm [-r] [-f] <файли>"

#: src/commands.cc:425
msgid ""
"Remove remote files\n"
" -r  recursive directory removal, be careful\n"
" -f  work quietly\n"
msgstr ""
"Вилучення віддалених файлів.\n"
" -r  рекурсивне вилучення каталогів, будьте обережні\n"
" -f  обробка без виведення повідомлень\n"

#: src/commands.cc:428
msgid "rmdir [-f] <dirs>"
msgstr "rmdir [-f] <каталоги>"

#: src/commands.cc:429
msgid "Remove remote directories\n"
msgstr "Вилучити віддалені каталоги\n"

#: src/commands.cc:430
msgid "scache [<session_no>]"
msgstr "scache [<номер сеансу>]"

#: src/commands.cc:431
msgid "List cached sessions or switch to specified session number\n"
msgstr ""
"Показати кешовані сеанси або перемкнутися на сеанс з вказаним номером\n"

#: src/commands.cc:432
msgid "set [OPT] [<var> [<val>]]"
msgstr "set [ПАРАМЕТР] [<змінна> [<значення>]]"

#: src/commands.cc:433
msgid ""
"Set variable to given value. If the value is omitted, unset the variable.\n"
"Variable name has format ``name/closure'', where closure can specify\n"
"exact application of the setting. See lftp(1) for details.\n"
"If set is called with no variable then only altered settings are listed.\n"
"It can be changed by options:\n"
" -a  list all settings, including default values\n"
" -d  list only default values, not necessary current ones\n"
msgstr ""
"Встановити для змінної вказане значення. Якщо значення не вказано,\n"
"скасувати визначення змінної.\n"
"Назву змінної слід вказувати у форматі «назва/замикання», де замикання\n"
"визначає точне застосування параметра. Див. lftp(1), щоб дізнатися більше.\n"
"Якщо set викликано без назви змінної, буде виведено лише змінені значення\n"
"параметрів.\n"
"Змінити типову поведінку команди можна за допомогою таких параметрів:\n"
" -a  показати список усіх параметрів, зокрема з типовими значеннями\n"
" -d  показати лише типові значення, не обов’язково поточні\n"

#: src/commands.cc:441
msgid "site <site-cmd>"
msgstr "site <команда сайту>"

#: src/commands.cc:442
msgid ""
"Execute site command <site_cmd> and output the result\n"
"You can redirect its output\n"
msgstr ""
"Віддати команду сайту <команда сайту> і вивести результат.\n"
"Виведені дані можна переспрямовувати.\n"

#: src/commands.cc:446
msgid ""
"Usage: slot [<label>]\n"
"List assigned slots.\n"
"If <label> is specified, switch to the slot named <label>.\n"
msgstr ""
"Користування: slot [<мітка>]\n"
"Показати список призначених слотів.\n"
"Якщо вказано мітку <мітка>, перемкнутися на слот з назвою <мітка>.\n"

#: src/commands.cc:449
msgid "source <file>"
msgstr "source <файл>"

#: src/commands.cc:450
msgid "Execute commands recorded in file <file>\n"
msgstr "Виконати команди, записані до файла <файл>\n"

#: src/commands.cc:452
msgid "torrent [OPTS] <file|URL>..."
msgstr "torrent [ПАРАМЕТРИ] <файл|адреса>..."

#: src/commands.cc:453
msgid "user <user|URL> [<pass>]"
msgstr "user <користувач|адреса> [<пароль>]"

#: src/commands.cc:454
msgid ""
"Use specified info for remote login. If you specify URL, the password\n"
"will be cached for future usage.\n"
msgstr ""
"Використати вказані дані для входу до віддаленої системи. Якщо ви вкажете "
"адресу,\n"
"пароль буде кешовано для наступних сеансів користування.\n"

#: src/commands.cc:457
msgid "Shows lftp version\n"
msgstr "Показати дані щодо версії lftp.\n"

#: src/commands.cc:458
msgid "wait [<jobno>]"
msgstr "wait [<номер завдання>]"

#: src/commands.cc:459
msgid ""
"Wait for specified job to terminate. If jobno is omitted, wait\n"
"for last backgrounded job.\n"
msgstr ""
"Зачекати на завершення вказаного завдання. Якщо не вказано номер завдання,\n"
"зачекати на завершення останнього завдання, що виконувалося у тлі.\n"

#: src/commands.cc:461
msgid "zcat <files>"
msgstr "zcat <файли>"

#: src/commands.cc:462
msgid "Same as cat, but filter each file through zcat\n"
msgstr "Те саме, що і cat, але з фільтруванням усіх файлів за допомогою zcat\n"

#: src/commands.cc:463
msgid "zmore <files>"
msgstr "zmore <файли>"

#: src/commands.cc:464
msgid "Same as more, but filter each file through zcat\n"
msgstr ""
"Те саме, що і more, але з фільтруванням усіх файлів за допомогою zcat\n"

#: src/commands.cc:466
msgid "Same as cat, but filter each file through bzcat\n"
msgstr ""
"Те саме, що і cat, але з фільтруванням усіх файлів за допомогою bzcat\n"

#: src/commands.cc:468
msgid "Same as more, but filter each file through bzcat\n"
msgstr ""
"Те саме, що і more, але з фільтруванням усіх файлів за допомогою bzcat\n"

#: src/commands.cc:524
#, c-format
msgid "Usage: %s local-dir\n"
msgstr "Користування: %s локальний_каталог\n"

#: src/commands.cc:560
#, c-format
msgid "lcd ok, local cwd=%s\n"
msgstr "lcd виконано, локальний робочий каталог=%s\n"

#: src/commands.cc:576
#, c-format
msgid "Usage: cd remote-dir\n"
msgstr "Користування: cd віддалений_каталог\n"

#: src/commands.cc:588
#, c-format
msgid "%s: no old directory for this site\n"
msgstr "%s: для цього сайта немає старого каталогу\n"

#: src/commands.cc:677
#, c-format
msgid "Usage: %s [<exit_code>]\n"
msgstr "Користування: %s [<код виходу>]\n"

#: src/commands.cc:686
msgid ""
"There are running jobs and `cmd:move-background' is not set.\n"
"Use `exit bg' to force moving to background or `kill all' to terminate "
"jobs.\n"
msgstr ""
"Виконання частини завдань не завершено, а «cmd:move-background» не "
"встановлено.\n"
"Скористайтеся командою «exit bg», щоб примусово перевести програму у фоновий "
"режим,\n"
"або командою «kill all», щоб перервати виконання усіх завдань.\n"

#: src/commands.cc:703
msgid ""
"\n"
"lftp now tricks the shell to move it to background process group.\n"
"lftp continues to run in the background despite the `Stopped' message.\n"
"lftp will automatically terminate when all jobs are finished.\n"
"Use `fg' shell command to return to lftp if it is still running.\n"
msgstr ""
"\n"
"Зараз lftp накаже командній оболонці перевести програму у групу фонових "
"процесів.\n"
"lftp продовжити роботу у фоновому режимі, незважаючи на повідомлення "
"«Stopped».\n"
"lftp автоматично завершить роботу, коли буде виконано усі завдання.\n"
"Скористайтеся командою оболонки «fg» для повернення до lftp, доки програма "
"працюватиме.\n"

#: src/commands.cc:837 src/commands.cc:3616
#, c-format
msgid "Try `%s --help' for more information\n"
msgstr "Щоб дізнатися більше, віддайте команду «%s --help»\n"

#: src/commands.cc:856
#, c-format
msgid "%s: -c, -f, -v, -h conflict with other `open' options and arguments\n"
msgstr ""
"%s: -c, -f, -v, -h конфліктують із іншими параметрами і аргументами «open»\n"

#: src/commands.cc:956
#, c-format
msgid "Usage: %s [-e cmd] [-p port] [-u user[,pass]] <host|url>\n"
msgstr ""
"Користування: %s [-e команда] [-p порт] [-u користувач[,пароль]] <вузол|"
"адреса>\n"

#: src/commands.cc:1046 src/commands.cc:2276 src/DummyProto.cc:65
#: src/MirrorJob.cc:2172 src/MirrorJob.cc:2194
msgid " - not supported protocol"
msgstr " — немає серед підтримуваних протоколів"

#: src/commands.cc:1078 src/commands.cc:2260
msgid "Password: "
msgstr "Пароль: "

#: src/commands.cc:1080
#, c-format
msgid "%s: GetPass() failed -- assume anonymous login\n"
msgstr "%s: помилка GetPass() - припускаємо, що вхід є анонімним\n"

#: src/commands.cc:1191 src/commands.cc:1284 src/commands.cc:1660
#: src/commands.cc:1691 src/commands.cc:1829 src/commands.cc:1914
#: src/commands.cc:2187 src/commands.cc:2381 src/commands.cc:2543
#: src/commands.cc:2588 src/commands.cc:2616 src/commands.cc:2623
#: src/commands.cc:2930 src/commands.cc:2957 src/commands.cc:2964
#: src/commands.cc:3293 src/lftp.cc:267 src/MirrorJob.cc:2136
#: src/SleepJob.cc:144 src/SleepJob.cc:200 src/Torrent.cc:4169
#, c-format
msgid "Try `help %s' for more information.\n"
msgstr "Віддайте команду «help %s», щоб дізнатися більше.\n"

#: src/commands.cc:1201
#, c-format
msgid "Usage: %s [OPTS] command args...\n"
msgstr "Користування: %s [ПАРАМЕТРИ] команда аргументи...\n"

#: src/commands.cc:1254
#, c-format
msgid "%s: -n: positive number expected. "
msgstr "%s: -n: мало бути вказано додатне число. "

#: src/commands.cc:1306
#, c-format
msgid "Created a stopped queue.\n"
msgstr "Створено призупинену чергу.\n"

#: src/commands.cc:1349 src/commands.cc:1377
#, c-format
msgid "%s: No queue is active.\n"
msgstr "%s: жодна з черг не є активною.\n"

#: src/commands.cc:1369
#, c-format
msgid "%s: -m: Number expected as second argument. "
msgstr "%s: -m: другим аргументом мало бути число. "

#: src/commands.cc:1421
#, c-format
msgid "Usage: %s <cmd>\n"
msgstr "Користування: %s <команда>\n"

#: src/commands.cc:1527
msgid "invalid argument for `--sort'"
msgstr "некоректний аргумент «--sort»"

#: src/commands.cc:1557
msgid "invalid block size"
msgstr "некоректний розмір блоку"

#: src/commands.cc:1700
#, c-format
msgid "Usage: %s [OPTS] files...\n"
msgstr "Користування: %s [ПАРАМЕТРИ] файли...\n"

#: src/commands.cc:1785 src/commands.cc:1814
#, c-format
msgid "%s: %s: Number expected. "
msgstr "%s: %s: мало бути вказано число. "

#: src/commands.cc:1834
#, c-format
msgid "%s: --continue conflicts with --remove-target.\n"
msgstr "%s: --continue конфліктує з --remove-target.\n"

#: src/commands.cc:1844 src/commands.cc:1920
#, c-format
msgid "File name missed. "
msgstr "Не вказано назви файла. "

#: src/commands.cc:1989
#, c-format
msgid "Usage: %s %s[-f] files...\n"
msgstr "Користування: %s %s[-f] файли...\n"

#: src/commands.cc:2032
#, c-format
msgid "Usage: %s [-e] <file|command>\n"
msgstr "Користування: %s [-e] <файл|команда>\n"

#: src/commands.cc:2079
#, c-format
msgid "Usage: %s [-v] [-v] ...\n"
msgstr "Користування: %s [-v] [-v] ...\n"

#: src/commands.cc:2093 src/commands.cc:2347 src/commands.cc:2469
#: src/commands.cc:2685 src/commands.cc:3101 src/commands.cc:3182
#: src/lftp.cc:279
#, c-format
msgid "%s: %s - not a number\n"
msgstr "%s: %s - не є числом\n"

#: src/commands.cc:2100 src/commands.cc:2326 src/commands.cc:2356
#: src/commands.cc:2487
#, c-format
msgid "%s: %d - no such job\n"
msgstr "%s: %d - такого завдання немає\n"

#: src/commands.cc:2135
#, c-format
msgid "Usage: %s [-p]\n"
msgstr "Користування: %s [-p]\n"

#: src/commands.cc:2227
#, c-format
msgid "debug level is %d, output goes to %s\n"
msgstr "рівень діагностики - %d, виведені дані спрямовуються до %s\n"

#: src/commands.cc:2230
#, c-format
msgid "debug is off\n"
msgstr "діагностику вимкнено\n"

#: src/commands.cc:2241
#, c-format
msgid "Usage: %s <user|URL> [<pass>]\n"
msgstr "Користування: %s <користувач|адреса> [<пароль>]\n"

#: src/commands.cc:2316 src/commands.cc:2479
#, c-format
msgid "%s: no current job\n"
msgstr "%s: зараз завдань не виконується\n"

#: src/commands.cc:2328
#, c-format
msgid "Usage: %s <jobno> ... | all\n"
msgstr "Користування: %s <номер завдання> ... | all\n"

#: src/commands.cc:2409
#, c-format
msgid "%s: %s. Use `set -a' to look at all variables.\n"
msgstr "%s: %s. Скористайтеся «set -a», щоб переглянути усі змінні.\n"

#: src/commands.cc:2453
#, c-format
msgid "Usage: %s [<jobno>]\n"
msgstr "Користування: %s [<номер завдання>]\n"

#: src/commands.cc:2492
#, c-format
msgid "%s: some other job waits for job %d\n"
msgstr "%s: на виконання завдання %d очікує інше завдання\n"

#: src/commands.cc:2497
#, c-format
msgid "%s: wait loop detected\n"
msgstr "%s: виявлено цикл очікування\n"

#: src/commands.cc:2553
#, c-format
msgid "Usage: %s [OPTS] <files> <target-dir>\n"
msgstr "Користування: %s [ПАРАМЕТРИ] <файли> <каталог_призначення>\n"

#: src/commands.cc:2615 src/commands.cc:2956
#, c-format
msgid "Invalid command. "
msgstr "Некоректна команда. "

#: src/commands.cc:2622 src/commands.cc:2963
#, c-format
msgid "Ambiguous command. "
msgstr "Неоднозначна команда. "

#: src/commands.cc:2641
#, c-format
msgid "%s: Operand missed for size\n"
msgstr "%s: пропущено операнд розміру\n"

#: src/commands.cc:2658
#, c-format
msgid "%s: Operand missed for `expire'\n"
msgstr "%s: Пропущено операнд «expire»\n"

#: src/commands.cc:2691
#, c-format
msgid ""
"%s: %s - no such cached session. Use `scache' to look at session list.\n"
msgstr ""
"%s: %s - такого кешованого сеансу не виявлено. Скористайтеся командою "
"«scache», щоб переглянути чинний список сеансів.\n"

#: src/commands.cc:2715
#, c-format
msgid "Sorry, no help for %s\n"
msgstr "Вибачте, довідки щодо %s не передбачено\n"

#: src/commands.cc:2720
#, c-format
msgid "%s is a built-in alias for %s\n"
msgstr "%s є вбудованою альтернативою до %s\n"

#: src/commands.cc:2725
#, c-format
msgid "Usage: %s\n"
msgstr "Користування: %s\n"

#: src/commands.cc:2733
#, c-format
msgid "%s is an alias to `%s'\n"
msgstr "%s є альтернативою до «%s»\n"

#: src/commands.cc:2737
#, c-format
msgid "No such command `%s'. Use `help' to see available commands.\n"
msgstr ""
"Команди «%s» не передбачено. Скористайтеся командою «help», щоб переглянути "
"список передбачених команд.\n"

#: src/commands.cc:2739
#, c-format
msgid "Ambiguous command `%s'. Use `help' to see available commands.\n"
msgstr ""
"Неоднозначна команда «%s». Скористайтеся командою «help», щоб переглянути "
"список передбачених команд.\n"

#: src/commands.cc:2805
#, c-format
msgid "LFTP | Version %s | Copyright (c) 1996-%d Alexander V. Lukyanov\n"
msgstr "LFTP | Версія %s | ©  Alexander V. Lukyanov, 1996-%d\n"

#: src/commands.cc:2808
#, c-format
msgid ""
"LFTP is free software: you can redistribute it and/or modify\n"
"it under the terms of the GNU General Public License as published by\n"
"the Free Software Foundation, either version 3 of the License, or\n"
"(at your option) any later version.\n"
"\n"
"This program is distributed in the hope that it will be useful,\n"
"but WITHOUT ANY WARRANTY; without even the implied warranty of\n"
"MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n"
"GNU General Public License for more details.\n"
"\n"
"You should have received a copy of the GNU General Public License\n"
"along with LFTP.  If not, see <http://www.gnu.org/licenses/>.\n"
msgstr ""
"LFTP є вільним програмним забезпеченням; ви можете поширювати її \n"
"і/або змінювати її за умов дотримання GNU General Public License у тому \n"
"вигляді, у якому її оприлюднено Free Software Foundation; версії 3 цієї \n"
"ліцензії, або (за потреби) будь-якої пізнішої версії\n"
"\n"
"Ця програма поширюється у сподіванні, що вона буде корисною, але БЕЗ БУДЬ-\n"
"ЯКИХ ГАРАНТІЙ; навіть без очевидної гарантії КОМЕРЦІЙНОЇ ЦІННОСТІ або \n"
"ПРИДАТНОСТІ ДЛЯ ЯКОЇСЬ МЕТИ.  Докладніше про це ви можете дізнатися з \n"
"GNU General Public License.\n"
"Разом з LFTP ви маєте отримати копію GNU General Public License. \n"
"Якщо ви її не отримали, зверніться за адресою <http://www.gnu.org/licenses/"
">, щоб дізнатися про подальші дії.\n"

#: src/commands.cc:2822
#, c-format
msgid "Send bug reports and questions to the mailing list <%s>.\n"
msgstr ""
"Повідомлення щодо вад та питання надсилайте до списку листування <%s>.\n"

#: src/commands.cc:2828
msgid "Libraries used: "
msgstr "Використані бібліотеки: "

#: src/commands.cc:2979 src/commands.cc:3007
#, c-format
msgid "%s: bookmark name required\n"
msgstr "%s: слід вказати назву закладки\n"

#: src/commands.cc:2996
#, c-format
msgid "%s: spaces in bookmark name are not allowed\n"
msgstr "%s: не можна використовувати у назві закладки пробіли\n"

#: src/commands.cc:3009
#, c-format
msgid "%s: no such bookmark `%s'\n"
msgstr "%s: немає закладки з назвою «%s»\n"

#: src/commands.cc:3032
#, c-format
msgid "%s: import type required (netscape,ncftp)\n"
msgstr "%s: слід вказати тип імпортованих даних (netscape,ncftp)\n"

#: src/commands.cc:3110
#, c-format
msgid "Usage: %s [-d #] dir\n"
msgstr "Користування: %s [-d #] каталог\n"

#: src/commands.cc:3215
#, c-format
msgid "%s: invalid block size `%s'\n"
msgstr "%s: некоректний розмір блоку, «%s»\n"

#: src/commands.cc:3226
#, c-format
msgid "Usage: %s [options] <dirs>\n"
msgstr "Користування: %s [параметри] <каталоги>\n"

#: src/commands.cc:3232
#, c-format
msgid "%s: warning: summarizing is the same as using --max-depth=0\n"
msgstr ""
"%s: попередження: підбиття підсумків це те ж саме, що й --max-depth=0\n"

#: src/commands.cc:3236
#, c-format
msgid "%s: summarizing conflicts with --max-depth=%i\n"
msgstr "%s: підбиття підсумків конфліктує з --max-depth=%i\n"

#: src/commands.cc:3280
#, c-format
msgid "Usage: %s command args...\n"
msgstr "Користування: %s команда аргументи...\n"

#: src/commands.cc:3292
#, c-format
msgid "Usage: %s module [args...]\n"
msgstr "Користування: %s модуль [аргументи...]\n"

#: src/commands.cc:3310 src/LocalAccess.cc:642
msgid "cannot get current directory"
msgstr "не вдається отримати поточний каталог"

#: src/commands.cc:3374
#, c-format
msgid "Usage: %s [OPTS] mode file...\n"
msgstr "Користування: %s [параметри] режим файл...\n"

#: src/commands.cc:3394
#, c-format
msgid "invalid mode string: %s\n"
msgstr "некоректний рядок режиму: %s\n"

#: src/commands.cc:3457 src/commands.cc:3466
msgid "Invalid range format. Format is min-max, e.g. 10-20."
msgstr ""
"Некоректний діапазон часу. Діапазон слід вказувати у форматі мінімум-"
"максимум. Приклад: 10-20."

#: src/commands.cc:3478
#, c-format
msgid "Usage: %s [OPTS] file\n"
msgstr "Користування: %s [параметри] файл\n"

#: src/CopyJob.cc:82
#, c-format
msgid "`%s' at %lld %s%s%s%s"
msgstr "«%s» на %lld %s%s%s%s"

#: src/CopyJob.cc:159
#, c-format
msgid "%lld $#ll#byte|bytes$ transferred in %ld $#l#second|seconds$"
msgstr ""
"Передано %lld $#ll#байт|байта|байтів$ за %ld $#l#секунду|секунди|секунд$"

#: src/CopyJob.cc:167
#, c-format
msgid "%lld $#ll#byte|bytes$ transferred"
msgstr "Передано %lld $#ll#байт|байти|байтів$"

#: src/CopyJob.cc:283
#, c-format
msgid "Transfer of %d of %d $file|files$ failed\n"
msgstr "Не вдалося передати %d з %d $файла|файлів|файлів$\n"

#: src/CopyJob.cc:289
#, c-format
msgid "Total %d $file|files$ transferred\n"
msgstr "Загалом перенесено %d $файл|файлів|файлів$\n"

#: src/FileAccess.cc:160
msgid "Access failed: "
msgstr "Спроба доступу зазнала невдачі: "

#: src/FileAccess.cc:161
msgid "File cannot be accessed"
msgstr "Не вдалося отримати доступ до файла"

#: src/FileAccess.cc:163 src/Fish.cc:982 src/ftpclass.cc:4599
#: src/ftpclass.cc:4608 src/SFtp.cc:1339 src/Torrent.cc:3533
msgid "Not connected"
msgstr "Не з’єднано"

#: src/FileAccess.cc:166 src/FileAccess.cc:167
msgid "Fatal error"
msgstr "Критична помилка"

#: src/FileAccess.cc:169
msgid "Store failed - you have to reput"
msgstr "Помилка сховища даних. Слід надіслати дані ще раз."

#: src/FileAccess.cc:172 src/FileAccess.cc:173
msgid "Login failed"
msgstr "Невдала спроба увійти"

#: src/FileAccess.cc:176 src/FileAccess.cc:177
msgid "Operation not supported"
msgstr "Підтримки дії не передбачено"

#: src/FileAccess.cc:180
msgid "File moved"
msgstr "Файл пересунуто"

#: src/FileAccess.cc:182
msgid "File moved to `"
msgstr "Файл пересунуто до «"

#: src/FileCopy.cc:141
msgid "copy: destination file is already complete\n"
msgstr "copy: файл призначення вже повністю передано\n"

#: src/FileCopy.cc:182
msgid "copy: put is broken\n"
msgstr "copy: не вдалося виконати put\n"

#: src/FileCopy.cc:201
msgid "seek failed"
msgstr "помилка позиціювання"

#: src/FileCopy.cc:207
msgid "no progress timeout"
msgstr "час очікування на поступ"

#: src/FileCopy.cc:235
msgid "cannot seek on data source"
msgstr "не вдалося виконати позиціювання у джерелі даних"

#: src/FileCopy.cc:238
#, c-format
msgid "copy: put rolled back to %lld, seeking get accordingly\n"
msgstr ""
"copy: put відкинуто до %lld, виконуємо позиціювання get відповідним чином\n"

#: src/FileCopy.cc:251
msgid "copy: all data received, but get rolled back\n"
msgstr "copy: отримано усі дані, але наказано повторити отримання даних\n"

#: src/FileCopy.cc:267
#, c-format
msgid "copy: get rolled back to %lld, seeking put accordingly\n"
msgstr ""
"copy: get відкинуто до %lld, виконуємо позиціювання put відповідним чином\n"

#: src/FileCopy.cc:364
msgid "file size decreased during transfer"
msgstr "під час передавання розмір файла зменшився"

#: src/FileCopy.cc:1227
#, c-format
msgid "copy: received redirection to `%s'\n"
msgstr "copy: отримано переспрямування на «%s»\n"

#: src/FileCopy.cc:1290
#, fuzzy
#| msgid "file size decreased during transfer"
msgid "file size increased during transfer"
msgstr "під час передавання розмір файла зменшився"

#: src/FileCopy.cc:1390
msgid "file name missed in URL"
msgstr "у адресі не вказано назви файла"

#: src/FileCopy.cc:2086
msgid "Verify command failed without a message"
msgstr ""
"Не вдалося виконати команду перевірки. Додаткових повідомлень не виведено."

#: src/FileCopyFtp.cc:95
msgid "**** FXP: trying to reverse ftp:fxp-passive-source\n"
msgstr "**** FXP: намагаємося скасувати ftp:fxp-passive-source\n"

#: src/FileCopyFtp.cc:102
msgid "**** FXP: trying to reverse ftp:fxp-passive-sscn\n"
msgstr "**** FXP: намагаємося скасувати ftp:fxp-passive-sscn\n"

#: src/FileCopyFtp.cc:110
msgid "**** FXP: trying to reverse ftp:ssl-protect-fxp\n"
msgstr "**** FXP: намагаємося скасувати ftp:ssl-protect-fxp\n"

#: src/FileCopyFtp.cc:116
msgid "**** FXP: giving up, reverting to plain copy\n"
msgstr "**** FXP: здаємося, повертаємося до звичайного копіювання\n"

#: src/FileCopyFtp.cc:125
msgid "ftp:fxp-force is set but FXP is not available"
msgstr "Встановлено ftp:fxp-force, але FXP недоступне"

#: src/FileCopy.h:285
msgid "Verifying..."
msgstr "Перевіряємо…"

#: src/FileSetOutput.cc:230
msgid "non-option arguments found"
msgstr "виявлено аргументи без параметрів"

#: src/Filter.cc:166
msgid "pipe() failed: "
msgstr "помилка pipe(): "

#: src/Filter.cc:200 src/PtyShell.cc:135
#, c-format
msgid "chdir(%s) failed: %s\n"
msgstr "не вдалося виконати chdir(%s): %s\n"

#: src/Filter.cc:208
#, c-format
msgid "execvp(%s) failed: %s\n"
msgstr "не вдалося виконати execvp(%s): %s\n"

#: src/Filter.cc:213 src/PtyShell.cc:147
#, c-format
msgid "execl(/bin/sh) failed: %s\n"
msgstr "Не вдалося виконати execl(/bin/sh): %s\n"

#: src/Filter.cc:415
msgid "file already exists and xfer:clobber is unset"
msgstr "файл вже існує, а xfer:clobber не встановлено"

#: src/FindJobDu.cc:101
msgid "total"
msgstr "загалом"

#: src/Fish.cc:75 src/ftpclass.cc:1292 src/Http.cc:1232 src/SFtp.cc:77
#, c-format
msgid "Closing idle connection"
msgstr "Закриваємо неактивне з’єднання"

#: src/Fish.cc:162 src/SFtp.cc:177
msgid "Running connect program"
msgstr "Запускаємо програму для встановлення з’єднання"

#: src/Fish.cc:588 src/ftpclass.cc:2887 src/ftpclass.cc:3107 src/Http.cc:1510
#: src/SFtp.cc:742 src/SFtp.cc:1083 src/SFtp.cc:1084 src/SSH_Access.cc:167
#, c-format
msgid "Peer closed connection"
msgstr "З’єднання перервано з ініціативи віддаленого комп’ютера"

#: src/Fish.cc:634 src/ftpclass.cc:3037 src/ftpclass.cc:4219 src/SFtp.cc:1108
#, c-format
msgid "extra server response"
msgstr "зайва відповідь сервера"

#: src/Fish.cc:987 src/ftpclass.cc:4611 src/Http.cc:2329 src/Http.cc:2336
#: src/SFtp.cc:1345 src/Torrent.cc:3536 src/TorrentTracker.cc:624
msgid "Connecting..."
msgstr "Встановлюємо з’єднання…"

#: src/Fish.cc:989 src/ftpclass.cc:4617 src/SFtp.cc:1347
msgid "Connected"
msgstr "З'єднано"

#: src/Fish.cc:991 src/ftpclass.cc:4594 src/ftpclass.cc:4622
#: src/ftpclass.cc:4636 src/Http.cc:2338 src/SFtp.cc:1349
#: src/TorrentTracker.cc:626
msgid "Waiting for response..."
msgstr "Очікуємо на відповідь…"

#: src/Fish.cc:993 src/ftpclass.cc:4656 src/Http.cc:2341 src/SFtp.cc:1351
msgid "Receiving data"
msgstr "Отримуємо дані…"

#: src/Fish.cc:995 src/ftpclass.cc:4654 src/Http.cc:2334 src/SFtp.cc:1353
msgid "Sending data"
msgstr "Надсилаємо дані"

#: src/Fish.cc:997 src/SFtp.cc:1355
msgid "Done"
msgstr "Виконано"

#: src/Fish.cc:1136 src/FtpDirList.cc:123 src/HttpDir.cc:1384 src/SFtp.cc:2200
#: src/SFtp.cc:2320
#, c-format
msgid "Getting file list (%lld) [%s]"
msgstr "Отримуємо список файлів (%lld) [%s]"

#: src/ftpclass.cc:197
#, c-format
msgid "Data connection peer has wrong port number"
msgstr "Для вузла з’єднання для обміну даними вказано помилковий номер порту"

#: src/ftpclass.cc:203
#, c-format
msgid "Data connection peer has mismatching address"
msgstr "Для вузла з’єднання для обміну даними вказано невідповідну адресу"

#: src/ftpclass.cc:225 src/ftpclass.cc:250
#, c-format
msgid "Switching to NOREST mode"
msgstr "Перемикаємося на режим NOREST"

#: src/ftpclass.cc:425
#, c-format
msgid "Server reply matched ftp:retry-530, retrying"
msgstr "Відповідь сервера збігається з ftp:retry-530, повторюємо спробу."

#: src/ftpclass.cc:433
#, c-format
msgid "Server reply matched ftp:retry-530-anonymous, retrying"
msgstr ""
"Відповідь сервера збігається з ftp:retry-530-anonymous, повторюємо спробу."

#: src/ftpclass.cc:466
msgid "Account is required, set ftp:acct variable"
msgstr "Потрібен обліковий запис, встановіть значення змінної ftp:acct"

#: src/ftpclass.cc:483
msgid "ftp:skey-force is set and server does not support OPIE nor S/KEY"
msgstr ""
"Встановлено ftp:skey-force, а на сервері не передбачено підтримки ні OPIE, "
"ні S/KEY"

#: src/ftpclass.cc:501
#, c-format
msgid "assuming failed host name lookup"
msgstr "припускаємо, що слід виконати пошук за назвою помилкового вузла"

#: src/ftpclass.cc:809 src/ftpclass.cc:810
#, c-format
msgid "cannot parse EPSV response"
msgstr "не вдалося обробити відповідь EPSV"

#: src/ftpclass.cc:837 src/ftpclass.cc:838
#, fuzzy, c-format
#| msgid "cannot parse EPSV response"
msgid "cannot parse custom EPSV response"
msgstr "не вдалося обробити відповідь EPSV"

#: src/ftpclass.cc:1122
#, c-format
msgid "Closing control socket"
msgstr "Закриваємо контрольний сокет"

#: src/ftpclass.cc:1341
msgid "MLSD is disabled by ftp:use-mlsd"
msgstr "MLSD вимкнено параметром ftp:use-mlsd"

#: src/ftpclass.cc:1411 src/Http.cc:1431 src/Torrent.cc:3204
#: src/Torrent.cc:3743 src/TorrentTracker.cc:395
#, c-format
msgid "cannot create socket of address family %d"
msgstr "не вдалося створити сокет сімейства адрес %d"

#: src/ftpclass.cc:1442 src/Http.cc:1457
#, c-format
msgid "Socket error (%s) - reconnecting"
msgstr "Помилка сокета (%s) — встановлюємо з’єднання повторно"

#: src/ftpclass.cc:1715
msgid "MFF and SITE CHMOD are not supported by this site"
msgstr "На цьому сайті не передбачено підтримки MFF і SITE CHMOD"

#: src/ftpclass.cc:1720
msgid "MLST and MLSD are not supported by this site"
msgstr "MLST і MLSD не підтримуються цим сайтом"

#: src/ftpclass.cc:2031
msgid "SITE SYMLINK is not supported by the server"
msgstr "Підтримки SITE SYMLINK на цьому сервері не передбачено"

#: src/ftpclass.cc:2204
msgid "unsupported network protocol"
msgstr "непідтримуваний протокол роботи у мережі"

#: src/ftpclass.cc:2253 src/ftpclass.cc:2355
#, c-format
msgid "Data socket error (%s) - reconnecting"
msgstr "Помилка сокета даних (%s) — виконуємо спробу повторного з’єднання"

#: src/ftpclass.cc:2281
#, c-format
msgid "Accepted data connection from (%s) port %u"
msgstr "Прийнято з’єднання з передавання даних з (%s), порт %u"

#: src/ftpclass.cc:2330
#, c-format
msgid "Connecting data socket to (%s) port %u"
msgstr "З’єднуємо сокет даних з (%s), порт %u"

#: src/ftpclass.cc:2336
#, c-format
msgid "Connecting data socket to proxy %s (%s) port %u"
msgstr ""
"Встановлюємо з’єднання сокета передавання даних з проксі-сервером %s (%s), "
"порт %u"

#: src/ftpclass.cc:2358 src/ftpclass.cc:4414
#, c-format
msgid "Switching passive mode off"
msgstr "Вимикаємо пасивний режим"

#: src/ftpclass.cc:2366
#, c-format
msgid "Data connection established"
msgstr "Встановлено з’єднання для обміну даними"

#: src/ftpclass.cc:2688 src/ftpclass.cc:4548
msgid "ftp:ssl-force is set and server does not support or allow SSL"
msgstr ""
"Встановлено ftp:ssl-force, а на сервері не підтримується і не дозволяється "
"SSL"

#: src/ftpclass.cc:3053
#, c-format
msgid "Persist and retry"
msgstr "Продовжувати повторні спроби"

#: src/ftpclass.cc:3321
#, c-format
msgid "Closing data socket"
msgstr "Закриваємо сокет передавання даних"

#: src/ftpclass.cc:3343
#, c-format
msgid "Closing aborted data socket"
msgstr "Закриваємо перерваний сокет передавання даних"

#: src/ftpclass.cc:4193
#, c-format
msgid "saw file size in response"
msgstr "розмір файла saw у відповіді"

#: src/ftpclass.cc:4233 src/ftpclass.cc:4260
#, c-format
msgid "Turning on sync-mode"
msgstr "Вмикаємо режим синхронізації"

#: src/ftpclass.cc:4434
#, c-format
msgid "Switching passive mode on"
msgstr "Вмикаємо пасивний режим"

#: src/ftpclass.cc:4585
msgid "FEAT negotiation..."
msgstr "Узгодження FEAT…"

#: src/ftpclass.cc:4592
msgid "Sending commands..."
msgstr "Надсилаємо команди…"

#: src/ftpclass.cc:4596
msgid "Delaying before retry"
msgstr "Затримка перед повторною спробою"

#: src/ftpclass.cc:4597 src/Http.cc:2331
msgid "Connection idle"
msgstr "Неактивне з’єднання"

#: src/ftpclass.cc:4604 src/Http.cc:2323 src/Resolver.cc:172
#: src/Resolver.cc:217 src/TorrentTracker.cc:622
#, c-format
msgid "Resolving host address..."
msgstr "Визначаємо адресу вузла…"

#: src/ftpclass.cc:4615
msgid "TLS negotiation..."
msgstr "Узгодуємо TLS…"

#: src/ftpclass.cc:4619
msgid "Logging in..."
msgstr "Вхід до системи…"

#: src/ftpclass.cc:4623
msgid "Making data connection..."
msgstr "Створюємо з’єднання для передавання даних…"

#: src/ftpclass.cc:4626
msgid "Changing remote directory..."
msgstr "Змінюємо віддалений каталог…"

#: src/ftpclass.cc:4632
msgid "Waiting for other copy peer..."
msgstr "Очікуємо на інший вузол копіювання…"

#: src/ftpclass.cc:4634 src/ftpclass.cc:4658
msgid "Waiting for transfer to complete"
msgstr "Очікуємо на завершення передавання даних"

#: src/ftpclass.cc:4638
msgid "Waiting for TLS shutdown..."
msgstr "Очікуємо на завершення роботи TLS…"

#: src/ftpclass.cc:4640
msgid "Waiting for data connection..."
msgstr "Очікуємо на з’єднання для обміну даними…"

#: src/ftpclass.cc:4646
msgid "Sending data/TLS"
msgstr "Надсилаємо дані/TLS"

#: src/ftpclass.cc:4648
msgid "Receiving data/TLS"
msgstr "Отримуємо дані/TLS"

#: src/Http.cc:230
#, c-format
msgid "Closing HTTP connection"
msgstr "Розриваємо з’єднання HTTP"

#: src/Http.cc:240
msgid "POST method failed"
msgstr "Помилка методу POST"

#: src/Http.cc:1381
msgid "ftp over http cannot work without proxy, set hftp:proxy."
msgstr ""
"ftp крізь http не може працювати без проксі-сервера, встановлено hftp:proxy."

#: src/Http.cc:1537
#, c-format
msgid "Sending request..."
msgstr "Надсилаємо запит…"

#: src/Http.cc:1575
#, c-format
msgid "Hit EOF while fetching headers"
msgstr "Надіслано EOF під час отримання заголовків"

#: src/Http.cc:1695
#, c-format
msgid "Could not parse HTTP status line"
msgstr "Не вдалося обробити рядок стану HTTP"

#: src/Http.cc:1726
msgid "Object is not cached and http:cache-control has only-if-cached"
msgstr "Об’єкт не кешовано, а http:cache-control містить only-if-cached"

#: src/Http.cc:1891
#, c-format
msgid "Receiving body..."
msgstr "Отримуємо вміст…"

#: src/Http.cc:2092
#, c-format
msgid "Hit EOF"
msgstr "Отримано EOF"

#: src/Http.cc:2095
#, c-format
msgid "Received not enough data, retrying"
msgstr "Отримано недостатньо даних, повторюємо спробу"

#: src/Http.cc:2106
#, c-format
msgid "Received all"
msgstr "Отримано все"

#: src/Http.cc:2111
#, c-format
msgid "Received all (total)"
msgstr "Отримано все (загалом)"

#: src/Http.cc:2135 src/Http.cc:2159
msgid "chunked format violated"
msgstr "порушено формат поділу на шматки"

#: src/Http.cc:2145
#, c-format
msgid "Received last chunk"
msgstr "Отримано останній шматок"

#: src/Http.cc:2339
msgid "Fetching headers..."
msgstr "Отримуємо заголовки…"

#: src/Job.cc:293
#, c-format
msgid "[%d] Done (%s)"
msgstr "[%d] Виконано (%s)"

#: src/lftp.cc:370
#, c-format
msgid "[%u] Terminated by signal %d. %s\n"
msgstr "[%u] Перервано сигналом %d. %s\n"

#: src/lftp.cc:445
#, c-format
msgid "[%u] Started.  %s\n"
msgstr "[%u] Розпочато.  %s\n"

#: src/lftp.cc:463
#, c-format
msgid "[%u] Detaching from the terminal to complete transfers...\n"
msgstr "[%u] Від’єднуємося від термінала для завершення передавання даних…\n"

#: src/lftp.cc:465
#, c-format
msgid "[%u] Exiting and detaching from the terminal.\n"
msgstr "[%u] Виходимо і від’єднуємося від термінала.\n"

#: src/lftp.cc:470
#, c-format
msgid "[%u] Detached from terminal. %s\n"
msgstr "[%u] Від’єднано від термінала. %s\n"

#: src/lftp.cc:480
#, c-format
msgid "[%u] Finished. %s\n"
msgstr "[%u] Завершено. %s\n"

#: src/lftp.cc:484
#, c-format
msgid "[%u] Moving to background to complete transfers...\n"
msgstr "[%u] Змінюємо режим на фоновий для завершення передавання даних…\n"

#: src/lftp.cc:553
msgid "history -w file|-r file|-c|-l [cnt]"
msgstr "history -w файл|-r файл|-c|-l [кількість]"

#: src/lftp.cc:554
msgid ""
" -w <file> Write history to file.\n"
" -r <file> Read history from file; appends to current history.\n"
" -c  Clear the history.\n"
" -l  List the history (default).\n"
"Optional argument cnt specifies the number of history lines to list,\n"
"or \"all\" to list all entries.\n"
msgstr ""
" -w <файл> записати дані журналу до вказаного файла.\n"
" -r <файл> прочитати дані журналу з вказаного файла; дописати дані до "
"поточного журналу.\n"
" -c  спорожнити журнал.\n"
" -l  показати журнал (типова поведінка).\n"
"Додатковий аргумент «кількість» визначає кількість рядків журналу у списку\n"
"або, якщо вказано «all», наказує показати усі записи.\n"

#: src/lftp.cc:561
msgid "Attach the terminal to specified backgrounded lftp process.\n"
msgstr ""
"Долучити термінал до вказаного переведеного у фоновий режим процесу lftp.\n"

#: src/lftp_ssl.cc:1291
#, c-format
msgid "No certificate presented by %s.\n"
msgstr "%s не надано жодних сертифікатів.\n"

#: src/LocalAccess.cc:604 src/NetAccess.cc:686
msgid "Getting directory contents"
msgstr "Отримуємо дані щодо вмісту каталогу"

#: src/LocalAccess.cc:606 src/NetAccess.cc:690
msgid "Getting files information"
msgstr "Отримуємо дані щодо файлів"

#: src/LsCache.cc:190
#, c-format
msgid "%ld $#l#byte|bytes$ cached"
msgstr "кешовано %ld $#l#байт|байти|байтів$"

#: src/LsCache.cc:194
msgid ", no size limit"
msgstr ", без обмеження розміру"

#: src/LsCache.cc:196
#, c-format
msgid ", maximum size %ld\n"
msgstr ", максимальний розмір — %ld\n"

#: src/mgetJob.cc:78
#, c-format
msgid "%s: %s: no files found\n"
msgstr "%s: %s: не знайдено жодних файлів\n"

#: src/MirrorJob.cc:100
#, c-format
msgid "%sTotal: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
msgstr ""
"%sЗагалом: %d каталог$|и|ів$, %d файл$|и|ів$, %d $символічне посилання|"
"символічні посилання|символічних посилань$\n"

#: src/MirrorJob.cc:104
#, c-format
msgid "%sNew: %d file$|s$, %d symlink$|s$\n"
msgstr ""
"%sНові: %d файл$|и|ів$, %d $символічне посилання|символічні посилання|"
"символічних посилань$\n"

#: src/MirrorJob.cc:108
#, c-format
msgid "%sModified: %d file$|s$, %d symlink$|s$\n"
msgstr ""
"%sЗмінено: %d файл$|а|ів$, %d $символічне посилання|символічні посилання|"
"символічних посилань$\n"

#: src/MirrorJob.cc:115
#, c-format
msgid "%sRemoved: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
msgstr ""
"%sВилучено: %d каталог$|и|ів$, %d файл$|и|ів$, %d $символічне посилання|"
"символічні посилання|символічних посилань$\n"

#: src/MirrorJob.cc:120
#, c-format
msgid "%s%d error$|s$ detected\n"
msgstr "%sвиявлено %d $помилку|помилки|помилок$\n"

#: src/MirrorJob.cc:231
#, c-format
msgid "Finished %s"
msgstr "Завершено %s"

#: src/MirrorJob.cc:344 src/MirrorJob.cc:519 src/MirrorJob.cc:1218
#, c-format
msgid "Removing old file `%s'"
msgstr "Вилучаємо застарілий файл «%s»"

#: src/MirrorJob.cc:346
#, c-format
msgid "Overwriting old file `%s'"
msgstr "Перезаписуємо застарілий файл «%s»"

#: src/MirrorJob.cc:355
#, c-format
msgid "Skipping file `%s' (only-existing)"
msgstr "Пропускаємо файл «%s» (only-existing)"

#: src/MirrorJob.cc:361
#, c-format
msgid "Transferring file `%s'"
msgstr "Переносимо дані файла «%s»"

#: src/MirrorJob.cc:439
#, c-format
msgid "Skipping directory `%s' (only-existing)"
msgstr "Пропускаємо каталог «%s» (only-existing)"

#: src/MirrorJob.cc:461 src/MirrorJob.cc:550 src/MirrorJob.cc:902
#, c-format
msgid "Removing old local file `%s'"
msgstr "Вилучаємо застарілий локальний файл «%s»"

#: src/MirrorJob.cc:487
#, c-format
msgid "Scanning directory `%s'"
msgstr "Скануємо каталог «%s»"

#: src/MirrorJob.cc:489
#, c-format
msgid "Mirroring directory `%s'"
msgstr "Створюємо дзеркальну копію каталогу «%s»"

#: src/MirrorJob.cc:525 src/MirrorJob.cc:567
#, c-format
msgid "Making symbolic link `%s' to `%s'"
msgstr "Створюємо символічне посилання «%s» на «%s»"

#: src/MirrorJob.cc:562
#, c-format
msgid "Skipping symlink `%s' (only-existing)"
msgstr "Пропускаємо символічне посилання «%s» (only-existing)"

#: src/MirrorJob.cc:807
#, c-format
msgid "mirror: protocol `%s' is not suitable for mirror\n"
msgstr ""
"mirror: протокол «%s» не може бути використано для дзеркального копіювання\n"

#: src/MirrorJob.cc:923
#, c-format
msgid "Making directory `%s'"
msgstr "Створюємо каталог «%s»"

#: src/MirrorJob.cc:1181
#, c-format
msgid "Old directory `%s' is not removed"
msgstr "Старий каталог «%s» не вилучено"

#: src/MirrorJob.cc:1183
#, c-format
msgid "Old file `%s' is not removed"
msgstr "Старий файл «%s» не вилучено"

#: src/MirrorJob.cc:1216
#, c-format
msgid "Removing old directory `%s'"
msgstr "Вилучаємо старий каталог «%s»"

#: src/MirrorJob.cc:1339
#, c-format
msgid "Removing source directory `%s'"
msgstr "Вилучаємо початковий каталог «%s»"

#: src/MirrorJob.cc:1405
#, c-format
msgid "Removing source file `%s'"
msgstr "Вилучаємо файл джерела «%s»"

#: src/MirrorJob.cc:1425
#, c-format
msgid "Retrying mirror...\n"
msgstr "Повторюємо спробу створення дзеркальної копії…\n"

#: src/MirrorJob.cc:1694
msgid "pattern is empty"
msgstr ""

#: src/MirrorJob.cc:1779
#, c-format
msgid "%s must be one of: %s"
msgstr "%s має бути одним з таких значень: %s"

#: src/MirrorJob.cc:2019
#, c-format
msgid ""
"%s: multiple --file or --directory options must have the same base "
"directory\n"
msgstr ""
"%s: якщо вказано декілька параметрів --file або --directory, усі вони "
"повинні мати однаковий базовий каталог\n"

#: src/MirrorJob.cc:2160
#, c-format
msgid "%s: ambiguous source directory (`%s' or `%s'?)\n"
msgstr "%s: неоднозначний початковий каталог («%s» чи «%s»?)\n"

#: src/MirrorJob.cc:2182
#, c-format
msgid "%s: ambiguous target directory (`%s' or `%s'?)\n"
msgstr "%s: неоднозначний каталог призначення («%s» чи «%s»?)\n"

#: src/MirrorJob.cc:2223
#, c-format
msgid "%s: source directory is required (mirror:require-source is set)\n"
msgstr "%s: слід вказати каталог джерела (встановлено mirror:require-source)\n"

#: src/MirrorJob.cc:2343
msgid ""
"\n"
"Mirror specified remote directory to local directory\n"
"\n"
" -R, --reverse          reverse mirror (put files)\n"
"Lots of other options are documented in the man page lftp(1).\n"
"\n"
"When using -R, the first directory is local and the second is remote.\n"
"If the second directory is omitted, basename of the first directory is "
"used.\n"
"If both directories are omitted, current local and remote directories are "
"used.\n"
"\n"
"See the man page lftp(1) for a complete documentation.\n"
msgstr ""
"\n"
"Дзеркало, яке визначає віддалений каталог для локального каталогу\n"
"\n"
" -R, --reverse          зворотне дзеркало (запис файлів)\n"
"Документацію з інших параметрів можна знайти на сторінці підручника з "
"lftp(1).\n"
"\n"
"Якщо використовується -R, першим каталогом є локальний каталог, а другом — "
"віддалений.\n"
"Якщо другий каталог не вказано, використовується базова назва першого "
"каталогу.\n"
"Якщо не вказано обидва каталоги, використовуватимуться поточні локальний і "
"віддалений каталоги.\n"
"\n"
"Із повною документацією можна ознайомитися на сторінці підручника з "
"lftp(1).\n"

#: src/mkdirJob.cc:128
#, c-format
msgid "%s ok, `%s' created\n"
msgstr "%s виконано, створено «%s»\n"

#: src/mkdirJob.cc:130 src/rmJob.cc:51
#, c-format
msgid "%s failed for %d of %d director$y|ies$\n"
msgstr "Помилка %s для %d з %d каталог$у|ів$\n"

#: src/mkdirJob.cc:133
#, c-format
msgid "%s ok, %d director$y|ies$ created\n"
msgstr "Виконано %s, створено %d каталог$|ів$\n"

#: src/module.cc:190
#, c-format
msgid "depend module `%s': %s\n"
msgstr "модуль залежності «%s»: %s\n"

#: src/module.cc:210
msgid "modules are not supported on this system"
msgstr "у цій системі не передбачено підтримки модулів"

#: src/mvJob.cc:92
#, c-format
msgid "rename successful\n"
msgstr "успішне перейменування\n"

#: src/NetAccess.cc:168
#, c-format
msgid "Connecting to %s%s (%s) port %u"
msgstr "Встановлюємо з’єднання з %s%s (%s), порт %u"

#: src/NetAccess.cc:224 src/Torrent.cc:3326
#, c-format
msgid "Timeout - reconnecting"
msgstr "Перевищено час очікування. Повторна спроба встановлення з’єднання."

#: src/NetAccess.cc:323
msgid "Connection limit reached"
msgstr "Перевищено обмеження на кількість спроб"

#: src/NetAccess.cc:330
msgid "Delaying before reconnect"
msgstr "Виконуємо затримку перед повторним з’єднанням"

#: src/NetAccess.cc:354 src/NetAccess.cc:356
msgid "max-retries exceeded"
msgstr ""
"Перевищено значення максимальної кількості повторних спроб (max-retries)"

#: src/OutputJob.cc:145
#, c-format
msgid "%s (filter)"
msgstr "%s (фільтрування)"

#: src/parsecmd.cc:290
msgid "parse: missing filter command\n"
msgstr "parse: не вказано команди фільтрування\n"

#: src/parsecmd.cc:292
msgid "parse: missing redirection filename\n"
msgstr "parse: не вказано назви файла переспрямування\n"

#: src/PatternSet.cc:110
#, c-format
msgid "regular expression `%s': %s"
msgstr "формальний вираз «%s»: %s"

#: src/pgetJob.cc:127
msgid "pget: falling back to plain get"
msgstr "pget: повертаємося до звичайного get"

#: src/pgetJob.cc:131
msgid "the target file is remote"
msgstr "файл призначення є віддаленим"

#: src/pgetJob.cc:136
msgid "the source file size is unknown"
msgstr "розмір початкового файла є невідомим"

#: src/pgetJob.cc:173
#, c-format
msgid "pget: warning: space allocation for %s (%lld bytes) failed: %s\n"
msgstr ""
"pget: попередження: спроба отримання об'єму пам'яті для %s (%lld байтів) "
"завершилася невдало: %s\n"

#: src/pgetJob.cc:234
#, c-format
msgid "`%s', got %lld of %lld (%d%%) %s%s"
msgstr "«%s», отримано %lld з %lld (%d%%) %s%s"

#: src/plural.c:96
msgid "=1 =0|>1"
msgstr "%100!11%10=1 %100>1<5|%100>20%10>1<5"

#: src/PollVec.cc:69
#, c-format
msgid "%s: BUG - deadlock detected\n"
msgstr "%s: вада — виявлено нерозв’язне блокування\n"

#: src/PtyShell.cc:68
msgid "pseudo-tty allocation failed: "
msgstr "Не вдалося розмістити pseudo-tty: "

#: src/QueueFeeder.cc:68
msgid "Added job$|s$"
msgstr "Додано завдання$||$"

#: src/QueueFeeder.cc:164 src/QueueFeeder.cc:185
#, c-format
msgid "No queued jobs.\n"
msgstr "У черзі немає завдань.\n"

#: src/QueueFeeder.cc:166
#, c-format
msgid "No queued job #%i.\n"
msgstr "У черзі немає завдання з номером %i.\n"

#: src/QueueFeeder.cc:171 src/QueueFeeder.cc:192
msgid "Deleted job$|s$"
msgstr "Вилучено завдання$||$"

#: src/QueueFeeder.cc:187
#, c-format
msgid "No queued jobs match \"%s\".\n"
msgstr "У черзі немає завдань, що відповідають ключу пошуку «%s».\n"

#: src/QueueFeeder.cc:212 src/QueueFeeder.cc:230
msgid "Moved job$|s$"
msgstr "Пересунуто завдання$||$"

#: src/QueueFeeder.cc:354
msgid "Commands queued:"
msgstr "Команди у черзі:"

#: src/ResMgr.cc:123
msgid "no such variable"
msgstr "такої змінної немає"

#: src/ResMgr.cc:127
msgid "ambiguous variable name"
msgstr "неоднозначна назва змінної"

#: src/ResMgr.cc:335
msgid "invalid boolean value"
msgstr "некоректне булеве значення"

#: src/ResMgr.cc:357
msgid "invalid boolean/auto value"
msgstr "некоректне булеве або автоматичне значення"

#: src/ResMgr.cc:402 src/ResMgr.cc:713
msgid "invalid number"
msgstr "некоректне число"

#: src/ResMgr.cc:415
msgid "invalid floating point number"
msgstr "некоректне число з рухомою крапкою"

#: src/ResMgr.cc:429
msgid "invalid unsigned number"
msgstr "некоректне додатне число"

#: src/ResMgr.cc:678
msgid "Invalid time unit letter, only [smhd] are allowed."
msgstr ""
"Некоректна літера одиниці часу, можна використовувати лише літери з набору "
"[smhd]."

#: src/ResMgr.cc:686
msgid "Invalid time format. Format is <time><unit>, e.g. 2h30m."
msgstr ""
"Некоректний формат часу. Час має бути вказано так: <час><одиниця>. Приклад: "
"2h30m."

#: src/ResMgr.cc:718
msgid "integer overflow"
msgstr "переповнення типу цілого значення"

#: src/ResMgr.cc:804
msgid "Invalid IPv4 numeric address"
msgstr "Некоректна числова адреса IPv4"

#: src/ResMgr.cc:814
msgid "Invalid IPv6 numeric address"
msgstr "Некоректна числова адреса IPv6"

#: src/ResMgr.cc:884 src/ResMgr.cc:888
msgid "this encoding is not supported"
msgstr "підтримки цього кодування не передбачено"

#: src/ResMgr.cc:894
msgid "no closure defined for this setting"
msgstr "не визначено замикання для цього параметра"

#: src/ResMgr.cc:900
msgid "a closure is required for this setting"
msgstr "має бути визначено замикання для цього параметра"

#: src/Resolver.cc:236
msgid "host name resolve timeout"
msgstr "перевищено час очікування на визначення адреси вузла"

#: src/Resolver.cc:282
#, c-format
msgid "%d address$|es$ found"
msgstr "виявлено %d адрес$у|и|$"

#: src/Resolver.cc:327
msgid "Link-local IPv6 address should have a scope"
msgstr "Локальна адреса посилання IPv6 має містити область"

#: src/Resolver.cc:765
msgid "DNS resolution not trusted."
msgstr "Визначення DNS не є довіреним."

#: src/Resolver.cc:871
msgid "Host name lookup failure"
msgstr "Помилка під час визначення назви вузла"

#: src/Resolver.cc:903
#, c-format
msgid "no such %s service"
msgstr "немає служби %s"

#: src/Resolver.cc:930
msgid "No address found"
msgstr "Не знайдено жодної адреси"

#: src/resource.cc:50 src/resource.cc:108
msgid "Proxy protocol unsupported"
msgstr "Підтримки протоколу проксі-сервера не передбачено"

#: src/resource.cc:54
msgid "ftp:proxy password: "
msgstr "Пароль ftp:proxy: "

#: src/resource.cc:70
#, c-format
msgid "%s must be one of: "
msgstr "%s має бути одним з таких значень: "

#: src/resource.cc:72
msgid "must be one of: "
msgstr "мало бути одне зі значень: "

#: src/resource.cc:84
msgid ", or empty"
msgstr ", або порожнє значення"

#: src/resource.cc:116
msgid "only PUT and POST values allowed"
msgstr "можна використовувати лише значення PUT і POST"

#: src/resource.cc:148
#, c-format
msgid "unknown address family `%s'"
msgstr "невідоме сімейство адрес, «%s»"

#: src/rmJob.cc:47
#, c-format
msgid "%s ok, `%s' removed\n"
msgstr "%s виконано, «%s» вилучено\n"

#: src/rmJob.cc:54
#, c-format
msgid "%s failed for %d of %d file$|s$\n"
msgstr "%s не вдалося виконати для %d з %d файл$а|ів$\n"

#: src/rmJob.cc:60
#, c-format
msgid "%s ok, %d director$y|ies$ removed\n"
msgstr "%s виконано, вилучено %d каталог$y|ів$\n"

#: src/rmJob.cc:63
#, c-format
msgid "%s ok, %d file$|s$ removed\n"
msgstr "%s виконано, вилучено %d файл$|ів$\n"

#: src/SFtp.cc:1099 src/SFtp.cc:1100
#, c-format
msgid "invalid server response format"
msgstr "некоректний формат відповіді сервера"

#: src/SleepJob.cc:99
msgid "Sleeping forever"
msgstr "Необмежена у часі бездіяльність"

#: src/SleepJob.cc:100
msgid "Sleep time left: "
msgstr "Залишилось часу бездіяльність: "

#: src/SleepJob.cc:108
#, c-format
msgid "\tRepeat count: %d\n"
msgstr "\tЛічильник повторів: %d\n"

#: src/SleepJob.cc:142
#, c-format
msgid "%s: argument required. "
msgstr "%s: слід вказати аргумент. "

#: src/SleepJob.cc:262
#, c-format
msgid "%s: date-time specification missed\n"
msgstr "%s: не вказано специфікації дати і часу\n"

#: src/SleepJob.cc:269
#, c-format
msgid "%s: date-time parse error\n"
msgstr "%s: помилка під час спроби обробити значення дати і часу\n"

#: src/SleepJob.cc:303
msgid ""
"Usage: sleep <time>[unit]\n"
"Sleep for given amount of time. The time argument can be optionally\n"
"followed by unit specifier: d - days, h - hours, m - minutes, s - seconds.\n"
"By default time is assumed to be seconds.\n"
msgstr ""
"Користування: sleep <час>[одиниця]\n"
"Призупинити роботу на вказаний період часу. Разом з числовим аргументом "
"можна\n"
"вказати одиницю виміру: d — дні, h - години, m - хвилини, s - секунди.\n"
"Типовою одиницею часу є секунди.\n"

#: src/SleepJob.cc:310
msgid ""
"Repeat specified command with a delay between iterations.\n"
"Default delay is one second, default command is empty.\n"
" -c <count>  maximum number of iterations\n"
" -d <delay>  delay between iterations\n"
" --while-ok  stop when command exits with non-zero code\n"
" --until-ok  stop when command exits with zero code\n"
" --weak      stop when lftp moves to background.\n"
msgstr ""
"Повторити виконання вказаної команди із вказаною затримкою між\n"
"повтореннями.\n"
"Типовою є затримка у одну секунду. Типовою є порожня команда.\n"
" -c <число>  максимальна кількість повторень\n"
" -d <затримка> затримка між повтореннями\n"
" --while-ok  зупинитися, коли команда поверне ненульовий стан виходу\n"
" --until-ok  зупинитися, коли команда поверне нульовий стан виходу\n"
" --weak      зупинитися, коли lftp буде переведено у фоновий режим.\n"

#: src/Speedometer.cc:90
#, c-format
msgid "%.0fb/s"
msgstr "%.0f Б/с"

#: src/Speedometer.cc:93
#, c-format
msgid "%.1fK/s"
msgstr "%.1f КБ/с"

#: src/Speedometer.cc:96
#, c-format
msgid "%.2fM/s"
msgstr "%.2f МБ/с"

#: src/Speedometer.cc:103
#, c-format
msgid "%.0f B/s"
msgstr "%.0f Б/с"

#: src/Speedometer.cc:105
#, c-format
msgid "%.1f KiB/s"
msgstr "%.1f КіБ/с"

#: src/Speedometer.cc:107
#, c-format
msgid "%.2f MiB/s"
msgstr "%.2f МіБ/с"

#: src/Speedometer.cc:129
msgid "eta:"
msgstr "оцінка часу завершення:"

#: src/SSH_Access.cc:97
msgid "Password required"
msgstr "Потрібен пароль"

#: src/SSH_Access.cc:102
msgid "Login incorrect"
msgstr "Помилкове ім’я користувача"

#: src/SSH_Access.cc:196
#, c-format
msgid "Disconnecting"
msgstr "Від'єднуємося"

#: src/SysCmdJob.cc:73
#, c-format
msgid "execlp(%s) failed: %s\n"
msgstr "Помилка execlp(%s): %s\n"

#: src/TimeDate.cc:155
msgid "day"
msgstr "день"

#: src/TimeDate.cc:156
msgid "hour"
msgstr "година"

#: src/TimeDate.cc:157
msgid "minute"
msgstr "хвилина"

#: src/TimeDate.cc:158
msgid "second"
msgstr "секунда"

#: src/Torrent.cc:585
msgid "announced via "
msgstr "оголошено через "

#: src/Torrent.cc:599
#, c-format
msgid "next announce in %s"
msgstr "наступне оголошення за %s"

#: src/Torrent.cc:1142
#, c-format
msgid "%d file$|s$ found, now scanning %s"
msgstr "Знайдено %d файл$|и|ів$, тепер скануємо %s"

#: src/Torrent.cc:1143
#, c-format
msgid "%d file$|s$ found"
msgstr "виявлено %d файл$|и|ів$"

#: src/Torrent.cc:2075 src/Torrent.cc:2085
#, c-format
msgid "Getting meta-data: %s"
msgstr "Отримання метаданих: %s"

#: src/Torrent.cc:2077
#, c-format
msgid "Validation: %u/%u (%u%%) %s%s"
msgstr "Перевірка: %u/%u (%u%%) %s%s"

#: src/Torrent.cc:2088
msgid "Waiting for meta-data..."
msgstr "Очікуємо на метадані…"

#: src/Torrent.cc:2096
msgid "Shutting down: "
msgstr "Завершення роботи: "

#: src/Torrent.cc:3207
#, c-format
msgid "Connecting to peer %s port %u"
msgstr "Встановлюємо з’єднання з вузлом %s, порт %u"

#: src/Torrent.cc:3258 src/Torrent.cc:3375
#, c-format
msgid "peer unexpectedly closed connection after %s"
msgstr "вузол несподівано розірвав з’єднання після %s"

#: src/Torrent.cc:3259 src/Torrent.cc:3376
msgid "peer unexpectedly closed connection"
msgstr "з’єднання несподівано розірвано віддаленим вузлом"

#: src/Torrent.cc:3261 src/Torrent.cc:3262
#, c-format
msgid "peer closed connection (before handshake)"
msgstr ""
"з’єднання перервано з ініціативи віддаленого комп’ютера (до початкового "
"обміну даними)"

#: src/Torrent.cc:3265 src/Torrent.cc:3378 src/Torrent.cc:3379
#, c-format
msgid "invalid peer response format"
msgstr "некоректний формат відповіді вузла"

#: src/Torrent.cc:3360 src/Torrent.cc:3361
#, c-format
msgid "peer closed connection"
msgstr "з’єднання перервано з ініціативи віддаленого комп’ютера"

#: src/Torrent.cc:3538
msgid "Handshaking..."
msgstr "Виконуємо початковий обмін даними…"

#: src/Torrent.cc:3798
msgid "Cannot bind a socket for torrent:port-range"
msgstr "Не вдалося пов’язати сокет для torrent:port-range"

#: src/Torrent.cc:3858
#, c-format
msgid "Accepted connection from [%s]:%d"
msgstr "Прийнято пропозицію щодо з’єднання від [%s]:%d"

#: src/Torrent.cc:3924
#, c-format
msgid "peer sent unknown info_hash=%s in handshake"
msgstr ""
"віддаленим вузлом надіслано невідоме значення info_hash=%s під час "
"узгодження зв’язку"

#: src/Torrent.cc:3948
#, c-format
msgid "peer handshake timeout"
msgstr "перевищення часу початкового етапу обміну даними"

#: src/Torrent.cc:3960
#, c-format
msgid "peer short handshake"
msgstr ""
"під час початкового обміну даними з віддаленим вузлом отримано обрізане "
"повідомлення"

#: src/Torrent.cc:3962
#, c-format
msgid "peer closed just accepted connection"
msgstr "вузлом розірвано щойно встановлене з’єднання"

#: src/Torrent.cc:4013
#, c-format
msgid "Seeding in background...\n"
msgstr "Поширення у фоновому режимі...\n"

#: src/Torrent.cc:4176
#, c-format
msgid "%s: --share conflicts with --output-directory.\n"
msgstr "%s: --share конфліктує з --output-directory.\n"

#: src/Torrent.cc:4180
#, c-format
msgid "%s: --share conflicts with --only-new.\n"
msgstr "%s: --share конфліктує з --only-new.\n"

#: src/Torrent.cc:4184
#, c-format
msgid "%s: --share conflicts with --only-incomplete.\n"
msgstr "%s: --share конфліктує з --only-incomplete.\n"

#: src/Torrent.cc:4226
#, c-format
msgid "%s: Please specify a file or directory to share.\n"
msgstr ""
"%s: будь ласка, вкажіть файл або каталог, який слід надати у спільне "
"користування.\n"

#: src/Torrent.cc:4228
#, c-format
msgid "%s: Please specify meta-info file or URL.\n"
msgstr "%s: будь ласка, вкажіть файл метаданих або адресу.\n"

#: src/Torrent.cc:4258
msgid ""
"Start BitTorrent job for the given torrent-files, which can be a local "
"file,\n"
"URL, magnet link or plain info_hash written in hex or base32. Local "
"wildcards\n"
"are expanded. Options:\n"
" -O <base>      specifies base directory where files should be placed\n"
" --force-valid  skip file validation\n"
" --dht-bootstrap=<node>  bootstrap DHT by sending a query to the node\n"
" --share        share specified file or directory\n"
msgstr ""
"Запустити завдання BitTorrent для вказаних файлів торентів, якими можуть "
"бути локальні\n"
"файли, адреси, маґнет-посилання або звичайні info_hash у форматі hex або "
"base32. Локальні\n"
"замінники каталогів буде розгорнуто. Параметри:\n"
" -O <каталог>   вказати базовий каталог для зберігання файлів\n"
" --force-valid  пропустити перевірку файлів\n"
" --dht-bootstrap=<вузол>  оновити DHT надсиланням запиту до вказаного вузла\n"
" --share        поширити вказаний файл або каталог\n"

#: src/TorrentTracker.cc:178
msgid "not started"
msgstr "не розпочато"

#: src/TorrentTracker.cc:181
#, c-format
msgid "next request in %s"
msgstr "наступний запит за %s"

#: src/TorrentTracker.cc:284 src/TorrentTracker.cc:501
#, c-format
msgid "Received valid info about %d peer$|s$"
msgstr "Отримано коректні дані щодо %d вузл$а|ів$"

#: src/TorrentTracker.cc:298
#, c-format
msgid "Received valid info about %d IPv6 peer$|s$"
msgstr "Отримано коректні дані щодо %d вузл$а|ів$ IPv6"

#~ msgid "%s: option '--%s' doesn't allow an argument\n"
#~ msgstr "%s: додавання аргументів до параметра «--%s» не передбачено\n"

#~ msgid "%s: unrecognized option '--%s'\n"
#~ msgstr "%s: невідомий параметр «--%s»\n"

#~ msgid "%s: option '-W %s' is ambiguous\n"
#~ msgstr "%s: параметр «-W %s» не є однозначним\n"

#~ msgid "%s: option '-W %s' doesn't allow an argument\n"
#~ msgstr "%s: додавання аргументів до параметра «-W %s» не передбачено\n"

#~ msgid "%s: option '-W %s' requires an argument\n"
#~ msgstr "%s: до параметра «-W %s» слід додати аргумент\n"

#~ msgid "Usage: mv <file1> <file2>\n"
#~ msgstr "Користування: mv <файл1> <файл2>\n"

#, fuzzy
#~ msgid "number"
#~ msgstr "некоректне число"

#, fuzzy
#~ msgid ""
#~ "\n"
#~ "Mirror specified remote directory to local directory\n"
#~ "\n"
#~ " -c, --continue         continue a mirror job if possible\n"
#~ " -e, --delete           delete files not present at remote site\n"
#~ "     --delete-first     delete old files before transferring new ones\n"
#~ " -s, --allow-suid       set suid/sgid bits according to remote site\n"
#~ "     --allow-chown      try to set owner and group on files\n"
#~ "     --ignore-time      ignore time when deciding whether to download\n"
#~ " -n, --only-newer       download only newer files (-c won't work)\n"
#~ " -r, --no-recursion     don't go to subdirectories\n"
#~ " -p, --no-perms         don't set file permissions\n"
#~ "     --no-umask         don't apply umask to file modes\n"
#~ " -R, --reverse          reverse mirror (put files)\n"
#~ " -L, --dereference      download symbolic links as files\n"
#~ " -N, --newer-than=SPEC  download only files newer than specified time\n"
#~ " -P, --parallel[=N]     download N files in parallel\n"
#~ " -i RX, --include RX    include matching files\n"
#~ " -x RX, --exclude RX    exclude matching files\n"
#~ "                        RX is extended regular expression\n"
#~ " -v, --verbose[=N]      verbose operation\n"
#~ "     --log=FILE         write lftp commands being executed to FILE\n"
#~ "     --script=FILE      write lftp commands to FILE, but don't execute "
#~ "them\n"
#~ "     --just-print, --dry-run    same as --script=-\n"
#~ "\n"
#~ "When using -R, the first directory is local and the second is remote.\n"
#~ "If the second directory is omitted, basename of first directory is used.\n"
#~ "If both directories are omitted, current local and remote directories are "
#~ "used.\n"
#~ "See lftp(1) for a complete list of options.\n"
#~ msgstr ""
#~ "\n"
#~ "Створити дзеркальну копію віддаленого каталогу у локальному каталозі\n"
#~ "\n"
#~ " -c, --continue         продовжити створення копії, якщо це можливо\n"
#~ " -e, --delete           вилучати файли, яких немає у віддаленій копії\n"
#~ "     --delete-first     вилучати застарілі файли до перенесення нових\n"
#~ " -s, --allow-suid       встановлювати біти suid/sgid у відповідності до\n"
#~ "                         бітів віддаленої копії\n"
#~ "     --allow-chown      намагатися зберегти дані щодо власника і групи\n"
#~ "     --ignore-time      ігнорувати позначки часу під час визначення "
#~ "потрібних                          файлів\n"
#~ " -n, --only-newer       отримувати лише новіші файли (-c не працюватиме)\n"
#~ " -r, --no-recursion     не переходити до підкаталогів\n"
#~ " -p, --no-perms         не встановлювати права доступу\n"
#~ "     --no-umask         не застосовувати umask до режимі доступу до "
#~ "файлів\n"
#~ " -R, --reverse          зворотне створення дзеркальної копії "
#~ "(вивантаження)\n"
#~ " -L, --dereference      отримувати символічні посилання як файли\n"
#~ " -N, --newer-than=ЧАС   отримувати лише файли з новішою за вказану "
#~ "позначкою\n"
#~ "                         часу\n"
#~ " -P, --parallel[=N]     отримувати до N файлів паралельно\n"
#~ " -i RX, --include RX    включити відповідні файли\n"
#~ " -x RX, --exclude RX    виключити відповідні файли\n"
#~ "                        RX має бути розширеним формальним виразом\n"
#~ " -v, --verbose[=N]      режим докладних повідомлень\n"
#~ "     --log=ФАЙЛ         записувати виконані команди lftp до файла ФАЙЛ\n"
#~ "     --script=ФАЙЛ      записувати команди lftp до файла ФАЙЛ, але не "
#~ "виконувати\n"
#~ "     --just-print, --dry-run    те саме, що --script=-\n"
#~ "\n"
#~ "Якщо використано -R, першим каталогом вказують локальний, а другим — "
#~ "віддалений.\n"
#~ "Якщо не вказано другий каталог, використовуватиметься основний шлях до "
#~ "першого.\n"
#~ "Якщо не вказано обох каталогів, буде використано поточні локальний і "
#~ "віддалений каталоги.\n"

#~ msgid "SITE CHMOD is disabled by ftp:use-site-chmod"
#~ msgstr "SITE CHMOD вимкнено ftp:use-site-chmod"

#~ msgid ""
#~ "ftp:proxy-auth-type must be one of: user, joined, joined-acct, open, "
#~ "proxy-user@host"
#~ msgstr ""
#~ "значенням ftp:proxy-auth-type має бути один з таких рядків: user, joined, "
#~ "joined-acct, open, proxy-user@host"

#~ msgid "ftp:ssl-auth must be one of: SSL, TLS, TLS-P, TLS-C"
#~ msgstr "ftp:ssl-auth мало бути одним зі значень: SSL, TLS, TLS-P, TLS-C"
