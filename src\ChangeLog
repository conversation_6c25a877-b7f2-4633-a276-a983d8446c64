2014-05-06  <PERSON> <<EMAIL>>

	* DHT.cc: prefer fresh node over non-good and not responded one.
	* xarray.h: fixed a typo which could cause segfault.
	* FileAccess.cc, FileAccess.h, src/Fish.cc, src/Fish.h, src/Http.cc,
	  Http.h, NetAccess.cc, SFtp.cc, SFtp.h, SSH_Access.cc, SSH_Access.h,
	  ftpclass.cc, ftpclass.h: add last_disconnect_cause; display it
	  for 5 seconds in the status.

2014-04-09  <PERSON> <<EMAIL>>

	* SSH_Access.cc, SSH_Access.h: display last error message from ssh.

2014-04-07  <PERSON> <<EMAIL>>

	* misc.cc: add EIO and EPIPE to the list of temp network errors.
	* FileAccess.cc, FileAccess.h, Http.cc, NetAccess.cc, ftpclass.cc,
	  ftpclass.h: replace try_time with Timer reconnect_timer;
	  use temporary_network_error in FA::NotSerious.
	* SSH_Access.cc: detect name lookup error from ssh.

2014-03-26  <PERSON> <<EMAIL>>

	* Job.cc, Job.h: (FormatShortStatus) new virtual method; don't check
	  Deleted() for jobs in the lists; use xlist_for_each_safe when it's
	  possible to remove scanned node from the list.
	* MirrorJob.cc, MirrorJob.h: implement FormatShortStatus.
	* CopyJob.cc: use Speedometer::GetStrProper for final rate reporting.

2014-03-25  Alexander V. Lukyanov <<EMAIL>>

	* pgetJob.h: fixed bytes and rate reporting via Get methods.
	* Job.cc, Job.h: use xlist; add children_jobs list; (NumberOfChildrenJobs) new method.
	* commands.cc, CmdExec.cc: use NumberOfChildrenJobs to avoid counting "this" Job.

2014-03-24  Alexander V. Lukyanov <<EMAIL>>

	* buffer.cc: (MoveDataHere) set eof only when all data are exhausted.
	* misc.cc, misc.h: (xhuman) new function.
	* Job.cc, Job.h: (GetTransferRate) new method;
	  (GetBytesCount) count recursively over descendants.
	* CopyJob.cc, CopyJob.h, FileCopy.h: (GetTransferRate) implement;
	  (GetTimeSpent) don't count overlapped time intervals twice;
	  (GetBytesCount) count recursively for real time monitoring.
	* MirrorJob.cc, MirrorJob.h: show real-time bytes transferred and
	  rate in the mirror job title.
	* HttpDir.cc: try to parse as plain html when props format is unrecognized.

2014-03-18  Sami Farin <<EMAIL>>

	* FileSet.cc, FtpDirList.cc, Http.cc, PollVec.cc, complete.cc, lftp.cc:
	  fix warnings

2014-03-18  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.h, mkdirJob.h, FindJob.h: don't access the
	  FileAccessRefC ref in dtor, as the original FileAccessRef can be
	  already deleted.
	* FindJob.cc: don't add slash for plain files in URLs.

2014-03-17  Alexander V. Lukyanov <<EMAIL>>

	* resource.cc: add hftp:decode setting.
	* xarray.cc, xarray.h: use xrealloc instead of realloc; truncate
	  xarray_m in dtor, so that proper dispose method is called.
	* xmap.cc: use xfree for xmalloc'ed memory.

2014-03-14  Alexander V. Lukyanov <<EMAIL>>

	* SSH_Access.cc: add another password prompt format for FreeBSD.

2014-03-13  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.cc, FileAccess.h: get rid of direct delete calls.

2014-03-11  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc, resource.cc: (http:accept-encoding, http:decode) new settings.

2014-03-04  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc, CmdExec.h, commands.cc: keep track of parent CmdExec;
	  add 'exit parent' subcommand; flush command buffer when exiting.
	* SFtp.cc: don't send FSETSTAT when file modification time is unknown.

2014-02-13  Alexander V. Lukyanov <<EMAIL>>

	* xarray.h, StringSet.h: (xarray_m) new template for malloc'ed
	  pointers; use it in StringSet.
	* xmap.h: (dispose) new method.

2014-02-12  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: don't call ExpandTildeInCWD every time.
	* Http.cc: use a perfect hash for header handling.
	* MirrorJob.cc: clear source/target sets when looping.

2014-02-11  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc, MirrorJob.h: fix transfer rate reporting for parallel
	  mode mirror (--parallel option).
	* commands.cc: (--env-password) new open option to take password from
	  LFTP_PASSWORD environment variable.

2014-02-04  Alexander V. Lukyanov <<EMAIL>>

	* ResMgr.cc, ResMgr.h, resource.cc: move some methods to ResType;
	  make ResMgr a descendant of ResType; make type_value_list a pointer
	  to avoid calling a ctor for each static ResType.
	* Torrent.cc: use ~/.lftp/torrent/md directory; fix file permissions.
	* FileAccess.cc, FileAccess.h: use xlist and xmap to speed up FA
	  creation and deleting.

2014-01-24  Alexander V. Lukyanov <<EMAIL>>

	* pgetJob.cc: (pget:min-chunk-size) new setting with 1M by default.

2014-01-23  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.cc, commands.cc, history.cc: don't add a slash if it's
	  already there.
	* xmap.cc: slightly better hash.
	* xarray.h: (xarray_p::qsort) new method.
	* StringSet.h: (StringSet::qsort) new method.
	* ResMgr.cc, ResMgr.h, Http.cc, resource.cc: use xlist and xmap to
	  manage settings (for better performance).
	* Torrent.cc, Torrent.h: save metadata and load if needed;
	  (torrent:save-metadata) new setting.

2014-01-17  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: allow UTC along with GMT in http timestamps.
	* Http.cc: fixed Content-Length handling after PROPFIND.

2014-01-16  Alexander V. Lukyanov <<EMAIL>>

	* DHT.cc: prefer responded nodes in search over not responded ones.
	* Torrent.cc, Torrent.h: add shutting_down_timer to fix a hang on
	  unresponsive trackers.
	* TorrentTracker.h: use 30 if interval<30.

2014-01-16  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: fixed error handling for new _Read semantics.

2014-01-15  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: fixed rmdir for WebDAV by adding trailing slash to the path.

2014-01-10  Alexander V. Lukyanov <<EMAIL>>

	* Torrent.cc, Torrent.h: use SMTaskRef for FDCache.

2013-12-13  Alexander V. Lukyanov <<EMAIL>>

	* RateLimit.cc, RateLimit.h, ftpclass.cc: use RateLimit settings
	  to set maximum buffer size.
	* xstring.cc, xstring.h: (swap, set_length_no_z) new methods.
	* buffer.cc, buffer.h: (MoveDataHere) new method, optimize data
	  copying by swapping buffers.
	* FileCopy.cc: tune read delaying; add comments.
	* DummyProto.cc, DummyProto.h, FileAccess.h, FileCopy.cc, Fish.cc,
	  Fish.h, Http.cc, Http.h, LocalAccess.cc, LocalAccess.h, SFtp.cc,
	  SFtp.h, example1.cc, example2.cc, ftpclass.cc, ftpclass.h:
	  change FA::Read to use Buffer and MoveDataHere.
	* PollVec.cc, PollVec.h: (FDSetNotReady) new method.

2013-12-12  Alexander V. Lukyanov <<EMAIL>>

	* TimeDate.h: include sys/time.h to get struct timeval.
	* buffer.cc: don't shrink read buffer size.
	* ftpclass.cc, ftpclass.h: use xfer:buffer-size for max_buf size;

2013-12-11  Alexander V. Lukyanov <<EMAIL>>

	* xstring.cc, xstring.h: shrink buffer explicitly.
	* PollVec.cc, PollVec.h: use select; (FDReady) new method.
	* TimeDate.cc, TimeDate.h: change to microsecond resolution.
	* Timer.cc, Timer.h: (GetTimeoutTV) new method.
	* SMTask.cc, SMTask.h: use Timer::GetTimeoutTV; add resumed task to
	  the tail of new_tasks list.
	* buffer.cc, buffer.h: change buffer size tuning; remove last_got member;
	  don't call down->Do in BufferStacked; use last select result
	  to save a read() system call.
	* ftpclass.h: don't suspend control_recv so that control_sock gets
	  polled when delaying data read ops.
	* CharReader.cc: check last select result before reading.
	* FileCopy.cc: use microseconds for delaying reads; don't reset delay
	  on data unavailability; don't increase delay when read returned
	  enough data.

2013-12-09  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc, FileCopy.h: add a delay when Read returns too small
	  portions of data, it should decrease the number of syscalls.
	* Bencode.cc: fixed a mem leak (thanks to David Binderman).
	* FileCopyFtp.cc, FileCopy.cc: minor style changes.
	* ResMgr.cc, HttpDir.cc: remove unused old code.
	* log.cc: check Log::global for NULL, avoid SEGV on exit.

2013-12-06  Alexander V. Lukyanov <<EMAIL>>

	* xheap.h: min-heap implementation with random remove.
	* Timer.cc, Timer.h, Makefile.am: use xheap and xlist for Timer.

2013-12-05  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc, Http.h: fixed ARRAY_INFO for the case of PROPFIND;
	  use symbolic error codes.

2013-12-04  Alexander V. Lukyanov <<EMAIL>>

	* Torrent.cc: use Delete method for peers.
	* MirrorJob.cc, MirrorJob.h: use SMTaskRef for ListInfo.
	* Speedometer.cc, Speedometer.h: don't inherit SMTask.
	* FileCopy.cc, FileCopy.h: don't use SMTaskRef for Speedometer.
	* SMTask.h: don't inherit SMTaskRef from Ref, make SMTask dtor
	  protected.
	* buffer.h: use plain Ref for Speedometer; make IOBuffer dtor protected.
	* FileAccess.h, ProcWait.h: make SMTask heirs dtors protected.

2013-12-03  Alexander V. Lukyanov <<EMAIL>>

	* buffer.cc, buffer.h: (TuneGetSize) new method.
	* ProtoLog.cc: check Log::global for NULL.
	* FileCopy.cc, FileCopy.h: use TuneGetSize, fixed `verify' type.
	* FindJobDu.h, Fish.h, Http.h, HttpDir.h, PtyShell.h, SFtp.h:
	  use SMTaskRef for tasks.
	* SMTask.cc, SMTask.h: use new xlist; add `deleted' list; add `new' list.

2013-12-02  Alexander V. Lukyanov <<EMAIL>>

	* SMTask.cc: (CollectGarbage) fixed deleting list manipulation.

2013-11-29  Alexander V. Lukyanov <<EMAIL>>

	* GetPass.cc, complete.cc: don't call SMTask dtor directly.
	* SMTask.cc, SMTask.h: add deleting list; use static array for stack.
	* xarray.cc, xarray.h: optimize space allocation by inlining some code.
	* SMTask.cc: don't spin on collected garbage.
	* DirColors.h: no need to inherit SMTask, use ResClient.
	* SMTask.h: make deleting, running and ref_count private;
	  (Deleted) new method.
	* IdNameCache.cc, Job.cc, ProcWait.cc, Resolver.cc, Torrent.cc:
	  use public methods of SMTask, don't use `deleting' member directly.


2013-11-27  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: fixed ARRAY_INFO in keep-alive http connection.

2013-11-26  Alexander V. Lukyanov <<EMAIL>>

	* CopyJob.cc: show file name in error messages, if not there already.
	* FileCopy.cc: don't check file size when the file is already complete;
	  handle the case of defined limit without file size.
	* FileCopy.cc: use max of get/put positions in size checking, since
	  put_eof_pos is uninitialized yet.

2013-11-25  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: add debug message on chunk sizes; handle 400/501 codes
	  to turn off PROPFIND in ARRAY_INFO mode.
	* FileCopy.cc: clean up GetArrayInfo call condition, don't call it
	  if not needed.
	* OutputJob.cc: free input and output in PrepareToDie.
	* FileCopy.cc, FileCopy.h: add check for file size at EOF.

2013-11-15  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc: fixed spinning when GetArrayInfo gives an error.
	* commands.cc, FindJob.cc, FindJob.h, FileSet.h: add find -l (--ls)
	  option to show long listing.

2013-11-11  Alexander V. Lukyanov <<EMAIL>>

	* Torrent.cc: don't show verbose 100% peer completeness.

2013-11-08  Alexander V. Lukyanov <<EMAIL>>

	* SFtp.cc: check reply type before casting, fixes a coredump.
	* Torrent.cc: improve status of complete torrents by showing ratio.

2013-11-07  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: fixed a hang in ARRAY_INFO mode.

2013-10-10  Alexander V. Lukyanov <<EMAIL>>

	* Torrent.cc: use human_readable for up/dn counters.

2013-10-08  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, resource.cc: (ftp:use-utf8) new setting.
	* buffer.cc: don't start translation if source and target encodings
	  are the same.

2013-10-04  Alexander V. Lukyanov <<EMAIL>>

	* NetAccess.cc: fixed occasional hang in GenericParseListInfo,
	  when result!=NULL && old_mode!=mode.

2013-09-16  Alexander V. Lukyanov <<EMAIL>>

	* buffer.cc: compile in SetTranslator unconditionslly (for Ftp).
	* Http.cc, Http.h: use PROPFIND for ARRAY_INFO if supported.
	* Torrent.cc, Torrent.h, DHT.cc: count dht announces, show the counters
	  in the status; don't count a peer complete until the metadata are
	  known; show next dht announce time.
	* PtyShell.cc: kill the shell in dtor.

2013-08-30  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: handle atotm parse error.
	* TimeDate.cc, TimeDate.h: fixed localtime cacheing.
	* commands.cc: (cd) automatically add slash if needed.
	* LsCache.cc, LsCache.h, NetAccess.cc: (UpdateFileSet) new method; use it.
	* PatternSet.cc, PatternSet.h: (AddFirst, GetFirstType) new methods.
	* MirrorJob.cc, MirrorJob.h: add --file/-f and --target-directory/-O options.
	* buffer.h: allow IOBufferFileAccess creation with plain pointer.
	* Http.cc, Http.h: don't log empty commands;
	  handle PROPFIND result of CHANGE_DIR op; add "quote PROPFIND" support
	  (mostly for debugging); don't send Content-Type for PROPFIND requests
	  if there is no request body; don't request file info if not needed in
	  ARRAY_INFO mode.
	* HttpDir.cc: automatically detect xml file listing.
	* HttpDirXML.cc: handle atotm errors.

2013-08-16  Alexander V. Lukyanov <<EMAIL>>

	* Speedometer.cc, Speedometer.h: (GetStrProper) new method for proper unit names.
	* FileCopy.cc: use Speedometer::GetStrProper for logging.
	* FileCopy.cc, LocalDir.cc: use FD_CLOEXEC on descriptors.
	* FtpListInfo.cc: fixed MLSD parsing for semicolons in file names.
	* FileSet.cc: allow NULL sets in certain places.
	* MirrorJob.cc, MirrorJob.h: don't create empty directories with
	  --no-empty-dirs when all source files are filtered out.

2013-08-13  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, resource.cc: add woraround for wrong multiline
	  replies and ftp:strict-multiline setting.
	* SFtp.cc: add a workaround for RouterOS v6 - symlink as the root dir.

2013-08-05  Alexander V. Lukyanov <<EMAIL>>

	* Torrent.cc: don't decode %xx for plain file names of meta-info.

2013-07-30  Alexander V. Lukyanov <<EMAIL>>

	* SFtp.cc, SFtp.h: request more information about symlink targets.

2013-07-29  Alexander V. Lukyanov <<EMAIL>>

	* SFtp.cc: follow symlinks when requested.

2013-07-23  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc, MirrorJob.h: add progress meter for parent mirrors.
	* Job.cc, Job.h: (GetCmdLine) new virtual method.
	* FileSet.cc, FileSet.h: (CountBytes) new method.
	* ftpclass.cc: move line and all_lines along with connection.

2013-07-15  Alexander V. Lukyanov <<EMAIL>>

	* attach.h: pass all three std fds.
	* lftp.cc: simplify attach/detach logic; get rid of goto;
	  handle Detached condition.

2013-07-04  Alexander V. Lukyanov <<EMAIL>>

	* network.cc: don't use IPV6_V6ONLY if not defined.
	  (from <EMAIL>).
	* FileSet.cc: (FileInfo::Merge) copy size properly.

2013-07-04  Alexander V. Lukyanov <<EMAIL>>

	* FileSet.cc: recognize and ignore human readable sizes.

2013-05-30  Alexander V. Lukyanov <<EMAIL>>

	* lftp_tinfo.cc: fixed segfault when there is no TERM env var.
	* buffer_zlib.cc: fixed compilation with older zlib.

2013-05-28  Alexander V. Lukyanov <<EMAIL>>

	* buffer_zlib.cc, buffer_zlib.h: implement inflator.
	* Http.cc, Http.h: add support for gzip Content-Encoding.
	* Makefile.am: buffer_zlib for proto-http.
	* buffer.cc, buffer.h: (SetTranslator) translate unread data.
	* buffer.h: (IsFull) new method.
	* FileCopy.cc, FileCopy.h: (FileCopyPeerMemory) new peer type;
	  use IOBuffer::SetMaxBuffered and IsFull.
	* Torrent.cc, Torrent.h: use FileCopy for metainfo fetching.

2013-05-27  Alexander V. Lukyanov <<EMAIL>>

	* SFtp.cc: (SFtpListInfo::Do) fixed return value (prevents an endless loop).
	* FileCopy.cc: init date member; clean up date/size requesting in FileCopyPeerFA.

2013-05-21  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc: set size=NO_SIZE when the size could not be found.
	* FileSet.cc: allow info merging for files with the same basename.
	* Fish.cc: fixed file correption and garbage logging by requiring
	  the file to have a size; improve error handling.

2013-05-20  Alexander V. Lukyanov <<EMAIL>>

	* Fish.cc: disconnect in more cases on Close.

2013-05-13  Alexander V. Lukyanov <<EMAIL>>

	* TorrentTracker.cc, TorrentTracker.h: use xstring for InfoHash and
	  PeerId.

2013-05-07  Alexander V. Lukyanov <<EMAIL>>

	* Torrent.cc: fixed NULL dereference when use-dht is off.

2013-04-18  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc, Http.h: improve DirFile; add slash for directories in ARRAY_INFO.
	* FileAccess.cc, FileAccess.h, FileCopy.cc, FileCopy.h, FileSet.cc,
	  FileSet.h, Fish.cc, GetFileInfo.cc, GetFileInfo.h, Http.cc,
	  LocalAccess.cc, NetAccess.cc, NetAccess.h, SFtp.cc, SFtp.h,
	  ftpclass.cc: use FileSet for ARRAY_INFO; add READLINK support in sftp.

2013-04-12  Alexander V. Lukyanov <<EMAIL>>

	* DHT.cc, DHT.h, Torrent.cc, Torrent.h: (DenounceDHT) new method; use it.
	* complete.cc, FileGlob.cc, SFtp.cc: show .. and . in glob results.
	* FileSetOutput.cc: add -a option for completion results display to show
	  dot files.

2013-03-13  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: change reget to "resume transfer".

2013-03-11  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: list all jobs from the top.
	* Job.cc: don't add empty line for no-session jobs.
	* IdNameCache.cc: fixed lookup by name.

2013-02-27  Alexander V. Lukyanov <<EMAIL>>

	* lftp_ssl.cc, resource.cc: add SNI support.
	* commands.cc, NetAccess.cc: add cls -a option; don't filter out
	  . and .. by default.
	* TorrentTracker.cc: Enter/Leave parent for proper debug context.
	* Torrent.cc: use Enter/Leave for proper debug context.
	* Torrent.cc: better status when shutting down.
	* TorrentTracker.{cc,h}: add UDP tracker IPv6 support as action 4;
	  fixed error handling; fixed loop range.

2013-02-26  Alexander V. Lukyanov <<EMAIL>>

	* Torrent.cc, Torrent.h: add numeric key for udp tracker.
	* buffer.cc, buffer.h: (Append(xstring)) new method.
	* TorrentTracker.cc, TorrentTracker.h: add udp tracker support.

2013-02-25  Alexander V. Lukyanov <<EMAIL>>

	* Torrent.cc, Torrent.h, TorrentTracker.cc, TorrentTracker.h:
	  split off TorrentTracker; add TrackerBackend class; prepare
	  for udp tracker implementation.
	* ResMgr.cc: include stdlib.h for strtoll.
	* DHT.cc, DHT.h: add requests_in_flight node counter; clean up
	  nodes more aggresively; make just loaded nodes non-good.

2013-02-20  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: add "jobs" command arguments description.

2013-02-15  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc, CmdExec.h: (cmd:show-status) new setting.

2013-02-07  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc, FileSetOutput.cc, Fish.cc, FtpDirList.cc, Http.cc, HttpDir.cc,
	  OutputJob.cc, SFtp.cc, SMTask.cc, SMTask.h, buffer.cc, ftpclass.cc:
	  add ready task list to optimize Schedule when there are too many suspended
	  tasks.

2013-01-31  Alexander V. Lukyanov <<EMAIL>>

	* xstring.cc, xstring.h: add flags in url_decode.
	* Torrent.cc: pass URL_DECODE_PLUS flag to decode '+' into space.
	* Makefile.am: add uninstall-hook to remove modules and some empty directories.

2013-01-29  Alexander V. Lukyanov <<EMAIL>>

	* xstring.cc: (_url_decode) fixed decoding plus sign.
	* Timer.cc, Timer.h: (StopDelayed) new method.
	* Torrent.cc: don't call AnnounceDHT in StartTrackers, do it
	  only by timer; stop dht_announce_timer at the start; use Roll
	  method to set log context; decode magnet URL values properly.
	* DHT.cc: refresh routes after loading the node cache; load the
	  cache quickly using Roll; add some debug log messages.

2013-01-25  Alexander V. Lukyanov <<EMAIL>>

	* lftp.cc: don't run at-exit-fg when backgrounded.
	* xstring.cc: (append(const char*,size_t)) fixed a bug;
	  (_url_decode) decode plus to space.

2013-01-24  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc, Torrent.cc, attach.h, bookmark.cc, history.cc,
	  lftp.cc, lftp_rl.c, misc.cc, misc.h:
	  (get_lftp_{config,data,cache}_dir) new functions; use them.
	* xdg-move: new script.
	* CmdExec.cc, CmdExec.h, lftp.cc: (cmd:at-exit-fg) new setting.

2012-12-14  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc, CmdExec.h, commands.cc, lftp.cc: make cmd:interactive tri-bool.

2012-12-13  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc: don't show status line unless interactive.

2012-12-04  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: remove obsolete assert.

2012-12-03  Alexander V. Lukyanov <<EMAIL>>

	* Torrent.cc: fixed coredump after trying to start a duplicate torrent.

2012-11-14  Alexander V. Lukyanov <<EMAIL>>

	* Job.cc: fixed status message.
	* buffer.cc, buffer.h: limit maximum buffer size.
	* ftpclass.cc: use buffer size limit; don't Resume on several methods;
	  fixed CWD for certain paths; fixed try count resetting on uploading.

2012-09-27  Alexander V. Lukyanov <<EMAIL>>

	* Torrent.cc: add support for base32 btih in magnet.

2012-09-26  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc: call AtFinish after parsing an empty command.

2012-09-26  Christoph Junghans <<EMAIL>>

	* module.cc: use ".bundle" for Darwin.

2012-09-26  Alexander V. Lukyanov <<EMAIL>>

	* xstring.cc, xstring.h: (ends_with) new method.

2012-09-24  Robert Story <<EMAIL>>

	* Resolver.cc: fix a typo in error message and add a warning for
	  untrusted domains if dns:strict-dnssec is not set.

2012-08-30  Alexander V. Lukyanov <<EMAIL>>

	* lftp_tinfo.cc: fixed termcap coredump.

2012-08-27  Alexander V. Lukyanov <<EMAIL>>

	* DHT.cc, DHT.h: add rate limit; improve FindNodes by checking
	  PrefixMatch on every next route bucket.
	* Torrent.cc, Torrent.h: add name.utf-8 and path.utf-8 support.
	* DHT.cc, DHT.h: remove name (unknown encoding); better printing
	  of route prefix; (DHT::KnownTorrent::AddPeer) new method.

2012-08-24  Alexander V. Lukyanov <<EMAIL>>

	* network.cc: fixed port byte order in compact.
	* Torrent.cc, Torrent.h: split DHT off; save DHT to separate directory;
	  check peer duplication and black-listing after setting port.
	* DHT.cc, DHT.h: new files; send pings only when there is new node
	  candidate and bucket is full; limit ping rate; change disk-cache
	  format, save node_id too; handle changing node_id of remote nodes.
	* network.cc, network.h: (sockaddr_compact) new class.
	* StatusLine.cc: call termcap functions from StatusLine ctor, not _init.

2012-08-22  Alexander V. Lukyanov <<EMAIL>>

	* network.cc: fixed set_compact for ipv6.
	* Torrent.cc, Torrent.h: limit udp send rate; check POLLOUT before
	  sending a udp packet; tune dht timers; improve logging;
	  save node id in sent requests; try to send find request to all nodes
	  if no good nodes found; fixed route bucket splitting.
	* ResMgr.cc, ResMgr.h: (NumberPair) new type; support number power
	  suffix in ranges and pairs.

2012-08-20  Alexander V. Lukyanov <<EMAIL>>

	* network.cc, network.h; make set_compact return bool.
	* Bencode.h: add lookup* methods.
	* Torrent.cc, Torrent.h: init ping_lost_count properly; make ValidNodeId
	  call MakeNodeId, move masks info MakeNodeId; fixed route table refreshing;
	  check address type before using; use new lookup methods.

2012-08-16  Alexander V. Lukyanov <<EMAIL>>

	* Torrent.cc: fixed ut_metadata total_size.
	* xmap.cc, xmap.h: (each_finished, exists) new methods.
	* network.cc, network.h: (set_compact(const char*,size_t)) new method.
	* Torrent.cc, Torrent.h: added ut_pex extension; remember if the torrent
	  is private; remember is a peer is upload_only; (Seed) new method;
	  set tracker error only if last URL failed; disallow huge packets.
	* network.cc, network.h: (is_compatible) new method.

2012-08-16  Alexander V. Lukyanov <<EMAIL>>

	* network.cc: fixed typo.
	* xstring.cc, xstring.h: (begins_with, instr, hexdump_to) new methods;
	  improved dump() for invalid utf8 strings.
	* xarray.h: (xqueue_p) new class.
	* Torrent.cc, Torrent.h: added timeout for tracker requests;
	  added support for tracker groups; new option --dht-bootstrap
	  for manual bootstrapping.
	* ftp-opie.c: use newer version from wget (fixes a bug on 64-bit hw).
	* xstring.cc, xstring.h: (hex_decode) new method.
	* Torrent.cc, Torrent.h: use hex_decode; allow pure hash instead of
	  magnet links; make tracker Fail if no valid URLs found.

2012-08-15  Alexander V. Lukyanov <<EMAIL>>

	* Bencode.cc, Bencode.h: new ctor; Pack to xstring.
	* Filter.cc, Filter.h: (set_create_mode, set_lock) new methods,
	  do file locking if requested.
	* misc.cc, misc.h, attach.h: (get_nodename) new function.
	* xstring.cc, xstring.h: add explicit clone ctor; (move_here) new
	  method.
	* xmap.cc, xmap.h: (empty) new method; xmap_p now uses delete.
	* xarray.h: xarray_p now uses delete.
	* network.cc, network.h: (family, set_port, compact, compact_addr,
	  set_compact) new methods.
	* Torrent.cc, Torrent.h: add DHT and magnet support.

2012-08-08  Alexander V. Lukyanov <<EMAIL>>

	* Torrent.cc, Torrent.h: add mt_metadata extension.

2012-08-07  Alexander V. Lukyanov <<EMAIL>>

	* Bencode.cc, Bencode.h: new ctor BeNode(const char *);
	  (ComputeLength, Pack, Format1) new methods.
	* buffer.h: add Put(char) method.
	* xmap.h: add add(const char*) method.
	* Torrent.cc, Torrent.h: add LTEP and FAST Extensions.

2012-07-26  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: send PROT after AUTH in any case, for servers
	  requiring a PROT command.

2012-07-25  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: don't try to upload if ALLO fails.

2012-07-19  Alexander V. Lukyanov <<EMAIL>>

	* lftp_ssl.cc: fixed fatal error checking for gnutls.
	* IdNameCache.cc, IdNameCache.h: allow numeric names.

2012-07-03  Alexander V. Lukyanov <<EMAIL>>

	* lftp_ssl.cc: tread decryption error as non-fatal.
	* ftpclass.cc: define TELNET_* ad char constants, get rid of type casts.
	* FileSet.cc: fixed buffer size (string was truncated).
	* Http.cc, resource.cc: add use-allprop settings.
	* HttpDirXML.cc: fixed PROPFIND result parsing.

2012-05-30  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc: don't Feed at-finish commands if the command buffer is
	  not empty.
	* FileFeeder.cc, FileFeeder.h: read by 4K.
	* commands.cc: set need_exact_time=true when the time is needed at all,
	  this removes complex and incomplete guess logic.

2012-04-23  Solar Designer <<EMAIL>>

	* Torrent.cc: fix for large PIDs.

2012-04-20  Alexander V. Lukyanov <<EMAIL>>

	* lftp_ssl.cc: don't check hostname in openssl case with ssl:check-hostname off;
	  show a warning when hostname checking is disabled.

2012-03-26  Alexander V. Lukyanov <<EMAIL>>

	* Fish.cc, Job.h, SleepJob.cc, mgetJob.cc: Fix errors reported
	  by -Werror=format-security (Simon Ruderich <<EMAIL>>)

2012-03-16  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h: check USER reply against retry-530 RExp;
	  (Retry530) new method.
	* complete.cc: add "edit" command to the list of remote commands.

2012-03-13  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc, Http.h: add support for status "429 Too Many Requests"
	  and Retry-After header; don't handle redirect for CONNECT method.
	* ftpclass.cc: better handle extra server messages in sync mode.

2012-02-14  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: fixed error reporting.
	* Torrent.cc: add explicit retracker support.

2012-01-24  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: resume connection when moving it.
	* ftpclass.cc, ftpclass.h: add Suspend/Resume.

2012-01-23  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc, Http.h: add Connection subclass.
	* FileCopy.cc: fixed ascii mode size getting for local files.

2012-01-20  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc: fixed core dump on "local" command without
	  arguments.

2012-01-17  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: fixed compilation without ssl.

2011-12-28  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h: fixed the case when home is unknown;
	  accept new cwd when real_cwd equals the new cwd.

2011-12-14  Alexander V. Lukyanov <<EMAIL>>

	* QueueFeeder.cc, QueueFeeder.h: (JobCount) new method.
	* CmdExec.cc, CmdExec.h, lftp.cc: new settings cmd:at-exit-bg,
	  cmd:at-finish, cmd:at-queue-finish.
	* MirrorJob.h, mkdirJob.h: use 0,1 exit codes.
	* Torrent.cc: fixed status command line.

2011-12-13  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: fixed exit code of queue command.

2011-12-09  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc: follow local symlinks with -L option.

2011-12-06  Alexander V. Lukyanov <<EMAIL>>

	* Torrent.cc: open files with CLOEXEC flag.
	* misc.cc, misc.h, Fish.cc, LocalAccess.cc: (shell_encode) return xstring ref.

2011-11-18  Alexander V. Lukyanov <<EMAIL>>

	* TimeDate.cc: fixed IsoDateTime (added seconds).
	* lftp_rl.c: save timestamps in history; show them.

2011-11-16  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.cc: fixed "URL mismatch" message for root directory.

2011-11-09  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: fixed NULL-crash on ssl.
	* lftp.cc: (attach) check process existence; remove stale sockets.

2011-10-06  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc: save the buffer of target_dir using alloca.
	* ResMgr.cc, ResMgr.h: (ResValue::to_tri_bool, *::QueryTriBool)
	  new methods; use const for const methods; remove ifdef0'ed code.
	* FileSetOutput.cc, commands.cc: use QueryTriBool.
	* ftpclass.cc, resource.cc: new setting ftp:use-tvfs.

2011-09-22  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, FileAccess.cc, commands.cc:
	  use ParsedURL for URL path verification; compare path to home
	  dir ~ properly; send first path component together with root
	  directory to ftp server; use reconnect if cannot change back to
	  home dir.

2011-09-16  Alexander V. Lukyanov <<EMAIL>>

	* ResMgr.cc: (ResMgr::Resource::ClosureMatch) try to match basename,
	  it helps matching torrent metadata urls to *.torrent pattern.

2011-09-15  Alexander V. Lukyanov <<EMAIL>>

	* Torrent.cc: don't try to send stop to a failed tracker.

2011-08-01  Alexander V. Lukyanov <<EMAIL>>

	* Fish.cc: check for '###' before parsing ls line.
	* Torrent.cc: use a separate check for posix_fadvise.
	* attach.h: don't use "sun" name; define SUN_LEN macro if missing
	  (<NAME_EMAIL>).
	* lftp_ssl.cc: use gnutls_priority_set_direct instead of deprecated
	  gnutls_protocol_set_priority.

2011-06-24  Alexander V. Lukyanov <<EMAIL>>

	* SleepJob.cc: print error when date-time spec is missed; remove
	  a hack with Combine.

2011-06-21  Alexander V. Lukyanov <<EMAIL>>

	* Torrent.cc: check ifa_addr!=0.

2011-06-17  Alexander V. Lukyanov <<EMAIL>>

	* lftp.cc: attach without arguments; don't use long for pid.
	* attach.h: use unsigned for pid; better error handling.

2011-06-16  Alexander V. Lukyanov <<EMAIL>>

	* attach.cc, attach.h: new files.
	* lftp.cc: (attach) new command, pass tty fd to a backgrounded lftp.
	* NetAccess.cc: init reconnect_interval_current properly.
	* complete.cc: re-set SIGINT handler.
	* ftpclass.cc: don't send FEAT after QUIT.

2011-06-10  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc, CmdExec.h: when cmd:fail-exit is true, save the non-zero
	  exit code for the case of cmd:parallel>1 (patch by Fernando Gutierrez).

2011-06-08  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.h: use ResClient to hook Reconfig (reported by Fernando
          Gutierrez <<EMAIL>>).
	* CmdExec.cc: parse buffered command even without a feeder, when
	  cmd:parallel>1 (patch by Fernando Gutierrez).
	* CmdExec.cc: set exit_code to 0 when a job has been created
	  (patch by Fernando Gutierrez).

2011-05-10  Alexander V. Lukyanov <<EMAIL>>

	* xstring.cc, xstring.h: (tmp_buf) new method.
	* CmdExec.cc, network.cc, ResMgr.cc: get rid of static buffers.
	* Torrent.cc, Torrent.h: auto set torrent:ipv6; better status.

2011-04-29  Alexander V. Lukyanov <<EMAIL>>

        * CmdExec.cc, CmdExec.h, CopyJob.cc, CopyJob.h, DirColors.cc,
	  FileSet.cc, FileSetOutput.cc, FileSetOutput.h, FindJob.cc,
	  FindJob.h, Http.cc, Job.cc, Job.h, MirrorJob.cc, MirrorJob.h,
	  QueueFeeder.cc, QueueFeeder.h, SleepJob.cc, SleepJob.h,
	  StatusLine.cc, Torrent.cc, Torrent.h, TreatFileJob.cc,
	  TreatFileJob.h, commands.cc, complete.cc, ftpclass.cc, mgetJob.cc,
	  mgetJob.h, mkdirJob.cc, mkdirJob.h, mvJob.cc, mvJob.h, pgetJob.cc,
	  pgetJob.h, url.cc, xstring.cc, xstring.h: replace PrintStatus with
	  FormatStatus; allow `jobs' output filtering; get rid of complex
	  alloca tricks.
	* Torrent.cc, Torrent.h: don't show not connected peers by default
	  (jobs -vv still lists them); show validation rate and eta.

2011-04-22  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc, resource.cc: (mirror:no-empty-dirs) new setting.
	* FileCopy.cc: remove password from URLs before logging.

2011-04-11  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: save tmp buffer, this fixes `cd -'.
	* FileCopy.cc: (FileCopyPeerFA::Clone) copy orig_url.

2011-03-25  Alexander V. Lukyanov <<EMAIL>>

	* xstring.cc, xstring.h: (url_decode, append_url_encoded) new methods.
	* url.cc, url.h: drop functions decode_string and encode_string.
	* network.h: use proper includes.
	* netrc.cc, CmdExec.cc, FileAccess.cc, FileCopy.cc, FileSet.cc,
	  FileSetOutput.cc, Fish.cc, FtpDirList.cc, Http.cc, HttpDir.cc,
	  IdNameCache.cc, LocalAccess.cc, MirrorJob.cc, MirrorJob.h,
	  NetAccess.cc, OutputJob.cc, OutputJob.h, ResMgr.cc, Resolver.cc,
	  SFtp.cc, StringSet.cc, misc.cc, misc.h, commands.cc, complete.cc,
	  history.cc, lftp.cc, netrc.cc: don't use sprintf, decode_string,
	  encode_string; drop xasprintf and xvasprintf.
	* ftpclass.cc, resource.cc: (ftp:use-ip-tos) new setting.

2011-03-16  Alexander V. Lukyanov <<EMAIL>>

	* SFtp.cc, SFtp.h: don't use uid_t and gid_t, may fix a problem
	  with sign expansion on some systems.

2011-03-09  Alexander V. Lukyanov <<EMAIL>>

	* Torrent.cc, Torrent.h: use fadvise during validation;
	  fixed fallocate call to pass correct file length;
	  optimized validation by removing realloc call.
	* xstring.cc, xstring.h: outline add_space; (get_space2) new method;
	  change get_space to take one arg.
	* xmap.cc: optimize make_hash for hash_size==1.
	* buffer.cc: use get_space2.
	* ftpclass.cc: better wording in debug.

2011-03-03  Alexander V. Lukyanov <<EMAIL>>

	* netkey.c: new file (from Ryan Thomas <<EMAIL>>).
	* ftpclass.cc, ftpclass.h, resource.cc: add netkey support;
	  (ftp:netkey-allow) new setting (original patch from Ryan Thomas).

2011-02-24  Alexander V. Lukyanov <<EMAIL>>

	* pgetJob.cc: check for chunk[0] error, avoids a segfault.
	* Torrent.cc, Torrent.h: (Torrent::CanAccept, NoTorrentCanAccept)
	  new methods; don't accept connections if no torrent can accept.

2011-02-10  Alexander V. Lukyanov <<EMAIL>>

	* lftp_ssl.cc: use curl version of openssl hostname check.
	* FileCopy.cc, ResMgr.cc, ResMgr.h, resource.cc: (xfer:log-file)
	  new setting (original patch by Timur Sufiev <<EMAIL>>).

2011-02-08  Alexander V. Lukyanov <<EMAIL>>

	* lftp_ssl.cc: check CN in openssl (code taken from wget);
	  (cert_error) new flag.
	* Http.cc: rtrim error text.
	* commands.cc: allow selecting jobs in 'jobs' command.
	* misc.cc, misc.h: (remove_tags) don't call strlen repeatedly;
	  (rtrim) new func.
	* ftpclass.cc: don't fall back to plain connection when cert error found.
	* network.cc, network.h: (sockaddr_u::set_defaults) new method;
	  (Networker::SocketSinglePF) new method to bind to single protocol family.
	* Torrent.cc, Torrent.h: add ipv6 support.
	* resource.cc: make ssl:verify-certificate on by default.

2011-02-08  Alexander V. Lukyanov <<EMAIL>>

	* lftp_ssl.cc: force SSL3 when ssl-auth is set to SSL (needed for
	  some ftp servers). Patch from Jiri Skala <<EMAIL>>;
	  disable SSLv2 and weak ciphers (Ludwig Nussel <<EMAIL>>);

2011-01-17  Alexander V. Lukyanov <<EMAIL>>

	* Torrent.cc: check for null; set done when preparing to die.

2010-12-28  Alexander V. Lukyanov <<EMAIL>>

	* Torrent.cc, Torrent.h: detect and remove duplicate peers.
	* LocalAccess.cc: dir_file saving is not needed anymore (it uses
	  revolver).
	* MirrorJob.cc: add remote symlink creation support.
	* mvJob.cc, mvJob.h: add support for pre-removing target; better
	  status messages.

2010-12-17  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.cc, FileAccess.h: new modes (LINK,SYMLINK);
	  (Open2) new method; make Rename, Mkdir, Chdir non-virtual.
	* Fish.cc, SFtp.cc SFtp.h, LocalAccess.cc, ftpclass.cc: support ln.
	* Http.cc: handle new open modes in switches.
	* mvJob.cc, mvJob.h: add link support.
	* commands.cc: (ln) new command.
	* resource.cc: use sftp v6 by default.
	* xstring.h: add inline strlen for xstring.

2010-12-15  Alexander V. Lukyanov <<EMAIL>>

	* Torrent.cc: better trace messages; reset recv_rate on completion.

2010-11-24  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc: fixed setting of cmdline in new jobs.
	* Torrent.cc, lftp_rl.c, xstring.cc: fixed warnings
	  (patch from Openwall, Alexander Peslyak).
	* complete.cc: use CmdExec::is_space for parsing.
	* xstring.h: fixed compilation with non-gcc and old gcc compilers
	  (patch from Openwall, Vasiliy Kulikov).

2010-11-22  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc, SleepJob.cc: use parse_datetime instead of obsolete
	  get_date.
	* CmdExec.cc, CmdExec.h: (AddNewJob) new method.
	* ArgV.cc, ArgV.h: (Combine) add end index argument.
	* Torrent.cc: dispose tracker session when shutting down;
	  divide numwant to number of trackers going to request the peers;
	  better shutting down status; glob local torrent files; use AddNewJob
	  to create multiple torrent jobs.
	* mgetJob.cc: call globfree even when no files found.

2010-11-19  Alexander V. Lukyanov <<EMAIL>>

	* Torrent.cc, Torrent.h: add multitracker support.

2010-11-15  Alexander V. Lukyanov <<EMAIL>>

	* FileSet.cc: optimize b-search.
	* StatusLine.cc: use tmp xstring.
	* Torrent.cc, Torrent.h: (TorrentTracker) new class;
	  use posix_fallocate to pre-allocate disk space.

2010-10-19  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc, CmdExec.h, commands.cc: new CmdExec ctor; `local' builtin.
	* MirrorJob.cc: allow continue of pget.
	* Torrent.cc, Torrent.h: better diag msg; don't show `next tracker in'
	  msg when validating; allow starting of multiple torrents at once;
	  show torrent name in status; add torrent to listener earlier;
	  better handle shutting down of multiple torrents.
	* convert-mozilla-cookies: fix info duplication;
	* pgetJob.cc: save status at the start.

2010-09-09  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h: add TVFS support; send path components
	  separately as required by RFC1738 unless TVFS is declared by server.
	* example-cmd.cc: fix assertion failure.

2010-09-01  Alexander V. Lukyanov <<EMAIL>>

	* convert-netscape-cookies: add sqlite support.
	* NetAccess.cc: use cached fset.
	* ResMgr.cc: match *.domain.org to domain.org in closure.

2010-08-17  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc: fixed coredump on --log=unopenable-file.
	* FileAccess.cc, ftpclass.cc, Http.cc, LocalAccess.cc: carefully
	  save errno value.

2010-06-10  Alexander V. Lukyanov <<EMAIL>>

	* Timer.cc: fixed Reset for timer which have been just Stop()ped.
	* commands.cc: document (repeat) options.

2010-06-09  Alexander V. Lukyanov <<EMAIL>>

	* Torrent.cc, Torrent.h: don't try to re-download invalid piece
	  from the same peer; black-list peers with many invalid pieces.

2010-06-09  Claus K. Larsen <<EMAIL>>

	* xstring.h: include unistd.h to workaround truncate64 problem on aix.

2010-06-04  gabriele balducci <<EMAIL>>

	* ftpclass.cc: fixed a problem with zeroed errno.

2010-06-02  Alexander V. Lukyanov <<EMAIL>>

	* CopyJob.cc: use no_status flag.
	* CopyJob.h: (NoStatus) accept optional parameter.
	* OutputJob.cc: pass no_status flag.

2010-05-24  Alexander V. Lukyanov <<EMAIL>>

	* GetJob.cc: fixed get/mget -c with xfer:clobber=no.

2010-05-20  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: workaround for servers with LANG but without UTF8
	  (original patch by Henry Hu <<EMAIL>>).

2010-04-30  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc, FileCopy.h, pgetJob.cc: (do_verify) new flag; unset it
	  for pget chunks.

2010-04-28  Alexander V. Lukyanov <<EMAIL>>

	* misc.cc, misc.h: (random_init, random01) new functions.
	* ResMgr.cc: use them.
	* TimeDate.cc, TimeDate.h: (TimeDiff::valueOf) new static method.
	* Timer.cc, Timer.h: (AddRandom) new method for randomization.
	* Torrent.cc: randomize peer connections in time.

2010-04-19  Alexander V. Lukyanov <<EMAIL>>

	* GetJob.cc: make xfer:clobber=no by default.
	* FileCopy.cc, FileCopy.h: fixed retries counting on upload;
	  track upload watermark.
	* ftpclass.cc: handle 426 reply on upload specially;
	  retry FEAT after login on any 5xx reply but explicit unsup code.

2010-03-18  Alexander V. Lukyanov <<EMAIL>>

	* resource.cc, commands.cc: add setting xfer:auto-rename to control
	  server's suggested file name usage.

2010-03-09  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: prolong y2k workaround for next 300 years.

2010-03-05  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h: (ssl-copy-sid) new setting; added
	  recognition of "too many users" condition in Transient5XX method;
	  more const on methods; shutdown ssl properly after CCC command.
	* resource.cc: (ftp:ssl-copy-sid) new setting; update RETRY_530 list.
	* Torrent.cc, Torrent.h: add black_list; refuse to start the same
	  torrent twice.

2010-02-05  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.cc, FileAccess.h, network.cc, network.h: more const.
	* misc.h: (ListScanPtr) new macro.

2010-01-27  Alexander V. Lukyanov <<EMAIL>>

	* pgetJob.cc: resume transfer at the end when there is not status file
	  (fixes re-downloading of complete files and allows resuming of plain
	  transfers with pget).

2010-01-21  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.cc: more restrictions on suggested file name.
	* FileCopy.cc: use suggested file name only if no such file exists.
	* GetJob.cc, FileCopy.cc: use O_EXCL when xfer:clobber is no.

2009-12-21  Alexander V. Lukyanov <<EMAIL>>

	* pgetJob.cc, pgetJob.h: add PrepareToDie method (fixes a core dump
	  on "kill all").
	* HttpDir.cc: don't use strlcpy.
	* Torrent.cc, Torrent.h: fix a warning on x86_64; when shutting down,
	  terminate immediately on a new signal.

2009-12-18  Alexander V. Lukyanov <<EMAIL>>

	* xstring.cc: use xrealloc.
	* misc.cc: (dir_file) use xstring::get_tmp (fixes sftp rename).
	* HttpDir.cc: add parser for lightttpd listing.

2009-11-17  Alexander V. Lukyanov <<EMAIL>>

	* lftp.cc, lftp_rl.c, lftp_rl.h: new setting cmd:stifle-rl-history.

2009-11-17  Robert Story <<EMAIL>>

	* Resolver.cc: use VAL_GETADDRINFO_HAS_STATUS macro.

2009-10-30  Robert Story <<EMAIL>>

	* Resolver.cc: validate "no such name" answers.

2009-10-26  Alexander V. Lukyanov <<EMAIL>>

	* Resolver.cc, Resolver.h: use default dnssec context; cosmetics.

2009-10-23  Robert Story <<EMAIL>>

	* ResMgr.cc: check is_nil for numeric settings, prevents core dump
	  when accessing inexisting settings.
	* resource.cc, Resolver.cc, Resolver.h: add dnssec support.

2009-10-23  Alexander V. Lukyanov <<EMAIL>>

	* ResMgr.cc: use strtoll for signed numbers.

2009-10-16  Daniel Fazekas <<EMAIL>>

	* FileSetOutput.cc: fixed buffer size for human_readable.

2009-10-09  Alexander V. Lukyanov <<EMAIL>>

	* mgetJob.cc: better error messages; fixed exit code.
	* FileGlob.cc better error handling.
	* FileSet.cc, MirrorJob.cc: get rid of HAVE_LSTAT.
	* lftp_ssl.cc: include string.h

2009-09-23  Alexander V. Lukyanov <<EMAIL>>

	* Torrent.cc, Torrent.h: show tracker timer time left; don't connect
	  to passive peers; show torrent name as context.

2009-09-17  Alexander V. Lukyanov <<EMAIL>>

	* Torrent.cc, ChmodJob.cc: add missing headers for NetBSD.

2009-09-16  Alexander V. Lukyanov <<EMAIL>>

	* buffer.cc: fixed DataRecoder for the case of failed iconv_open;
	  retry iconv_open without //TRANSLIT.

2009-09-15  Alexander V. Lukyanov <<EMAIL>>

	* NetAccess.cc: fixed tilde handling.
	* FtpListInfo.cc: ignore 'Status of' line.
	* Torrent.cc: don't reset activity timer on connect (the peer may
	  disconnect immediately).
	* ftpclass.cc, network.cc, network.h: don't bind data socket to
	  default address (fixes an infinite bind loop).

2009-08-18  Alexander V. Lukyanov <<EMAIL>>

	* Speedometer.cc, Speedometer.h: (GetStrS) return "" when not valid.
	* Torrent.cc, Torrent.h: better peer status; better traffic balance
	  between different torrents; handle IO-buffer errors; add settings
	  closure - name of the torrent; push uploaded data faster.

2009-08-11  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: (print_cmd_index) better formatting.
	* CopyJob.h: pass FileTimestamp correctly.
	* FileCopy.cc: fix prec comparision.

2009-08-10  Alexander V. Lukyanov <<EMAIL>>

	* lftp_ssl.cc: use SSL_OP_NO_TICKET to work with old servers.

2009-08-05  Alexander V. Lukyanov <<EMAIL>>

	* buffer.cc, buffer.h: (Dump) new method.
	* Torrent.cc, Torrent.h: (ScanPeers) new method; add key, ip params;
	  add --force-valid option to skip validating; add seed-min-peers
	  parameter; better debug messages.

2009-08-04  Alexander V. Lukyanov <<EMAIL>>

	* Speedometer.cc, Speedometer.h: (GetStr) return "" when not valid.

2009-07-29  Jeroen Roovers <<EMAIL>>

	* Makefile.am: link cmd-torrent with liblftp-network.

2009-07-28  Alexander V. Lukyanov <<EMAIL>>

	* RateLimit.cc, RateLimit.h: (Relaxed) new method.

2009-07-24  Alexander V. Lukyanov <<EMAIL>>

	* buffer.cc: fixed Put for large blocks with translation, make
	  scheduler run after putting some data into the buffer.

2009-07-23  Alexander V. Lukyanov <<EMAIL>>

	* xmap.h: fixed xmap lookup returning zero.
	* xmap.cc: fixed hash_size calculation.
	* Speedometer.cc: speed-optimize by skipping Valid call.

2009-07-21  Alexander V. Lukyanov <<EMAIL>>

	* xmap.h: (lookup_Lv) new method; make lookup const.
	* commands.cc: add short desc for torrent.
	* lftp.cc: send signal to jobs.
	* network.cc, network.cc: (SocketAccept) new method; move NonBlock and CloseOnExec here.
	* FileAccess.cc, FileAccess.h: ... from here.
	* ftpclass.cc: use SocketAccept and SocketCreateTCP.
	* Torrent.cc: use SocketAccept.

2009-07-17  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc, CmdExec.cc, CmdExec.h: (static_cmd_table_length) new const; use it;
	  fixed core dump on help command (reported by Ganael LAPLANCHE <<EMAIL>>).
	  (torrent) new command.
	* ProtoLog.cc, ProtoLog.h: new class.
	* FileAccess.cc, FileAccess.h: use ProtoLog; assume "file" protocol
	  by default.
	* Http.cc: strip interface name from hostname for ipv6.
	* Job.cc: (Kill) try a signal first.
	* LocalAccess.cc, LocalAccess.h: use current cwd by default.
	* NetAccess.cc, NetAccess.h, network.cc, network.h: (Networker)
	  new class, use it; add is_* methods for IP address classes.
	* Resolver.cc, Resolver.h: use ProtoLog.
	* ftpclass.cc: use sockaddr_u methods.
	* url.cc, url.h: use xstring, more unsafe chars.
	* xarray.h: (allocate) new method; (RefQueue) new class.
	* xstring.cc, xstring.h: (append_padding, dump, dump_to, is_binary) new methods.
	* xmap.cc, xmap.h, Error.cc, Error.h, Torrent.cc, Torrent.h: new files.
	* Makefile.am: add new files.

2009-06-11  Alexander V. Lukyanov <<EMAIL>>

	* FileSet.cc: set time precision +-1 second on msdos fs (linux only).
	* xstring.h: add ctor(str,len).

2009-06-11  Jiri Skala <<EMAIL>>

	* url.cc: fixed decode_string return value (also fixes http ls).

2009-05-14  Alexander V. Lukyanov <<EMAIL>>

	* SFtp.cc: fixed coredump when ssh gets killed.
	* SleepJob.cc: show exec'd command status; fixed a deadlock msg.
	* ChmodJob.cc: fixed modes and umask (use octal as intended).

2009-05-13  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc: use line buffering for log file.

2009-05-07  Alexander V. Lukyanov <<EMAIL>>

	* Filter.cc: fflush stdout before fork().
	* SFtp.cc: use xstring::get_tmp to avoid clobbering buffer in
	  lc_to_utf8 and utf8_to_lc.

2009-04-29  Alexander V. Lukyanov <<EMAIL>>

	* CopyJob.h: check for null ptr (fixes a core dump).

2009-04-27  Alexander V. Lukyanov <<EMAIL>>

	* CopyJob.cc, CopyJob.h, FileCopy.cc: fixed core dump on kill command.
	* ftpclass.cc, ftpclass.h: better handle unsupported EPSV command.
	* mputJob.cc: fixed a coredump on 'mput -d'.

2009-04-16  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.cc, FileAccess.h: (MkdirMakeSet) new method.
	* ftpclass.cc, SFtp.cc: use it (fixes mkdir -p for sftp).

2009-04-10  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: size_t fun.

2009-03-20  Alexander V. Lukyanov <<EMAIL>>

	* xarray.cc, xarray.h, CmdExec.cc, CmdExec.h:
	  (xarray::insert_ordered, xarray::bsearch) new methods;
	  use them to add new commands.
	* Speedometer.cc, Speedometer.h, FileCopy.cc: use xstring;
	  fixed eta display.
	* commands.cc: drop some unneeded isdigit calls; cast char to
	  unsigned char to prevent sign expansion.
	* CopyJob.cc: use xstring.
	* url.cc: optimize; cast char to unsigned char to prevent sign expansion.
	* log.cc, log.h: outline SetOutput.
	* StringSet.cc, StringSet.h, DummyProto.cc, DummyProto.h: outline ctor/dtor.

2009-03-18  Alexander V. Lukyanov <<EMAIL>>

	* TimeDate.cc, TimeDate.h: (toString) new method.
	* Speedometer.cc: use it.
	* SleepJob.cc, SleepJob.h: show sleep time left.
	* commands.cc: fixed 'exit kill'.

2009-03-17  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: set NOREST mode on specific error message;
	  unset epsv_supported if it actually isn't.
	* resource.cc: make ftp:prefer-epsv off by default.
	* pgetJob.cc: fixed off-by-one -c bug.

2009-03-03  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc, FileCopy.h: outline SetRange.
	* pgetJob.cc: convert off_t to long long before passing to printf.
	* ftpclass.cc: fix warning; pre-select both PROT modes.
	* FileInfo.cc: fixed parsing of NT unix-like listings with one digit hour.

2009-02-02  Kevin Lo <<EMAIL>>

	* FileCopy.cc: silent some warnings.
	* lftp_pty.c: add missing headers for openpty(3) on OpenBSD.

2009-01-28  Jiri Skala <<EMAIL>>

	* ftpclass.cc: fixed a possible null dereference.

2009-01-27  Alexander V. Lukyanov <<EMAIL>>

	* Resolver.cc: make it compile on old ipv6 systems.

2009-01-27  Arkadiusz Miskiewicz <<EMAIL>>

	* Resolver.cc, Resolver.h: add support for IPv6 scope for link-local addresses.

2009-01-27  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h, resource.cc: (ftp:prefer-epsv) new setting;
	  fixed message for ignore-pasv-address; use EPSV when ftp:prefer-epsv
	  is true and it's supported.

2009-01-14  Alexander V. Lukyanov <<EMAIL>>

	* buffer_ssl.cc: shutdown ssl connection only when there is no data left.

2008-12-12  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, resource.cc: (ftp:trust-feat) new setting.

2008-11-27  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, resource.cc: add another proxy-auth-type: proxy-user@host.

2008-11-11  Alexander V. Lukyanov <<EMAIL>>

	* OutputJob.cc: fixed a memory corruption bug (occured in zmore).

2008-11-10  Justin Piszcz <<EMAIL>>

	* commands.cc: add include sys/wait.h

2008-11-07  Alexander V. Lukyanov <<EMAIL>>

	* CopyJob.cc, FileSetOutput.cc, Job.cc, Job.h, MirrorJob.cc,
	  SleepJob.cc, echoJob.cc, mgetJob.cc: make AddWaiting set parent/fg.

2008-11-06  Alexander V. Lukyanov <<EMAIL>>

	* FileFeeder.cc, FileFeeder.h: use Ref.
	* FileSet.cc, MirrorJob.cc: don't include lstat.h
	* SysCmdJob.cc: call super::PrepareToDie.
	* Job.cc, Job.h: use Ref for fg_data; don't call Done for running job;
	  add PrepareToDie method; use List macros; use xarray::search.
	* CmdExec.cc, CmdExec.h: add auto_terminate_in_bg; use resource names;
	  new setting cmd:move-background-detach.
	* commands.cc: add no-detach bg exit option.

2008-10-29  Alexander V. Lukyanov <<EMAIL>>

	* misc.cc, misc.h: (in_foreground_pgrp) new function.
	* lftp.cc: use in_foreground_pgrp.
	* ftpclass.cc: don't require ftp:acct when proxy settings provide an acct.

2008-10-01  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc: clone session properly - it fixed mirror -RP.
	* lftp_ssl.cc, lftp_ssl.h: (shutdown) new method.
	* ftpclass.cc, ftpclass.h: automatically try PROT P if enforced
	  by server.
	* buffer_ssl.cc, buffer_ssl.h: shutdown ssl when sending eof.

2008-08-23  Alexander V. Lukyanov <<EMAIL>>

	* FileCopyFtp.cc: add another timeout check.
	* SFtp.cc: don't ignore UNKNOWN type entries; add debug.

2008-08-15  Alexander V. Lukyanov <<EMAIL>>

	* ResMgr.cc, ResMgr.h: add number suffix support (i.e. 10k for 10240).
	* LsCache.cc, resource.cc: use new number suffixes.
	* ftpclass.cc: use new ResValue::to_number method.

2008-08-05  Alexander V. Lukyanov <<EMAIL>>

	* SMTask.cc, SMTask.h: add PrepareToDie virtual method.
	* FileAccess.cc, FileAccess.h, FileCopy.cc, FileCopy.h, GetFileInfo.cc,
	  GetFileInfo.h, SleepJob.cc, SleepJob.h, SysCmdJob.cc, SysCmdJob.h,
	  TreatFileJob.cc, TreatFileJob.h, echoJob.cc, echoJob.h, mgetJob.cc,
	  mgetJob.h: use PrepareToDie, use Refs.
	* buffer.cc, buffer.h: allow to make IOBufferFDStream with FDStream ref.
	* PtyShell.cc, PtyShell.h: use Refs.
	* Job.cc, Job.h: use const in RemoveWaiting.

2008-07-23  Alexander V. Lukyanov <<EMAIL>>

	* misc.cc: use gnulib's unsetenv.
	* SleepJob.cc, MirrorJob.cc: use gnulib's get_date.
	* Makefile.am: link only liblftp-tasks with gnulib.
	* DummyProto.cc: inc stddef.h.
	* FileAccess.cc: hook md5 and glob from gnulib.

2008-05-27  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.cc, pgetJob.cc: don't include unneeded headers;
	  move assert.h where needed.
	* PollVec.h: include poll.h with CDECL.

2008-05-23  Alexander V. Lukyanov <<EMAIL>>

	* ascii_ctype.h: use gnulib c-ctype.

2008-05-22  Alexander V. Lukyanov <<EMAIL>>

	* resource.cc: use locale_charset.
	* FileSet.cc, MirrorJob.cc: use lstat.h.
	* ftpclass.cc, ResMgr.cc, NetAccess.cc: use inet_pton.

2008-05-16  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h: outline some funcs; disable ALLO on 202 reply;
	  add support for two-arg SITE UTIME; add support for MFMT; new setting
	  ftp:use-site-utime2.

2008-05-14  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h: add ALLO check and set use-allo if unsupported.

2008-05-08  Alexander V. Lukyanov <<EMAIL>>

	* SFtp.cc: fixed handling of missed strings in Status reply.

2008-05-06  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: (MergeCookie) use xstring methods; fixed NULL dereference.
	* xstring.cc, xstring.h: xstring::skip_all new method.

2008-04-24  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: fixed cwd tracking for ftps; fixed rate_limit allocation
	  for HTTP proxy; don't re-create data_iobuf when receiving data over
	  HTTP proxy, because we could lose some already received data (reported
	  by Arkady Sherman <<EMAIL>>).

2008-04-08  Alexander V. Lukyanov <<EMAIL>>

	* GetFileInfo.cc: init size/time fields.
	* SFtp.cc: more debug messages; fill array_info even in case of error.
	* buffer.cc, resource.cc: don't use empty charset name.

2008-03-14  Alexander V. Lukyanov <<EMAIL>>

	* resource.cc: make sftp:use-full-path on by default; validate it
	  properly as a boolean.

2008-03-13  Alexander V. Lukyanov <<EMAIL>>

	* FileSet.cc: use time 12:00:00 when time is unknown (instead of 12:00:30).

2008-03-05  Alexander V. Lukyanov <<EMAIL>>

	* Fish.cc: handle stderr messages during file transfer.
	* Http.cc: add 303 and 307 codes (suggested by Stephane Chazelas <<EMAIL>>).

2008-02-26  Alexander V. Lukyanov <<EMAIL>>

	* SSH_Access.cc, SSH_Access.h, Fish.cc, Fish.h, SFtp.cc, SFtp.h, Makefile.am:
	  split off the ssh interaction logic to separate module (SSH_Access).
	* MirrorJob.cc: add basename only if it is not already there.

2008-02-20  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc: fix spinning when fail-exit is triggered with non-empty
	  root cmd_buf.

2008-02-17  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h, resource.cc: new setting ftp:ssl-shutdown-timeout;
	  wait for ssl shutdown after CCC (new wait state WAITING_CCC_SHUTDOWN);
	  move some timers and connection state variables to Connection class;
	  use xstring_c for some members.
	* FileAccess.h, NetAccess.h: use xstring_c for some members.

2008-02-16  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h, resource.cc: ftp:proxy-auth-type replaces
	  old ftp:proxy-auth-joined setting; implement two more auth types:
	  joined-acct and open (partly based on patch from David Wolfe <<EMAIL>>).

2008-02-15  Alexander V. Lukyanov <<EMAIL>>

	* network.cc, network.h, Makefile.am: (network.*) new files.
	* xarray.h: (search) new method.
	* Resolver.h, Resolver.cc: use new sockaddr_u methods;
	  don't add the same address multiple times; report found addrs.
	* NetAccess.cc, NetAccess.h: use new sockaddr_u methods.
	* ftpclass.cc: workaround proftpd NLST bug; workaround for
	  a server bug sending single NL; simplify encode_eprt.

2008-02-06  Laurent MONIN <<EMAIL>>

	* commands.cc: add open -s option, describe slot command syntax.

2008-02-06  Alexander V. Lukyanov <<EMAIL>>

	* parsecmd.cc: don't delete Ref (fixes double delete bug).

2008-01-31  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h, resource.cc: (ftp:waiting-150-timeout) new setting;
	  limit time to wait for 150 reply; stop waiting for 150 reply after receiving
	  another reply.
	* FileCopy.cc: (xfer:buffer-size) new setting.

2008-01-23  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: always use passive mode for proxy_is_http==true;
	  use GetFlag function everywhere to test flags.

2008-01-21  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.h: use SMTaskRef properly (fixes kill all core dump).
	* SFtp.cc: don't use FSETSTAT to truncate the file on upload when
	  not needed (pos==0 can use open mode with truncation).
	* Fish.cc: unset translation when charset is set to an empty string.
	* SFtp.cc, SFtp.h, resource.cc: (sftp:use-full-path) new setting;
	  use sftp:charset properly; unset translation when charset eq "".

2008-01-17  Alexander V. Lukyanov <<EMAIL>>

	* SFtp.cc: fixed hex printing of the handle.

2008-01-03  Alexander V. Lukyanov <<EMAIL>>

	* buffer.cc: use copy of va_list properly (fixes a coredump on some systems).
	* url.cc: (encode) use xstring::get_tmp to allow several encode calls at once.
	* HttpDir.cc: init link_target properly (fixes NULL pointer fault).

2007-12-28  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc, FileGlob.cc, HttpDir.cc, LocalAccess.cc, ftpclass.cc:
	  fixed constness for strchr, strrchr and strstr
	  (reported by Yann Rouillard <<EMAIL>>).
	* GetJob.cc, GetJob.h: don't decode URL escapes when no URL schema used.

2007-12-12  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: workaround the case when transfer ends without data connection.

2007-12-12  Martin Nagy <<EMAIL>>

	* FileCopy.cc: (GetPercentDoneStr) prevent buffer overflow.

2007-11-13  Alexander V. Lukyanov <<EMAIL>>

	* xstring.cc, xstring.h: make most xstring methods return *this;
	  new function get_tmp(s,n).
	* misc.cc: use new get_tmp.
	* xmalloc.h: add ().

2007-11-12  Alexander V. Lukyanov <<EMAIL>>

	* xstring.cc, xstring.h: (get_tmp(const char*)) new function.
	* misc.cc: use it.
	* FileAccess.cc, FileAccess.h: optimize Log* methods.
	* MirrorJob.cc: count rm errors properly.

2007-11-08  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: don't reset timeout_timer all the time.
	* resource.cc: make ftp:use-stat-for-list off by default.

2007-11-03  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.cc: (Log2) don't use %.*s (it mishandles invalid mb characters).
	* SleepJob.cc, SleepJob.h: repeat --weak new option.

2007-10-24  Alexander V. Lukyanov <<EMAIL>>

	* parsecmd.cc: don't accept incomplete comments.
	* CmdExec.cc: set slot name before ChangeSession (fixes a glitch).
	* ConnectionSlot.cc, ConnectionSlot.h: use FileAccessRef.
	* url.cc, FileAccess.cc: use const for FindSession result.

2007-10-18  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: set prot='P' for ftps if PROT command fails.
	* IdNameCache.cc, IdNameCache.h: use Ref; use xstrcmp.

2007-10-15  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: handle timeouts when 150 reply is not received yet.

2007-10-13  Alexander V. Lukyanov <<EMAIL>>

	* Job.cc: fix `wait all'.

2007-10-12  Alexander V. Lukyanov <<EMAIL>>

	* NetAccess.cc: clear proxy_proto properly.
	* ftpclass.cc: (TurnOffStatForList) new method; fine tune STAT for LIST
	  handling for some servers.

2007-10-09  Alexander V. Lukyanov <<EMAIL>>

	* Ref.h: (get) new method.
	* FileSet.cc: workaround for OSF/1 compiler.

2007-10-08  Alexander V. Lukyanov <<EMAIL>>

	* xstring.cc: fix xstring::cat.
	* xarray.h: drop automatic cast to T*.
	* Resolver.cc, Resolver.h: use explicit xarray::get.
	* SFtp.cc: fix type mismatch warning.

2007-10-01  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: handle and log STAT errors properly.

2007-09-24  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: fix for non-standard FEAT reply.
	* SFtp.cc, SFtp.h: use timer to flush imcomplete data.

2007-09-21  Alexander V. Lukyanov <<EMAIL>>

	* module.cc: use xstring; (access_so) new function.

2007-09-20  Alexander V. Lukyanov <<EMAIL>>

	* LocalAccess.cc, LocalAccess.h: let other tasks run while doing
	  ListInfo; better status messages.
	* NetAccess.cc: optimize translations; use xstring::format.
	* FileSet.h: (count, curr_index, curr_pct) new methods.

2007-09-17  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: skip leading space in STAT reply.
	* resource.cc: add another server overload message.
	* MirrorJob.cc, MirrorJob.h: (depth-first, no-empty-dirs, ascii)
	  new options.
	* CmdExec.cc, CmdExec.h, complete.cc: (CmdByIndex) return cmd name.

2007-09-12  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: improve STAT reply handling.
	* ArgV.cc, ArgV.h: (getopt_error_message) new method.
	* commands.cc: use getopt_error_message; don't pass longopt - unneeded;
	  (cls) remove options after time_style is used.

2007-09-12  Lamer <<EMAIL>>

	* lftp_ssl.cc, resource.cc: (ssl:check-hostname) new setting.

2007-09-10  Alexander V. Lukyanov <<EMAIL>>

	* ArgV.cc: use strtok instead of tokenize.
	* misc.cc, misc.h: drop tokenize.
	* CmdExec.cc, CmdExec.h, commands.cc: use xarray for dyn_cmd_table.
	* ColumnOutput.cc, ColumnOutput.h: use RefArray for datum.
	* FileAccess.cc, FileAccess.h: outline DirList ctor and dtor.
	* FileCopy.cc, FileCopy.h, FileGlob.cc, FileGlob.h, FileSet.cc,
	  FileSet.h: outline ctor/dtor.
	* FindJob.cc, FindJob.h: use RefArray for stack.
	* FindJobDu.cc, FindJobDu.h: use RefArray for size_stack; use Ref.
	* xarray.cc, xarray.h: (xqueue) new class; (xarray::chop,last,qsort,
	  move_here) new methods; (xarray_s) new class.
	* xstring.h, Ref.h: (_clear,_set,unset) new methods.
	* Fish.cc: use xqueue for RespQueue.
	* HttpDirXML.cc: use Refs, use xarray_s for stack.
	* Job.cc, Job.h: use xarray for waiting; (AllWaitingFg) new method.
	* CopyJob.cc, MirrorJob.cc: define waiting_num.
	* Resolver.cc: use xarray.
	* SMTask.cc, SMTask.h: use xarray for stack.
	* history.cc: use url::encode.
	* SleepJob.cc: fixup for new waiting.
	* ftpclass.cc: when directory STAT reply includes 211- prefixes, strip them.

2007-09-02  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: fixed typo leading to coredump.

2007-08-31  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: set entity_size only for RETRIEVE mode; better handle 1xx replies.

2007-08-31  Craig Ruff <<EMAIL>>

	* ftpclass.cc: handle 125 reply as 150.

2007-08-30  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc: clone session when doing transfer (fixes -P).
	* buffer.cc, buffer.h: (Append, AppendTranslated) new methods.

2007-08-29  Alexander V. Lukyanov <<EMAIL>>

	* buffer.cc, buffer.h: move Put_LL shortcut to IOBuffer.
	* FileAccess.cc, FileAccess.h: (expand_tilde) add start index arg;
	  (Path::Optimize) use xstrings, add a special case for optimization of url paths,
	  (Path::Change) use xstrings, expand single tilde in url.
	* misc.cc, misc.h: dirname works with xstring.
	* GetFileInfo.cc: use new dirname.
	* FileCopy.cc: use new dirname; getfd in FileCopyPeerFDStream mode PUT, so that
	  it becomes IOReady.
	* HttpDir.cc: use xstrings.
	* xstring.cc, xstring.h: (append(s,len), append(xstring)) new methods; fix set_substr;
	  (set_substr(i,len1,s,len2)) new method.

2007-08-24  Alexander V. Lukyanov <<EMAIL>>

	* xstring.cc, xstring.h: (get_tmp, format, vformat, cat, join) new static methods.
	* CopyJob.cc, Fish.cc, Http.cc, ftpclass.cc, log.cc, misc.cc, url.cc, SFtp.cc:
	  use new xstring methods; prefer xstring::length over strlen.
	* ftpclass.cc, ftpclass.h: (ReceiveOneLine) new method; clean up ReceiveResp.
	* buffer.h: (Put(xstring&)) new method.

2007-08-23  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc: don't use put_ll_timer in ascii mode.

2007-08-20  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.cc, FileAccess.h: new Log* methods.
	* log.cc, log.h: (vFormat) new method.
	* RateLimit.cc, RateLimit.h, NetAccess.cc, Makefile.am: move RateLimit
	  implementation to a separate file.
	* ftpclass.cc, ftpclass.h: use new Log* methods; don't call getpeername
	  for just connected socket; wait for 150 reply before accessing data socket.
	* Fish.cc, SFtp.cc, Http.cc, LocalAccess.cc, Resolver.cc,
	  NetAccess.cc: use new Log* methods.
	* SFtp.cc: limit upload packet size.
	* LsCache.cc: increase cache size to 16Mb.

2007-08-16  Alexander V. Lukyanov <<EMAIL>>

	* FileSetOutput.cc: truncate xstring correctly.
	* lftp.cc, commands.cc, CmdExec.cc, CmdExec.h: use Ref for top_exec.
	* FileCopy.cc, FileCopy.h, resource.cc: add transfer log support
	  and xfer:log setting.

2007-08-15  Alexander V. Lukyanov <<EMAIL>>

	* xstring.h: disable clone and assign for xstring_ca.
	* xstring.cc: use a clearer alloc condition.
	* FtpListInfo.cc: simplify parser with xstring.
	* xarray.cc, xarray.h: (keep_extra) new member.

2007-08-14  Alexander V. Lukyanov <<EMAIL>>

	* FileSet.cc, FileSet.h: fix clone ctor, add Dump method.
	* MirrorJob.cc: use NO_DATE.
	* ftpclass.cc: don't do ResetLocationDate in clone ctor - it leads
	  to loss of cwd; set correct real_cwd when handling of CWD reply.
	* NetAccess.cc: save listing in cache with proper mode.
	* commands.cc: update copyright.
	* ftpclass.cc: don't change TYPE unless needed.

2007-08-13  Alexander V. Lukyanov <<EMAIL>>

	* xarray.h: zero terminate xarray_p.
	* ftpclass.cc: correctly truncate xstring.
	* FtpDirList.cc: fixed list colouring (line parse).
	* StringSet.cc: optimize.

2007-08-10  Alexander V. Lukyanov <<EMAIL>>

	* Ref.h, xarray.h, SMTash.h: make a base class _RefArray; new class
	  TaskRefArray; new class xarray_p; (xarray::borrow) new method.
	* StringPool.cc, StringPool.h, StringSet.cc, StringSet.h: use xarray_p.
	* pgetJob.cc, pgetJob.h: use TaskRefArray.
	* ftpclass.cc, ftpclass.h: check anonymous password for leading minus sign
	  before enabling stat-for-list.

2007-08-08  Alexander V. Lukyanov <<EMAIL>>

	* ResMgr.cc, url.cc: use xstring and StringSet.
	* xarray.cc, xarray.h: new files.
	* Makefile.am: add xarray files.
	* NetAccess.cc, NetAccess.h, ftpclass.cc, Http.cc, FileSet.cc,
	  FileSet.h, PollVec.cc, PollVec.h, ColumnOutput.cc, ColumnOutput.h,
	  Resolver.cc, Resolver.h: use xarray.
	* StringSet.h: (borrow) new method.
	* Ref.h: make RefArray a friend.

2007-08-06  Alexander V. Lukyanov <<EMAIL>>

	* FileSet.h, FileSet.cc: (NotNewerThan, NotOlderThan, Subtract*) new methods.
	* MirrorJob.cc: use SubtractNotNewerThan and SubtractNotOlderThan.

2007-08-02  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, NetAccess.cc, NetAccess.h: increment retries on
	  soft transfer errors which do not cause disconnect; check retry
	  count before re-starting transfer.
	* FileCopy.cc: increment saved retry count before next try.
	* resource.cc: remove obsolete code.
	* log.cc, log.h: use Ref; use xstring::vsetf.
	* keyvalue.cc, keyvalue.h: use xstring::appendf.
	* ArgV.cc, CmdExec.cc: use xstring.

2007-07-31  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: set STORE_FAILED error properly in some cases.
	* ftpclass.cc, ftpclass.h, resource.cc: new setting ftp:use-stat-for-list.

2007-07-30  Alexander V. Lukyanov <<EMAIL>>

	* SFtp.cc: clear handle on disconnect (fixes coredump).

2007-07-27  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: add $$ eval substitution.
	* xstring.cc, xstring.h: (appendf, vappendf) new methods.

2007-07-26  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: (eval) new command.

2007-07-26  Alexander V. Lukyanov <<EMAIL>>

	* ColumnOutput.cc, ColumnOutput.h: use Refs.
	* CopyJob.cc, CopyJob.h: use Refs; allow to specify CopyJob creator
	  for CopyJobEnv.
	* FileCopy.cc, FileCopy.h: close session when it's not needed; suspend
	  only open sessions; new Clone methods; disallow negative buf size.
	* FileSetOutput.cc, FileSetOutput.h: use Refs; remember data source error.
	* Job.h: make JobRef===SMTaskRef.
	* OutputJob.h: remove unused members/methods.
	* MirrorJob.cc, commands.cc: use new pget.
	* complete.cc: use JobRef.
	* pgetJob.cc, pgetJob.h: derive from CopyJob.

2007-07-25  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h: allow to borrow active connection if last
	  connection failed.

2007-07-06  Alexander V. Lukyanov <<EMAIL>>

	* buffer.cc: fixed DataRecoder.
	* buffer.h: (PutRaw) new method.
	* ftpclass.cc, ftpclass.h: send encoded parts of url untranslated.
	* url.cc, url.h: (path_ptr) new method.

2007-06-15  Alexander V. Lukyanov <<EMAIL>>

	* ResMgr.cc: (CharsetValidate) return error without iconv.
	* buffer.cc, buffer.h: nicer conditionals.

2007-06-15  Jakob Truelsen <<EMAIL>>

	* ResMgr.cc, SFtp.cc, buffer.cc, buffer.h: allow compilation without iconv.

2007-06-08  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.h: don't include LsCache.h.
	* FileSet.cc, FileSet.h: const Count.
	* GetPass.cc: don't return NULL for empty pass.
	* LocalAccess.cc, LocalAccess.h, LsCache.cc, LsCache.h,
	  MirrorJob.cc, MirrorJob.h: use Refs.
	* xstring.h: (last_char) new method.
	* xmalloc.h: (alloca_append) new macro.
	* url.cc, url.h: (url::decode) new static method; make
	  url::dir_needs_trailing_slash support urls instead of just proto.
	* FileAccess.cc: add trailing slash to urls when needed; keep url when doing cd ..
	* commands.cc: consult cache when deciding how to Chdir.
	* NetAccess.cc: move peer cloning here from Http; cache FileSet.
	* Http.cc: don't add slash if url is known; cache dir status.

2007-06-04  Alexander V. Lukyanov <<EMAIL>>

	* buffer.cc, buffer.h: make Unpack* const.
	* Ref.h: (get_non_const) new method; Cast<T> new template.
	* SFtp.cc, SFtp.h: use Refs, use const where appropriate.

2007-05-31  Alexander V. Lukyanov <<EMAIL>>

	* Fish.cc: fixed Disconnect to unset buffers.
	* FileGlob.h: dir_list should not be Ref.
	* xstring.cc, xstring.h: (xstring_c::vset, xstring::setf) new methods.
	* CopyJob.cc, misc.cc, pgetJob.cc, resource.cc: use xstring::setf.
	* OutputJob.cc: use xstring_c::vset.
	* url.cc: encode non-ascii chars; use setf.
	* xmalloc.cc: better debug print.
	* Http.cc: fixed Read (broken when _Read was split off);
	  fixed handling of urls without path.

2007-05-24  Alexander V. Lukyanov <<EMAIL>>

	* convert-netscape-cookies: use strict; better error message.

2007-05-22  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h: use Ref for expect.
	* xmalloc.cc, xmalloc.h: 3-arg xstrset - new function.
	* xstring.h: (xstring_c::nset) new method.
	* FileAccess.cc, FileCopy.cc, FindJob.cc, HttpDirXML.cc, MirrorJob.cc,
	  commands.cc, misc.cc, resource.cc, url.cc, url.h: use xstring in ParsedURL.

2007-05-21  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc, Http.h: (_Read) new private method to use in LogErrorText
	  (fixed incorrect resetting of retries).

2007-05-15  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.h: use static_cast (as we use -fno-rtti).
	* NetAccess.cc, NetAccess.h: use Ref; split off RateLimit.
	* RateLimit.h: new file.
	* Fish.cc, SFtp.cc: change for Refs in NetAccess.
	* Http.cc, Http.h: use Ref.
	* ProcWait.h: make dtor public.
	* Resolver.cc, Resolver.h, buffer_ssl.cc, buffer_ssl.h, ftpclass.cc,
	  ftpclass.h: use Ref.

2007-04-27  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.cc, FileAccess.h: more inlines; make dtor public.
	* StatusLine.h: make dtor public.
	* SMTask.h: make SMTaskRef inherit from Ref.

2007-04-25  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc: use explicit borrow().
	* Fish.cc, Fish.h, SFtp.cc, SFtp.h: use Refs.
	* Ref.h: declare Ref::null; deny assignment from other Ref.
	* HttpDir.cc: use Ref::null.
	* buffer.h: optimize ctors.

2007-04-21  Alexander V. Lukyanov <<EMAIL>>

	* CatJob.cc: use new FileCopyPeerFA::New.
	* CmdExec.cc, CmdExec.h: use Ref for saved_session.
	* ConnectionSlot.cc, ConnectionSlot.h: add const.
	* DirColors.cc, DirColors.h: pass const Ref&.
	* DummyProto.cc: use new DirList ctor.
	* FileAccess.cc, FileAccess.h: use Refs; (FileAccessRef::null) new
	  const;
	* FileCopy.cc, FileCopy.h, FileCopyFtp.cc, FileCopyFtp.h: use Refs;
	  const; Cast method.
	* FileGlob.cc, FileGlob.h, Filter.cc, Filter.h: use Refs.
	* FileSet.cc, FileSet.h: use consts.
	* FindJob.cc, FindJob.h, Fish.cc, Fish.h, FtpDirList.cc, FtpDirList.h,
	  GetFileInfo.cc, GetFileInfo.h, GetJob.cc GetJob.h, HttpDir.cc,
	  OutputJob.cc, OutputJob.h, SFtp.cc, SFtp.h, SleepJob.cc, SleepJob.h,
	  buffer.cc, buffer.h, commands.cc, mkdirJob.cc, mkdirJob.h: use Refs.
	* Http.cc, ftpclass.cc: change order of args to *DirList.
	* ftpclass.h, history.cc, history.h: add const.
	* Job.cc, Job.h: use Refs in SessionJob; drop Reuse method.
	* LocalAccess.cc: pass 0 as session to DirList.
	* NetAccess.cc, commands.cc: use non-static Roll method.
	* SMTask.h: drop operator=(SMTaskRef&).

2007-04-18  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc: don't delete Refs.
	* CopyJob.h, OutputJob.cc, OutputJob.h: return ref to Ref instead of
	  pointer.
	* FileCopy.cc, FileCopy.h: use Refs; make more virtual methods for fxp.
	* FileCopyFtp.cc: use the new virtual methods instead of ptr casting.
	* FileSetOutput.cc, FileSetOutput.h: (FileSetOutput::parse_res) new
	  method.
	* GetJob.cc, GetJob.h: use Ref and new FileCopyPeerFDStream ctor.
	* SMTask.cc, SMTask.h: fixed Ref assignment; more info in PrintTasks.
	* commands.cc, complete.cc: use FileSetOutput::parse_res.
	* pgetJob.cc, pgetJob.h: use new FileCopyPeerFDStream ctor.
	* CatJob.cc, CatJob.h, FileCopyOutputJob.cc: use JobRef; pass const ref to JobRef.
	* FileCopyOutputJob.h, FileCopy.h, Makefile.am: split off FileCopyOutputJob.h.

2007-04-13  Alexander V. Lukyanov <<EMAIL>>

	* Ref.h: (Ref) new template class.
	* SMTask.h: (SMTaskRef) new template class.
	* Job.h: (JobRef) new template class.
	* CatJob.cc, CatJob.h, CmdExec.cc, CmdExec.h, CopyJob.cc, CopyJob.h,
	  FileSetOutput.cc, FileSetOutput.h, FindJob.cc, FindJob.h, Job.cc,
	  Job.h, MirrorJob.cc, MirrorJob.h, OutputJob.cc, OutputJob.h,
	  TreatFileJob.cc, TreatFileJob.h, buffer.cc, buffer.h, commands.cc,
	  echoJob.cc, echoJob.h, mgetJob.cc, mgetJob.h, mvJob.cc, mvJob.h,
	  pgetJob.cc, pgetJob.h: use Ref.

2007-04-12  Alexander V. Lukyanov <<EMAIL>>

	* HttpDir.cc, HttpDir.h, LocalAccess.cc, LsCache.cc, LsCache.h: use xstring.
	* xstring.cc, xstring.h: make vstrlen do va_copy.

2007-04-11  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc, CmdExec.h, parsecmd.cc: use Buffer for cmd_buf;
	  use xstring where needed; fix FIXME in WriteCmds and ReadCmds;
	* buffer.cc, buffer.h: make Get const; (Prepend) new method.
	* MirrorJob.cc, MirrorJob.h, PatternSet.cc, PatternSet.h,
	  PtyShell.cc, PtyShell.h, commands.cc, lftp_ssl.cc, lftp_ssl.h:
	  use xstring.
	* ArgV.h: (CombineCmd) new method.
	* CmdExec.cc, CmdExec.h, CopyJob.cc, CopyJob.h, DummyProto.cc,
	  DummyProto.h, FileCopy.cc, FileCopy.h, FileGlob.cc, FileGlob.h,
	  Filter.cc, Filter.h, FindJob.cc, FindJob.h, FindJobDu.cc,
	  FindJobDu.h, Fish.cc, FtpDirList.cc, FtpDirList.h, GetFileInfo.cc,
	  GetFileInfo.h, GetJob.cc, GetJob.h, LocalDir.cc, LocalDir.h,
	  PtyShell.cc, alias.cc, alias.h, bookmark.cc, bookmark.h, commands.cc,
	  complete.cc, ftpclass.cc, history.cc, history.h, lftp.cc,
	  mgetJob.cc, mgetJob.h: use xstring.
	* lftp_rl.c, lftp_rl.h: const in filename_dequoting_function.
	* resource.cc: const mismatch.
	* GetPass.cc, GetPass.h: use file descriptor instead of FILE;
	  use xstring; return const char*.
	* ColumnOutput.cc, ColumnOutput.h: use StringSet, xstring.
	* xstring.cc, xstring.h: (chomp, rtrim) new methods.

2007-04-09  Alexander V. Lukyanov <<EMAIL>>

	* xstring.h: (eq, ne, xstring0::length) new methods.
	* OutputJob.cc, OutputJob.h: use xstring.

2007-04-06  Alexander V. Lukyanov <<EMAIL>>

	* xstring.cc, xstring.h: allow truncate on unset xstrings; change
	  truncate_at so that it works on binary strings.
	* FileSetOutput.cc, FileSetOutput.h, commands.cc: use xstring;
	  disable FileSetOutput cloning and assignment.
	* xstring.h: (xstring_ca) new class for allocated strings.
	* FileSetOutput.cc, SFtp.cc: use it.
	* CmdExec.cc, CmdExec.h: use xstring; make unquote return result length.
	* QueueFeeder.cc, QueueFeeder.h, StatusLine.cc: use xstring.
	* SleepJob.cc: use xstring_ca.
	* CmdExec.cc: don't add extra \001 for readline prompt.

2007-04-05  Alexander V. Lukyanov <<EMAIL>>

	* xstring.h: disable cloning of xstring*.
	* FindJob.cc, Http.cc, keyvalue.cc: avoid unnecessary xstring cloning.
	* FileAccess.h, FileCopy.cc, FileCopy.h, FileSet.cc, FileSet.h,
	  FileSetOutput.cc, Filter.cc, Filter.h, MirrorJob.cc, NetAccess.cc:
	  (FileTimestamp) new struct; use it where needed; submit even
	  imprecise timestamp from MirrorJob to CopyJob.

2007-04-04  Alexander V. Lukyanov <<EMAIL>>

	* keyvalue.cc: truncate key and value before reading next record.

2007-04-02  Alexander V. Lukyanov <<EMAIL>>

	* misc.cc: use xstring.

2007-03-30  Alexander V. Lukyanov <<EMAIL>>

	* ChmodJob.cc, TreatFileJob.cc: FileInfo::name is now xstring -
	  use get().
	* xstring.cc, xstring.h: move get_non_const to xstring0;
	  get rid of AUTO len (unused); make get_space public and make it
	  take granularity as argument; (set_length) new method.
	* Http.cc: (LogErrorText) get rid of unused code.
	* FileSet.h: use xstring_c for symlink.
	* xstring.cc: allow set_allocated(0); simplify truncate.
	* FileCopy.cc, FileCopy.h, FileCopyOutputJob.cc, buffer.cc,
	  buffer.h, buffer_ssl.cc, Resolver.cc: use xstring as base for Buffer;
	  in_buffer is gone; ZeroTerminate is too.
	* xstring.cc, xstring.h: append(char) new method; (borrow) new method.
	* keyvalue.cc, keyvalue.h: use xstring.

2007-03-29  Alexander V. Lukyanov <<EMAIL>>

	* xstring.cc, xstring.h: (set_substr) new method.
	* FileGlob.cc, FileSet.cc, FileSet.h, MirrorJob.cc, NetAccess.cc,
	  mgetJob.cc, mgetJob.h, rmJob.cc: use xstring in FileInfo.
	* xstring.cc: support overlapping set.
	* xstring.h: add compact variant of xstring (xstring_c).
	* Resolver.cc, Resolver.h: use xstring_c in cache.
	* xstring.h: add xstring_c::truncate.
	* xmalloc.cc: allow overlapped xstrset.
	* FileSetOutput.cc, FileSetOutput.h, ResMgr.cc, ResMgr.h, resource.cc:
	  use xstring_c for resources.

2007-03-28  Alexander V. Lukyanov <<EMAIL>>

	* SleepJob.cc, SleepJob.h: use xstring.
	* xstring.cc, xstring.h: add len member; support binary strings.
	* SFtp.cc, SFtp.h: use xstring.
	* Resolver.cc, Resolver.h: use xstring.

2007-03-26  Alexander V. Lukyanov <<EMAIL>>

	* LocalAccess.cc: prevent double-delete of args in LocalDirList
	  (reported by Liu Yubao <<EMAIL>>).
	* FileAccess.cc, commands.cc: cosmetics.
	* pgetJob.cc: fixed core dump when target directory is not writable
	  (reported by Justin Piszcz <<EMAIL>>).

2007-03-26  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc, Job.cc, Job.h, MirrorJob.cc, SleepJob.cc, commands.cc,
	  mgetJob.cc, pgetJob.cc: use xstring for cmdline.

2007-03-23  Alexander V. Lukyanov <<EMAIL>>

	* Fish.cc, Fish.h: use StringSet for path_queue.
	* StringSet.cc, StringSet.h: (LastString, Pop) new methods.
	* NetRC.cc, NetRC.h: use xstring.
	* commands.cc: fixes for xstring in NetRC.
	* pgetJob.cc, pgetJob.h: use xstring.

2007-03-13  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h: change line to xstring; drop line_len;
	  improve <NUL> handling; require <CR><NL> to end line.

2007-03-13  Alexander V. Lukyanov <<EMAIL>>

	* xstring.cc, xstring.h, Makefile.am, xmalloc.h: split off xstring.
	* FileAccess.cc, FileAccess.h, Fish.cc, Fish.h, Http.cc, Http.h,
	  LocalAccess.cc, NetAccess.cc, NetAccess.h, SFtp.cc, ftpclass.cc,
	  ftpclass.h: use xstring.

2007-03-09  Alexander V. Lukyanov <<EMAIL>>

	* misc.h: don't delete the object in ListDel.

2007-03-06  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc: tell target to find file size before requesting
	  source to get file information - this avoids HEAD when doing
	  `get -c' to local destination.
	* Http.cc, Http.h: try to log error text if it is already available.
	* HttpDir.cc, misc.cc, misc.h: move remove_tags to misc.
	* buffer.cc, buffer.h: (ZeroTerminate) new method.
	* xmalloc.h: (xstring) new class.

2007-02-27  Damon Harper <<EMAIL>>

	* MirrorJob.cc, MirrorJob.h: new option --only-existing.

2007-02-27  Alexander V. Lukyanov <<EMAIL>>

        * CmdExec.cc, FileAccess.cc, FileCopy.cc, Filter.cc,
	  FindJob.cc, Fish.cc, GetFileInfo.cc, GetPass.cc, Http.cc,
	  LocalAccess.cc, MirrorJob.cc, NetAccess.cc, OutputJob.cc,
	  PtyShell.cc, QueueFeeder.cc, ResMgr.cc, SFtp.cc, StringSet.cc,
	  alias.cc, buffer.cc, commands.cc, lftp.cc, misc.cc: use xstrset
	  to assign dynamic strings.
	* commands.cc: -p option has priority over the url.

2007-01-11  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: use default closure feature of ResClient;
	  clear ls cache when ftp:list-options changes.
	* xmalloc.cc, xmalloc.h: (xstrset) new function.
	* ftpclass.cc: use xstrset.

2007-01-06  Alexander V. Lukyanov <<EMAIL>>

	* misc.cc, misc.h, Fish.cc, Fish.h: move shell_encode to misc.cc.
	* MirrorJob.cc: use shell_encode to quote properly file names for shell;
	  this fixes potential security vulnerability when one generates a
	  script with mirror --script and then executes it.

2006-12-27  Alexander V. Lukyanov <<EMAIL>>

	* Cache.h: (Cache::AddCacheEntry) new method.
	* Resolver.cc, Resolver.h, LsCache.cc, LsCache.h: use it.
	* ResMgr.cc, ResMgr.h: (ResClient) new base class for resource users.
	* FileAccess.cc, FileAccess.h, Resolver.h, Resolver.cc,
	  Speedometer.cc, Speedometer.h: use it.
	* misc.h: add list macros.

2006-12-18  Alexander V. Lukyanov <<EMAIL>>

	* Timer.cc, Timer.h: reset timer when creating it from TimeInterval,
	  this fixes sleep command; (re_set) forced resetting; use it.

2006-12-14  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: remove obsolete note about infinite cache size.

2006-12-10  Alexander V. Lukyanov <<EMAIL>>

	* SMTask.cc, SMTask.h: eliminate sched_total and the need to merge
	  multiple block vectors; tasks set the global SMTask::block directly.
	* Job.cc: (WaitDone) make it more robust - protect from deleting the
	  jobs we wait for.

2006-12-06  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: reset timeout_timer when waiting for transfer
	  confirmation; reset it when sending a command after some idle
	  time; (Read) fixed check for null iobuf.
	* Timer.cc: (Reset) reset only to a later time.

2006-11-28  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc, MirrorJob.h: (--on-change) new option.
	* log.cc, log.h: optimize Log::Format for when the message is not
	  going to be output.
	* buffer.cc, buffer.h, ftpclass.cc, ftpclass.h: (DataTranslator) new
	class; use it in DirectedBuffer; (DataRecoder) new class for charset
	conversion; use DataTranslator in IOBufferTelnet; skip 3-byte telnet
	commands; optimize buffer data movement.

2006-10-10  Alexander V. Lukyanov <<EMAIL>>

	* CopyJob.cc: clear and reset cp when a sub-job finishes (fixes
	  coredump in mget).

2006-09-27  Alexander V. Lukyanov <<EMAIL>>

	* buffer.cc: use Delete on SMTask derivative Speedometer.
	* SMTask.cc: add debug print when deleting running task.
	* FileAccess.cc: fixed coredump when path.url is null after Change.
	* CopyJob.cc: Delete done CopyJob - fix a leak.

2006-08-29  Alexander V. Lukyanov <<EMAIL>>

	* FileSet.cc, FileSet.h, MirrorJob.cc, MirrorJob.h, resource.cc:
	  new mirror option --skip-noaccess and setting mirror:skip-noaccess.
	  (FileSet::ExcludeUnaccessible) new method.

2006-08-22  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc, CmdExec.h, QueueFeeder.h, commands.cc: add settings
	  cmd:parallel and cmd:queue-parallel; backout QueueFeeder changes;
	  drop queue --parallel option.

2006-08-22  Markus Schoder <<EMAIL>>

	* CmdExec.cc, commands.cc, QueueFeeder.h: add queue --parallel option.

2006-08-22  Alexander V. Lukyanov <<EMAIL>>

	* SMTask.h: drop MakeRef macro, it makes trouble with multiple arg evaluation.

2006-08-08  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc, resource.cc: add mirror:include-regex setting.

2006-08-07  Alexander V. Lukyanov <<EMAIL>>

	* FileSetOutput.cc, FileSetOutput.h, commands.cc,
	  FindJobDu.cc, FindJobDu.h: update for new human_readable; use enum
	  for long options.
	* MirrorJob.cc: use enum for long options.
	* ftpclass.cc: load ssl keys for data connection.
	* resource.cc: new setting ftp:ssl-data-use-keys.

2006-08-04  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: disable ssl on errors if not forced.
	* Timer.cc: fix timer chain head corruption.

2006-08-02  Daniel Black <<EMAIL>>

	* Makefile.am: fix linking with modules and socks.
	* CmdExec.cc, CmdExec.h, ConnectionSlot.cc, Job.cc: save url returned
	  from GetConnectURL from overwriting by next GetConnectURL call.
	* lftp_ssl.cc: don't free cred if it was not allocated.

2006-08-01  Pascal Bleser <<EMAIL>>

	* Makefile.am: fixed install with modules.

2006-07-28  Alexander V. Lukyanov <<EMAIL>>

	* HttpDir.cc: fixed proto comparision when using hftp with ftp url scheme.

2006-07-27  Alexander V. Lukyanov <<EMAIL>>

	* FileSet.cc, FileSet.h: optimize FileSet::Merge.
	* NetAccess.cc: make sure FileSet is properly sorted after tilde
	  transformation.

2006-07-26  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: change use_propfind_now in Reconfig.
	* FileAccess.cc: add references to classes which are used in modules,
	  this fixes static compilation.
	* lftp.cc: remove DirColors reference (in FileAccess.cc now).
	* Makefile.am: better use of libtool capabilities.

2006-07-25  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc: use timestamp only if it's precise.
	* HttpDir.cc: fix month off-by-one.
	* FileCopy.cc: use SetSize so that seek_pos gets changed from FILE_END;
	  debug state changes.
	* Makefile.am: use liblib.la; don't use lib for includes.

2006-07-22  Nix  <<EMAIL>>

        Adapt to synch with gnulib-20060722.

        * ChmodJob.cc: filemode.h needs CDECL guards.
        * ChmodJob.cc (ChmodJob): mode_free() is gone, just free() now.
        * ChmodJob.cc (GetMode): Adjust mode_adjust() prototype, keeping
        current behaviour by deactivating umasking (which is meaningless
        on remote systems in any case).
        * ChmodJob.cc (CurrentFinished): mode_string() is now named strmode().
        * ChmodJob.cc (RelativeMode): mode_change is now an array, not a
        linked list.
        * ColumnOutput.cc (append): mbswidth()'s flags have flipped.
        * FindJobDu.cc (print_size): human_readable() and human_readable_inexact()
        have merged.
        * FileSetOutput.cc: filemode.h needs CDECL guards.
        * FileSetOutput.cc (print): human_readable(), mode_string() and mbswidth
        flag changes as above.
        * MirrorJob.cc (ShowRunStatus): mbswidth flag changes.
        * StatusLine.cc (update): Likewise.
        * commands.cc (CMD): Likewise.
        * misc.cc (squeeze_file_name): Likewise.

        * filemode.c: Synched with gnulib; moved to lib/.
        * human.c: Likewise.
        * modechange.c: Likewise.
        * xstrtol.c: Likewise.
        * xstrtoul.c: Likewise.

        * mbswidth.c: Synched with gnulib.
        * mbswidth.h: New, from gnulib.
        * wcwidth.h: Likewise.

        * Makefile.am (liblftp_tasks_la_SOURCES): Remove stuff that's
        moved to lib/.
        * Makefile.am (liblftp_jobs_la_SOURCES): This depends on
        liblftp-tasks.la.
        * Makefile.am (INCLUDES): Include from lib/.

2006-07-18  Alexander V. Lukyanov <<EMAIL>>

	* lftp.cc: add --user and --password options for open.

2006-07-10  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: use ascii mode for ls, as before.

2006-07-07  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: update help for pget.

2006-07-06  Alexander V. Lukyanov <<EMAIL>>

	* lftp_ssl.cc, lftp_ssl.h, ftpclass.cc, Http.cc: don't load keys/certs
	  for ftp data connection.

2006-07-05  Alexander V. Lukyanov <<EMAIL>>

	* SleepJob.cc: disable option reordering in repeat; small cleanup.
	* pgetJob.cc: fix a coredump.

2006-07-04  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.cc, FileAccess.h, Http.cc: (SetLimit) new method;
	  (UNKNOWN_POS) new constant; move FILE_END here.
	* FileCopy.cc, FileCopy.h: use SetLimit; move FILE_END to FileAccess.h.

2006-07-03  Alexander V. Lukyanov <<EMAIL>>

	* CopyJob.h: (SetRangeLimit) new method.
	* FileCopy.h: (SetRangeLimit) new method; make SetRange seek when needed.
	* pgetJob.cc, pgetJob.h: add explicit start0 and limit0 members for chunk 0;
	  read position for chunk 0 before trying to fetch it (when pget_cont).

2006-06-30  Alexander V. Lukyanov <<EMAIL>>

	* ResMgr.cc, ResMgr.h, resource.cc, Cache.h: rename ResDecl to ResType;
	  make ResDecl a derived from ResType; make resources a static array
	  of structures (to save on constructors); make ResDecls class to
	  register bulk of ResType's.
	* GetJob.cc, GetJob.h: add truncate_target_first flag (for pget).
	* pgetJob.cc, pgetJob.h, MirrorJob.cc, commands.cc: support pget -c.

2006-06-28  Alexander V. Lukyanov <<EMAIL>>

	* GetJob.cc, GetJob.h, pgetJob.cc, pgetJob.h: simplify copy peer
	  creation; pass url from pget to CreateCopyPeer (this fixes
	  pget http://...?args).
	* resource.cc, ftpclass.cc: add use-allo setting.

2006-06-26  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, FileCopy.cc, FileCopy.h: support for ALLO command; get file size
	  for local sources in any case.
	* Fish.cc: use GNU head if available for upload; use a larger bs for dd in upload
	  and count characters with wc -c (a great speed up for both cases). A fixed
	  version of script by Dmitry Butskoj <<EMAIL>> was used (from mc).

2006-06-19  Alexander V. Lukyanov <<EMAIL>>

	* StatusLine.cc: handle prev_line==0 case (fixes coredump).

2006-06-16  Alexander V. Lukyanov <<EMAIL>>

	* SleepJob.cc, SleepJob.h: new options for repeat (--while-ok, --until-ok).

2006-06-15  Alexander V. Lukyanov <<EMAIL>>

	* SleepJob.cc, SleepJob.h, commands.cc: new options for repeat (--count, --delay).
	* NetAccess.cc, NetAccess.h, Http.cc: drop two arg SetSocketBuffer and
	  SetSocketMaxseg methods.
	* ftpclass.cc, ftpclass.h, resource.cc: move abor_close_timer to Ftp::Connection;
	  init it from resource ftp:abor-max-wait; read and discard data from aborted
	  data connection.

2006-06-14  Alexander V. Lukyanov <<EMAIL>>

	* lftp.cc: use TimeInterval two arg ctor.
	* ftpclass.cc, ftpclass.h: use abor_close_timer instead of abor_time;
	  use Time::Passed and TimeDiff vs int cmp.
	* Timer.cc, Timer.h: new method IsInfty; reset event when start==now.
	* TimeDate.cc, TimeDate.h: fix normalize for negative msec;
	  inline lt; new method Time::Passed; comparision of TimeDiff with
	  int seconds.
	* SleepJob.cc, SleepJob.h: use Timer.
	* commands.cc: don't use Ascii for quote, this fixes `quote POST'.

2006-06-13  Alexander V. Lukyanov <<EMAIL>>

	* DummyProto.cc, DummyProto.h, : const everywhere.
	* FileAccess.cc, FileAccess.h: consts; drop url member; new cache; ClassCleanup.
	* FileCopy.cc, FileCopy.h, Fish.cc, Fish.h, FtpDirList.cc,
	  FtpListInfo.cc, GetFileInfo.cc, Http.cc, Http.h, HttpDir.cc,
	  Job.cc, LocalAccess.cc, LocalAccess.h, NetAccess.cc, NetAccess.h,
	  SFtp.cc, SFtp.h, commands.cc, ftpclass.cc, ftpclass.h: const; new cache.
	* Cache.cc, Cache.h: new files; base Cache class.
	* LsCache.cc, LsCache.h, Resolver.cc, Resolver.h: use base Cache class.
	* Makefile.am: add Cache files.
	* ResMgr.cc, ResMgr.h: consts.
	* Timer.cc, Timer.h: add ctor from resource/closure; drop iterators.
	* lftp.cc: call FileAccess::ClassCleanup; ajust max tasks left.

2006-06-09  Alexander V. Lukyanov <<EMAIL>>

	* Fish.cc, FtpDirList.cc, HttpDir.cc, SFtp.cc, NetAccess.cc: cache is now enabled per host.
	* LsCache.cc, LsCache.h: derive LsCache from Timer; drop expire_helper.
	* ResMgr.cc, ResMgr.h: add ResMgr::SimpleQuery(ResDecl); optimize queries.
	* SMTask.cc, SMTask.h: add PrintTasks debug method.
	* TimeDate.h: add TimeInterval comparisions.
	* Timer.cc, Timer.h: add Iterate* methods; add TimeLeft method.
	* commands.cc: call PrintTasks from .tasks command.
	* FileAccess.cc, FileAccess.h, NetAccess.cc, NetAccess.h: make Connect
	  and ConnectVerify non-virtual; init timers in ResetLocationData.
	* Timer.cc, Timer.h: add ResetDelayed method; drop Reset(time_t).
	* buffer.h: make event_time of type Time.
	* ftpclass.cc, ftpclass.h: use Timer for STAT timing.

2006-05-26  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc: use correct ctor of Timer.
	* IdNameCache.cc, IdNameCache.h: don't use Delete for Timer.
	* SMTask.cc: use Timer timeout and call Timer::ReconfigAll.
	* TimeDate.h: drop unneeded operators.
	* Timer.cc, Timer.h: don't inherit from SMTask for efficiency.
	* ftpclass.cc: don't call Timer::Reconfig - not needed now.

2006-05-26  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.cc, FileAccess.h: move Poll and CheckHangup to NetAccess;
	  drop event_time member and BumpEventTime method.
	* Fish.cc, Http.cc, SFtp.cc, ftpclass.cc, ftpclass.h: use
	  timeout_timer.Reset instead of BumpEventTime.
	* NetAccess.cc, NetAccess.h: drop timeout member; add timeout_timer;
	  drop duplicate member retries (fixes a bug).
	* TimeDate.h: make time_t operator const.
	* Timer.cc, Timer.h: allow Reset from Time or Timer; reset only to a
	  later time.

2006-05-25  Alexander V. Lukyanov <<EMAIL>>

	* Fish.cc, Http.cc, NetAccess.cc, NetAccess.h, Resolver.cc, Resolver.h,
	  SFtp.cc, ftpclass.cc, ftpclass.h: use Timer for net:idle, dns:fatal-timeout.
	* ResMgr.h, TimeDate.h: add default TimeInterval{R,} ctors.
	* LsCache.cc: use TimeIntervalR to convert string to TimeInterval.
	* Timer.h, Timer.cc: add const; new methods GetLastSetting, TimePassed,
	  TimeRemains.
	* misc.h: move MINUTE, HOUR, DAY to TimeDate.h.
	* resource.cc: change net:idle format to TimeInterval.
	* SleepJob.cc: use SMTask::now.
	* SMTask.cc, SMTask.h, lftp.cc: use TimeInterval and Timer in RollAll.
	* Timer.cc, Timer.h: use TimeInterval in Set.

2006-05-24  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc, FileSetOutput.cc, Fish.cc, Http.cc, Resolver.cc,
	  SFtp.cc, SMTask.cc, ftpclass.cc: explicitly request unix time.
	* LsCache.cc: use numeric TimeInterval ctor.
	* ResMgr.cc, ResMgr.h: split off TimeIntervalR from TimeInterval;
	  move TimeInterval to TimeDate.{cc,h}.
	* Resolver.cc: use TimeInterval::Finished method.
	* SleepJob.cc, SleepJob.h: use Time; TimeInterval::GetTimeout.
	* Speedometer.cc: explicitly create TimeDiff.
	* TimeDate.cc, TimeDate.h: optimize time_tuple and Time methods;
	  move TimeInterval here.
	* Timer.cc: use TimeIntervalR.
	* ftpclass.cc: close aborted data connection in few seconds after ABOR;
	  don't use ABOR just after another command.
	* ftpclass.h: add abor_time and last_cmd_time members.

2006-05-19  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc: fixed dyn_cmd_table initialization - last static command
	  was overwritten due to off-by-one error.

2006-05-13  Alexander V. Lukyanov <<EMAIL>>

	* Fish.cc, SFtp.cc, resource.cc: add support for recoding in fish
	  and sftp (version<4); new settings fish:charset, sftp:charset.

2006-05-03  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: fixed compilation without ssl.
	* SMTask.cc, SMTask.h: fix compilation with some compilers (e.g. sun).
	* commands.cc: support auto-rename in get1.
	* FileCopy.h: add AutoRename method.
	* Http.cc: fixed extraction of suggested file name; fixed decoding
	  of chunked transfers when content-length is explicitly known.

2006-04-25  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: add zlib version; wrap library line nicely.

2006-04-24  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: init ssl layer after connection through http proxy is
	  established.
	* commands.cc: pass port to FileAccess::New along with hostname, this
	  fixes opening sites with explicit port (it could either use default
	  port or spin).

2006-04-19  Alexander V. Lukyanov <<EMAIL>>

	* ArgV.cc, ArgV.h: include getopt.h in the header.
	* CmdExec.cc: add defaults for session and cwd when creating CmdExec.
	* FileAccess.cc, FileAccess.h: make sure classes are inited; add port
	  argument for New method; do Connect in New.
	* Job.cc, Job.h: add WaitDone method.
	* MirrorJob.cc: no need to include getopt.h.
	* ResMgr.h, resource.cc: add class_inited static member.
	* commands.cc: move history command to lftp.cc.
	* ftpclass.cc: handle PRET errors.
	* getdate.y: use plain free.
	* lftp.cc: move history here; ClassInit's are called automatically now;
	  use Job::WaitDone.
	* log.h: add ShowNothing method.
	* example1.cc, example2.cc: fixed for new interfaces.
	* example1-cmd.cc: new example.
	* example-module1.cc: new example.

2006-04-07  Alexander V. Lukyanov <<EMAIL>>

	* SFtp.cc, Fish.cc: send password twice if needed, it handles the case
	  of a protected private key.

2006-04-05  Alexander V. Lukyanov <<EMAIL>>

	* FileCopyOutputJob.cc: don't return MOVED if nothing changed, this
	  fixes rare spinning.
	* CmdExec.cc, CmdExec.h, lftp.cc: rename top_exec to CmdExec::top;
	  add CmdExec::Exit method.
	* Job.cc: don't show status of deleted jobs.
	* commands.cc: add exit options: top, kill; accept exit code along
	  with options.

2006-03-31  Alexander V. Lukyanov <<EMAIL>>

	* lftp.cc: get exit_code before AtExit, so it is not changed by
	  cmd:at-exit commands.
	* Http.cc: workaround some servers' problem with size>2G.

2006-03-30  Alexander V. Lukyanov <<EMAIL>>

	* lftp_ssl.cc: make scheduler run after ssl handshake - this fixes
	  ftp upload hang when the file is empty.

2006-03-15  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc, resource.cc: add mirror:dereference setting,
	  --no-dereference, --perms options.

2006-03-15  Alexander V. Lukyanov <<EMAIL>>

	* FindJob.cc, FindJob.h: add PROCESSING state, so Chdir
	  is not called continuously.
	* LsCache.cc: use Path::Change instead of Chdir.

2006-03-13  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc: don't create target file if source does not exist.
	* Job.cc: don't delete killed job right not; CollectGarbage after
	  KillAll or Cleanup, don't delete the jobs in loop.
	* Job.h: make Kill and KillAll static; add Cleanup method.
	* commands.cc: show done jobs before exit.
	* lftp.cc: do Job::Cleanup before termination.
	* resource.cc: change http:use-propfind default to no.

2006-02-16  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.cc, FileAccess.h: Close session in Chdir, VerifyPath
	  (fixed coredump).
	* LsCache.cc: avoid Chdir, as it Closes session.
	* FileCopy.cc: use SMTask ref count.
	* SMTask.cc, SMTask.h: (ref_count) new member.
	* ftpclass.cc: remove unneeded abort().

2006-02-14  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: fixed condition when exact time is needed for cls.

2006-02-08  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: fixed a stupid bug with cls (time-style).
	* resource.cc: assign env TIME_STYLE to cmd:time-style.

2006-02-06  Alexander V. Lukyanov <<EMAIL>>

	* FileSetOutput.cc, commands.cc: add cmd:time-style setting.

2006-01-23  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc, Http.h: use PROPFIND for cd check before HEAD.

2006-01-15  Alexander V. Lukyanov <<EMAIL>>

	* FileSetOutput.cc, FileSetOutput.h, commands.cc: add cls --time-style
	  option.

2006-01-12  Alexander V. Lukyanov <<EMAIL>>

	* complete.cc: fixed an abortion when cmd:remote-completion is false.
	* lftp_ssl.cc: don't do shutdown/bye, as it makes problems with CCC.
	* Makefile.am: use automake conditionals to select modules to build.

2005-12-28  Alexander V. Lukyanov <<EMAIL>>

	* FileSet.cc, Filter.cc, Fish.cc, FtpListInfo.cc, Http.cc, HttpDir.cc,
	  ftpclass.cc, keyvalue.cc: handle return values (fix warnings).
	* Makefile.am: install shared libs liblftp-tasks and liblftp-jobs;
	  don't make static libs when not needed.

2005-12-27  Alexander V. Lukyanov <<EMAIL>>

	* FileSet.cc, FtpListInfo.cc, bookmark.cc, ftpclass.cc, history.cc,
	  misc.cc, netrc.cc, keyvalue.cc: handle return values (fix warnings).
	* ResMgr.cc, ResMgr.h: add TimeInterval::Set method.
	* Resolver.cc, resource.cc: add dns:max-retries setting; prefer
	  getaddrinfo if available; change dns:fatal-timeout to TimeInterval type.
	* lftp_ssl.cc: treat GNUTLS_E_UNEXPECTED_PACKET_LENGTH as EOF indicator;
	  output a warning when that error appears.
	* FileCopy.cc: try to put available data in case of `get' error;
	  optimize line-buffering.
	* misc.cc, misc.h: (memrchr) new function.
	* ftpclass.cc, ftpclass.h: (Connection::CloseDataSocket) new method;
	  close data socket in case of data connection error for the case
	  when error handling is delayed.

2005-12-22  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: add libiconv version to `version' output.

2005-12-15  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc, rmJob.h, commands.cc: don't remove directories
	  recursively when NO_RECURSION flag is set; drop rmdirJob class;
	  add rmJob::Rmdir method.
	* SMTask.h, LsCache.cc: (SMTask::DeleteLater) new method; use it.

2005-12-14  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: fix netrc usage when no user name is given.
	* ftpclass.cc: flush cache when changing charset.

2005-12-08  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc, commands.cc: show all queued commands on `queue' command;
	  clear done jobs even if queue is stopped.

2005-12-04  Alexander V. Lukyanov <<EMAIL>>

	* ResMgr.cc, ResMgr.h: support open ranges.

2005-12-02  Alexander V. Lukyanov <<EMAIL>>

	* misc.cc: fix guess_year for month boundary.
	* ftpclass.cc: another fix for sun c++ compiler.
	* lftp_tinfo.cc: use termcap.h if available and no curses; fix usage of tgetstr.

2005-11-30  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc, MirrorJob.h: add --max-errors option.
	* SFtp.cc, SFtp.h: add support for sftp version 5 and 6.

2005-11-29  Alexander V. Lukyanov <<EMAIL>>

	* CatJob.cc: use DEFAULT_PAGER.
	* FileCopy.cc, FileCopy.h: add suggested_filename member; copy it
	  from get to put; add auto_rename member; prepare for automatic renaming.
	* ftpclass.cc: fix compilation with Sun comiler.

2005-11-22  Alexander V. Lukyanov <<EMAIL>>

	* lftp_ssl.cc: set handshake_done for openssl - this fixes IOBufferSSL::Done().

2005-11-21  Alexander V. Lukyanov <<EMAIL>>

	* FileSet.cc, FileSet.h: add new filtering modes - SubtractNewerThan,
	  SubtractSizeOutside.
	* MirrorJob.cc, MirrorJob.h: add --size-range and --older-than options.
	* ResMgr.cc, ResMgr.h: change Range to use long long; fixed a bug with
	  start/end reversed.

2005-11-18  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h: allow PROT command to be used
	  after CCC; issue PROT before CCC if all transfers has to be
	  protected; open data ssl layer when PROT=P even after CCC.

2005-11-17  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, buffer.h: move doubling of IAC to proper level,
	  that is do it in IOBufferTelnet.

2005-11-11  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: fixed handling of 334 reply to AUTH command.

2005-11-10  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: fix compilation without ssl.
	* lftp_rl.c: fix compilation with some compilers.

2005-11-08  Alexander V. Lukyanov <<EMAIL>>

	* Resolver.cc: fix compilation when dn_expand is not declared (e.g. HP-UX).
	* commands.cc: fix `open -u user URL'.
	* ftpclass.cc, ftpclass.h, resource.cc: add ftp:ssl-use-ccc setting.
	* lftp_ssl.cc: shutdown ssl connection before destruction.

2005-10-25  Alexander V. Lukyanov <<EMAIL>>

	* bookmarks.cc: (hide_password) move to url.
	* url.cc, url.h: (hide_password) move here; (remove_password) new
	  function.
	* CmdExec.cc: use squeeze_file_name when showing cd status.
	* misc.cc: (squeeze_file_name) use url::remove_password.
	* buffer.cc, buffer_ssl.cc: don't return MOVED if we had eof already -
	  this fixes a rare spinning.

2005-10-20  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: add workaround for proftpd resetting data connection
	  when there is no files for NLST.

2005-10-19  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, Http.cc: check ReconnectAllowed() after host name
	  resolving.

2005-10-18  Alexander V. Lukyanov <<EMAIL>>

	* misc.cc: (get_lftp_home) return NULL if LFTP_HOME is empty.
	* history.cc, bookmark.cc, lftp_rl.c: don't read/write anything to
	  user-specific files if get_lftp_home returns NULL.
	* FileAccess.cc: swap default SameSiteAs and SameLocationAs, this
	  fixes loop in `open file:/path'.
	* SMTask.cc: (CollectGarbage) don't protect scan->next if it is NULL.

2005-10-18  Alexander V. Lukyanov <<EMAIL>>

	* SMTask.cc, SMTask.h: do delete the task in Delete if not running;
	  (CollectGarbage) protect next element in chain when deleting one task.
	* Job.cc: (BuryDoneJobs) avoid looping caused by Delete not
	  always deleting the task.
	* misc.cc, misc.h: (get_lftp_home) new function.
	* bookmark.cc, commands.cc, history.cc, import-ncftp, import-netscape,
	  lftp.cc, lftp_rl.c: use it (based on patch by Nikolai Weibull
	  <<EMAIL>>).

2005-10-17  Alexander V. Lukyanov <<EMAIL>>

	* SMTask.cc, SMTask.h: simplify Schedule; make garbage collection
	  a separate function; don't delete the task in Delete, just mark
	  it for GC.
	* ProcWait.cc: don't rely on Delete to delete the task, just mark
	  it for deletion.

2005-10-17  Paul TBBle Hampson <<EMAIL>>

	* lftp.cc: add missing initialization for add_newline.

2005-10-06  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: use xfer:destination-directory only for get/mget,
	  not for put/mput.

2005-10-06  Alexander V. Lukyanov <<EMAIL>>

	* Job.cc: fixed another coredump with KillAll/Schedule.

2005-10-05  Alexander V. Lukyanov <<EMAIL>>

	* Job.cc, Job.h: fixed core dump with KillAll.
	* HttpDir.cc: fixed prefix path removing.

2005-10-01  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: skip all spaces in FEAT reply to workaround some
	  broken servers.

2005-09-30  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc, resource.cc: new setting xfer:destination-directory.

2005-09-27  Alexander V. Lukyanov <<EMAIL>>

	* lftp_ssl.h: make handshake_done public.
	* lftp_ssl.cc: do_handshake even when writing no data.
	* buffer_ssl.cc, buffer_ssl.h: call ssl->write even with no data
	  when handshake is not done yet; (Done) check handshake_done -
	  this fixes a bug with uploading empty files.
	* ftpclass.cc: check buf->Done instead of Size.

2005-09-26  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: simplify path combination; pre-encode %2F for hftp.
	* HttpDir.cc: ignore special and closing tags; move workaround for
	  broken proxies to handle all links; specially handle empty paths.
	* FileAccess.cc: don't change url when going up .. - simply drop it.

2005-09-22  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: fixed exit code of slot command.

2005-09-19  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc: don't add basename of source dir if target is slot:
	  or bm: pseudo-url without trailing slash.
	* history.cc: decode only non-urls.
	* Http.cc: fix path combination (avoid double slash); use new_cwd->url
	  when available.

2005-09-07  Alexander V. Lukyanov <<EMAIL>>

	* lftp_ssl.cc: free ca_list and crl_list when an error happens while
	  loading the certificates. This fixes a core dump.

2005-09-05  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: fixed `open host.com/path'; fixed a coredump
	  when a bookmark contained lots of quotable characters.

2005-08-12  Alexander V. Lukyanov <<EMAIL>>

	* parsecmd.cc: return PARSE_AGAIN if the command ends with \0,
	  which means it is incomplete.
	* CmdExec.cc: use now instead of time().
	* StatusLine.cc: use ShowN in Clear so that status line is not
	  continuously cleared while executing scripts.

2005-08-09  Alexander V. Lukyanov <<EMAIL>>

	* HttpDir.cc: don't try PROPFIND on a file to get file listing.

2005-08-05  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc, MirrorJob.h: fixed --loop without --delete (the loop
	  was infinite if there were files to be deleted).

2005-08-04  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc, Fish.cc, SFtp.cc: fixed a hang-up in http (and possibly
	  in other protocols).
	* FileAccess.cc, FileAccess.h: make Path::Change accept url-encoded
	  path too.
	* CmdExec.cc: allow cd redirections to file.

2005-08-03  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc, CmdExec.h: use FileAccess::Path for old_cwd.
	* ConnectionSlot.cc, ConnectionSlot.h, FindJob.cc, FindJob.h,
	  GetFileInfo.cc, GetFileInfo.h, LsCache.cc: use FileAccess::Path.
	* FileAccess.cc, FileAccess.h: use cwd.url in GetFileURL; new method
	  PathVerify; set new_cwd->url in SetFileURL; use Path for saved_cwd;
	  modify url in Path::Change; unset is_file for some known directories;
	  implement operator== for Path; new method ChdirAccept.
	* Http.cc: modify url generation using cwd.url and cwd.is_file.
	* commands.cc: modify cd and open to work with file URLs;
	  fixed `source' command to return error code on bad files.
	* history.cc, history.h: save URL in cwd history if present;
	  add single argument Set method.
	* url.cc, url.h: (dir_needs_trailing_slash) new function.

2005-07-18  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc: don't request date/size information when not needed.

2005-07-06  Alexander V. Lukyanov <<EMAIL>>

	* SFtp.h: fixed a seg fault on empty directories.
	* LsCache.cc: use replace_value.

2005-07-04  Alexander V. Lukyanov <<EMAIL>>

	* resource.cc: make inet6 the first af to lookup.
	* CmdExec.cc: add cmd:trace setting.

2005-06-29  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: accept AUTH and REST with no arguments in FEAT reply;
	  use ftp:auto-passive-mode setting.
	* resource.cc: add ftp:auto-passive-mode setting.
	* MirrorJob.cc: workaround FreeBSD tcgetpgrp bug.

2005-06-28  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.cc, FileAccess.h: (FileAccess::Path) new class.
	* Fish.cc, Http.cc, LocalAccess.cc, SFtp.cc, ftpclass.cc: use it properly.
	* FileAccess.cc: (Path::ExpandTilde) fix NULL dereference.
	* Fish.cc: use home_auto; call LsCache::SetDirectory.

2005-06-23  Alexander V. Lukyanov <<EMAIL>>

	* bookmarks.cc, bookmarks.h, commands.cc: (bmk:auto-sync) new setting;
	  (list-p, load, save) new bookmark subcommands.

2005-06-22  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc, FileCopy.h: (GetURL) new method; don't delete peers too
	  early, so info can be get from them after transfer.
	* CmdExec.cc, CopyJob.h, FileCopy.cc, FileCopy.h, FileSetOutput.cc,
	  FileSetOutput.h, Fish.cc, Fish.h, FtpDirList.cc, FtpDirList.h,
	  Http.cc, Http.h, HttpDir.cc, HttpDir.h, OutputJob.cc, OutputJob.h,
	  SFtp.cc, SFtp.h, SMTask.cc, SMTask.h, buffer.cc, buffer.h,
	  ftpclass.cc: (SuspendInternal, ResumeInternal, SuspendSlave,
	  ResumeSlave) new methods to simplify suspend/resume with slave tasks;
	  make suspend member private, use IsSuspended.
	* CopyJob.cc, CopyJob.h, Job.h, MirrorJob.cc: (GetBytesCount,
	  GetTimeSpent) make them virtual; (FormatBytesTimeRate) new method;
	  use it in mirror.

2005-06-21  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc, CmdExec.h, FileFeeder.cc, FileFeeder.h: add Fg/Bg methods
	  to CmdFeeder to allow passing terminal to subprocesses; allocate
	  FgData for FileFeeder.
	* Filter.cc, Filter.h: move cwd to FDStream; add GetCwd, SetProcGroup
	  methods.
	* PtyShell.h: make GetProcGroup const.
	* ProcWait.h: (GetState) return enum type.
	* ResMgr.cc, ResMgr.h: (FileExecutable) new method; optimize file checking.
	* commands.cc: use SetCwd instead of Chdir for setting cwd from history.
	* lftp_ssl.cc: add Fedora-4 ca-bundle location.
	* misc.cc: fix gcc4 warning.
	* FileCopy.cc, FileCopy.h, resource.cc: new settings xfer:verify,
	  xfer:verify-command; verify completely received file.
	* FileCopy.cc, FileCopy.h: show verify status; fix problems with
	  xfer:verify=no; show only last line of verify error.
	* Filter.cc, Filter.h: (StdoutToNull) new method.
	* verify-file: add rpm support.

2005-06-16  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc: fix exit code for redirections.

2005-06-14  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, SFtp.cc, Fish.cc, Http.cc: use BumpEventTime
	  instead of copying event_time in MoveConnectionHere, this fixes
	  timeout error when net:idle > net:timeout.
	* Filter.cc, Filter.h: move full_name to base class FDStream.
	* LsCache.cc, Resolver.cc, Timer.cc: use new TimeInterval ctor.
	* NetAccess.cc, resource.cc: make net:timeout a TimeInterval.
	* ResMgr.cc, ResMgr.h: make TimeInterval ctor for ResValue.

2005-06-08  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc: improve status line.

2005-06-02  Alexander V. Lukyanov <<EMAIL>>

	* FindJob.cc: don't add a slash for root directory.

2005-05-24  Alexander V. Lukyanov <<EMAIL>>

	* lftp_ssl.cc, lftp_ssl.h: make it compatible with gnutls 1.0.x.

2005-05-20  Alexander V. Lukyanov <<EMAIL>>

	* SFtp.cc: fix handling of file names starting with a tilde.

2005-05-19  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: fix compilation on AIX 5.1
	* commands.cc: fixed minor problem with `lftp -u user --help'.

2005-05-18  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: use dlsym to find library versions, this fixes
	  compilation with modules.
	* lftp_ssl.cc, lftp_ssl.h: (LoadCA, LoadCRL) new methods.
	* lftp.cc: don't call lftp_ssl as it can be modular.

2005-05-17  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc, buffer_ssl.h, ftpclass.cc, ftpclass.h, module.cc,
	  resource.cc: fixed compilation without ssl.

2005-05-13  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc: fix coredump when symlink target is unknown.
	* lftp_ssl.cc: set cert error if cert type is not supported.

2005-05-12  Alexander V. Lukyanov <<EMAIL>>

	* FtpListInfo.cc: add AS/400 parser, handle trailing slash in NLST.

2005-05-11  Alexander V. Lukyanov <<EMAIL>>

	* lftp_ssl.cc: allow V1 root crt.

2005-05-06  Alexander V. Lukyanov <<EMAIL>>

	* lftp_ssl.cc, lftp_ssl.h: fixes compilation with openssl; add instance
	  to openssl code; improve temp error handling for gnutls.
	* commands.cc: improve version info.

2005-05-05  Alexander V. Lukyanov <<EMAIL>>

	* lftp_ssl.cc, lftp_ssl.h: verify peer certificate chain with gnutls;
	  load CA and CRL globally; gnutls debugging support.
	* lftp.cc: deinit ssl.
	* resource.cc: ssl:*-path are only available with openssl.

2005-04-29  Alexander V. Lukyanov <<EMAIL>>

	* HttpDir.cc, HttpDirXML.cc: replace atoll with sscanf.
	* GetJob.cc: do expand_home_relative on output file name.
	* Filter.cc, PtyShell.cc: fix gcc4 warning.
	* Makefile.am: add gnutls support.
	* buffer_ssl.cc: use ssl->error member.
	* lftp_ssl.cc, lftp_ssl.h: implement basic gnutls support.

2005-04-29  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc, buffer_ssl.cc, buffer_ssl.h, ftpclass.cc, ftpclass.h,
	  lftp_ssl.cc, lftp_ssl.h, misc.cc, misc.h: prepare for gnutls
	  usage - make lftp_ssl class.

2005-04-20  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: (data_address_ok) don't return false if no checking
	  needed and getpeername fails; send CLNT before LANG and OPTS UTF8.

2005-04-11  Alexander V. Lukyanov <<EMAIL>>

	* SFtp.cc: resume recv_buf in Close, this fixes a rare hang.

2005-04-07  Alexander V. Lukyanov <<EMAIL>>

	* SFtp.cc: (SFtpListInfo::Do) check if done first, this fixes
	  looping in SMTask::Roll.

2005-04-06  Alexander V. Lukyanov <<EMAIL>>

	* NetAccess.cc: retry with LONG_LIST in case of any error with MP_LIST.
	* MirrorJob.cc: don't let parent mirror act in between our actions;
	  let other tasks run when ListInfo are created, this fixes mirror
	  multitasking when using listings from cache.

2005-04-05  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc, MirrorJob.h: add --loop option.

2005-04-04  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.cc, FileAccess.h, buffer.cc, buffer.h: (SetErrorCached) new method.
	* commands.cc: don't quote lftp -c argument when it is single.
	* FileCopy.cc, Fish.cc, FtpDirList.cc, HttpDir.cc, LsCache.cc, LsCache.h,
	  NetAccess.cc, SFtp.cc: add negative cache support.
	* Http.cc: set use-propfind and use-mkcol when those methods are unsupported.

2005-04-01  Alexander V. Lukyanov <<EMAIL>>

	* SFtp.cc: re-request missed data (could happen when file is growing).

2005-03-29  Alexander V. Lukyanov <<EMAIL>>

	* HttpDir.cc, HttpDir.h, HttpDirXML.cc: parse invalid XML as html,
	  this works around some servers that return plain index for PROPFIND.

2005-03-28  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: interpret the rest of line after lftp -c as command.
	* Http.cc, HttpDir.cc, HttpDir.h, HttpDirXML.cc: use USE_EXPAT define.

2005-03-24  Alexander V. Lukyanov <<EMAIL>>

	* HttpDirXML.cc: fixed coredump when there is no files; added
	  handler for creator-displayname; check parent node for href.

2005-03-13  Alexander V. Lukyanov <<EMAIL>>

	* buffer.cc: prevent spinning when disk is full in IOBufferFDStream;
	  better saving of errno.
	* Filter.cc, Filter.h: allow passing errno value to MakeErrorText.

2005-03-11  Alexander V. Lukyanov <<EMAIL>>

	* HttpDir.cc: extract listing info for HttpListInfo.

2005-03-09  Alexander V. Lukyanov <<EMAIL>>

	* FileCopyFtp.cc, ftpclass.h: handle timeout when waiting for ack.
	* misc.cc: (strip_trailing_slashes) don't strip last slash in root directory.

2005-03-05  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.cc: don't strip trailing slash in OptimizePath.
	* Http.cc: send full URL in Destination header; fix Done for a number
	  of methods.
	* misc.cc, misc.h: (strip_trailing_slashes) new function.
	* mvJob.cc: append basename of target ends with a slash.
	* ftpclass.cc, resource.cc: (ftp:ignore-pasv-address) new setting.
	* Http.cc, Http.h: add support for `quote MOVE/COPY'.
	* FileAccess.cc: set event_time in Open to prevent false timeout.

2005-03-01  Alexander V. Lukyanov <<EMAIL>>

	* NetAccess.cc: make TOS changes compile on solaris.

2005-03-01  Mark Schreiber <<EMAIL>>

	* ftpclass.cc, NetAccess.cc, NetAccess.h: use TOS on ftp connections.

2005-02-25  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: set C locale before time conversion (fixes Last-Modified
	  parsing).
	* FileSetOutput.cc: get time format at run time, not statically.
	* Http.cc, resource.cc: add use-propfind and use-mkcol settings, turn
	  them off by default for hftp.

2005-02-23  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc, HttpDir.cc, HttpDir.h, HttpDirXML.cc: compile without
	  expat.
	* SFtp.cc: fix Buffered so that it does not return more than possible.
	* misc.cc: fix guess_year for timezone mismatches.

2005-02-10  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.cc: more verbose error for NOT_SUPP.
	* Http.cc: add NOT_SUPP error for MP_LIST mode.
	* HttpDir.cc, HttpDirXML.cc, HttpDir.h: add support for MP_LIST
	  transformation into human readable listing.
	* ftpclass.cc: don't send further commands until data socket is
	  connected (in sync mode).

2005-02-08  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc: split off FileCopyPeerOutputJob.
	* FileSet.cc, FileSet.h, FileSetOutput.cc, FileSetOutput.h,
	  commands.cc: add support for reverse sorting.
	* example2.cc: blocking fix.
	* FileCopyOutputJob.cc: new file (split from FileCopy.cc).
	* Makefile.am: move some modules to libtasks, where they belong.

2005-02-04  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.cc, FileAccess.h: move entity_content_type here; add
	  entity_charset.
	* FileSet.cc, FileSet.h: (allocated) new member; optimize memory
	  allocation.
	* Http.cc, Http.h: make atotm a static method; implement MP_LIST
	  as PROPFIND; extract entity charset.
	* HttpDir.cc, HttpDir.h: try MP_LIST; call ParseProps.
	* HttpDirXML.cc: new file; implement ParseProps.

2005-02-03  Alexander V. Lukyanov <<EMAIL>>

	* SFtp.cc: don't set error when optional STAT or FSTAT return error.
	* FileGlob.cc: skip ./ in file names so that globbing works with tildes.
	* FileSet.cc: allow setting name to tail of itself.

2005-01-21  Alexander V. Lukyanov <<EMAIL>>

	* Fish.cc, Http.cc, LsCache.cc, SFtp.cc, ftpclass.cc: cast `now' to
	  time_t explicitly to please some compilers.

2005-01-21  Thomas Glanzmann <<EMAIL>>

	* SFtp.cc, Fish.cc, resource.cc: change connect-program to work with
	  Solaris 9 ssh.

2005-01-21  Alexander V. Lukyanov <<EMAIL>>

	* Fish.cc: make reget more efficient by using larger block size and
	  dd skip.
	* Resolver.cc: declare dn_expand and dn_skipname if needed.

2005-01-11  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, TimeDate.cc: fix buffer size handling for strftime.

2005-01-09  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: fixed SITE UTIME and overloaded MDTM - send proper
	  modification time.

2005-01-04  Alexander V. Lukyanov <<EMAIL>>

	* LocalAccess.cc: move Block'ing to Read and Write, this fixes spinning
	  if disk is full.
	* SFtp.cc: fix timeout handling when idle time > timeout.

2004-12-28  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc, Http.h: (DirFile) new method; use MKCOL, MOVE, add Depth
	  header for DELETE and support 102 reply (WebDAV preliminary support).
	* MirrorJob.cc, resource.cc: add mirror:set-permissions setting.

2004-12-17  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc: use get -O to reduce log size.
	* commands.cc: add -e for pget.

2004-12-16  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc: fixed mirror --no-symlinks.
	* ftpclass.cc: fixed a core dump.

2004-12-01  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: add debug message about EOF on data socket.
	* NetAccess.cc: initialize reconnect_interval_current properly.

2004-11-26  Alexander V. Lukyanov <<EMAIL>>

	* SFtp.cc: suspend/resume pty buffers too.

2004-11-19  Alexander V. Lukyanov <<EMAIL>>

	* NetAccess.cc: set TIOCOUTQ_tested so that it is not tested each time.
	* resource.cc: make ftps:initial-prot empty so that PROT would be always
	  used for ftps connections.

2004-11-18  Alexander V. Lukyanov <<EMAIL>>

	* Fish.cc: fix downloading of growing files.
	* SFtp.cc: fix excluding in SFtpListInfo (fixes e.g. mirror).

2004-11-16  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc, NetAccess.cc: don't call Do directly, use Roll.
	* SMTask.cc, SMTask.h: use separate task stack, drop prev_current member.
	* ftpclass.cc: fix Query for ftps:initial-prot; don't Close in ~Ftp.

2004-11-15  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h, Http.cc, misc.cc, misc.h: send authorization
	  to http proxy in CONNECT requests.

2004-11-13  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: fixed mkdir.
	* FileSet.cc: fixed file mode comparision.
	* resource.cc, ftpclass.cc: new setting ftps:initial-prot.

2004-11-03  Alexander V. Lukyanov <<EMAIL>>

	* Fish.cc: change command to RETRP to differentiate from plain RETR
	  in transfer continuation; modify shell command to speed up partial
	  transfers.
	* complete.cc, lftp_rl.c, lftp_rl.h: improve completion of aliases.

2004-11-03  Loic Le Loarer <<EMAIL>>

	* Fish.cc: add support for continue mode in fish protocol.

2004-11-01  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: fix condition for unbound data socket (this fixes FXP).

2004-10-28  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc, MirrorJob.h: add --no-symlinks option.
	* Http.cc: fixed Cache-Control combination - use comma instead of space.

2004-10-21  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc: fixed file removing with --delete-first option;
	  optimized done job deletion.

2004-10-20  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc: remove bogus assert.
	* MirrorJob.cc: fix permissions comarision; don't Roll parent mirror
	  if DONE (this fixes a mysterious corruption - looks like trouble
	  when MirrorJob's are deleted in wrong order).
	* SMTask.cc: add assert; check deleting before Roll loop.

2004-10-18  Alexander V. Lukyanov <<EMAIL>>

	* Fish.cc, Fish.h, SFtp.cc, SFtp.h: don't sent password several times,
	  as it most probably is incorrect.
	* CmdExec.h, parsecmd.cc: (quotable, is_space, is_quote) add utility
	  functions; use them.
	* complete.cc: (copy_word_skip, skip_word) new functions; use them for
	  argument counting; better mirror -R completion.

2004-10-17  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc: fixed rare coredump when parent_mirror was referenced
	  from dtor, and it was already deleted.

2004-10-15  Alexander V. Lukyanov <<EMAIL>>

	* Fish.cc: prepend ./ to file names starting with - or ~
	* FileCopy.cc, FileCopy.h: fixed upload corruption bug.

2004-10-14  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc: don't chmod if not needed.
	* NetAccess.cc, NetAccess.cc: (TrySuccess) new method; optimize
	  reconnect interval calculation, add reconnect_interval_curr member.
	* Http.cc, ftpclass.cc, SFtp.cc, Fish.cc: use TrySuccess.

2004-10-04  Alexander V. Lukyanov <<EMAIL>>

	* complete.cc: rough mirror -R handling.

2004-09-27  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: don't send empty Cache-Control.

2004-09-22  Alexander V. Lukyanov <<EMAIL>>

	* CatJob.cc: use ascii mode only for tty output by default.

2004-09-16  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc: fix endless loop in FileCopyPeerFDStream.
	* ftpclass.cc: don't apply ftp:port-ipv4 to FXP.

2004-09-13  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h: fix a core dump in FXP mode.

2004-09-09  Alexander V. Lukyanov <<EMAIL>>

	* Fish.cc, SFtp.cc: fix FreeBSD sshd interaction (different password
	  prompt).

2004-09-06  Alexander V. Lukyanov <<EMAIL>>

	* lftp.cc: reduce number of left over tasks after cleaning due to
	  Luke Macken modifications.

2004-09-06  Luke Macken <<EMAIL>>

	* SMTask.cc, SMTask.h, SignalHook.cc, SignalHook.h, log.cc, log.h:
	  add Cleanup methods.
	* lftp.cc: use them.

2004-09-06  Alexander V. Lukyanov <<EMAIL>>

	* Resolver.cc: use dn_expand if available.

2004-08-31  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc: make sure the file is created in FileCopyPeerFDStream
	  before setting done.

2004-08-30  Alexander V. Lukyanov <<EMAIL>>

	* url.cc: make nice orig_url in case of bm: scheme, so that
	  get `bm:<web-bookmark>/?arg=value' works well.

2004-08-23  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: add closure to cache-control setting.
	* resource.cc: add hftp:cache-control.
	* Fish.cc, Http.cc: fail store if application tries to write more than
	  requested originally.

2004-08-12  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc, Http.h, resource.cc: add http:authorization setting.

2004-08-03  Alexander V. Lukyanov <<EMAIL>>

	* buffer_std.cc, buffer_std.h: new files.
	* Makefile.am: add buffer_std.* files.
	* FindJob.cc, FindJobDu.cc: use IOBuffer_STDOUT.
	* LocalAccess.cc: workaround solaris nfs bug in case of ENOSPC.
	* SMTask.cc: little optimization for timeout==0.
	* SysCmdJob.cc: return proper error code.
	* buffer.cc, buffer.h: add generic IOBuffer::Do, remove
	  IOBufferFDStream::Do and IOBufferFileAccess::Do.
	* commands.cc: don't create FDStream for stdout in CMD(du).
	* ftpclass.cc: don't bind data socket according to net:socket-bind-*;
	  move verbose debug for bind(); take control socket address if data
	  socket is not bound to a particular address.
	* resource.cc: new setting http:cache-control.
	* Http.cc: use new setting http:cache-control; handle 503 Gateway
	  timeout specially if only-if-cached is specified.
	* NetAccess.cc: clear files info before getting.

2004-07-26  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: handle network timeout after FEAT and in some other
	  conditions.

2004-07-21  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: don't send FEAT to proxy before login.

2004-07-13  Alexander V. Lukyanov <<EMAIL>>

	* SFtp.cc, SFtp.h: implemented slow start; handle passphrase prompt;
	  print in debug wire path.
	* ftpclass.cc: proxy with combined auth supported.
	* resource.cc: new setting ftp:proxy-auth-joined; change
	  sftp:max-packets-in-flight to 16.

2004-07-08  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: remove redundant output default.
	* FindJobDu.cc: set show_sl properly.

2004-06-12  Alexander V. Lukyanov <<EMAIL>>

	* StatusLine.cc: (ShowN) show status line immediately if update_timer
	  stopped.

2004-06-10  Alexander V. Lukyanov <<EMAIL>>

	* ArgV.h, CharReader.cc, echoJob.h: use trio.h
	* ftpclass.cc: apply ftp:port-range to passive connection too; increase
	  minimum amount of data to upload before retry counter is reset.

2004-06-07  Alexander V. Lukyanov <<EMAIL>>

	* complete.cc: complete `bm:' urls.
	* misc.cc: fix url_file with file[0]=='~'.
	* url.cc: unquote host name in valid_bm; don't re-check bm validity
	  after lookup.

2004-06-02  Marc Bevand <<EMAIL>>

	* FindJobDu.cc: use long long to hold file size.

2004-06-01  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: save old directory when no cd verification is done.

2004-05-31  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: set copy_failed in NoFileCheck and TransferCheck on 4xx codes;
	  do CPSV only when copy_tls_connect is set.
	* FileCopyFtp.cc: fix compilation without ssl.

2004-05-28  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: retry FEAT after login on 503 (invalid sequence of commands).
	* Resolver.cc: fill sa_len if present.
	* Http.cc, NetAccess.cc, ResMgr.cc, misc.cc: new DECL tests and defines.
	* bookmark.cc, bookmark.h, commands.cc, CmdExec.h: move lftp_bookmarks.
	* ftpclass.cc: accept CLNT with argument in FEAT reply.
	* url.cc: (bm:) new URL scheme to refer to bookmarked URLs.

2004-05-27  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: assume SSCN is supported without FEAT.

2004-05-26  Alexander V. Lukyanov <<EMAIL>>

	* FileCopyFtp.cc, FileCopyFtp.h: vary passive_ssl_connect to find
	  suitable combination of pasv/sscn.
	* ftpclass.cc, ftpclass.h: (copy_ssl_connect) new member; clean up
	  prot and sscn modes choice; don't disconnect on pasv and port errors.
	* resource.cc: (ftp:fxp-passive-sscn) new setting to choose initial
	  sscn mode of passive end of fxp.

2004-05-25  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: drop eof flag if transfer returns error code after
	  data connection is closed.

2004-05-22  Alexander V. Lukyanov <<EMAIL>>

	* resource.cc: increase sftp:max-packets-in-flight to 8.

2004-05-21  Alexander V. Lukyanov <<EMAIL>>

	* SMTask.cc, SMTask.h: get rid of static sched_scan; don't Merge empty
	  PollVec-s.
	* PollVec.h: (IsEmpty) new method.
	* SFtp.cc, Fish.cc: don't return MOVED if recv_buf contains data and
	  was not suspended (reduces cpu usage).
	* Http.cc: check if buffer was suspended before Resume.

2004-05-20  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h: SSCN support added.
	* getdate.y: remove ^L as it confuses some broken compilers.
	* Speedometer.cc: don't Add(0) more often than 10 times a second
	  (reduces cpu usage).

2004-05-18  Alexander V. Lukyanov <<EMAIL>>

	* FileCopyFtp.cc, FileCopyFtp.h: protect FXP if ssl-protect-fxp is
	  true for any of the two servers; try to turn off protection
	  if it fails.
	* ftpclass.cc, ftpclass.h: (copy_protect) new member.
	* resource.cc: ftp:ssl-protect-fxp can now have a closure.

2004-05-17  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: wait until LANG/OPTS UTF8 complete before sending
	  more commands; activate specified charset if utf8 activation
	  fail; actually send CLNT and HOST commands; activate charset
	  from Reconfig only if no utf8 activation commands pending.
	* ftpclass.cc: use ssl-protect-fxp in FXP mode; send CPSV even
	  if seems unsupported - no choice. (suggested by Tillmann Steinbrecher
	  <<EMAIL>> w/patch).
	* resource.cc: (ftp:ssl-protect-fxp) new setting.

2004-05-11  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc: call ShowRunStatus only if necessary.
	* PollVec.cc, PollVec.h: inline Empty method.
	* SMTask.cc, SMTask.h: inline Enter/Leave methods.
	* StatusLine.cc: (CanShowNow) new method.
	* ftpclass.cc, ftpclass.h: support CPSV; add utf8_activated flag;
	  added workaround for servers which refuse to do OPTS before login.

2004-05-07  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: check use-mlsd and use-site-chmod before connecting.

2004-05-06  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: fixed core dump in case of extra server reply; don't
	  unconditionally disconnect on 221 as some broken servers use it for
	  FEAT reply. (reported by Wang WenRui <<EMAIL>>)

2004-05-04  SHIOTA Shoichi <<EMAIL>>

	* StatusLine.cc: don't write zero bytes to terminal - it can produce
	  unexpected results.

2004-04-29  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: store zero sized files properly.

2004-04-28  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: accept multiple AUTH lines in FEAT reply; check if
	  ssl is activavted before trying to make protected data connection.
	* NetAccess.cc: retry after NOT_SUPP fast so that the status message
	  won't appear on the screen.

2004-04-22  Alexander V. Lukyanov <<EMAIL>>

	* FileSet.cc, FileSet.h: add UNKNOWN file type; handle Door type.
	* MirrorJob.cc: add UNKNOWN to switch.
	* FtpListInfo.cc: ignore special files, don't treat them as errors.

2004-04-21  Alexander V. Lukyanov <<EMAIL>>

	* LsCache.cc: (IsDirectory) use dirname_modify; check for MP_LIST
	  in cache; add assert.
	* misc.cc: fix comment; special case for tz=GMT for speed-up.
	* FileSet.cc: (FileInfo::parse_ls_line) new function, moved from
	  FtpListInfo.cc with minor modifications; set longname in it.
	* FtpListInfo.cc, Fish.cc: use FileInfo::parse_ls_line.
	* SFtp.cc: try to extract owner/group/nlinks from longname, use
	  parse_ls_line.

2004-04-19  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: fixed STAT command in FXP mode; fixed coredump when
	  disconnecting in FXP mode (when DataAbort disconnects too).
	* SFtp.h: fix mem leaks in Packet_WRITE and Packet_RENAME.
	* ftpclass.cc: follow ftp:ssl-force even if ftp server does not
	  advertise AUTH in FEAT reply (reported by Nikos Mavroyanopoulos <<EMAIL>>).

2004-04-16  Alexander V. Lukyanov <<EMAIL>>

	* misc.h, misc.cc: (last_char) new utility function.
	* CmdExec.cc, FileAccess.cc, Http.cc, MirrorJob.cc, QueueFeeder.cc,
	  commands.cc, lftp.cc: use last_char.
	* LsCache.cc: don't treat trailing slash as existing directory inidication.
	* GetFileInfo.cc: check for trailing slash here instead of LsCache.cc.

2004-04-14  Alexander V. Lukyanov <<EMAIL>>

	* resource.cc: don't enable mlsd by default.

2004-04-13  Alexander V. Lukyanov <<EMAIL>>

	* SFtp.cc: don't check timeout if there should be no activity.
	* Makefile.am: use install-data-hook to do cleaning after install.

2004-04-12  Alexander V. Lukyanov <<EMAIL>>

	* FtpListInfo.cc: use sscanf instead of atoll.

2004-04-09  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h: support CLNT, HOST commands; send OPTS MLST;
	  translate data in MP_LIST mode.
	* resource.cc: ftp:client new resource.
	* FtpListInfo.cc: support MLSD attrs UNIX.{owner,group,uid,gid};
	  workaround ncftpd bug - it does not append semicolon to last MLST fact.

2004-04-08  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: send ACCT before FEAT after login.
	* OutputJob.cc: don't disable writing when there are data pending,
	  this fixes spinning in cls|less.

2004-04-07  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: fixed coredump when ftp:proxy does not have a protocol
	  specified; fixed assertion failure when using ftp over http proxy
	  with CONNECT method.

2004-04-06  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: add "closed by the remote host" string to the transient
	  errors.

2004-04-05  Alexander V. Lukyanov <<EMAIL>>

	* SFtp.cc: fix data packet debug message (s was not initialized);
	  call CheckTimeout properly, so that net timeout will be handled.
	* ftpclass.cc, ftpclass.h: move expect_t to Expect; make ExpectQueue
	  a friend so that it can access Expect; (Ftp::DisconnectNow) new method;
	  (Connection::AbortDataConnection, CloseAbortedDataConnection) new methods;
	  use DisconnectNow instead of setting quit_sent.
	* SFtp.h: make enums public so that it compiles with sun compiler.
	* SFtp.cc, SFtp.h: move stuff around, make some friends/publics to make
	  it compile with sun compiler.

2004-04-04  Alexander V. Lukyanov <<EMAIL>>

	* SFtp.cc, SFtp.h: outline HasID as it fails to inline on certain
	  compilers (e.g. gcc-2.95.2).

2004-04-02  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h: fix compilation without ssl; fixed coredump
	  in case of some network errors; copy error message before Disconnect,
	  so that it won't be lost and a freed memory won't be referenced.
	* Makefile.am: add SOCKSLIBS to lftp_LDADD.

2004-03-25  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.h: (MP_LIST) new mode for MLSD - Machine Parseable List;
	  (GetErrorCode) new method.
	* Fish.cc, Http.cc, LocalAccess.cc, SFtp.cc: handle MP_LIST.
	* ftpclass.cc: send MLSD for MP_LIST.
	* FtpListInfo.cc: use MP_LIST.
	* NetAccess.cc: use MP_LIST, fallback to LONG_LIST if not supported.
	* resource.cc: ftp:list-empty-ok - new setting.

2004-03-25  Alexander V. Lukyanov <<EMAIL>>

	* FileSet.cc: init all fields in FileInfo.
	* FtpDirList.cc: support MLSD; use parsers from ListInfo.
	* FtpListInfo.cc: support MLSD.
	* SFtp.h: fix 2 mem leaks.
	* SFtp.cc: register protocol in module_init; add assert.
	* ftpclass.h, ftpclass.cc: support MLSD.
	* resource.cc: new setting ftp:use-mlsd.

2004-03-19  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc: still wait for put confirmation to set file mtime
	  even if target file is already complete - this fixes repeated
	  transferring a file in mirror -c.
	* ftpclass.cc: handle storing 0 bytes by skipping data connection
	  establishment and still set the file mtime.

2004-03-18  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: set proper timeout for retry_time; better error handling
	  for port-range.
	* FileAccess.cc: (DebugPrint) check for \r\n in the string.
	* Makefile.am: use libtool for libtasks and libjobs; rename them.

2004-03-15  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: fix a number of null refs; set auth_sent properly.
	* log.cc: don't write trailing \0.

2004-03-13  Alexander V. Lukyanov <<EMAIL>>

	* StatusLine.cc: don't redisplay status line if not needed.
	* ftpclass.cc: check loopback match in Handle_PASV; Connection
	  and ExpectQueue subclasses.
	* commands.cc: rewind args before calling builtin_open from builtin_lftp.

2004-03-04  Alexander V. Lukyanov <<EMAIL>>

	* misc.cc, misc.h: (dirname_modify) new function.
	* LsCache.cc: (Changed) use dirname_modify to fix bug with trailing
	  slash.

2004-03-02  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: fix multiline error joining to eliminate all numeric
	  status codes except the first one.

2004-03-01  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc: rewind args before calling command-specific handler.
	* commands.cc: don't rewind args, it is not rewinded in CmdExec.
	* ftpclass.cc: use all_lines in error reporintg; join multiline error
	  into single line.

2004-02-25  Alexander V. Lukyanov <<EMAIL>>

	* pgetJob.cc: nice progress bar.
	* ArgV.cc: init ind in ctor.
	* StatusLine.h: new Show methods to show StringSet.
	* StringSet.cc, StringSet.h: (AppendFormat) new method.
	* ftpclass.cc, Http.cc, Fish.cc, SFtp.cc: take over suspended sessions.

2004-02-24  Alexander V. Lukyanov <<EMAIL>>

	* ChmodJob.cc: fix recursing.

2004-02-19  Alexander V. Lukyanov <<EMAIL>>

	* StatusLine.cc: don't write negative amount of chars.

2004-02-17  Alexander V. Lukyanov <<EMAIL>>

	* StringSet.cc, StringSet.h: new files; implement string array for StatusLine.
	* ArgV.cc, ArgV.h: base on StringSet.
	* CmdExec.cc: modify CombineQuoted based on StringSet.
	* FileFeeder.cc: send CONT to feeder process.
	* FileSetOutput.cc, Filter.cc, FindJob.cc, FindJobDu.cc, PtyShell.cc,
	  SleepJob.cc, TreatFileJob.cc, mgetJob.cc, mkdirJob.cc, mkdirJob.h:
	  fix ArgV const-ness.
	* StatusLine.cc, StatusLine.h: base on StringSet.
	* commands.cc: fix ArgV const-ness; implement `source -e' - execute output
	  of external command.
	* Makefile.am: add StringSet.

2004-02-13  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc: new option --use-pget-n; implement using pget for
	  transfers; fixed bug which caused old files to be removed even
	  if -e option was not specified.
	* ArgV.h: allow appending numbers.
	* GetJob.h: (RemoveSourceFiles) add method named as in CopyJob.
	* pgetJob.h: make NewChunk protected.
	* resource.cc: add mirror:use-pget-n resource.

2004-02-12  Alexander V. Lukyanov <<EMAIL>>

	* StatusLine.cc, StatusLine.h: support multiline status.
	* ftpclass.cc: fix hang on data eof (introduced in last change set).
	* MirrorJob.cc: --ignore-size and --only-missing options added.
	* netrc.cc, netrc.h: allow user matching.
	* commands.cc: user netrc user matching; log netrc actions;
	  set password immediately to skip looking up netrc second time.

2004-02-09  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc: (SetNewerThan) try get_date first.
	* SleepJob.cc: print get_date error.
	* commands.cc: fix mirror help; get1 --target-position instead of
	  --target-region.
	* getdate.y, getdate.h: save error string instead of printing.
	* ftpclass.h, ftpclass.cc: drop log_resp and result; use data_iobuf
	  for QUOTE_CMD and all_lines for OPIE/SKEY.
	* Makefile.am: move getdate.y and getdate.h to libtasks (it is used in
	  two separate modules).

2004-02-04  Alexander V. Lukyanov <<EMAIL>>

	* SFtp.cc: (SkipHome) fix it to skip / too; handle ~.
	* SMTask.h, SMTask.cc: (Enter, Leave) new methods; now a task can
	  be run nested many times (int running); Schedule optimizations.
	* CmdExec.cc: use Enter/Leave.
	* log.cc, log.h: add support for pid, time and context log prefixes.
	* lftp.cc: enable pid, time and context on exit to bg.
	* ftpclass.cc: move DataClose from Read to Do; use Enter/Leave to
	  show proper context.
	* FileAccess.cc: modify DebugPrint to use prefixes of Log class;
	  use Enter/Leave in CleanupAll.
	* FileAccess.h: (GetLogContext) new method, use hostname as context.
	* commands.cc: add -t -c -p options for debug command; add range
	  options for get1.
	* Resolver.h: add GetLogContext method.

2004-01-28  Alexander V. Lukyanov <<EMAIL>>

	* NetAccess.cc, NetAccess.h: (proxy_proto) new member.
	* ftpclass.cc, ftpclass.h: close data socket on temp transfer errors;
	  enum pasv_state instead of addr_received; http proxy support with
	  CONNECT method.
	* resource.cc: new setting ftp:use-hftp to select between GET and
	  CONNECT methods when ftp:proxy points to http:// url.

2004-01-24  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc, commands.cc: --delete-first, not --remove-first;
	  update help text for mirror.
	* buffer.cc: accept '' charset as 'char' (they are equivalent in
	  libiconv and '' seems to be supported by glibc).

2004-01-21  Alexander V. Lukyanov <<EMAIL>>

	* FileSet.h, FileSet.cc: add SubtractNotDirs, SubtractSameType.
	* MirrorJob.cc, MirrorJob.h: remove directories to be replaced with
	  plain files first (with --delete option); --remove-first option.
	* commands.cc: create bookmark file before calling editor.

2004-01-19  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc, Http.h: extract content-type (not used yet).
	* ftpclass.cc, ftpclass.h: add rest_supported flag.

2004-01-18  Alexander V. Lukyanov <<EMAIL>>

	* ResMgr.cc: better charset test.
	* resource.cc: file:charset new setting; set it from nl_langinfo(CODESET).
	* SFtp.cc: move is_valid_reply to Packet class; drop unused is_valid_status.
	* buffer.cc: use file:charset instead of unportable "char".

2004-01-16  Alexander V. Lukyanov <<EMAIL>>

	* PtyShell.cc: call TIOCSCTTY on FD 2 instead of FD 0, as it is a tty
	  even if use_pipes==true.
	* ResMgr.cc, ResMgr.h: (CharsetValidate) new static method.
	* buffer_ssl.cc: allow translation.
	* ftpclass.cc, ftpclass.h: implement recoding from any server charset.
	* resource.cc: new setting ftp:charset.

2004-01-15  Alexander V. Lukyanov <<EMAIL>>

	* buffer.h: use CDECL around iconv.h.
	* buffer.cc: use const_cast.
	* Makefile.am: add iconv library.

2004-01-15  Alexander V. Lukyanov <<EMAIL>>

	* buffer.cc: use ICONV_CONST.
	* FileCopy.cc: workaround solaris nfs bug in case of ENOSPC.

2004-01-15  Glenn Maynard <<EMAIL>>

	* buffer.cc: include errno.h early.

2004-01-14  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc: set entity size at end of transfer too, in case it was
	  not known at the beginning.
	* SFtp.cc: don't use non-portable S_IAMB macro.
	* buffer.cc, buffer.h: fixed translation by adding bool from_untranslated;
	  new class IOBufferStacked.
	* ftpclass.cc: don't disconnect on some temporary errors, just re-send
	  the command after delay; (IOBufferTelnet) new class; use it;
	  (telnet_layer_send) new member; add site utime support; better status
	  for FEAT negotiation; add overloaded MDTM support.
	* resource.cc: (ftp:use-site-utime, ftp:use-mdtm-overloaded) new settings.

2004-01-14  Alexander V. Lukyanov <<EMAIL>>

	* ProcWait.cc: don't call NonFatalError, as it causes ProcWait to spin.
	* commands.cc: fix compilation with IRIX compiler.

2003-12-22  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc, Http.h: add Buffered method; allow storing in https mode;
	  use NO_DATE and NO_SIZE constants.
	* NetAccess.cc, NetAccess.h: move SocketBuffered here from ftpclass.cc.
	* SFtp.cc: more precise Buffered method.
	* buffer.cc: add Buffered method.
	* buffer_ssl.cc: add ErrorFatal method.
	* ftpclass.cc, ftpclass.h: read/write through buffer;
	  use NetAccess::SocketBuffered; follow RFC2640; FEAT, LANG, UTF8
	  support;
	* resource.cc: new settings ftp:use-feat, ftp:use-lang.

2003-12-19  Alexander V. Lukyanov <<EMAIL>>

	* LocalAccess.cc: (StoreStatus) return OK when not opened for storing.
	* FileCopy.cc: skip storing past eof only if there is such file already;
	  do better line buffering at the cost of performance.

2003-12-17  Alexander V. Lukyanov <<EMAIL>>

	* module.cc: rename module_into to lftp_module_info to avoid
	  name conflict.

2003-12-17  albert chin <<EMAIL>>

	* Makefile.am: remove LOCALE_DIR, use datadir/locale instead.
	* FileAccess.h, CharReader.h, IdNameCache.h, FileFeeder.h: use enum
	  to declare integer constants.
	* FileSetOutput.cc: include filemode.h after config.h.

2003-12-16  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: fix core dump where first reply line is empty (reported by
	  Nalin Dahyabhai); remove incorrect parsing of status line in case
	  of 0.9 protocol; shutdown socket if sent uncomplete entity and
	  unset keep_alive; handle STORE in case of 0.9 protocol.
	* misc.cc: add CDECL around regex.h

2003-12-16  albert chin <<EMAIL>>

	* Makefile.am: Because we use libtool to build lftp, use $(LTLIBINTL)
	  for the gettext libraries.

2003-12-15  Mark Blackburn <<EMAIL>>

	* parsecmd.cc: skip \r character for dos-like script files.

2003-12-15  Alexander V. Lukyanov <<EMAIL>>

	* HttpDir.cc: fix a compilation error (reported by Albert Chin).
	* Http.cc: make base64 char table const.

2003-12-11  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc: handle the case when put retry seeks to the eof.
	* Http.cc: don't try to send empty body to non-zero offset; don't
	  shutdown socket when done sending data (PUT/POST) and data size
	  was known beforehand.
	* Resolver.cc: fixed compilation on AIX, where h_errno is a macro.

2003-12-08  Alexander V. Lukyanov <<EMAIL>>

	* buffer.cc, buffer.h, SFtp.cc, SFtp.h: (DirectedBuffer) new buffer
	  layer; add iconv support in DirectedBuffer, almost transparent.

2003-12-08  Alexander V. Lukyanov <<EMAIL>>

	* HttpDir.cc: (find_eol, try_csm_proxy) fix bugs found by
	  Solar Designer <<EMAIL>> - increment *eol_size,
	  not eol_size; use proper %lld format for size; use larger
	  buffer for additional_file_info to store trailing \0; some
	  style mods; use %32s instead of %32c.

2003-12-08  Harnhammar, Ulf <<EMAIL>>

	* HttpDir.cc: (try_netscape_proxy, try_squid_eplf) fixed buffer overflows.

2003-12-01  Alexander V. Lukyanov <<EMAIL>>

	* buffer.cc, buffer.h: move SMTask to IOBuffer.
	* FileAccess.cc, OutputJob.cc: Delete -> delete for Buffer.
	* FileCopy.cc, FileCopy.h: use IOBuffer as base class for FileCopyPeer.
	* FindJob.h, FindJobDu.h, Fish.cc, Fish.h, FtpDirList.cc, FtpDirList.h,
	  HttpDir.h, LocalAccess.cc, LocalAccess.h, Resolver.cc, Resolver.h:
	  use IOBuffer.
	* NetAccess.cc, NetAccess.h: use IOBuffer; FindHomeAuto in ctor.
	* SFtp.cc, SFtp.h: fixed FSTAT request (protocol_version was duplicated);
	  fixed STAT/FSTAT for protocol v4 - add flags field.

2003-12-01  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: fix possible freed memory access.
	* MirrorJob.cc: don't append basename of source to / destination.

2003-11-26  Alexander V. Lukyanov <<EMAIL>>

	* FileCopyFtp.cc: add ftps to acceptable fxp protocols.
	* Fish.cc, SFtp.cc: report status from PtyShell.
	* PtyShell.cc: close pipes correctly.
	* buffer.cc, buffer.h: add Status method.

2003-11-21  Alexander V. Lukyanov <<EMAIL>>

	* Fish.cc, SFtp.cc, resource.cc: allow args in connect-program
	  resource; move -a and -x options to connect-program.
	* Makefile.am: rename lftp libs.

2003-11-20  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.h, HttpDir.h: move LsOptions to FileAccess.h.
	* FileSet.cc, FileSet.h: (EstimateMemory) new method.
	* LsCache.cc, LsCache.h: cache FileSet along with listing.
	* SFtp.cc, SFtp.h: more work. All is working now.
	* ConnectionSlot.cc, ConnectionSlot.h: outline ctor/dtor to avoid warning.

2003-11-20  Alexander V. Lukyanov <<EMAIL>>

	* Fish.cc: add Reconfig to clone ctor.
	* ResMgr.cc: allow 0x numbers.
	* ResMgr.h: use strtol.
	* SFtp.cc, SFtp.h: use new resources.
	* resource.cc: new sftp resources.

2003-11-19  Alexander V. Lukyanov <<EMAIL>>

	* PtyShell.cc, PtyShell.h: add pipe support, thus the ability to
	  separate data and interaction with ssh (passwords etc).
	* buffer.cc, buffer.h: pack/unpack - signed int, 64 bit, debugging.
	* FileSet.cc, FileSet.h: add longname.
	* NetAccess.cc, NetAccess.h, ftpclass.cc, ftpclass.h: move home_auto
	  to NetAccess.
	* SFtp.cc, SFtp.h: more work; ls works.
	* Makefile.am: libpty; add sftp.

2003-11-14  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc: drop size/date if read pos>size.
	* FileCopy.h: (GetDescriptionForLog) new method.
	* commands.cc: add --sort=time for cls; add long options for open;
	  optional verification of cached dirs.
	* CmdExec.cc, CmdExec.h: optional verification of cached dirs.
	* Resolver.cc: define C_IN if undefined.
	* buffer.cc, buffer.h: pack/unpack methods added.

2003-11-11  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h, resource.cc: new setting ftp:ssl-protect-list
	  to selectively protect listings; propagate pret_supported; set
	  real_pos to 0 always if REST fails.

2003-10-21  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: handle long options in open command.

2003-10-03  Alexander V. Lukyanov <<EMAIL>>

	* SignalHook.cc: unblock signals upon restoring.
	* StatusLine.cc: (update) support multibyte characters.
	* Speedometer.cc: (GetETAStrFromTime) support multibyte characters.
	* misc.cc: (squeeze_file_name) support multibyte characters.
	* ftpclass.cc, ftpclass.h, resources.cc: add use_pret and
	  pret_supported flags, ftp:use-pret setting.
	* pgetJob.cc: make more room for status, less for file name.

2003-10-02  Morgan Christiansson <<EMAIL>>

	* ftpclass.cc: send PRET before PASV command for distributed ftp servers.

2003-08-21  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: (Disconnect) ignore replies when Disconnect is called,
	  call CloseRespQueue for that.
	* FileCopyFtp.cc: fixed fxp for zero sized files.

2003-08-11  Johannes Zellner <<EMAIL>>

	* HttpDir.cc:
	  1) implemented the hftp protocol over a CSM proxy
	  2) hftp listings are colored according to DIRECTORY / SYMLINK
	  3) do NOT print hour:minute, if it wasn't specified in the
	     http proxy output

2003-07-15  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h, resource.cc: add ftp:use-telnet-iac setting.

2003-07-10  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc: save cwd history before destroying last feeder (this
	  fixes a bug with saving last working directory).

2003-06-26  Alexander V. Lukyanov <<EMAIL>>

	* LsCache.cc: (SetDirectory) use SetCwd to restore cwd, this fixes
	  a problem with home values without leading slash.
	* GetJob.cc, GetJob.h: re-do file: handling.

2003-06-21  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc: optimize FileCopyPeerFDStream speed by writing out
	  data by portions of >=PUT_LL_MIN or by timeout 0.1 second.
	* buffer.cc, buffer.h: add put_ll_timer in IOBufferFDStream too.
	* FileSet.cc: remove unneeded type casting.
	* GetJob.cc: create FileCopyPeerFDStream for file: url scheme.
	  This fixes closing and re-opening output file when transfer
	  breaks and restarts. Besides, it is slightly faster.
	* Http.cc: turn off sending plain password in URI along with
	  Authorization in hftp.
	* Timer.h: add Stop method.
	* ftpclass.cc, ftpclass.h, resource.cc: new settings ftp:use-size
	  and ftp:use-mdtm.

2003-06-16  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: set exit_code in CMD(help) (reported
	  by <EMAIL>).

2003-06-03  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: don't assign NO_SIZE when SIZE returned 0.
	* commands.cc: set exit_code=0 properly in builtin_lftp
	  (<NAME_EMAIL>).

2003-05-28  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: send password always in hftp, even along with Authorization
	  header. This is required for some proxies (reported by Onno Benschop).
	* ftpclass.cc: fixed catching of file size from 150 server message.
	* lftp.cc: load and save histories only when user interaction happens.

2003-05-23  Alexander V. Lukyanov <<EMAIL>>

	* LocalAccess.cc: move Exclude to after file type setting. This
	  fixes exclude/include of directories on local side.
	* commands.cc: new command `.tasks'.
	* complete.cc: don't show hidden commands in completion.
	* ftpclass.cc, ftpclass.h: fix borrowing not-logged-in connection.

2003-05-23  Diego Reyes <<EMAIL>>

	* ResMgr.cc: (DirReadable, FileReadable) fix a bug.

2003-05-14  Geoffrey Lee <<EMAIL>>

	* Http.cc: fix a problem with empty path; change it to "/".

2003-04-30  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc: default mode for directories is 0775; raise sane
	  connection limit to insane 64.

2003-04-28  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: fix hftp file information retrieval when use-head is off
	  (bug reported by Robert A. Thompson <<EMAIL>>).

2003-04-15  AIDA Shinra <<EMAIL>>

	* HttpDir.cc: fix bug with unterminated string; add wwwoffle support.

2003-04-08  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc: fixes for --script (bug reported by Olaf Kaehler
	  <<EMAIL>>).

2003-03-14  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: use net:timeout when draining data socket on upload;
	  ignore 0 size reported by SIZE (for sunsolve.sun.com).

2003-02-28  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: close data connection when sending ABOR over ssl.
	* MirrorJob.cc: add --just-print and --dry-run options.
	* CmdExec.cc, FileGlob.cc, FileGlob.h, commands.cc, complete.cc, mgetJob.cc:
	  (GlobURL::NewGlob) new method; save some glob options and use this.
	* buffer.h: increase GET_BUFSIZE.

2003-02-26  Alexander V. Lukyanov <<EMAIL>>

	* OutputJob.cc: fixed large file support (should have used off_t).
	* HttpDir.cc: handle \r just in case.
	* CmdExec.cc: set slot's cwd even if non-interactive.

2003-02-14  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc, FileCopyFtp.cc: don't start "put" peer if target file
	  is already complete; don't get size multiple times (occasionally).
	* MirrorJob.cc: new options --log and --script. Implement producing
	  command log or script file with commands to do mirroring.
	* ftpclass.cc: fix dead-lock when resuming fxp with passive source.

2003-02-11  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: another workaround for proftpd.
	* FileCopy.cc: re-get file size/date after redirect.

2003-02-10  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: lower bind error message level; work around for proftpd;
	  don't send ABOR if disconnecting immediately (quit_sent).
	* FileAccess.cc: handle EPIPE as temporary; properly initialize
	  array_cnt and array_ptr.

2003-01-27  Oskar Liljeblad <<EMAIL>>

	* FileSet.cc, FileSet.h, commands.cc: add cls sorting by date.

2003-01-19  Alexander V. Lukyanov <<EMAIL>>

	* NetAccess.cc: include sys/types.h as suggested by Camille Huot
	  <<EMAIL>> to make it compile on OpenBSD.

2003-01-16  Alexander V. Lukyanov <<EMAIL>>

	* GetFileInfo.cc: don't check if `..', `.' and `/' are files.
	* NetAccess.cc: don't set filetype to NORMAL, unset it instead when
	  following a link; fix compilation on solaris 2.6.

2003-01-10  Paul Lew <<EMAIL>>

	* resource.cc: fix build on solaris 2.6.

2003-01-05  Alexander V. Lukyanov <<EMAIL>>

	* Fish.cc: add -a option for ls.
	* commands.cc: add -d option for lftp -f and lftp -c.

2002-12-25  Alexander V. Lukyanov <<EMAIL>>

	* resource.cc: new settings net:socket-bind-ipv4 and net:socket-bind-ipv6.
	* ResMgr.cc, ResMgr.h: new method IPv6AddrValidate.
	* NetAccess.cc, NetAccess.h: new methods SocketCreate and SocketCreateTCP.
	* ftpclass.cc, Http.cc: use SocketCreateTCP.
	* FileCopy.cc, FileCopy.h: get size before starting a continuation
	  transfer and don't start the transfer if not needed.

2002-12-19  Alexander V. Lukyanov <<EMAIL>>

	* NetAccess.cc: check set!=NULL.

2002-12-18  Alexander V. Lukyanov <<EMAIL>>

	* NetAccess.cc: (GenericParseListInfo) handle ~ here.
	* LocalAccess.cc (LocalListInfo) handle ~.
	* FindJob.cc: revert ~ change.
	* MirrorJob.cc: handle ~.
	* misc.cc: handle /dir with ./file concatenation nicely.
	* lftp_tinfo.cc: compilation fix for certain versions of cygwin.

2002-12-10  Alexander V. Lukyanov <<EMAIL>>

	* FindJob.cc: handle ~ directories properly.
	* Fish.cc: fix rename.
	* resource.cc: make verify-certificate off by default.

2002-10-23  Alexander V. Lukyanov <<EMAIL>>

	* Speedometer.cc: fixed wrong eta calculation (reported by Haas
	  Wernfried <<EMAIL>>).

2002-10-23  Glenn Maynard <<EMAIL>>

	* OutputJob.cc: fix abort when output file is not writable.

2002-10-11  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: change state to WAITING_STATE from
	  DATASOCKET_CONNECTING_STATE properly on DataClose().
	* Fish.cc: (StoreStatus) check state; send a different store
	  command when entity_size==0 to avoid dd bug.

2002-09-30  Glenn Maynard <<EMAIL>>

	* misc.cc: unset put_tz properly on set_tz(0).

2002-09-30  Alexander V. Lukyanov <<EMAIL>>

	* misc.cc: call tzset for glibc.

2002-09-26  Albert Chin <<EMAIL>>

	* FileAccess.cc: fix const mismatch (found with HP C++).

2002-09-23  Alexander V. Lukyanov <<EMAIL>>

	* keyvalue.h: make Pair public.
	* FileCopy.cc: don't report percent if pos>size.

2002-09-09  Alexander V. Lukyanov <<EMAIL>>

	* lftp.cc: save readline history conditionally.

2002-09-05  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: allow single LF as EOL (suggested by Peter Vreman
	  <<EMAIL>>). Also recommended by RFC2616.

2002-09-04  Alexander V. Lukyanov <<EMAIL>>

	* IdNameCache.cc, IdNameCache.h: fixed a bug which caused a single
	  object to be put on two lists (rare); fixed a memory leak.
	  Reported by Tillmann Steinbrecher <<EMAIL>>.

2002-09-03  Alexander V. Lukyanov <<EMAIL>>

	* Filter.cc: handle ENOSPC and EDQUOT specially when the file is
	  removed (nlink==0).
	* lftp.cc, lftp_rl.c, lftp_rl.h: read/write readline history.

2002-09-02  Glenn Maynard <<EMAIL>>

	* CopyJob.cc, CopyJob.h, pgetJob.cc: add and use dispname to hide
	  passwords in urls.
	* commands.cc, MirrorJob.cc, MirrorJob.h: add --ignore-time option.

2002-08-29  Alexander V. Lukyanov <<EMAIL>>

	* rmJob.cc: set recursive properly. This fixes bug with DELE instead
	  of RMD.
	* OutputJob.cc: handle PutEOF when InitCopy temporarily failed.
	* Job.h: (ClearStatus) new method.
	* CopyJob.cc: use ClearStatus.
	* OutputJob.cc: handle temporary pipe() error.
	* resource.cc: add ifdef USE_SSL around AuthArgValidate.

2002-08-28  Alexander V. Lukyanov <<EMAIL>>

	* OutputJob.cc: don't create LocalAccess directly (it breaks modularity).
	* OutputJob.cc: check if output_fd really has an error.
	* FileCopy.cc: allow FileCopyPeer to be broken, but not done.
	* OutputJob.cc: create a temporary buffer if InitCopy don't succeed
	  at first (temporary error); prepare for pipe() tmp error handling.

2002-08-09  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc, FileAccess.cc: fixed core dump when Content-Disposition
	  is handled (suggested_filename was not properly initialized).
	* Speedometer.cc: fix eta rounding (reported by Igor Zhbanov
	  <<EMAIL>>).

2002-08-07  Glenn Maynard <<EMAIL>>

	* lftp_tinfo.cc: use extern "C".

2002-08-06  Alexander V. Lukyanov <<EMAIL>>

	* Job.cc: resume job in Fg.

2002-08-06  Glenn Maynard <<EMAIL>>

	* commands.cc: disallow 0 as queue position.

2002-08-05  Alexander V. Lukyanov <<EMAIL>>

	* misc.cc, misc.h: split off terminfo stuff.
	* lftp_tinfo.cc, lftp_tinfo.h: new files.
	* StatusLine.cc: use new header lftp_tinfo.h.
	* Makefile.am: add new files.

2002-08-02  Alexander V. Lukyanov <<EMAIL>>

	* misc.cc: include termios.h for solaris 9.

2002-08-02  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc: append source basename to target if target ends with
	  a slash.

2002-08-01  Alexander V. Lukyanov <<EMAIL>>

	* url.cc: allow a path in slot pseudo-url.

2002-07-31  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc, MirrorJob.h: do cd on source session first to check
	  if the directory exists. This fixes creation of directory if source
	  dir does not exist.

2002-07-31  Ayamura KIKUCHI <<EMAIL>>

	* misc.cc: <curses.h> should be included before <term.h> on IRIX.

2002-07-31  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc, CmdExec.h, commands.cc: allow per-slot queues;
	  support queue start/stop; rename has_queue to queue_feeder.
	* CopyJob.cc, CopyJob.h, FileSetOutput.cc, FileSetOutput.h, FindJob.cc,
	  FindJob.h, Job.cc, Job.h, MirrorJob.cc, MirrorJob.h, QueueFeeder.cc,
	  QueueFeeder.h, SleepJob.cc, SleepJob.h, TreatFileJob.cc,
	  TreatFileJob.h, mgetJob.cc, mgetJob.h, mkdirJob.cc, mkdirJob.h,
	  mvJob.cc, mvJob.h, pgetJob.cc, pgetJob.h: PrintStatus with explicit
	  prefix.
	* MirrorJob.cc, FileSet.cc, FileSet.h: do SubtractDirs if not recursive.
	* CmdExec.cc, FileAccess.cc, FileAccess.h, misc.cc, resource.cc,
	  url.cc, url.h: properly Clone session in ChangeSlot; add assert;
	  better slot: url support; better url_file.


2002-07-30  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc: don't do SetTryTime(0).
	* ConnectionSlot.cc: fix SetCwd; Clone session properly.
	* commands.cc: call ConnectionSlot::SetCwd when not verifying in cd.

2002-07-25  Alexander V. Lukyanov <<EMAIL>>

	* misc.cc: provide errret for setupterm, so that it does not exit
	  on error, allowing lftp to work without valid TERM.
	* CmdExec.cc: set slot in ChangeSession, fixes scache+slot combo.

2002-07-24  Alexander V. Lukyanov <<EMAIL>>

	* complete.cc: no need for multiple functions slot-N.

2002-07-23  Glenn Maynard <<EMAIL>>

* misc.cc, misc.h: Add get_string_term_cap; tries both terminfo and termcap.
(We could probably get away with only termcap names, but it's nice to
have them side-by-side, since the terminfo names are more useful.)

StatusLine.cc, StatusLine.h: Add tsl/dsl cap support.  Used only if
*  cmd:term-status is blank.  Clear title in dtor, so we don't leave
 it behind if the user's shell (or invoking program) doesn't know
 to clear it.

* CmdExec.cc: Delete the statusbar on exit, so it doesn't stick around
 if we move to background.  This triggers the title clear.  (It also
 ensures nothing gets written to the title--now the log--though I don't
 think that ever happens.)

2002-07-18  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc: don't clear signals if not interactive (suggested by
	  Glenn Maynard <<EMAIL>>)
	* Fish.cc, Http.cc, NetAccess.cc, NetAccess.h, ResMgr.cc, ResMgr.h,
	  ftpclass.cc, resource.cc: separate rate limitations for download
	  and upload.

2002-07-17  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc, CmdExec.cc, CmdExec.h: fix mem leak by eliminating
	  var_ls member (it was not freed).
	* Speedometer.cc: count speed starting from first data byte.
	* ProcWait.cc: propagete SIGINT to main lftp process.

2002-07-17  Alexander V. Lukyanov <<EMAIL>>

	* ResMgr.cc, resource.cc, FileAccess.cc, FileAccess.h: ftp:device-prefix
	  setting (default off).

2002-07-12  Glenn Maynard <<EMAIL>>

	* FindJob.cc, commands.cc: delete args properly. This fixes a coredump.
	* CopyJob.cc: fix format string error.

2002-07-12  Glenn Maynard <<EMAIL>>

* OutputJob.cc, OutputJob.h:
 Update comments.

 Let the input copier own output_fd, so it'll delete it when it's done
 with it as intended (fixing the delete->close fd->output finished code path).
 This also means we don't have to deal with fg_data in OutputJob (since
 the copier owns the fd, CopyJob will do it for us.)

 eof is gone.

 Store width/tty status so we don't need output_fd after initialization.
 (needed in order to let the copier own it)

 Reversed DontFailIfBroken parameter.

 Disable fail_if_broken correctly; leave incorrect one in place for now
 (see comments.)  Fixes broken pipe with zcat.

 Use Timer; fixes scheduler problems with updating the statusline.

 Clear the status line on init, since if we have a statusline and
 we start a filter we won't be able to clear it later (due to changed
 pgrp.)

* StatusLine.cc, StatusLine.h, OutputJob.cc, all ShowStatusLine users:
 When we've output to stdout recently, instead of refusing to print a
 statusline, instead tell the statusline to only send the next update to
 the title.  This gives us a title status during "cat", etc.

* CmdExec.cc, StatusLine.cc, StatusLine.h: Don't clear the title when
 we're clearing the statusline due to stdout.

* CatJob.cc: Override ShowRunStatus to call output->ShowStatusLine.  (Any
 job using an OutputJob should call ShowStatusLine() before it or a child
 displays a statusline; CopyJob, CatJob's child, was displaying a
 statusline without doing this.)

* OutputJob.cc, OutputJob.h, CatJob.cc: Don't reenable the statusbar for
 commands that stream data from the server, since that's annoying when
 the server is rate-limited per-second.

2002-07-10  Glenn Maynard <<EMAIL>>

	* CmdExec.cc: fd leak fixed.

2002-07-10  Alexander V. Lukyanov <<EMAIL>>

	* CharReader.cc, FileAccess.cc, Filter.cc, ftpclass.cc: F_GETFL returns
	  flags as fcntl return value.

2002-07-10  Glenn Maynard <<EMAIL>>

	* CatJob.cc, CatJob.h, ColumnOutput.cc, ColumnOutput.h, CopyJob.cc,
	  CopyJob.h, FileCopy.cc, FileCopy.h, FileSetOutput.cc, FileSetOutput.h,
	  Filter.cc, Filter.h, Job.h, Makefile.am, OutputJob.cc, OutputJob.h,
	  commands.cc, complete.cc, echoJob.cc, echoJob.h: OuputJob support.

2002-07-10  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc, CmdExec.h, ConnectionSlot.cc, ConnectionSlot.h,
	  FileAccess.cc, commands.cc, complete.cc, keyvalue.cc, keyvalue.h,
	  lftp.cc, lftp_rl.cc, lftp_rl.h, misc.cc, url.cc: connection slot
	  support.

2002-07-07  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc, FileAccess.cc, FileAccess.h: (GetSuggestedFileName) new
	  method; handle Content-Disposition filename= in Http.

2002-07-07  Glenn Maynard <<EMAIL>>

	* MirrorJob.cc, MirrorJob.h: add FINISHING state; fix transfer_count
	  bug which led to more parallel transfers than was requested.

2002-07-06  Albert Chin <<EMAIL>>

	* ChmodJob.h: make HP-UX C++ happy.

2002-06-18  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc, ResMgr.cc, SMTask.cc, SMTask.h, buffer.cc, buffer.h,
	  buffer_ssl.cc, buffer_ssl.h, ftpclass.cc, lftp_ssl.cc, lftp_ssl.h,
	  resource.cc: ssl certificate support.

2002-06-17  Glenn Maynard <<EMAIL>>

	* MirrorJob.cc: pass timestamp to copier even if it is inaccurate.
	  This fixes mirror with servers not supporting MDTM.

2002-06-11  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.cc: copy vproto in clone ctor.
	* parsecmd.cc, complete.cc: allow ! to be backslash-quoted (for csh-history).
	* ResMgr.cc, ResMgr.h: new verificators: FileReadable, DirReadable.

2002-06-05  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc: set buf to 0 after xfree; this fixes double free.

2002-06-03  Glenn Maynard <<EMAIL>>

	* Resolver.cc: free_ha is not actually needed.

2002-05-31  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc: (GetStatus) don't use fixed buffer - it can be overflowed.
	  (reported by Lethal Weapon <<EMAIL>>).
	* ftpclass.cc: move PROT and PBSZ commands to after USER/PASS.
	  Requested by Nick Leuta <<EMAIL>>.

2002-05-29  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: fixed pasv address fixation.
	* Filter.cc: use xasprintf.

2002-05-20  Alexander V. Lukyanov <<EMAIL>>

	* pgetJob.cc: let the copyer run before the session to avoid unneeded
	  reconnect.

2002-05-16  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: handle '100 Continue' independently of open mode.
	* Resolver.cc: free hostent structure from getipnodebyname;
	  create ResolverCache only when needed.

2002-05-14  Alexander V. Lukyanov <<EMAIL>>

	* StatusLine.cc: fix for terminals with am and with no xenl (a space
	  could be written in last column).
	* Http.cc: limit non-absolute Location handling to POST method.

2002-05-13  Michail Litvak <<EMAIL>

	* Resolver.cc: check that addresses have proper length.

2002-05-08  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: more generic non-absolute Location: handling; use url::is_url.

2002-05-07  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: move to next peer if no greeting received.
	* Http.cc: handle redirection to non-absolute url specially.
	* FileCopy.cc: change FAmode to RETRIEVE when redirecting from QUOTE_CMD.

2002-05-05  Alexander V. Lukyanov <<EMAIL>>

	* LsCache.cc, LsCache.h, commands.cc: add cache:enable, cache:expire
	  and cache:size settings.

2002-04-30  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: add missing port to hftp request.
	* ftpclass.cc: ignore any 550 error for NLST if no file name given.

2002-04-27  Glenn Maynard <<EMAIL>>

	* LsCache.cc: add cache:cache-empty-listings.
	* buffer.h: add IsSaving().
	* FtpDirList.cc, Fish.cc: use alternative LsCache::Add.

2002-04-23  Alexander V. Lukyanov <<EMAIL>>

	* FileCopyFtp.cc, resource.cc: add ftp:fxp-force setting.
	* ftpclass.cc: fix status in fxp mode.

2002-04-18  Glenn Maynard <<EMAIL>>

	* Job.cc: Free waiting on dtor.
	* GetFileInfo.cc, GetFileInfo.h: fix comment.

2002-04-17  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc: create first target directory with all parent dirs.

2002-04-15  Glenn Maynard <<EMAIL>>

	* TreatFileJob.cc: remove unneeded return.

2002-04-08  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: accept file size in 416 reply.

2002-04-08  Glenn Maynard <<EMAIL>>

	* FileSetOutput.cc: fix unneeded assert.

2002-03-26  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: don't send CWD ~ when PWD failed.

2002-03-18  Alexander V. Lukyanov <<EMAIL>>

	* NetAccess.cc: explicit cast for Compaq C++.

2002-03-18  Alexander V. Lukyanov <<EMAIL>>

	* ProcWait.cc: don't flood stdout with waitpid errors, it can be
	  possible if lftp forks into background.

2002-03-15  Alexander V. Lukyanov <<EMAIL>>

	* Job.cc: fix occasional spinning when mirror terminates.

2002-03-14  Alexander V. Lukyanov <<EMAIL>>

	* SleepJob.cc: if the time is in the past, assume it is for tomorrow.

2002-03-13  Alexander V. Lukyanov <<EMAIL>>

	* NetAccess.cc: don't clear hostname etc on lookup error.
	* FileAccess.cc: (Chdir) handle cwd==0 correctly (reported by Glenn
	  Maynard <<EMAIL>>).

2002-03-10  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: add ifdef USE_SSL around data_ssl.

2002-02-27  Ayamura KIKUCHI <<EMAIL>>

	* FileSetOutput.cc, lftp.cc: locale.h is now included from config.h.

2002-02-26  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: add TLS flag to status when data connection is secured.

2002-02-24  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: flip copy_passive on any 425 reply.
	* ftpget: fix ' quoting.

2002-02-15  Alexander V. Lukyanov <<EMAIL>>

	* FtpListInfo.cc: restore line contents after each parser.

2002-02-12  Alexander V. Lukyanov <<EMAIL>>

	* CharReader.cc, CharReader.h: handle EINTR properly; try reading in
	  non-block mode instead of polling.

2002-02-11  Alexander V. Lukyanov <<EMAIL>>

	* lftpget: more ' quoting. -v should work now.

2002-02-07  Alexander V. Lukyanov <<EMAIL>>

	* ResMgr.cc, resource.cc: verify resource defaults in ResMgr::ClassInit.
	* MirrorJob.cc: modify transfer_count when MirrorJob becomes Done
	  and not in dtor.

2002-01-31  Alexander V. Lukyanov <<EMAIL>>

	* Job.cc: don't call Do in ShowRunStatus, it can do bad side effects.
	* SMTask.cc: remove invalid assert.

2002-01-30  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc: give direct parent a priority to continue transfers.
	* ftpclass.cc: better status for fxp transfers.

2002-01-29  Alexander V. Lukyanov <<EMAIL>>

	* LocalDir.cc, LocalDir.h: add forgotten dtor.

2002-01-27  Glenn Maynard <<EMAIL>>

	* commands.cc: implement du --block-size

2002-01-26  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: add missing \n.

2002-01-24  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, lftp_ssl.h: workaround for improperly closed ssl
	  data connection.

2002-01-22  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, lftp_ssl.cc: fix random SSL_write errors
	  (bad write retry); fix ftp spinning in ssl mode.
	* ftpclass.cc: don't use AUTH TLS for ftp proxy.

2002-01-17  Glenn Maynard <<EMAIL>>

	* ChmodJob.cc: add missing \n.

2002-01-14  Alexander V. Lukyanov <<EMAIL>>

	* Resolver.cc: fixed coredump on unknown service.

2002-01-13  Alexander V. Lukyanov <<EMAIL>>

	* StringPool.cc: handle null pointer.

2002-01-10  Alexander V. Lukyanov <<EMAIL>>

	* CatJob.cc, CatJob.h: use DontFailIfBroken.
	* LocalAccess.cc: (CurrentStatus) implemented; use stream->status;
	  use chdir to verify directories.

2002-01-10  Glenn Maynard <<EMAIL>>

	* FileCopy.cc, FileCopy.h: (fail_if_broken) new member.
	* ftpclass.cc: comments added; cosmetic changes.

2002-01-09  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc, FileCopy.h: (FileCopyPeer::done) new member; use it;
	  (FileCopyPeerFDStream::Done) method removed.

2002-01-08  Alexander V. Lukyanov <<EMAIL>>

	* Job.cc, Job.h: (CheckForWaitLoop) new method.
	* commands.cc: (wait) use it.

2002-01-04  Alexander V. Lukyanov <<EMAIL>>

	* LocalDir.cc, LocalDir.h: new files.
	* CmdExec.cc, CmdExec.h: use new LocalDirectory class; specify local
	  directory at CmdExec creation time.
	* LocalAccess.cc: use LocalDirectory.
	* SleepJob.cc, SleepJob.h: specify LocalDirectory at creation time;
	  use it to create CmdExec.
	* commands.cc: handle new CmdExec and SleepJob creation; allow lpwd
	  redirection.
	* lftp.cc: new CmdExec ctor.

2002-01-03  jfaith <<EMAIL>>

	* resource.cc: (OrderValidate) fix off by one xmalloc bug.

2002-01-03  Alexander V. Lukyanov <<EMAIL>>

	* Filter.cc: don't stop process if there is a process group already;
	  don't call second->Done if !delete_second.
	* lftpget: quote single quote properly.
	* Fish.cc: use fish:connect-program setting.
	* resource.cc: new fish setting; don't send referer by default.

2002-01-01  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h: fix SendCWD to set correct check case.

2001-12-26  Alexander V. Lukyanov <<EMAIL>>

	* ArgV.h: new ctor.
	* CatJob.cc, CatJob.h: use ArgV to launch filters; always launch
	  global command if there is for_each filter.
	* Filter.cc, Filter.h: DeleteSecondaryStream new method; improve
	  OutputFilter::{usesfd,Done,Kill}.

2001-12-26  Glenn Maynard <<EMAIL>>

	* ftpclass.cc: correct message.

2001-12-25  Alexander V. Lukyanov <<EMAIL>>

	* Http.cc: assume keep-alive for http/1.1 by default (reported by
	  Marc Lehmann).

2001-12-24  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: reset real_cwd when someone does quote CWD and the like;
	  scan PWD reply for rightmost quote (suggested by Glenn Maynard);
	  separate method SendCWD.

2001-12-24  Glenn Maynard <<EMAIL>>

	* StatusLine.h: make GetWidthDelayed const.

2001-12-24  Alexander V. Lukyanov <<EMAIL>>

	* LsCache.cc: ignore SetDirectory with NULL argument.

2001-12-21  Alexander V. Lukyanov <<EMAIL>>

	* mkdirJob.cc, mkdirJob.h: use orig_session approach instead of
	  url_session.
	* FindJob.cc, FindJob.h: Fg/Bg methods.

2001-12-20  Alexander V. Lukyanov <<EMAIL>>

	* ArgV.cc, ArgV.h: (seek) new method.
	* MirrorJob.cc: seek args before TreatJob creation.
	* ftpclass.cc, ftpclass.h, NetAccess.cc, NetAccess.h, Http.cc, Http.h,
	  Fish.cc, Fish.h: get rid of takeover_time; use last_priority and
	  idle_start (idea of Glenn Maynard).
	* ftpclass.cc: call PopResp before actual checking, so that Disconnect
	  can see empty RespQueue.
	* QueueFeeder.cc: don't print dots where there is only 5 jobs queued.
	* HttpDir.cc: more strict checking for squid ftp listings; handle TR
	  similarly to /TR.

2001-12-20  Glenn Maynard <<EMAIL>>

	* FileAccess.cc, FileAccess.h: add last_priority member.
	* ftpclass.cc, ftpclass.h: tune log levels.

2001-12-19  Alexander V. Lukyanov <<EMAIL>>

	* QueueFeeder.cc: pass correct job count to plural().

2001-12-19  Glenn Maynard <<EMAIL>>

	* GetFileInfo.cc, GetFileInfo.h: Use GetArrayInfo as a last-ditch effort
	  to get information.  Add most (hopefully all) branches of the basic
	  logic.  (It doesn't list cases where cache is wrong--that can happen
	  just about anywhere.)
	* CmdExec.cc, CmdExec.h: unquote, static buffer.
	* commands.cc, QueueFeeder.cc, QueueFeeder.h: give queue feedback.

2001-12-18  Glenn Maynard <<EMAIL>>

	* module.cc: change XXX to TODO.
	* buffer.cc buffer.h: add GetPos() and vFormat.
	* FileCopy.cc: FileCopyPeer can_seek and can_seek0 default to off;
	  enable it in the derived classes.
	* FileCopy.h: Allow re-disabling AllowWrite and finding out if a
	  FileCopy is line-buffered.

2001-12-18  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc, FileCopy.h, Filter.cc, Filter.h: an attempt to
	  show correct status on disk full condition.

2001-12-17  Alexander V. Lukyanov <<EMAIL>>

	* GetFileInfo.cc: move SetCwd(origdir) to dtor; improve comment.
	* MirrorJob.cc: try to enable write access to file being appended.

2001-12-17  Glenn Maynard <<EMAIL>>

	* commands.cc: queue --delete has no argument.

2001-12-17  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc, MirrorJob.h: clean up removing/chmod-ing; allow them
	  to be excuted in parallel; start scripting support.
	* GetJob.cc, GetJob.h, commands.cc: get -e option to remove target
	  file first.

2001-12-17  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: encode ' in bookmarks.
	* CmdExec.cc: quote ' properly.
	* MirrorJob.cc: handle mirror:parallel-transfer-count.
	* bookmark.cc, bookmark.h, keyvalue.cc, keyvalue.h, commands.cc:
	  don't show bookmarked passwords on `bookmark list', replace them
	  with XXXX.

2001-12-15  Alexander V. Lukyanov <<EMAIL>>

	* FindJob.cc: use default session's cwd as default url path.
	* Fish.cc, Fish.h, HttpDir.cc, Http.h: add ParseLongList method
	  (for Http - by Glenn Maynard).
	* GetFileInfo.cc: call LsCache::SetDirectory.
	* MirrorJob.cc: don't delete exclude in non-root mirror; add -X/-I
	  options; handle default exclude setting mirror:exclude-regex;
	  don't exclude all directories if first pattern is include pattern.
	* PatternSet.cc, PatternSet.h: simplify matching; fix Glob matching.
	* resource.cc: remove mirror:include-regex.

2001-12-14  Alexander V. Lukyanov <<EMAIL>>

	* FileSet.cc: Exclude appends / for directories before matching.
	* PatternSet.cc, PatternSet.h: match last directory components
	  in Glob::Match, e.g. abc/def/ghi.zip matches def/*.zip.

2001-12-14  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.cc, FileAccess.h, FileSet.cc, FileSet.h, FindJob.cc,
	  FindJob.h, GetFileInfo.cc, LocalAccess.cc, Makefile.am, MirrorJob.cc,
	  MirrorJob.h, NetAccess.cc, PatternSet.cc, PatternSet.h, commands.cc,
	  ftpclass.cc: (PatternSet) new class. Use it for exclude/include.
	* MirrorJob.cc: fixed session leak in case of options error.

2001-12-14  Alexander V. Lukyanov <<EMAIL>>

	* GetFileInfo.cc: add forgotten SetError.
	* TimeDate.h: don't use time_t as operator arg, it confuses compaq c++.

2001-12-14  Albert Chin-A-Young <<EMAIL>>

	* lftp_pty.c: add missing brace.
	* misc.cc: define unsetenv prototype if needed.

2001-12-13  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.cc, FileAccess.h: (OptimizePath) new method; optimize
	  path to cd to in ExpandTildeInCWD.
	* GetFileInfo.cc: try the file as directory even if showdir, to
	  return type info; use server returned error message; don't give up
	  if the file is not in FileSet; try directory to get error message
	  if we know from cache that there is no such file; try another
	  method only if we get NO_FILE error; strip all trailing slashes;
	  don't assume result!=0; don't descend to directory if showdir;
	  don't check verify_fn if was_directory; some xfree's added.
	* FileSetOutput.cc: don't print anything if GetFileInfo returned no set.
	* commands.cc: remove double Usage from chmod help.

2001-12-13  Glenn Maynard <<EMAIL>>

	* ChmodJob.cc: cosmetics.
	* FindJob.cc: fix mem leak; fix status.
	* GetFileInfo.cc: move the ~ and / special case check after the initial
	  chdir.
	* NetAccess.cc: fix status message.

2001-12-13  Alexander V. Lukyanov <<EMAIL>>

	* LsCache.h: rename Find to FindFileSet.
	* LsCache.cc: fixed test for fset reusability in FindFileSet;
	  fixed FindFileSet calling in IsDirectory - restore cwd first and
	  don't delete fset.

2001-12-11  Alexander V. Lukyanov <<EMAIL>>

	* ChmodJob.cc: handle undefined mode correctly.
	* misc.cc: fix dirname_alloc to strip all trailing slashes and
	  return / for /file.
	* FindJob.cc, FindJob.h, GetFileInfo.cc: support URLs and ~ in find;
	  print URL if processing an URL.

2001-12-11  Glenn Maynard <<EMAIL>>

	* ChmodJob.cc, ChmodJob.h: Add relative paths, -v, -c, -R.  Maintain
          simple numeric mode interface.
	* FileSetOutput.cc: make sure "cls -ld ." shows the directory, even
          without "show_dots".
	* FindJob.cc, FindJob.h: Accept files as well as directories to NextDir().
          Pass top-level path to ProcessFile. Don't get listing if we don't need it.
	* FindJobDu.cc: print files with "du filename" even without -a.
	* GetFileInfo.cc, GetFileInfo.h: Make prefixing the path optional.
          Add member to find out whether the given path was a file or directory.
          Fix verification with ".." at the end of path.
          If ".." was requested (with show_dirs on), return "..", not the
          name of the directory, etc.
	* TreatFileJob.cc, TreatFileJob.h: Derive from FinderJob.  AddFile() no
          longer needed.
	* commands.cc: chmod, rm, rmdir updated.  Give find "." instead of "" by
          default.
	* misc.cc, misc.h: Add dirname_alloc().  (Semantics are not exactly the
          same as dirname.)
	* rmJob.cc, rmJob.h: Handle rm -r directly.
	* FileAccess.h: (GetPriority) new method.

2001-12-11  Alexander V. Lukyanov <<EMAIL>>

	* mkdirJob.cc: set priority on newly created url_session.

2001-12-10  Alexander V. Lukyanov <<EMAIL>>

	* LsCache.cc: don't assume the listing to be parseable; call free_fset
	  when listing is replaced with new one.
	* mkdirJob.cc, mkdirJob.h, MirrorJob.cc, MirrorJob.h: add Fg/Bg.
	* MirrorJob.cc, MirrorJob.h, resource.cc: allow several directories
	  to be processed in parallel; new resources.
	* FileSet.cc: match trailing slash on directories in SortByPatternList;
	  make it case insensitive.

2001-12-10  Glenn Maynard <<EMAIL>>

	* LsCache.cc, LsCache.h, ftpclass.cc: Add explicit filetype caching.
	* LsCache.cc, LsCache.cc: Cache a single fileset.
	* GetFileInfo.cc, GetFileInfo.h: Fix up cache lookups. Call Close().

2001-12-07  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc, FileCopy.h: (RemoveTargetFirst) new method; do it.
	* MirrorJob.cc, MirrorJob.h: use RemoveTargetFirst; mirror -P
	  works again; fix NULL dereference in PrintStatus; don't derive
	  from SessionJob - use source_session member instead;
	  do mkdir in child mirror; new Statistics struct; add statistics
	  to parent mirror in child mirror's dtor.
	* TimeDate.h: rearrange inline functions to help compiler.

2001-12-05  Alexander V. Lukyanov <<EMAIL>>

	* NetAccess.cc: improve GenericParseListInfo status.
	* buffer.cc, buffer.h: allow a Speedometer to be associated with
	  Buffer class.

2001-12-05  Glenn Maynard <<EMAIL>>

	* FileSetOutput.cc, FileSetOutput.h: Add Suspend(), Resume(), Fg(), Bg().
	* QueueFeeder.cc: remove _GNU_SOURCE.

2001-12-05  Alexander V. Lukyanov <<EMAIL>>

	* LsCache.cc: use alloca in IsDirectory; style.
	* MirrorJob.cc: fix eprintf calls - double parent led to NULL.

2001-12-05  Glenn Maynard <<EMAIL>>

	* GetFileInfo.cc: use LsCache::IsDirectory.
	* LsCache.cc, LsCache.h: implement LsCache::IsDirectory.

2001-12-05  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.h: (ParseLongList) new virtual method.
	* NetAccess.h: call session->ParseLongList by default from
	  GenericParseListInfo.
	* FtpListInfo.cc, FtpListInfo.h, ftpclass.h: move parser to Ftp class.

2001-12-05  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.h: include Glob.h.
	* DummyProto.h, DummyProto.cc: little optimization; new MakeListInfo.
	* FileAccess.cc, FileAccess.h: ListInfo takes path; move Glob to
	  separate file.
	* Glob.cc, Glob.h: new files; new GenericGlob class uses ListInfo.
	* FileSet.cc, FileSet.h: fix date copying; remove unused method.
	* mgetJob.cc, FileSetOutput.cc: include Glob.h.
	* Fish.cc, Fish.h: use GenericGlob; new ListInfo ctor.
	* FtpListInfo.h: new ListInfo ctor.
	* GetFileInfo.cc, GetFileInfo.h: new ListInfo ctor; path renamed.
	* Http.cc, Http.h: new ListInfo ctor.
	* HttpDir.cc, HttpDir.h: prepare for date filling; use GenericGlob.
	* LocalAccess.cc, LocalAccess.h: new ListInfo ctor.
	* Makefile.am: adjust for added/removed files.
	* NetAccess.cc, NetAccess.h: remove GenericParseGlob class; new ListInfo ctor.
	* ftpclass.cc, ftpclass.h: new ListInfo ctor.
	* FtpGlob.cc, FtpGlob.h: removed files.

2001-12-04  Alexander V. Lukyanov <<EMAIL>>

	* NetAccess.cc, NetAccess.h: improve GenericParseListInfo so that
	  multiple modes can be tried.
	* FtpListInfo.cc, FtpListInfo.h: make it subclass of GenericParseListInfo;
	  try to guess listing type quickly.
	* FtpSplitList.cc, FtpSplitList.h: files removed.

2001-12-03  Glenn Maynard <<EMAIL>>

	* FileSetOutput.cc: don't add type suffix if type is not defined.
	* ftpclass.cc: lower disconnection message debug level if session
	  is closed.
	* FtpListInfo.cc: add forgotten new FileInfo.
	* GetFileInfo.cc: call UseCache; don't use type if it is not defined.

2001-11-30  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: ftp:use-site-chmod setting; don't simulate eof for
	  anything but RETRIEVE; don't do MKD ..
	* resource.cc: ftp:use-site-chmod setting; mirror:order instead of
	  two different settings.
	* MirrorJob.cc, MirrorJob.h: get rid of REVERSE; implement mirroring
	  between two sessions; recognize URLs.
	* FtpGlob.cc: don't show . as directory.
	* FtpListInfo.cc: try MDTM if date_prec>0.
	* Http.cc: send content-length for mkdir.
	* LocalAccess.cc: suppress EEXIST error message; correctly handle mkdir -p.

2001-11-29  Glenn Maynard <<EMAIL>>

	* CmdExec.cc, alias.cc, commands.cc, complete.cc: case insensitive
	  commands and aliases.

2001-11-26  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc, FileFeeder.cc, Filter.cc, Filter.h, Http.cc,
	  LocalAccess.cc, ProcWait.cc, PtyShell.cc, Resolver.cc, SMTask.cc,
	  SMTask.h, buffer.cc, ftpclass.cc, misc.cc, misc.h:
	  (NonFatalError) new function; use it; new setting xfer:full-disk-fatal;
	  use SMTask::now from guess_year.

2001-11-23  Glenn Maynard <<EMAIL>>

	* ftpclass.cc: fix query for ftp:web-mode.
	* FileSetOutput.cc: Fixed wildcard silliness in CLS.  First, I meant
	  strpbrk, not strspn; second, it needs to handle escapes anyway, and
	  you already have a function to do that.
	* GetFileInfo.cc: If a file "abc" exists, and we do "cls abc/def", fail.
	  Improve ENOENT and EACCES error messages.
	* FtpGlob.cc: Add path to FtpGlob status.
	* FtpSplitList.cc, FtpListInfo.cc: Move some status stuff from
	  FtpListInfo to FtpSplitList.  (FtpSplitList was essentially doing
	  this stuff anyway, just not as intelligently.)
	* commands.cc: improve error message of cls.

2001-11-22  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: fix - rmdir/rm were interchanged.

2001-11-21  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h: don't assume peer!=0, copy control socket
	  name to member peer_sa.
	* NetAccess.cc: remove obsolete commented out asserts.
	* Fish.cc: set user, group, nlinks and mode in FileInfo class.

2001-11-20  Alexander V. Lukyanov <<EMAIL>>

	* DirColors.cc, DirColors.h: move resource definition to make it
	  compile on irix.
	* CmdExec.cc: remove unneeded if's.
	* FileSet.cc: use IdNameCache.
	* TimeDate.cc, TimeDate.h: init Time from SMTask::now, this saves
	  a syscall.
	* Timer.cc, Timer.h: add ctor from TimeDiff.
	* lftp.cc: call IdNameCacheCleanup.
	* IdNameCache.cc, IdNameCache.h: new files.

2001-11-19  Alexander V. Lukyanov <<EMAIL>>

	* lftp.cc: don't set LC_NUMERIC. It breaks default float settings
	  on certain locales.
	* FileSetOutput.h: make it compile on irix.
	* FileSet.cc, FileSet.h: (SubtractCurr) new method; cosmetics.
	* FtpListInfo.cc: remove FileInfo copying.
	* LocalAccess.cc: remove files of unknown type from the set.
	* misc.cc: use va_copy, should help powerpc.

2001-11-15  Alexander V. Lukyanov <<EMAIL>>

	* DirColors.cc, DirColors.h: several new methods.
	* FileAccess.h: add color member to DirList.
	* FileSetOutput.cc: don't print size when it is not defined.
	* FtpDirList.cc, FtpDirList.h, commands.cc: add color ls support.

2001-11-15  Glenn Maynard <<EMAIL>>

	* FindJobDu.cc, FindJobDu.h, commands.cc: du --files implemented.
	* ftpclass.cc, resource.cc: ftp:ssl-allow-anonymous new setting;
	  add host closure for ftp:ssl-* settings.
	* FtpListInfo.cc: optimize unix list parser by not copying FileInfo.

2001-11-15  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc, MirrorJob.h: --allow-chown and -a - new options.
	* FileSet.cc, FileSet.h: (LocalChown) new method.
	* commands.cc: add --allow-chown to mirror help.
	* ftpclass.cc, ftpclass.h: fix web-mode.

2001-11-14  Alexander V. Lukyanov <<EMAIL>>

	* FileSet.cc, FileSet.h: add rank member; sorting by it;
	  SortByPatternList new method of FileSet; get rid of DATE_UNPREC;
	  add date_prec member.
	* FileSetOutput.cc, FtpListInfo.cc, Fish.cc: no more DATE_UNPREC.
	* LocalAccess.cc: fix uploading of empty files to file:.
	* MirrorJob.cc, MirrorJob.h: no DATE_UNPREC, no explicit date precision;
	  sort to_transfer set by mirror:order-upload/order-download.
	* commands.cc: change mirror help.
	* resource.cc: define new mirror settings, remove precision ones.

2001-11-13  Alexander V. Lukyanov <<EMAIL>>

	* StringPool.cc, StringPool.h: new files. String cache implemented.
	* FileSet.cc, FileSet.h: use them.
	* ColumnOutput.cc, DirColors.cc, DirColors.h, FileSetOutput.cc,
	  FileSetOutput.h, Makefile.am, lftp.cc, resource.cc: move LS_COLORS
	  parsing and storing into separate class DirColors; use it.
	* FtpListInfo.cc: support large file sizes.
	* HttpDir.cc: use some additional information from listing (size, perms,
	  user, group, nlink).

2001-11-13  Glenn Maynard <<EMAIL>>

	* FileSet.cc, FileSet.h, FileSetOutput.cc, FileSetOutput.h,
	FtpListInfo.cc, commands.cc:

	*user and *group field to FileInfo, parser support in ParseFtpLongList_UNIX
	and LocalFile.

	Added link count; cls -l output now has all expected fields (except for
	the ACL indicator.)

	All cls -l fields can now be enabled individually, to let people disable
	them selectively.

	Cleaned cls "tips" a little (not enough); added a forgotton \n.

	cls will now remove entire fields if no files have it, instead of
	padding every file.  (ColumnOutput only does this for the left side of
	an entire entry.)

2001-11-13  Glenn Maynard <<EMAIL>>

	* FileSet.cc, FileSet.h: (PrependPath) new FileSet method.

	* FileSetOutput.cc, FileSetOutput.h, FindJobDu.cc, GetFileInfo.cc,
	GetFileInfo.h, Makefile.am, commands.cc, ftpclass.cc:

	Added -d, --block-size, -h, and -k to cls.  Sizes now default to blocks
	of 1024 unless -l is given (full sizes in column-output display is too
	much; this also aligns with "ls -s".)  cls no longer has the
	trailing-slash problem.

	Parse cls-default before commandline arguments for cls, so they don't
	override explicit options.

	Changed CHANGE_DIR 550 to pri 4.

	Restore CWD in GetListInfo.  (Removed restore directory for cls.)

	Some du changes that aren't really needed; they're related to my
	attempts to make FinderJob work when given file arguments.  They won't
	cause problems without the FinderJob changes.

	* complete.cc: cosmetics.

	* ColumnOutput.cc: (addf) fix memory leak.

2001-11-12  Alexander V. Lukyanov <<EMAIL>>

	* resource.cc: remove NoClosure from net:connection-limit. Reported
	  by Piotr Krukowiecki <<EMAIL>>.

2001-11-09  Alexander V. Lukyanov <<EMAIL>>

	* ResMgr.cc, ResMgr.h, StatusLine.cc, TimeDate.cc, TimeDate.h,
	  Timer.cc, Timer.h, complete.cc: make TimeInterval subclass of
	  TimeDiff; add resource support in Timer; fix Timer to stop
	  spinning once it is Stopped.

2001-11-09  Glenn Maynard <<EMAIL>>

	* FileSet.cc FileSet.h FileSetOutput.cc FileSetOutput.h FindJob.cc
	FindJob.h FtpListInfo.cc Makefile.am TimeDate.h commands.cc
	complete.cc ftpclass.cc FindJobDu.cc FindJobDu.h:

	Fixed "cls dir/ dir2/", etc (restore directory.)

	Fixed (my) memleak in FileSet (clear sorted list in Empty(); not sure why
	this doesn't appear with FileSetOutput.)

	Fixed cosmetic problem with FinderJob (status had [session status] twice.)

	Fixed ParseFtpLongList_UNIX: guess_year needs -1900.  Also, DST rolling
	around uncovered a bug; set the hour to something other than 0 if we
	don't know it, or DST -1 will end up changing the date.

	Fixed Time::time_t operator-.  Why -(int) and -(long)?  Also, haing
	both Set() and set() is strange (and I don't see what the difference is.)

	Added a bit to FinderJob:

	Allow adding to ListInfo()'s Need.

	FinderJob now expects a NextDir() by the child class's constructor.
	NextDir needs to call a virtual; we can't do that from the base
	constructor.  (I think it's a little more straightforward, too: always
	start a find with NextDir, instead of the first path being an
	exception.)

	Regular find command now accepts multiple arguments.

	Call Enter(dir) when entering a dir and Exit() when leaving;
	Call ProcessList(fileset) when we have a new fileset, to give opportunity
	for ops on the whole list, including sorting.

	Added find and du to remote completers.

2001-11-08  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: set debug level 4 for `550 No files found' reply;
	  send QUIT from destructor.
	* buffer.cc: optimize Format method, vsnprintf directly to buffer.
	* Job.cc: let the job run once before showing status.

2001-11-08  Glenn Maynard <<EMAIL>>

	* FileSet.cc, FileSet.h: Added a pattern-exclude() for FileSets.
	* Fish.h, Http.h, log.h: no need for StatusLine.
	* StatusLine.cc: StatusLine doesn't update for the first 20ms.
	* buffer.cc, buffer.h: Added Buffer::Format().

2001-11-06  Alexander V. Lukyanov <<EMAIL>>

	* PtyShell.cc: include sys/types.h and sys/ioctl.h.
	* TimeDate.cc, TimeDate.h, Timer.cc, Timer.h: new files.
	* CopyJob.cc, CopyJob.h, FileCopy.cc, FileCopy.h, Fish.cc,
	  FtpListInfo.cc, Http.cc, HttpDir.cc, NetAccess.cc,
	  NetAccess.h, Resolver.cc, SMTask.cc, SMTask.h, SleepJob.cc,
	  Speedometer.cc, Speedometer.h, StatusLine.cc, StatusLine.h,
	  complete.cc, ftpclass.cc, misc.cc, misc.h: use new time classes.
	* commands.cc: (builtin_lcd) don't assume cwd!=0.
	* FtpDirList.cc: (EPLF) use TimeDate::IsoDateTime; add missing \n.

2001-11-05  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.h: fix coredump on `mget -E'.
	* complete.cc, parsecmd.cc: accept single quote as well as double
	  quote, no difference between the two yet.

2001-10-30  Alexander V. Lukyanov <<EMAIL>>

	* FileCopy.cc: no need to convert cached data - it is done by other
	  copy peer; don't translate really debugging messages.
	* misc.cc: no need for crlf_to_lf.
	* complete.cc: don't show completion status right after the tab since
	  the listing can come from cache fast.

2001-10-29  Alexander V. Lukyanov <<EMAIL>>

	* misc.cc: use dynamic allocation in set_tz; use unsetenv to unset TZ;
	  implement unsetenv is not available using environ array.
	* ColumnOutput.cc: make it compile with irix compiler.

2001-10-27  Alexander V. Lukyanov <<EMAIL>>

	* FtpDirList.cc: clean up; style.
	* FileCopy.cc: style; restore eof behaviour.
	* misc.cc: rewrite mem_crlf and crlf_to_lf to make them more clear.

2001-10-27  Glenn Maynard <<EMAIL>>

	* FileCopy.cc, FtpDirList.cc, FtpDirList.h, misc.cc, misc.h:
	  Speed up ascii mode transfer.
	* command.cc: cosmetics.

2001-10-25  Alexander V. Lukyanov <<EMAIL>>

	* misc.cc, misc.h: (mktime_from_tz) new function.
	* FtpListInfo.cc, FtpListInfo.h: use it.
	* resource.cc: (ftp:timezone) new setting.

2001-10-25  Glenn Maynard <<EMAIL>>

	* FileSetOutput.cc, FileSetOutput.h, commands.cc: -1 option for cls;
	  readded -q to help cls; added missing \n.
	* misc.cc, misc.h: -rwxr-xr-t implies S_IXOTH; -rwxr-xr-T does not;
	  mktime_from_utc has a problem when isdst is -1; the returned time is
	  off by an hour if the local timezone is in DST.
	* FtpListInfo.cc: assume unix server's timezone to be GMT.

2001-10-25  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc, FileAccess.h, FileCopyFtp.cc, FileSetOutput.cc, Fish.cc,
	  GetJob.cc, Http.cc, NetAccess.cc, ResMgr.h, Resolver.cc,
	  Speedometer.cc, StatusLine.cc, commands.cc, ftpclass.cc, ftpclass.h,
	  lftp.cc: (QueryBool) new functions, now lftp should compile with
	  a compiler without bool type.
	* Fish.cc: check line length before comparing with yes/no/password;
	  remove extra xfree(home), fortunately home was null at that point.

2001-10-22  Alexander V. Lukyanov <<EMAIL>>

	* complete.cc: fix local tilde completion.
	* Fish.cc, resource.cc: add fish:shell setting.
	* SMTask.cc, misc.cc: include time.h first.
	* Makefile.am: distribute convert-netscape-cookies.

2001-10-19  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: fix typo in cls help. Reported by Nicolas Noble.
	* complete.cc: restore prompt and clear message on SIGINT.

2001-10-17  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h: workaround for WarFTPD ABOR glitch.
	* CopyJob.cc, CopyJob.h, FileCopy.cc, FileCopy.h: NoStatusOnWrite added.
	* commands.cc: use it.
	* FileSetOutput.cc, FileSetOutput.h: fix recls.
	* misc.cc: (xasprinf) fix va_end argument.
	* complete.cc: make readline define correct rl_message prototype.
	* FileSetOutput.cc: make it output precise date too.

2001-10-16  Alexander V. Lukyanov <<EMAIL>>

	* misc.cc: use int to avoid warning.

2001-10-15  Alexander V. Lukyanov <<EMAIL>>

	* ColumnOutput.cc: use xfree.
	* FileAccess.h: add comment about ListInfo::GetResult.
	* FileSetOutput.cc: fix memory leaks; use xfree.
	* commands.cc: cosmetics.

2001-10-15  Alexander V. Lukyanov <<EMAIL>>

	* misc.cc, misc.h: conding style; safety precautions (better checking
	  on vsnprintf return value); just double buffer size; include stdarg.h
	  in misc.h.

2001-10-15  Glenn F. Maynard <<EMAIL>>

	* ColumnOutput.cc, ColumnOutput.h, FileSetOutput.cc, misc.h, misc.cc:

	Column output now treats width properly (using mbswidth().)

	Date output added to cls -l; uses locale-dependant date (like ls does.)

	Added xasprintf and xvasprintf.  (Trivial asprintf/vasprintf
	implementation so we don't need to deal with trio just for those.)

	Added some ColumnOutput support for date output (addf()).

	* PtyShell.cc: fix warnings.
	* Fish.cc: improve status.

2001-10-12  Alexander V. Lukyanov <<EMAIL>>

	* url.cc: optimize user/password extraction using strrchr.

2001-10-12  Jonas Jensen <<EMAIL>>

	* url.cc: allow @ in password.

2001-10-12  Alexander V. Lukyanov <<EMAIL>>

	* Fish.cc, Fish.h: use PtyShell; answer ssh questions about (yes/no)
	  and password.
	* StatusLine.cc: (Show) check timer even if newstr is equal to to_be_shown.
	* PtyShell.cc, PtyShell.h, lftp_pty.cc, lftp_pty.h: new files.
	* ArgV.h: (Add) alias to Append.
	* complete.cc: add > to remote completion status.
	* ftpclass.cc: don't add ./ to CWD in extreme case when PWD is not
	  absolute.
	* misc.cc: (dir_file) don't return NULL if possible.
	* Time.cc, Time.h: (ms) fixed integer overflow.

2001-10-10  Glenn F. Maynard <<EMAIL>>

	* StatusLine.cc, StatusLine.h, Time.cc, Time.h, complete.cc:

	added ms constructor for time; this gives us a free cast from int to
	Time, which means all of the ms operations can away (they're implicit.)

	Defined negative time values more clearly (in comments).  This usually
	doesn't matter.

	Fixed Time.h indentation.

2001-10-10  Alexander V. Lukyanov <<EMAIL>>

	* FileSet.cc: (LocalFile) don't set name if already set. This fixes
	  LocalAccess' ListInfo and mirror.
	* PollVec.cc: treat AddTimeout(negative) as AddTimeout(0).

2001-10-09  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h, FileAccess.cc: more home_auto fixes;
	  SameLocationAs returns true even if o->home is null.

2001-10-09  Glenn F. Maynard <<EMAIL>>

	* Time.cc, Time.h, commands.cc, complete.cc, StatusLine.cc, StatusLine.h,
	misc.cc, misc.h:

	Time and Timer classes in Time.{cc,h}.  Implemented only a
	couple operators; add others as required.  This can probably be fairly
	widely-used; I didn't implement it anywhere else, though.  It doesn't
	cast to anything; that just causes problems.

	Disabled short help for: rels renlist reget reput

	added references: [re]ls, [re]nlist, [re]get, [re]cls, [re]put
	Help is down to 23 lines (from 25.)  How about changing help to use
	ColumnOutput?  That might cut it down further.  (Due to the length of
	some short help, it might not, though--not on an 80x25, anyway.)

	Updated [c]ls help; it was too long.

	(xgettimeofday) new function. (cmd:status-interval) new setting.

2001-10-08  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: fix site command broken by ls changes.
	* FileAccess.h, FileAccess.cc, Http.cc, Http.h, ftpclass.cc, ftpclass.h:
	  NetAccess.cc, NetAccess.h: new method ResetLocationData; reset
	  home_auto on Connect/Login/AnonymousLogin.
	* FileAccess.h, Fish.cc, ftpclass.cc, ftpclass.h: (FA::set_home) new
	  method; use it; (Ftp::PropagateHomeAuto) new method; use it; fixed
	  bug with unsynced home's.

2001-10-08  Glenn F. Maynard <<EMAIL>>

	* ColumnOutput.cc, commands.cc, complete.cc, complete.h, lftp_rl.c:

	Reupdated my address.

	Add cls, recls and rels to list of remote-completers.

	Make local completion mimic remote completion by forming a FileSet.
	This simplifies completion, gets rid of ignore_non_dirs (which was
	causing some really obscure problems with completion adding one too
	many slashes--didn't track this down since ignore_non_dirs isn't used
	anymore) and gives us color (etc) for local completion.  I didn't
	actually delete ignore_non_dirs yet.

	Add status feedback for remote completion, in the message area.  If -q
	is given in cls_completion_default, this is disabled.  (This is also
	undocumented and slightly different from the effect -q is intended to
	have--but doesn't, since it's currently disabled--but it seems the most
	appropriate place to put this, since cls_completion_default honors just
	about every cls option; and even if status displaying was currently
	working for cls, job status wouldn't be appropriate for completion
	since it'd mangle the line.)  I'll hold off on documenting this until -q
	is fixed for cls (unless it turns out that it just can't reasonably be done,
	but I'm optimistic.)

	Keep glob_res around after completion until the next completion or the
	next command; this lets us use it to do our own completion result
	display.

	Handle our own completion results; this also gives us optimized column
	widths for all completions, not just filenames.

	Fixed color reset bug.

	Glitch: we don't honor the user's completion-ignore-case readline
	setting.  We can't get access to it portably.  It's not too big a deal;
	adding -i to cls-completion-default has the same effect, and honoring
	both of them would probably be a little confusing anyway.

2001-10-05  Alexander V. Lukyanov <<EMAIL>>

	* ColumnOutput.cc: fix memory leak; change \e to \\e.
	* FileSetOutput.cc: fix coredump when LS_COLORS is unset.
	* misc.h: add comment on Subst.
	* lftp.cc: set top_exec to zero after deletion, this fixes a coredump
	  at exit.
	* commands.cc: remove bzcat, bzmore and cls from command index,
	  it has become too long for 25 linear display.

2001-10-05  Glenn F. Maynard <<EMAIL>>

	* CmdExec.cc, ColumnOutput.cc, ColumnOutput.h, FileSet.cc, FileSet.h,
	FileSetOutput.cc, FileSetOutput.h, Filter.cc, Filter.h, Makefile.am,
	ResMgr.cc, ResMgr.h, commands.cc, misc.cc, misc.h:

	ColumnOutput.{cc,h}: column display, originally from FileUtils.  I've
	cleaned it up substantially, generalized color support, class-ized it
	and some other stuff.

	Moved FDStream::width() to misc.cc; nuked isatty().

	tokenize() bugfixes.

	Added TriBoolValidate; Bool with a third ("Auto").  Feel free to think
	of a better name.

	Don't give ls's job a blank name; results in unsightly empty `' in
	status.

	Add sorting to FileSets.  A second array of pointers (aliasing the main
	list) is created when the list is sorted; the list can't be modified if
	the list is sorted (so we don't have to keep track of how the list is
	sorted.)  Multiple-level sorting is possible (sort by name then
	directories-first, for example.)  This method doesn't have to touch much
	of FileSet and keeps the binary-search-insert intact.  (If needed, it
	could be made to allow adds after sorting by simply dumping new entries
	on the end of the sorted list, so new entries aren't sorted.)

	FileSet::operator[] uses the sorted list, if available.

	FindByName uses FindGEIndByName, making that function O(log n) rather
	than O(n).

	A bunch of static, very cut-and-paste code from FileUtils.cc for parsing
	LS_COLORS.

	FileSetPrint class.

2001-10-03  Glenn F. Maynard <<EMAIL>>

	* ArgV.cc, ArgV.h, CmdExec.cc, DummyProto.cc, DummyProto.h,
	FileAccess.cc, FileAccess.h, FileSet.cc, FileSet.h, Filter.cc,
	Filter.h, Http.cc, LocalAccess.cc, QueueFeeder.cc, StatusLine.cc,
	keyvalue.cc, keyvalue.h, misc.cc, misc.h:

	New ArgV constructor, to take a string and tokenize it.
	misc.cc code to do the real work.  (This could possibly be improved
	to make it usable for the main tokenizer.)

	misc.cc code to do \x, etc. substitution, and code elsewhere to use
	this.  (This would have been the third time this could would have been
	duplicated, so I merged it.)

	DummyNoProto for MakeListInfo, same as MakeDirList.

	Option for Glob to ignore case.

	isatty() and width() for FDStream.  (isatty() could be done away with,
	as it doesn't really save any code being a 1-liner; width() is more
	functional.)

	Move make-FileInfo-from-local-file into FileInfo::LocalFile().

	Dumb QueueFeeder bugfix (of my own bug, of course.)

	Made a bunch of KeyValue functions const.  (Not completely correctly:
	when returning a member value like those do, there really should be both
	a const and non-const version ...)

2001-10-02  Aaron Schrab <<EMAIL>>

	* Fish.cc, Http.cc, log.cc: move va_end so that it is called always.

2001-10-01  Aaron Schrab <<EMAIL>>

	* Fish.cc, Http.cc, log.cc: call va_start multiple
	  times when needed, fixes powerpc coredumps.

2001-10-01  Alexander V. Lukyanov  <<EMAIL>>

	* commands.cc: remove unneeded _().

2001-09-12  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: sometimes CWD was not sent after reconnection
	  because of missing set_real_cwd - fix it.

2001-09-10  Alexander V. Lukyanov  <<EMAIL>>

	* lftp_rl.c: print * for modified lines in history (like in bash).
	* resource.cc, ftpclass.cc: use retry-530-anonymous as an additional
	  regexp for retry-530.

2001-09-10  Glenn F. Maynard <<EMAIL>>

	* commands.cc, lftp_rl.c, lftp_rl.h: implement history command.

2001-08-28  Alexander V. Lukyanov  <<EMAIL>>

	* Resolver.cc: retry with use_fork=false; don't create pipe if
	  use_fork==false.

2001-08-27  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: (MoveConnectionHere) set home if null.

2001-08-27  Glenn F. Maynard <<EMAIL>>

	* FileSet.cc, FileSet.h, FileAccess.h, CmdExec.cc: remove the FileSet
	  sort stuff.  It's superfluous now.
	* commands.cc: fix a minor UI bug with queue -d.

2001-08-22  Alexander V. Lukyanov  <<EMAIL>>

	* FtpListInfo.cc: remove erroneous assert, handle error.
	* ftpclass.cc, ftpclass.h: (home_auto) new member; support ftp:home
	  variable.
	* resource.cc: add ftp:home, update ftp:retry-530.
	* ResMgr.cc: verify default value with supplied validator.

2001-08-20  Alexander V. Lukyanov  <<EMAIL>>

	* resource.cc: fixed ftp:ssl-auth validation.
	* ftpclass.cc: improve abort for passive mode - connect data socket
	  if it is not connected yet.

2001-08-16  Wolfram Gloger <<EMAIL>>

	* FileSet.cc: include stdlib.h; use labs instead of abs.

2001-08-15  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc, ResMgr.cc: declare inet_aton if needed.

2001-08-07  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: fix memory allocation error.

2001-07-30  Alexander V. Lukyanov  <<EMAIL>>

	* commands.cc: format queue help at 80 columns.
	* Resolver.cc: little correction for RAND_MAX.
	* Speedometer.cc: call Get once, maybe fix for math exception.

2001-07-26  Glenn F. Maynard <<EMAIL>>

	* CmdExec.cc, CmdExec.h, Job.cc, Makefile.am, commands.cc: queue
	  editing support (QueueFeeder class).
	* QueueFeeder.cc, QueueFeeder.h: new files.

2001-07-25  Alexander V. Lukyanov  <<EMAIL>>

	* FtpSplitList.cc: fix mem leak (reported by Glenn F. Maynard).
	* ftpclass.cc: break normal execution if Error().
	* Http.cc: workaround for range end ==-1.

2001-07-24  Glenn F. Maynard <<EMAIL>>

	* FileAccess.cc: make url protocol case insensitive.

2001-07-24  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: fix CONNECT_VERIFY in case of borrowed connection.
	* commands.cc: check find -d argument for being a number.

2001-07-19  Glenn F. Maynard <<EMAIL>>

	* FindJob.cc, FindJob.h, commands.cc: add find --maxdepth option.

2001-07-19  Glenn F. Maynard <<EMAIL>>

	* CmdExec.cc, CmdExec.h, commands.cc: implement queue editing -
	  queue -s and queue -e commands.

2001-07-18  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc, ftpclass.h, resource.cc: allow to choose AUTH argument;
	  ftp:ssl-auth new variable.

2001-07-14  Alexander V. Lukyanov  <<EMAIL>>

	* ResMgr.cc: make it compile with irix compiler.
	* FtpListInfo.cc: add MacWebStar parser.

2001-07-09  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: make it work without ssl.

2001-07-09  Glenn F. Maynard <<EMAIL>>

	* FileSet.cc, FileSet.h: speed-optimization using binary search.

2001-07-06  Alexander V. Lukyanov  <<EMAIL>>

	* misc.cc: little speed-optimization in squeeze_file_name.

2001-07-03  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc, FileAccess.h, ftpclass.cc, ftpclass.h:
	  get rid of group/gpass.
	* Resolver.cc, netrc.cc, Fish.cc, Http.cc: use case-insensitive
	  hostname comparision.
	* Http.cc, Http.h: support CONNECT method for https over http proxy.

2001-07-02  Alexander V. Lukyanov  <<EMAIL>>

	* Resolve.cc: update SRV support for RFC2782.
	* HttpDir.cc: add roxen listing support; reorganize.

2001-06-30  Alexander V. Lukyanov  <<EMAIL>>

	* url.cc: use host=localhost for urls like file:///path.

2001-06-26  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: fix closure generation for user==0.
	* CmdExec.cc: fix for cwd==0.
	* Http.cc: get rid of data_buf; handle identity encoding.
	* Fish.cc, Fish.h: more work.

2001-06-19  Alexander V. Lukyanov  <<EMAIL>>

	* resource.cc: new settings ftp:port-ipv4, ftp:acct, ftp:site-group.
	* ftpclass.cc, ftpclass.h: support for ACCT, SITE GROUP,
	  and for ftp:port-ipv4.
	* ResMgr.cc, ResMgr.h: IPv4AddrValidate new validator.

2001-06-18  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: handle 416 reply code.
	* ProcWait.cc, ProcWait.h, lftp.cc: (ProcWait::DeleteAll) new method;
	  use it.
	* FileAccess.cc, modconfig.h, Makefile.am: include Fish.

2001-06-12  Alexander V. Lukyanov  <<EMAIL>>

	* FileCopyFtp.cc: fix restart in fxp mode.
	* HttpDir.cc, HttpDir.h, NetAccess.cc, NetAccess.h: (GenericParseGlob)
	  new class; use it.
	* Fish.cc, Fish.h: implement Glob, ListInfo.
	* ftpclass.cc: better status for fxp.

2001-06-07  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc, FileAccess.h: NO_USER new GetFileURL option.
	* Http.cc: don't put user name in Referer.

2001-06-04  Alexander V. Lukyanov  <<EMAIL>>

	* commands.cc: fix `debug 9 -o log'.
	* ftpclass.cc: handle Serv-U reply to STAT command.
	* Fish.cc: some more work.

2001-06-01  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc, Http.h: implement Cleanup and CleanupThis methods.
	* ftpclass.cc, ftpclass.h: get rid of ftp_chain and ftp_next.

2001-05-30  Alexander V. Lukyanov  <<EMAIL>>

	* Resolver.cc: fix a memory leak in use-fork=no mode.

2001-05-29  Alexander V. Lukyanov  <<EMAIL>>

	* lftp.cc, Job.cc, Job.h, SMTask.cc, SMTask.h, SleepJob.cc, SleepJob.h:
	  add notification for moving to background; terminate some never-ending
	  jobs before moving to bg.
	* resource.cc: change default for ftp:fix-pasv-address to true.
	* Http.cc, Http.h: support keep-alive for chunked transfer-encoding.
	* ftpclass.cc, ftpclass.h: fix reget for non-existent files.
	* Resolver.cc: report number of addresses.
	* SMTask.cc: tune Schedule for recursive calling by setting sched_scan
	  to current.

2001-05-29  albert chin (<EMAIL>)

	* Fish.cc, Http.cc, StatusLine.cc, log.cc: use trio functions if needed.

2001-05-29  Alexander V. Lukyanov  <<EMAIL>>

	* complete.cc: (vars_generator) special generator for variable
	  names to match variable name using ResMgr::VarNameCmp.

2001-05-29  Nicolas Noble <<EMAIL>>

	* complete.cc, ResMgr.cc, ResMgr.h: add variable name completion
	  support.

2001-05-24  Alexander V. Lukyanov  <<EMAIL>>

	* MirrorJob.cc, MirrorJob.h: support `mirror -R -c'.
	* resource.cc: some more patterns for retry-530.

2001-05-15  Alexander V. Lukyanov  <<EMAIL>>

	* resource.cc: new settings ftp:retry-530 and ftp:retry-530-anonymous.
	  This settings should be useful to distinguish between overloaded
	  server (temporary condition) and incorrect password (permanent
	  condition).
	* ftpclass.cc: retry on server reply 530 for PASS command if text
	  matches above regular expressions. Retry ftp login quickly on next
	  address if the server has many (works well on ftp.redhat.com, for
	  example).
	* ResMgr.cc, ResMgr.h: improved fuzzy variable name matching. Exact
	  prefix and exact name after prefix are taken into account separately.
	  E.g. http:pro is not ambigous now between http:proxy and https:proxy.
	* misc.cc, misc.h: move re_match here.

2001-05-10  Alexander V. Lukyanov  <<EMAIL>>

	* CmdExec.cc: delete status_line in dtor.
	* CmdExec.h: SetStatusLine new method.
	* FileAccess.cc, FileAccess.h: SessionPool:ClearAll new method.
	* Makefile.am: fix rpm build, add buffer_ssl.h to sources; strip modules.
	* ResMgr.cc: don't call ReconfigAll in ResDecl dtor.
	* SMTask.cc, SMTask.h: TaskCount new method.
	* lftp.cc: delete most tasks, print warning if some extra left.

2001-05-04  Alexander V. Lukyanov  <<EMAIL>>

	* NetAccess.cc, NetAccess.h: (HandleTimeout) new function.
	* ftpclass.cc, ftpclass.h: redefine HandleTimeout to set quit_sent
	  (fixes non-fatal deadlock).
	* commands.cc: allow open "" and empty bookmarks.
	* resource.cc: don't use user name in anon-pass (suggested by
	  Eduardo Perez Ureta <<EMAIL>>).

2001-04-17  Alexander V. Lukyanov  <<EMAIL>>

	* MirrorJob.cc, mkdirJob.cc, StatusLine.cc: fix overflows.
	* complete.cc, lftp_rl.c, lftp_rl.h, alias.h: make it compile with
	  readline-4.2.

2001-04-04  Alexander V. Lukyanov  <<EMAIL>>

	* Fish.cc, SleepJob.cc, ftpclass.cc: move CDECL to modconfig.h.
	* FtpListInfo.cc: set tm_isdst to -1 (automatic DST).
	* Http.cc: don't add extra / to Referer.
	* Makefile.am: use libtool to build modules; libnetwork.so.
	* MirrorJob.cc: remove unneeded N_.
	* ResMgr.cc, ResMgr.h: add dtor for ResDecl.
	* Resolver.cc, Resolver.h: move resources to resource.cc;
	  remove ClassInit.
	* SMTask.cc, SMTask.h: add DeleteAll method.
	* buffer.cc, buffer.h: move IOBufferSSL to separate files.
	* buffer_ssl.cc, buffer_ssl.h: new files.
	* lftp_ssl.h: add ifdef USE_SSL.
	* log.cc: automatically init Log::global.
	* modconfig.h: add MODULE_NETWORK; prototype for module_init.
	* resource.cc: move resources from Resolver.cc here.

2001-03-22  Alexander V. Lukyanov  <<EMAIL>>

	* Resolver.cc: set default for dns:cache-expire to 1h.

2001-03-20  Alexander V. Lukyanov  <<EMAIL>>

	* keyvalue.cc: ignore certain lock errors.
	* Makefile.am: install modules into pkglibdir/version dir.
	* resource.cc, Http.cc: http:referer, hftp:use-type new settings.

2001-03-13  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: add \n to connect: message.
	* HttpDir.cc: do Exclude.
	* commands.cc: update help for `module'.
	* module.cc: search modules in module:path.
	* resource.cc: init module:path from env LFTP_MODULE_PATH.

2001-03-13  Arkadiusz Miskiewicz <<EMAIL>>

	* Job.cc, Job.h: undef printf (for new glibc).

2001-03-13  Glenn F. Maynard <<EMAIL>>

	* CmdExec.cc, CmdExec.h, StatusLine.cc, StatusLine.h: support for
	  terminal titlebar.

2001-02-23  Alexander V. Lukyanov  <<EMAIL>>

	* CmdExec.cc: (MakePrompt) fix \NNN recognition (reported by
	  Glenn F. Maynard)

2001-02-13  Alexander V. Lukyanov  <<EMAIL>>

	* HttpDir.cc: accept perms string 11 char long.
	* misc.cc: accept last + in perms string as ACL tag.
	* commands.cc: open command file in ascii mode.
	* Fish.cc, Http.cc, NetAccess.cc, NetAccess.h, ftpclass.cc:
	  create RateLimit with closure.

2001-02-07  Alexander V. Lukyanov  <<EMAIL>>

	* Resolver.cc: fix rare spinning when parent process terminates.
	* ftpclass.cc: don't bind data socket to loopback address.

2001-02-04  Alexander V. Lukyanov  <<EMAIL>>

	* url.cc: fix Combine when proto==NULL.

2001-02-03  Alexander V. Lukyanov  <<EMAIL>>

	* FileCopy.cc, FileFeeder.cc, Filter.cc, Http.cc, LocalAccess.cc,
	  ProcWait.cc, buffer.cc, ftpclass.cc, keyvalue.cc: use E_RETRY macro.
	* FileCopy.h: drop size information if pos>size.

2001-01-25  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: print error to debug output from Http::Read.
	* Job.cc: add timeout when displaying status of several jobs in cycle.
	* pgetJob.cc: more space in status line for eta.

2001-01-12  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: fix opie/skey reply.
	* Http.cc: some fixes for PUT and `100 Continue'.

2000-12-20  Alexander V. Lukyanov  <<EMAIL>>

	* commands.cc: also use ':' as user/pass separator.
	* ftpclass.cc, resource.cc: ftp:fix-pasv-address setting.

2000-12-18  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc, misc.cc, misc.h, ftpclass.cc: move mktime_from_utc to
	  misc.cc.
	* HttpDir.cc: correct array size.
	* lftp.cc: add cmd:save-cwd-history setting.
	* Speedometer.cc: show up to 99 seconds eta in seconds.

2000-12-08  Chmouel Boudjnah <<EMAIL>>

	* CatJob.cc, commands.cc, complete.cc: add bzcat and bzmore support.

2000-12-01  Alexander V. Lukyanov  <<EMAIL>>

	* resource.cc: hftp:use-authorization.
	* Http.cc: send port in Host: header; hftp:use-authorization new
	  setting.

2000-11-27  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc, FileAccess.h, Http.cc, ftpclass.cc, ftpclass.h,
	  NetAccess.cc, NetAccess.h: (ProtocolSubstitution) new virtual
	  method; check net:no-proxy before changing ftp to hftp.

2000-11-20  Alexander V. Lukyanov  <<EMAIL>>

	* url.cc: implement [ipv6::address]:port as specified by rfc2732.
	* complete.cc: fix local tilde globbing.

2000-11-14  Alexander V. Lukyanov  <<EMAIL>>

	* bookmark.cc, bookmark.h, keyvalue.cc, history.cc: don't wait for
	  lock forever; print warning if locking fails.

2000-11-05  Alexander V. Lukyanov  <<EMAIL>>

	* history.cc, history.h: add flag of modification.

2000-10-24  Alexander V. Lukyanov  <<EMAIL>>

	* commands.cc: set last_bg in queue.
	* lftpget: set max-redirections.

2000-10-23  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: set event_time in Read/Write.
	* FileAccess.cc: use `now' instead of time().

2000-10-19  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: don't call Poll on data socket (no real need).
	* FileSet.h, FileSet.cc: eliminate only_newer argument, use ignore mask.
	* MirrorJob.cc: use the new ignore mask values; don't assume only-newer
	  for reverse mirror.

2000-10-16  Alexander V. Lukyanov  <<EMAIL>>

	* mgetJob.cc: fix mput for directory case (continue loop instead
	  of returning).

2000-10-15  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: use service name `ftps' for ftps; don't queue up
	  too much MDTM/SIZE commands in sync mode.

2000-10-12  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc, FileAccess.h: fix globbing for tilde; optimize
	  the case when pattern does not contain wildcards.
	* HttpDir.cc, FtpGlob.cc: some code moved to FileAccess.cc.
	* complete.cc: call NoInhibitTilde.

2000-10-12  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc, FileAccess.h, FtpSplitList.cc, FtpSplitList.h,
	  FtpGlob.cc, HttpDir.cc: Glob::add cleanup.
	* complete.cc: fix completion for filenames starting with tilde.
	* ftpclass.cc, ftpclass.h: fix for old gcc (ftps undefined).

2000-10-04  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: handle CONNECT_VERIFY in CONNECTED state.
	* NetAccess.cc: Disconnect before Connect.

2000-10-03  Alexander V. Lukyanov  <<EMAIL>>

	* Makefile.am: use C compiler to compile modules, it should use c++
	  for c++ files automatically.
	* ResMgr.cc: use extern "C" for regex.h, this fixes linking for some
	  systems.

2000-09-29  Alexander V. Lukyanov  <<EMAIL>>

	* FileCopy.cc: fix for `ls|head'.
	* ftpclass.cc, resource.cc: ftp:bind-data-socket new setting;
	  fix for assert(rate_limit!=0).
	* url.cc, url.h: provisions for not using rfc1738 for ftp urls.

2000-09-23  Alexander V. Lukyanov  <<EMAIL>>

	* commands.cc: fix lcd & in queue.
	* commands.cc: fix cmd:move-background handling when there is no jobs.

2000-09-22  Alexander V. Lukyanov  <<EMAIL>>

	* commands.cc: fix cd & in queue.

2000-09-21  Arkadiusz Miskiewicz <<EMAIL>>

	* ftpclass.cc: fix addr_received initialization for ipv6.

2000-09-21  Alexander V. Lukyanov  <<EMAIL>>

	* module.cc: aliases for https and ftps.

2000-09-19  Alexander V. Lukyanov  <<EMAIL>>

	* CmdExec.cc: fix error on some compilers (e.g. irix).

2000-09-15  Alexander V. Lukyanov  <<EMAIL>>

	* pgetJob.cc: fix yet another hang bug (hopefully last).

2000-09-14  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: move out of CONNECTING_DATA_SOCKET after DataClose
	  (fixes rare `bad file number' error).
	* MirrorJob.cc: propagate remove_source_files to submirrors.

2000-09-12  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc: include modconfig.h. (fixes module compilation).

2000-09-11  Alexander V. Lukyanov  <<EMAIL>>

	* buffer.cc: handle eof on ssl connection properly.

2000-09-08  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: don't remember path=/ in cookies; skip all spaces
	  before a cookie.

2000-09-07  Alexander V. Lukyanov  <<EMAIL>>

	* MirrorJob.cc, MirrorJob.h: support for redirection.
	* CmdExec.cc: move `cd' out ot translated string.
	* Http.cc, LocalAccess.cc, Fish.cc, MirrorJob.cc, SleepJob.cc:
	  don't rely on MODULE.
	* Makefile.am: don't define MODULE.

2000-09-05  Alexander V. Lukyanov  <<EMAIL>>

	* GetJob.cc, GetJob.h: restore mode on new file.

2000-09-05  Matthias Andree <<EMAIL>>

	* FtpDirList.cc: reformat EPLF even if mtime if absent.

2000-08-30  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc, ftpclass.h: try PORT if connect fails after fixing
	  pasv address.
	* Http.cc: really skip rest of body from recv_buf; call recv_buf->Do.

2000-08-29  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: fix error handling for port-range; bind local address
	  to data socket in passive mode (helps for rare case of routing change).

2000-08-26  Nicolas Noble <<EMAIL>>

	* command.cc: fix N_ for queue help.

2000-08-26  Alexander V. Lukyanov  <<EMAIL>>

	* CmdExec.cc, CmdExec.h: follow redirections in `cd'.
	* mkdirJob.cc, rmJob.cc: add forgotten plural.
	* module.cc: define RTLD_GLOBAL to 0 if needed.
	* url.cc: set path=/ by default for http urls.
	* CopyJob.cc, pgetJob.cc: move string out of #define to help
	  xgettext to extract it (reported by Nicolas Noble).
	* FileAccess.cc: allow //path (required by posix and needed for
	  novell). Reported by Andreas Quitzow <<EMAIL>>.
	* ftpclass.cc, ftpclass.h: fix ssl support (tested on ftpd-tls).

2000-08-25  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: set *_ssl_connected after SSL_connect.

2000-08-24  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.h, Fish.h: fix last comma in enum.
	* ResMgr.h: make ResMgr::Resource public.

2000-08-23  Alexander V. Lukyanov  <<EMAIL>>

	* FileCopy.cc: fix Seek of FileCopyPeerFDStream. This fixes
	  problem with downloading from REST uncapable servers.

2000-08-21  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: if server returned address from private network
	  and its own address is not in private network, don't beleave it
	  and use original server address.

2000-08-20  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc, ftpclass.h: check data port for ftps-data; all_lines
	  new member; check all_lines for auto-sync-mode; fix ftps connection
	  borrowing.
	* resource.cc: make use-site-idle off by default; validate https:proxy.
	* HttpDir.cc: fix unterminated comment handling; handle bsd-like
	  ftp listings from apache proxy.

2000-08-13  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: (MergeCookies) null-terminate cookie properly.

2000-08-06  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc, ftpclass.h: support for ssl (AUTH TLS).
	* resource.cc: new settings ftp:ssl-allow, ssl-force, ssl-protect-data;
	  allow https for http:proxy; set https:proxy by https_proxy.
	* Http.cc: move Https::New to ifdef; fix cookie closure match.

2000-08-04  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc, Http.h: init https properly; rework cookie sending/receiving,
	  now cookies are joined from all matching closures, path is
	  also checked; use lftp_ssl_new; don't disconnect in some cases.
	* ResMgr.cc, ResMgr.h: (QueryNext) new method for cookies merging.
	* buffer.cc: use service lftp_ssl* functions.
	* ftpclass.cc, ftpclass.h: send SITE IDLE.
	* resource.cc: new setting ftp:use-site-idle.
	* ResMgr.cc: quote ;& properly.
	* MirrorJob.cc: increment error_count when cd fails.

2000-07-31  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc: add ] to list of wildcards.
	* FtpGlob.cc: fix coredump on empty directories.
	* complete.cc: fix filename quotation.
	* lftp_ssl.cc, lftp_ssl.h: new files.

2000-07-30  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc, FileAccess.h: res_prefix new member.
	* FindJob.cc, Fish.cc, Fish.h, FtpDirList.cc, LocalAccess.cc,
	  Resolver.cc: use new IOBuffer.
	* Http.cc, Http.h: implement https; use IOBuffer.
	* HttpDir.cc: skip comments in html; use IOBuffer.
	* buffer.cc, buffer.h: IOBuffer class; IOBufferSSL for ssl IO.
	* resource.cc: add https:proxy setting.

2000-07-29  Alexander V. Lukyanov  <<EMAIL>>

	* complete.cc: fix # quoting.

2000-07-28  Alexander V. Lukyanov  <<EMAIL>>

	* FileCopy.cc, FileCopy.h: don't create file in reget until data are
	  available.
	* buffer.cc, buffer.h: get_size new method; getsize_and_seek_end removed.

2000-07-27  Alexander V. Lukyanov  <<EMAIL>>

	* ResMgr.cc, ResMgr.h: NoClosure validator.
	* CmdExec.cc, FileCopy.cc, GetJob.cc, Resolver.cc, Speedometer.cc,
	  resource.cc: use NoClosure where needed.
	* commands.cc: add closure for cmd:default-protocol and
	  bmk:save-passwords.
	* resource.cc: add http:post-content-type; validate http:put-method.
	* Http.cc: use http:post-content-type.
	* FileSet.cc: (FileSet::Merge) fix long standing bug, which caused
	  rare coredumps on completion.

2000-07-26  Alexander V. Lukyanov  <<EMAIL>>

	* FileCopy.cc: apply non-absolute redirection to orig_url.
	* ResMgr.cc, ResMgr.h: regexp validator.
	* ftpclass.cc, resource.cc: ftp:auto-sync-mode setting.

2000-07-25  Alexander V. Lukyanov  <<EMAIL>>

	* MirrorJob.cc, MirrorJob.h: count errors; (ExitCode) implemented.
	* Resolver.cc: use getipnodebyname when available (for solaris8).
	* ftpclass.cc: handle "" in PWD reply; fix NextPeer selection.
	* NetAccess.cc: don't reset peer_curr on Resolve.
	* pgetJob.cc: fix eta.

2000-07-21  Alexander V. Lukyanov  <<EMAIL>>

	* CopyJob.cc: fix duplicate error messages.
	* commands.cc: help update: mirror -P, exit bg.

2000-07-20  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc, FileAccess.h: (GetVisualProto, SetVisualProto)
	  new methods; handle http proxy on creation of ftp session.
	* Http.cc: use ftp:proxy if session was created as ftp.
	* HttpDir.cc: check for ftp too.
	* commands.cc: create new session everytime.
	* resource.cc: check ftp:proxy for http://.

2000-07-19  Alexander V. Lukyanov  <<EMAIL>>

	* Job.cc, Job.h: support for several jobs being waited for
	  (preparation for mirror --parallel).
	* CmdExec.cc, CmdExec.h, CopyJob.cc, FindJob.cc, MirrorJob.cc,
	  SleepJob.cc, commands.cc, mgetJob.cc: according to Job.cc.
	* MirrorJob.cc, MirrorJob.h: --parallel option (finally!).
	* CatJob.cc, CatJob.h, commands.cc: auto ascii for cat; wait all
	  for new waiting scheme.
	* CopyJob.cc, CopyJob.h: support several CopyJob's in CopyJobEnv.
	* Job.cc, Job.h: (WaitForAllChildren) new method for `wait all'.

2000-07-18  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.h, FileAccess.cc: use FileSet in Glob.
	* CmdExec.cc, FileSet.h, FileSet.cc, FtpGlob.cc, FtpGlob.h,
	  FtpListInfo.cc, FtpListInfo.h, FtpSplitList.cc, HttpDir.cc,
	  HttpDir.h, LocalAccess.cc, mgetJob.cc: change for FileSet in Glob.
	* complete.cc: FileSet from Glob; append / for directories (finally!).

2000-07-17  Alexander V. Lukyanov  <<EMAIL>>

	* FileSet.cc: don't pass REG_NOSUB to regexec, this fixes
	  coredump on BSD. Reported by Andriy I Pilipenko <<EMAIL>>

2000-07-16  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc: invalidate dir listing cache on chmod.
	* Http.cc, Http.h: implement Suspend, Resume; resume and Roll
	  recv_buf on Close.
	* SMTask.h: (IsSuspended) new method.
	* buffer.cc: resume in_FA before Close.

2000-07-14  Alexander V. Lukyanov  <<EMAIL>>

	* NetAccess.cc: (SocketNumericAddress) use SocketAddrLen, this fixes
	  a problem with solaris8.
	* Http.cc: set real_pos=0 when no_ranges==true.
	* commands.cc: fix `open -u ... -p ... bookmark_name'.

2000-07-13  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc, ftpclass.h: (SendArrayInfoRequests) new method;
	  fix gcc-2.96 error; fix a coredump; add asserts.
	* LocalAccess.cc: fix rels.

2000-07-12  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: don't query for accept* if hftp.
	* pgetJob.cc: fix deadlock.

2000-07-11  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: fix error with gcc-2.96.

2000-07-10  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: fix potential memory leak.

2000-07-07  Alexander V. Lukyanov  <<EMAIL>>

	* lftp.cc, resource.cc: ask for password for ftp:proxy.
	* Http.cc: workaround for redirections without "\n\n";
	  check if all data arrived before disconnecting; set body_size=0
	  on "204 No Content"; handle "100 Continue".

2000-07-04  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc, FileAccess.h: (FileAccess::ClassInit) new function;
	  move calls of Ftp::ClassInit there.
	* lftp.cc: call FileAccess::ClassInit.
	* Filter.cc, Filter.h: add stderr_to_stdout flag.
	* FtpDirList.cc, FtpListInfo.cc, FtpSplitList.cc, HttpDir.cc:
	  fix printing for off_t.

2000-07-03  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: fix compilation problem on IRIX.

2000-06-29  Alexander V. Lukyanov  <<EMAIL>>

	* FileCopy.cc: don't Open NULL file, use "".

2000-06-26  Alexander V. Lukyanov  <<EMAIL>>

	* MirrorJob.cc: use lstat instead of stat on local file before mkdir.
	* Job.cc, Job.h: (FindAnyChild) new method.
	* CmdExec.cc, CmdExec.h, commands.cc: support for `wait all'.
	* ftpclass.cc: fix RETR reply handling (was broken in A00625 snapshot).
	* Http.cc: don't use |= on bool.

2000-06-25  Alexander V. Lukyanov  <<EMAIL>>

	* resource.cc: limit max-retries by default.
	* ftpclass.cc: fix 4xx handling in file access (broken by recent changes).

2000-06-23  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc: (StrError) optimize; add error text to "Login failed".
	* NetAccess.cc, NetAccess.h, resource.cc: net:persist-retries new setting.
	* ftpclass.cc, ftpclass.h: rework of reply handling; persist support.

2000-06-22  Alexander V. Lukyanov  <<EMAIL>>

	* FileCopy.cc, FileCopy.h: log redirections; fix init of redirections;
	  (FileCopyPeerFA::Init) new method.
	* Http.cc, resource.cc: new settings http:accept, http:accept-charset,
	  http:accept-language.

2000-06-21  Alexander V. Lukyanov  <<EMAIL>>

	* CmdExec.cc, CmdExec.h, commands.cc, lftp.cc: new setting
	  cmd:move-background; (Feeder::RealEOF) new method.
	* FileAccess.cc, FileAccess.h, Http.h, Http.cc, FileCopy.cc,
	  FileCopy.h: new setting xfer:max-redirections; redirection support;
	  in http code - check for 20X code before using size/date.

2000-06-18  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: use strncasecmp for special cookie checking; handle domain=.

2000-06-15  Alexander V. Lukyanov  <<EMAIL>>

	* FileCopyFtp.cc, Fish.cc, Fish.h, FtpDirList.cc, FtpListInfo.cc,
	  HttpDir.cc, NetAccess.cc, ftpclass.h: use off_t in many places.

2000-06-10  Alexander V. Lukyanov  <<EMAIL>>

	* GetJob.cc: new setting xfer:make-backup.

2000-06-09  Alexander V. Lukyanov  <<EMAIL>>

	* NetAccess.cc: use 2*rate as default limit-max.

2000-06-08  Alexander V. Lukyanov  <<EMAIL>>

	* CopyJob.cc, CopyJob.h, FileAccess.cc, FileAccess.h, FileCopy.cc,
	  FileCopy.h, FileSet.h, Filter.cc, Filter.h, Http.cc, Http.h,
	  LocalAccess.cc, Makefile.in, Speedometer.cc, Speedometer.h,
	  buffer.cc, buffer.h, ftpclass.cc, ftpclass.h, misc.cc, misc.h,
	  pgetJob.cc, pgetJob.h, plural.c, plural.h: large file size support.

2000-06-07  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: apply `;type=' to hftp urls too.
	* MirrorJob.cc: chmod files if needed before continuing download.
	* xmalloc.h: add alloca_strdup2 macro.
	* resource.cc: workaround IRIX CC and Unixware c++ bug by converting
	  array to plain variables. (debugged and workaround proposed by
	  Eugene Exarevsky <<EMAIL>>)

2000-06-01  Alexander V. Lukyanov  <<EMAIL>>

	* CmdExec.cc: better status line; quote more special symbols.
	* FileCopy.cc: fix positioning in FileCopyPeerFDStream.
	* FindJob.cc: change op in FinderJob_Cmd ctor.
	* Http.cc: don't pass user data as format, use %s.
	* Job.cc: fix kill all.
	* MirrorJob.cc: remove remote directories in reverse mode; fix chmod.

2000-05-25  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: fix quote-post; fix spurious 'Op not supp' messages and
	  looping.
	* HttpDir.cc: fix symlinks parsing in squid ftp listing.

2000-05-23  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: implement `quote set-cookie'; don't retry POST op if
	  some data were sent; implement `quote POST path data'.
	* Http.h: (post, post_data) new members.

2000-05-22  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc, Http.h, resourse.cc: add http:set-cookies var.
	* xmalloc.cc: (xstrdup) correct spare allocation when str==0.

2000-05-20  Alexander V. Lukyanov  <<EMAIL>>

	* resource.cc: remove `-' from default anon password.

2000-05-19  Alexander V. Lukyanov  <<EMAIL>>

	* Resolver.cc: use getaddrinfo only if gethostbyname2 is not available.
	* HttpDir.cc: (remove_tags) don't remove anchor tags; add debug.
	* Http.cc: fix few bugs concerned with keep-alive support.

2000-05-13  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: disconnect on 421/221 reply without sending QUIT.
	* Resolver.cc: use getaddrinfo if available. (original patch
	  from Brandon Hume)

2000-05-11  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: use vsnprintf only if available.
	* log.cc: move vsnprintf declaration to xstring.h.
	* ftpclass.cc, ftpclass.h, resource.cc: wait for QUIT reply before
	  closing the socket; ftp:use-quit new setting.

2000-05-04  Alexander V. Lukyanov  <<EMAIL>>

	* xmalloc.cc, xmalloc.h: add spare parameter to xstrdup.
	* HttpDir.cc: workaround some bugs in apache proxy.
	* GetJob.cc, bookmark.cc, history.cc: use new xstrdup parameter.
	* Http.cc, Http.h, resource.cc: add http:put-content-type setting;
	  fix Http::Send buffer allocation.
	* misc.cc, misc.h: optimize month_names.
	* CmdExec.cc: don't say Running when doing nothing.
	* FileAccess.cc, FileAccess.h: move retries here; don't reset try_time
	  on open; GetTryTime, SetTryTime, GetRetries, SetRetries new/moved here.
	* NetAccess.cc, NetAccess.h: ...from here.
	* FileCopy.cc, FileCopy.h: get/set try_time and retries on store retry.

2000-05-02  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: (Disconnect) don't set state before check.
	* CmdExec.cc, CmdExec.h: use TERM as closure for cmd:prompt.

2000-05-01  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: support http:put-method; don't use locale for sending
	  Last-Modified header line; add \n to Last-Modified line;
	  don't use keep-alive for PUT; fix coredump on ^C on PUT.
	* resource.cc: add http:put-method.
	* misc.cc, misc.h: export month_names.

2000-04-30  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc, Http.h: finish keep-alive support.
	* ftpclass.cc: return from GetBetterConnection if a connection breaks.

2000-04-29  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: add Cookie support; allow disabling user-agent.
	* resource.cc: new setting http:cookie.
	* CatJob.cc: simplify FileCopyPeerFA creation.
	* FileAccess.cc, FileAccess.h, url.cc, url.h, Http.cc,
	  FileCopy.cc, FileCopy.h: use original URL when sending HTTP request,
	  this allows requesting URLs containing e.g. '?'.

2000-04-26  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: don't disable ranges always, check condition before
	  disconnect.

2000-04-23  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: handle old servers version 0.9 correctly.
	* CmdExec.cc: actually commit the change for ^Z handling in `cd' command.

2000-04-20  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: query http:user-agent explicitly.
	* MirrorJob.cc: fix compilation problem.
	* CmdExec.cc: fix stall problem with glob.
	* DummyProto.h, complete.cc, pgetJob.h: fix warnings.

2000-04-19  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc, Http.cc: fix SetError(SEE_ERRNO) usage.
	* MirrorJob.cc: add -T option.
	* commands.cc: update mirror help.

2000-04-15  Alexander V. Lukyanov  <<EMAIL>>

	* FileSet.cc, FileSet.h: use class TimeInterval; add loose_prec arg.
	* MirrorJob.cc, MirrorJob.h: add --loose-time-prec; handle new mirror:
	  settings.
	* ResMgr.h: make some TimeInterval methods const.
	* ftpclass.cc: lower priority of messages for failed MDTM.
	* resource.cc: new settings mirror:time-prec and loose-time-prec.

2000-04-14  Alexander V. Lukyanov  <<EMAIL>>

	* Resolver.cc: workaround for `class' in nameser.h (freebsd3.2).
	* ftpclass.cc: (TransferCheck) fix wrong error handling in passive
	  STORE mode, it lead to infinite loops.

2000-04-12  Alexander V. Lukyanov  <<EMAIL>>

	* CmdExec.cc, CmdExec.h, lftp.cc: add cmd:interactive setting.
	* commands.cc: encode special characters in bookmark target.
	* GetPass.cc: (readline_from_file) treat ^C as empty line, not eof.

2000-04-09  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc: workaround gcc alloca misfeature.

2000-04-06  Alexander V. Lukyanov  <<EMAIL>>

	* MirrorJob.cc: better error messages.

2000-04-03  Alexander V. Lukyanov  <<EMAIL>>

	* FileCopy.cc: return m, not STALL.
	* Http.cc, Http.h, resource.cc: add http:user-agent setting.
	* MirrorJob.cc, MirrorJob.h: add --Remove-source-files option.
	* ResMgr.cc: fix small memory leak.
	* pgetJob.cc: resume CopyJob, maybe this fixes rare pget hang.

2000-03-29  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: disconnect even if control_sock==-1.
	* Filter.cc: add O_BINARY for windows.

2000-03-27  Alexander V. Lukyanov  <<EMAIL>>

	* MirrorJob.cc, MirrorJob.h: check --no-perms in reverse mirror;
	  take umask into account in reverse mirror (unless --no-umask
	  is specified).
	* Http.cc, Http.h: when there is "Accept-Ranges: none", don't send
	  Ranges: again; catch size of the file when Ranges: did not work;
	  workaround squid feature when server does not support Ranges:
	  but squid emulates it and thus lftp gets timeout.
	* FileCopy.cc, FileCopy.h: emulate eof when regetting a file which
	  is already complete and source does not support restarting.

2000-03-24  Alexander V. Lukyanov  <<EMAIL>>

	* Job.h: (SetParentFg) new method.
	* CmdExec.cc, CopyJob.cc, FindJob.cc, MirrorJob.cc, SleepJob.cc,
	  commands.cc, mgetJob.cc, pgetJob.cc: use it.
	* lftpget: -u is obsolete.
	* MirrorJob.cc: set use_cache in child MirrorJob.

2000-03-23  Alexander V. Lukyanov  <<EMAIL>>

	* Speedometer.cc: cast to int to avoid ambiguity on HP/UX.

2000-03-22  Alexander V. Lukyanov  <<EMAIL>>

	* CmdExec.cc, LsCache.cc, commands.cc: don't call macro alloca_strdup
	  on complex expressions.
	* FileCopy.cc: remove default value in function definition; count
	  rate when draining `put' in CONFIRM_STORE.
	* ResMgr.cc: include math.h for irix.
	* GetJob.cc: apply cwd in reverse mode.

2000-03-21  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc, ftpclass.h: allow \n in file names, substitute it
	  with \0; clean up command sending.

2000-03-20  Alexander V. Lukyanov  <<EMAIL>>

	* FileCopy.cc: add debug messages.
	* Http.cc: initialize bytes_received; add debug messages;
	  simulate eof if pos>=entity_size.
	* HttpDir.cc: fix bug in TR handling, introduced yesterday.
	* Http.cc: fix no-proxy handling, always use proxy for hftp.
	* ftpclass.cc: fix no-proxy handling.

2000-03-19  Alexander V. Lukyanov  <<EMAIL>>

	* HttpDir.cc: treat table row as a whole line; remove tags before
	  parsing. (this helps to parse boa_indexer indices, e.g.
	  http://download.sourceforge.net)
	* FtpDirList.cc, FtpListInfo.cc: recognize permissions in EPLF listing.
	* misc.cc, misc.h: (format_perms) new function.
	* ftpclass.cc: treat cat "" as raw LIST.
	* resource.cc: make passive mode the default.

2000-03-17  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc: debug code added.
	* FindJob.cc, FindJob.h: (quiet) new member; use it; set child jobs fg
	  if needed.
	* commands.cc: rm -f.
	* ftpclass.cc: don't delay session re-usage if connection limit is not
	  reached.

2000-03-16  Alexander V. Lukyanov  <<EMAIL>>

	* ResMgr.cc: call srandom if range is full.
	* ftpclass.cc: don't fail if port not available, but rather wait
	  and retry.
	* commands.cc: (command) handle args->count()==1.

2000-03-15  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc, ftpclass.h: disallow filenames with NL;
	  call ReuseAddress if could not get available port.
	* FileAccess.cc: (Rename) assign file1 before Open; (Open) don't Close
	  if not needed.
	* buffer.cc: ignore EINTR from write.
	* NetAccess.cc, NetAccess.h: (ReuseAddress) new method.

2000-03-14  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc, resource.cc: add ftp:web-mode.

2000-03-12  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: (DataAbort) disregard replies for commands sent
	  before ABOR.

2000-03-10  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: disallow double ABOR, disconnect instead.

2000-03-09  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: (GetBetterConnection) don't touch CONNECTING_STATE.
	* CmdExec.cc: fix prompt space allocation.
	* ResMgr.cc: declare random if needed.

2000-03-07  Alexander V. Lukyanov  <<EMAIL>>

	* ResMgr.cc, ResMgr.h: add Range class.
	* resource.cc: add ftp:port-range.
	* ftpclass.cc: use port-range to bind data socket.

2000-03-06  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc, FileAccess.h: make IsConnected int; simpler
	  IsBetterThan implementation.
	* Http.h, ftpclass.h: better IsConnected - assign to conneting state
	  connect level 1.

2000-03-05  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc: use NO_SIZE and NO_DATE.
	* ftpclass.cc: save size in entity_size and simulate eof on pos>=size;
	  send proper offset for REST.
	* NetAccess.cc, NetAccess.h: add takeover_time.
	* ftpclass.cc, ftpclass.h: don't use taken-over connection immediately
	  if priority<1; pass connection count to GetBetterConnection.

2000-03-02  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: don't take over a connection if it has NOREST_MODE;
	  check if connection_limit is reached before interrupting a transfer.
	* commands.cc: -E for get, put, mget, mput.

2000-03-01  Alexander V. Lukyanov  <<EMAIL>>

	* CmdExec.cc, CopyJob.h, FileAccess.cc, FileCopy.cc, FileCopy.h,
	  Job.cc, Job.h, NetAccess.cc, commands.cc, ftpclass.cc, resource.cc:
	  implement net:connection-limit and net:connection-takeover
	  settings; add Fg/Bg where needed.

2000-02-29  Alexander V. Lukyanov  <<EMAIL>>

	* CmdExec.cc: explain termination when cwd is not accessible.
	* FileAccess.cc, FileAccess.h: (priority) new member; (SetPriority);
	  (FirstSameSite, NextSameSite) new methods.
	* FileCopy.cc: (OpenSession) mark current task as moved.
	* Http.h: (IsConnected) implemented.
	* NetAccess.cc, NetAccess.h: (CountConnections) new method;
	  (connection_limit, connection_takeover) new members.
	* SMTask.cc, SMTask.h: add a dummy init_task so that current!=0.

2000-02-28  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc, ftpclass.h: remember last REST position and do `REST 0'
	  if last REST was >0 (for broken? ftp servers that don't reset REST).
	* MirrorJob.cc: (PrintStatus) improved.
	* pgetJob.cc: get size and offset after calling super::Do, because
	  the copier can be changed by it.

2000-02-27  Alexander V. Lukyanov  <<EMAIL>>

	* commands.cc: fix typo in mput help.

2000-02-24  Alexander V. Lukyanov  <<EMAIL>>

	* Job.cc, Job.h, CmdExec.cc: (Job::ListOneJob, Job::PrintJobTitle) new
	  methods; use them.

2000-02-23  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc, ftpclass.h: raise log level of CWD reply to 3.
	* MirrorJob.cc: log directory if verbose>=3; show directory in status.
	* misc.cc, misc.h: (squeeze_file_name) new function (from CopyJob).
	* CopyJob.cc: use it.

2000-02-22  Alexander V. Lukyanov  <<EMAIL>>

	* complete.cc: implement LOCAL_DIR mode; recognize -O;
	  use LOCAL_DIR for lcd.
	* FtpListInfo.cc: add OS2 list parser.

2000-02-20  Alexander V. Lukyanov  <<EMAIL>>

	* CmdExec.cc, CmdExec.h, commands.cc: (saved_session) new member;
	  save old session while new host name is verified and restore on error.
	* NetAccess.cc: nullify cwd too on bad host name error.
	* FileAccess.cc: (Rename) correct typo (which caused core dumps).

2000-02-19  Alexander V. Lukyanov  <<EMAIL>>

	* FileCopy.cc: count Buffered() change even when input buffer is empty.
	* Speedometer.cc: protect from rate<0.

2000-02-17  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: workaround server's y2k bug in MDTM.
	  (reported by Robert de Bath <<EMAIL>>)

2000-02-15  Alexander V. Lukyanov  <<EMAIL>>

	* CmdExec.cc: (SuspendJob) print a better status for CopyJobEnv.
	* Speedometer.cc: reduce initial rate period to 2 seconds.

2000-02-14  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: close data socket when switching to no-rest mode.
	* HttpDir.cc: strip quotes from refresh URL.

2000-02-12  Alexander V. Lukyanov  <<EMAIL>>

	* CmdExec.cc, GetPass.cc, Job.cc, LsCache.cc, MirrorJob.cc, ResMgr.cc,
	  alias.cc, commands.cc, complete.cc, lftp.cc, netrc.cc, parsecmd.cc,
	  pgetJob.cc:
	  call xfree everywhere.
	* Filter.cc: use xfree and xgetcwd.
	* Resolver.cc: don't dup2 to fd 1.
	* SMTask.cc: add task_count and debug.
	* SleepJob.cc: use TimeoutS.
	* ftpclass.cc, ftpclass.h: add FreeResult method.
	* misc.cc: (xgetcwd) call xmalloc_register_block.
	* xmalloc.cc, xmalloc.h: count memory blocks;
	  add xmalloc_register_block.
	* NetAccess.cc: workaround gcc bug, this fixes mem leak.
	* buffer.cc: clean up gotos.
	* lftp.cc, lftp_rl.c, lftp_rl.h: readline result must be freed;
	  register it with xmalloc_register_block.

2000-02-12  Alexander V. Lukyanov  <<EMAIL>>

	* FileCopy.cc: fix memory leaks.
	* LsCache.cc: use xfree.
	* commands.cc: help for repeat.
	* xmalloc.cc, xmalloc.h: outline xstrdup; MEM_DEBUG it.
	* ArgV.cc, ArgV.h: fix memory leak. Allocate memory in chunks.
	* FileAccess.cc, FileAccess.h, Filter.cc, Filter.h, LocalAccess.cc:
	  don't include ArgV.h in header.
	* LsCache.h, LsCache.cc: don't dup memory in Find method.
	* FtpDirList.cc, FtpSplitList.cc, HttpDir.cc: use new LsCache::Find.
	* url.h: start implementing class url.

2000-02-11  Alexander V. Lukyanov  <<EMAIL>>

	* CopyJob.cc: print rate using Speedometer::GetStr.

2000-02-09  Alexander V. Lukyanov  <<EMAIL>>

	* CopyJob.cc, CopyJob.h: don't use putchar; print summary when Done().
	* pgetJob.cc: don't use putchar.
	* mgetJob.cc: don't print empty line.

2000-02-08  Alexander V. Lukyanov  <<EMAIL>>

	* FileCopy.cc: don't show rate when put==0.
	* Makefile.am: remove ftpget.o.
	* SMTask.cc, SMTask.h: (current) new static member; empty all block's
	  before calling all Do's.
	* NetAccess.cc: (DelayingMessage) use current->Timeout.
	* PollVec.h: add GetTimeout for debugging.
	* Speedometer.cc: use current->Timeout.
	* StatusLine.cc: use TimeoutS.

2000-02-06  Alexander V. Lukyanov  <<EMAIL>>

	* CmdExec.cc: (PrintStatus) special case for CopyJobEnv in queue mode.

2000-02-04  Alexander V. Lukyanov  <<EMAIL>>

	* ProcWait.cc: if waitpid fails, check pid existence.

2000-02-02  Alexander V. Lukyanov  <<EMAIL>>

	* commands.cc: add -O option to help.
	* Http.cc: declare strptime if needed.
	* ftpclass.cc: handle "port theft" message in fxp mode.
	* FileCopyFtp.cc: check for reversing passive-source fixed; add
	  messages.
	* FileCopy.cc: add missing \n.

2000-02-01  Alexander V. Lukyanov  <<EMAIL>>

	* CopyJob.h: add GetETAStrSFromTime.
	* FileCopy.cc, FileCopy.h: split off Speedometer;
	  add GetETAStrSFromTime; pass reource name to Speedometer.
	* pgetJob.cc: call cp->GetETAStrSFromTime.
	* commands.cc: set old path before cd, so that after `open url'
	  `cd -' works; add --use-cache to mirror help.
	* Speedometer.cc, Speedometer.h: new files; accept resource name
	  instead of period.
	* ProcWait.cc: don't change state to ERROR at all (because process
	  can still run after waitpid fails)
	* FtpListInfo.cc: more verbose status.
	* FtpSplitList.cc, FtpSplitList.h: measure rate.
	* MirrorJob.cc: show only li->Status(); add use_cache member;

2000-01-31  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.h, FileAccess.cc: add NO_PASSWORD option for GetFileURL.
	* history.cc: use NO_PASSWORD; encode paths in cwd_history.
	* NetAccess.cc, NetAccess.h: (ReconnectInterval, DelayingMessage) new
	  methods.
	* ftpclass.cc, Http.cc: use DelayingMessage.
	* CmdExec.cc: show job number for queue's current job.
	* complete.cc: improve completion for queue command.
	* commands.cc: improve help for queue.

2000-01-30  Alexander V. Lukyanov  <<EMAIL>>

	* Resolver.cc: flush buffer in case of error.
	* ProcWait.cc: don't spin on error or done.
	* SMTask.cc: add debug code.
	* ftpclass.cc: add assert; remove obsolete code.
	* ProcWait.cc: handle waitpid errors better.

2000-01-29  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: check for "timed out" in reply; check if data were
	  sent or received before 5xx reply; don't spin on errors (not a bug).
	* PollVec.cc, PollVec.h: simplify.
	* CharReader.cc, FileCopy.cc, FileFeeder.cc, LocalAccess.cc,
	  ProcWait.cc, Resolver.cc, StatusLine.cc, SysCmdJob.cc, buffer.cc,
	  lftp.cc: don't use block directly.
	* GetJob.cc: check if output file is normal before renaming it.
	* Http.cc, Http.h: remove redundant CheckTimeout;
	  don't use block directly.
	* NetAccess.cc, NetAccess.h: bool CheckTimeout; don't use block
	  directly.
	* SMTask.cc, SMTask.h: use simplified PollVec.

2000-01-28  Alexander V. Lukyanov  <<EMAIL>>

	* url.cc: workaround for dos paths.
	* ftpclass.cc: unixware does not have IN6_ARE_ADDR_EQUAL - workaround.

2000-01-27  Alexander V. Lukyanov  <<EMAIL>>

	* CharReader.h, CmdExec.cc, CopyJob.cc, FileAccess.cc, FileAccess.h,
	  FileCopy.cc, FileCopy.h, Filter.cc, FindJob.cc, FtpDirList.cc,
	  FtpGlob.cc, FtpListInfo.cc, Http.cc, HttpDir.cc, Job.cc, Job.h,
	  LocalAccess.cc, LsCache.cc, LsCache.h, MirrorJob.cc, NetAccess.cc,
	  NetAccess.h, PollVec.h, ProcWait.cc, ProcWait.h, Resolver.cc,
	  Resolver.h, SMTask.cc, SMTask.h, SleepJob.cc, StatusLine.h,
	  buffer.h, commands.cc, ftpclass.cc, ftpclass.h, log.h, mgetJob.cc,
	  pgetJob.cc: (SMTask::Delete, SMTask::Roll) new methods; use them
	  everywhere; prohibit direct SMTask destruction by making destructors
	  protected; (SMTask::deleting) new member; don't show rate if it is
	  not valid; don't call SMTask::Do directly; (NetAccess::Close) new
	  method.
	* ftpclass.cc: turn on sync mode when 331 precedes 220. (for vftpd)

2000-01-26  Alexander V. Lukyanov  <<EMAIL>>

	* md5.c: check for HAVE_ENDIAN_H
	* commands.cc: options for glob; add help for glob and find.
	* ftpclass.cc: compilation fix for solaris251.

2000-01-25  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: runtime test for TIOCOUTQ.

2000-01-22  Alexander V. Lukyanov  <<EMAIL>>

	* ChmodJob.cc, TreatFileJob.cc, TreatFileJob.h, mkdirJob.cc,
	  mkdirJob.h, rmJob.cc: recognize urls.
	* CmdExec.cc: cmd:default-protocol new setting.
	* DummyProto.cc, DummyProto.h: add DummyNoProto class.
	* FileAccess.cc: use ParsedURL::Combine; get password from .netrc
	  if not present; create DummyNoProto if protocol is not supported;
	  (GlobURL) find url prefix and use it; make StrError virtual.
	* FileAccess.h: see above.
	* FileCopy.cc: now there is no need to handle unsupported protocol.
	* FileCopyFtp.cc, FileCopyFtp.h: save/restore try_time.
	* FileSet.cc: compare DATE_UNPREC too.
	* LocalAccess.cc, LocalAccess.h: ignore AnonymousLogin and Login;
	  improve globbing.
	* MirrorJob.cc: revert the change from 1998-10-13 - don't ignore date.
	* NetAccess.h: add GetTryTime.
	* Resolver.cc, Resolver.h: add dns:use-fork setting; pass data over
	  a Buffer.
	* commands.cc: add cmd:default-protocol; change protocol in open
	  if current one is file; use .netrc not only for ftp; add option -O
	  for get, put, mget, mput; use output_file_name.
	* complete.cc: in local completion check for URLs too.
	* ftpclass.cc: disallow empty home; (ReplyLogPriority) new method;
	  handle 221 reply as 421.
	* ftpclass.h: set try_time in SetCopyMode.
	* mgetJob.cc, mgetJob.h: (make_directory) new method; use output_file_name;
	  support for -O.
	* misc.cc, misc.h: (url_file) new function; (output_file_name) new
	  function.
	* url.cc, url.h: (url::path_index) new function;
	  (ParsedURL::Combine) new method.

2000-01-19  Alexander V. Lukyanov  <<EMAIL>>

	* FtpListInfo.cc: check file names for slashes.
	* FileCopy.cc: make percent using ranges.

2000-01-17  Alexander V. Lukyanov  <<EMAIL>>

	* commands.cc: don't use netrc if protocol is specified.
	* ftpclass.cc: don't use s6_addr32, use s6_addr instead (for
	  freebsd-4.0).
	* CopyJob.cc: suppress status in Echo if output uses stdout.
	* FileCopy.cc, FileCopy.h: reconfig Speedometers.

2000-01-15  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.h: move NO_DATE, NO_SIZE here.
	* FileCopy.h: from here.
	* FileCopy.cc: (FileCopyPeerFA::GetStatus) don't return status if
	  session is not open.
	* FtpDirList.cc: parse EPLF listing and show it pretty.
	* FtpListInfo.cc: parse EPLF listing.
	* misc.h: move find_char here.
	* HttpDir.cc: from here; parse EPLF listings from squid.
	* ftpclass.cc: use NO_SIZE and NO_DATE macros.
	* md5.c: don't compile anything if HAVE_MD5.

2000-01-13  Alexander V. Lukyanov  <<EMAIL>>

	* FileCopy.cc: (OpenSession) set correct pos in PUT mode;
	  don't call SaveRollback in PUT mode; handle eof in fxp PUT.
	* SMTask.h: ensure STALL|MOVED==MOVED.
	* ftpclass.cc, ftpclass.h: make FlushSendQueue and ReceiveResp
	  return flag indicating progress; OR it with m in Ftp::Do;
	  fix condition of Disconnect in copy mode; fix SwitchToState
	  to not ignore severe errors in copy mode; outline New;
	  (SendCmd2) new method; use it everywhere; new settings use-stat,
	  stat-interval.
	* HttpDir.cc: fix spinning on & in file names when no &amp; etc match.
	* FileCopy.h: make bytes_count protected.
	* FileCopyFtp.cc: increment bytes_count.
	* Http.cc, Http.h, LocalAccess.cc, LocalAccess.h: outline (New).
	* commands.cc: optimize cmd_user; update description for user;
	  update copyright string for year 2000.
	* resource.cc: new settings ftp:use-stat, ftp:stat-interval.

2000-01-12  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc, ftpclass.h, FileCopyFtp.cc: fixes for FXP.
	* FileCopy.cc: don't set pos to FILE_END; don't try to figure out
	  position in fxp mode; don't try to fake eof in PUT mode;
	  (RateReset) new method.
	* FileCopy.h: (SetFXP) accept argument.
	* FileCopyFtp.cc, FileCopyFtp.h: allow switching passive_source;
	  revert to plain copy if both cases fail; call RateReset; allow
	  store when necessary.
	* ftpclass.cc, ftpclass.h: check PORT reply, switch copy_passive
	  if it fails; add support for delayed STOR in copy mode; don't
	  check data address in copy mode.

2000-01-11  Alexander V. Lukyanov  <<EMAIL>>

	* NetAccess.cc: cast off const for solaris 2.5.1.
	* Filter.cc, Filter.h: allow Input/OutputFilter from ArgV.
	* LocalAccess.cc, LocalAccess.h: accept current system cwd as default;
	  log errors; implemented LocalDirList.
	* commands.cc: don't append var_ls if empty.
	* FileCopy.cc: set pos in FileCopyPeerFA::Seek.

2000-01-10  Alexander V. Lukyanov  <<EMAIL>>

	* FileCopyFtp.cc: check use-fxp for both sessions.
	* Makefile.am: remove FtpCopy.{cc,h}.
	* commands.cc: get rid of ftpcopy.
	* NetAccess.cc, NetAccess.h, ftpclass.cc, Http.cc, resource.cc:
	  support no_proxy environment variable; net:no-proxy setting.

2000-01-09  Alexander V. Lukyanov  <<EMAIL>>

	* FileCopyFtp.cc, FileCopyFtp.h: new files
	  (FXP implementation over FileCopy class).
	* CatJob.cc, CopyJob.cc, GetJob.cc, commands.cc, pgetJob.cc:
	  use FileCopy::New; update year.
	* DummyProto.cc, DummyProto.h, FileAccess.cc, FileAccess.h, FindJob.cc,
	  FtpDirList.cc, Http.cc, MirrorJob.cc, NetAccess.h, Resolver.cc,
	  SMTask.h, bookmark.cc, history.cc, lftp.cc, misc.cc, misc.h,
	  netrc.cc: update year.
	* FileCopy.cc, FileCopy.h: support for fxp; (New) new method;
	  (RateAdd) new method; (..Peer::CannotSeek) new method; (GetSession)
	  new method; (SetFXP) new method.
	* Makefile.am: add FileCopyFtp.{cc,h}.
	* buffer.h: (SetPos) new method.
	* ftpclass.cc: register FXP creator.
	* ftpclass.h: add FXP support methods.
	* resource.cc: new fxp resources (ftp:use-fxp, ftp:fxp-passive-source).

2000-01-08  Alexander V. Lukyanov  <<EMAIL>>

	* NetAccess.h, NetAccess.cc, ftpclass.cc, Http.cc: (SocketConnect,
	  SocketAddrLen) new methods; use them. This fixes a problem on NetBSD.

2000-01-07  Alexander V. Lukyanov  <<EMAIL>>

	* FileCopy.cc, FileCopy.h: prepare for fxp; reuse session in New
	  if told so; don't request date many times.
	* ftpclass.cc, ftpclass.h: support STAT while doing fxp.
	* SMTask.h: (TimeoutS) new method.

2000-01-06  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc: include netinet/tcp.h conditionally.
	* FindJob.cc: #if 0 dead code.
	* Http.cc: send Last-Modified in STORE mode.
	* Resolver.cc: include arpa/nameser.h and resolv.h conditionally;
	  conditionally compile functions depending on HAVE_RES_SEARCH.
	* bookmark.cc: use / if HOME is not set.
	* history.cc: same.
	* lftp.cc: don't create ./.lftp if HOME is not set.
	* netrc.cc: fail if HOME is not set.

2000-01-05  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc, FileAccess.h: (pass_open) new member;
	  (InsecurePassword) new method; (GetFileURL) include password if
	  pass_open is true; (OpenMode) new method.
	* FileCopy.cc: don't check session pos if it is not properly opened;
	  use OpenSession in Put_LL.
	* commands.cc: `pwd -p' to show password; call InsecurePassword
	  when password is entered on command line; forbid recursive bookmarks;
	  use ascii mode for ls.
	* ftpclass.cc: don't hide password if pass_open.
	* FtpDirList.cc: don't strip CRs.
	* Http.cc: better handle PUT errors.

2000-01-04  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc, FileAccess.h: delete args in DirList; outline virtual
	  functions; remove CopyOptions.
	* DummyProto.cc, DummyProto.h: implement DummyDirList.
	* FileCopy.cc, FileCopy.h: handle errors in DirList.
	* ftpclass.h: remove CopyOptions.

2000-01-03  Alexander V. Lukyanov  <<EMAIL>>

	* misc.cc, misc.h: (percent) new function.
	* pgetJob.cc: use it.
	* FileCopy.cc: use it - and thus avoid integer overflow.
	  (reported by Slama YA).

2000-01-02  Alexander V. Lukyanov  <<EMAIL>>

	* FileCopy.cc: check if line_buffer!=0 (by Slama YA)

2000-01-01  Alexander V. Lukyanov  <<EMAIL>>

	* CopyJob.h: add GetETA(long remainder) method.
	* Job.cc, Job.h: (ListOneJobRecursively) new method; list waiting
	  job before others.
	* LocalAccess.cc: expand tilde (finally).
	* pgetJob.cc, pgetJob.h: re-implement PrintStatus method.
	* FileCopy.cc: init need_seek.
	* commands.cc: add queue help.

1999-12-30  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc: fix SetPasswordGlobal.

1999-12-28  Alexander V. Lukyanov  <<EMAIL>>

	* FileCopy.cc, FileCopy.h: implemet LineBuffered; handle EPIPE.
	* commands.cc, CatJob.cc: use LineBuffered.
	* CopyJob.cc: implement SqueezeName.

1999-12-28  Alexander V. Lukyanov  <<EMAIL>>

	* CopyJob.h: put back FDStream for stdout creation.
	* FileCopy.h: no cache does not mean `don't save to cache'.
	* commands.cc: redo quote and site commands.
	* ftpclass.cc: advance pos in QUOTE_CMD mode; append file name
	  to error message if it is not mentioned already.
	* Makefile.am: remove QuoteJob.{cc,h}, XferJob.{cc,h}
	  (XferJob is dead - long live FileCopy)
	* parsecmd.cc: handle ? specially.
	* FileAccess.cc, FileAccess.h: (SetPasswordGlobal) new method;
	  make NoGlob to produce empty list if pattern contains wildcards.
	* commands.cc: use it.
	* complete.cc: few improvements.

1999-12-27  Alexander V. Lukyanov  <<EMAIL>>

	* CopyJob.cc, CopyJob.h: add op member for CopyJob.
	* FileCopy.cc: add ls caching support; (FileCopyPeerDirList) new class.
	* Makefile.am: remove LsJob.cc, LsJob.h, ftpget.
	* MirrorJob.cc: supply op for CopyJob.
	* buffer.cc, buffer.h: (SaveRollback) new method; define GET_BUFSIZE.
	* commands.cc: new ls, nlist; supply op for CopyJob.
	* pgetJob.cc: supply op for CopyJob.

1999-12-26  Alexander V. Lukyanov  <<EMAIL>>

	* CmdExec.cc, CmdExec.h: use GlobURL.
	* FileAccess.cc: (GetFileURL) handle file starting with '~';
	  (GlobURL::GetResult) implemented.
	* FileAccess.h: few methods for GlobURL added.
	* FtpGlob.cc: handle updir errors; handle dir starting with '~'.
	* GetJob.cc, GetJob.h: (NoProtoSrc, NoProtoDst) new methods;
	  handle reverse transfers (put).
	* LocalAccess.cc, LocalAccess.h: preserve home by ignoring Connect();
	  support ascii transfers.
	* Makefile.am: remove mputJob.cc, mputJob.h, PutJob.cc, PutJob.h,
	  FileXfer.cc, FileXfer.h.
	* MirrorJob.cc: don't include PutJob.h.
	* commands.cc: don't include PutJob.h, mputJob.h; move put, mput, mget
	  functionality to cmd_get; update help; use GlobURL;
	  support `login URL'.
	* mgetJob.cc, mgetJob.h: support mput.
	* url.h: (is_url) new method.
	* complete.cc: use GlobURL.
	* FileCopy.cc: don't open session if seek_pos>=size.
	* pgetJob.cc: correct accounting.

1999-12-26  Alexander V. Lukyanov  <<EMAIL>>

	* commands.cc: help update; support get -a for ascii mode;
	  don't use ascii mode for zcat and zmore.
	* CopyJob.cc, CopyJob.h: ascii support in CopyJobEnv; add SetDate and
	  SetSize methods.
	* FileCopy.cc, FileCopy.h: init can_seek0; add FileCopyPeerFA::NewGet,
	  NewPut methods.
	* MirrorJob.cc: use CopyJob.
	* commands.cc: use NewPut.
	* url.cc: allocate enough memory for file: urls.

1999-12-25  Alexander V. Lukyanov  <<EMAIL>>

	* CatJob.cc, ChangeLog, CopyJob.cc, CopyJob.h, FileAccess.cc,
	  FileAccess.h, FileCopy.cc, FileCopy.h, Filter.cc, Filter.h,
	  GetJob.cc, GetJob.h, MirrorJob.cc, commands.cc, mgetJob.cc,
	  mgetJob.h, pgetJob.cc, pgetJob.h: lots of changes - get, mget, pget
	  now work with new FileCopyEnv class.

1999-12-24  Alexander V. Lukyanov  <<EMAIL>>

	* FileCopy.cc: support ascii conversion.
	* commands.cc: ignore -u for compatibility.
	* CmdExec.cc: beware of stale pointers, use job number instead.

1999-12-22  Alexander V. Lukyanov  <<EMAIL>>

	* commands.cc, CatJob.cc, CatJob.h: add ascii mode.
	* HttpDir.cc: handle ~ as absolute path in check for icon.

1999-12-22  Robert de Bath <<EMAIL>>

	* FileAccess.cc, FileAccess.h, Http.cc, LocalAccess.cc, NetAccess.cc,
	  ftpclass.cc: rehash debug levels.

1999-12-22  Alexander V. Lukyanov  <<EMAIL>>

	* Job.cc: don't show job info as ? if there is no info.
	* CatJob.cc, CatJob.h: use new architecture w/ CopyJobEnv.
	* CopyJob.cc, CopyJob.h: (CopyJobEnv) new class; misc tweaks.
	* FileCopy.cc, FileCopy.h: add features for cat command.
	* commands.cc: new cat command creation.

1999-12-21  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: (DataAbort) Disconnect in copy mode if ABOR is disabled.

1999-12-21  Robert de Bath <<EMAIL>>

	* NetAccess.cc: correction for ReconnectAllowed

1999-12-20  Alexander V. Lukyanov  <<EMAIL>>

	* CopyJob.cc, CopyJob.h: add methods for creating different copiers:
	  NewGet, NewPut, NewEcho.
	* commands.cc: use CopyJob::NewEcho.
	* FileCopy.cc, FileCopy.h: new methods in Speedometer: GetStr, GetStrS,
	  GetETAStr, GetETAStrS; use them in FileCopy; add bytes_count,
	  start_time, end_time for accounting; fix FileCopyPeerString; add
	  Done to FileCopyPeerFDStream to wait for stream->Done.
	* NetAccess.cc: little fix in ReconnectAllowed.

1999-12-19  Alexander V. Lukyanov  <<EMAIL>>

        * HttpDir.cc: decode &amp; and similar; don't accept ~ for ListInfo.
	* FileCopy.h, FileCopy.cc, CopyJob.cc: (GetFgData) implemented;
	  fill CopyJob::fg_data; handle NeedSizeDateBeforehand.
	* FileAccess.h, Http.h: add NeedSizeDateBeforehand.

1999-12-18  Alexander V. Lukyanov  <<EMAIL>>

	* commands.cc: get1 command.
	* XferJob.cc: move xfer:eta-terse to CopyJob.
	* FtpCopy.cc, FtpCopy.h: save retries count.
	* CopyJob.cc, CopyJob.h: first version.
	* FileCopy.cc, FileCopy.h: finished.
	* NetAccess.cc: correct calculations in extreme cases
	  (reported by Robert de Bath)
	* FileAccess.cc, FileAccess.h: expand tilde in file and file1 too.
	  (ExpandTildeStatic) new method; New from ParsedURL; propagate
	  passwords.
	* ftpclass.cc: handle size==0 in Write; expand tilde for ARRAY_INFO;
	  correct test for ftp:rest-stor.
	* Http.cc: handle size==0 in Write; send port to proxy;
	  encode hostname; in error message append / conditionally.
	* url.h: add more URL_*_UNSAFE.

1999-12-14  Alexander V. Lukyanov  <<EMAIL>>

	* Filter.h, Filter.cc: add can_seek method.
	* buffer.cc, buffer.h: add UnSkip method.

1999-12-13  Alexander V. Lukyanov  <<EMAIL>>

	* LocalAccess.h: remove error_code (it is defined in super class) -
	  this fixes error handling in file: urls.
	* LocalAccess.cc: set size and date to -1 in case of error;
	  fill opt_size and opt_date.
	* FileAccess.h: IOReady checks IsOpen.
	* ftpclass.cc: IOReady checks IsOpen.
	* buffer.cc, buffer.h: add pos member; de-virtualize Skip.
	* Filter.cc: don't close -1; correct default file permissions.

1999-12-11  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: check use-abor before sending ABOR.
	* LsCache.cc: use SMTask::now.
	* misc.cc: include time.h

1999-12-10  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc, Http.cc, url.cc: fix ftp url handling (%2F for root)
	  (reported by Robert de Bath <<EMAIL>>)
	* url.h: no need to specify \033 in URL_UNSAFE; define URL_PATH_UNSAFE.
	* url.cc: avoid // for ftp urls.
	* FileAccess.cc: use URL_PATH_UNSAFE; specially hanle ~/ for ftp urls.
	* FtpListInfo.cc: move parse_perms and parse_month...
	* misc.cc, misc.h: ...here; implement parse_year_or_time, guess_year.
	* Http.cc: better handling for ftp urls; ascii support.
	* HttpDir.cc: handle apache proxy ftp listings.
	* resource.cc: add ftp:use-abor.

1999-12-09  Alexander V. Lukyanov  <<EMAIL>>

	* GetJob.cc: don't close session too early when deleting.
	* buffer.cc, buffer.h: add SetError method, use it.

1999-12-06  Alexander V. Lukyanov  <<EMAIL>>

	* check if GetBetterConnection(0) succeeded. (bug report from
	  Robert de Bath <<EMAIL>>).

1999-12-04  Alexander V. Lukyanov  <<EMAIL>>

	* resource.cc: new resources reconnect-interval-multiplier/max;
	  reconnect-interval renamed to reconnect-interval-base.
	* ResMgr.cc, ResMgr.h: (FloatValidate) new method.
	* NetAccess.cc, NetAccess.h: (ReconnectAllowed) (NextTry) new methods;
	  implement reconnect-interval-multiplier/max.
	* Http.cc: use ReconnectAllowed and NextTry.
	* commands.cc: disallow spaces in bookmark names.
	* ftpclass.cc, ftpclass.h: (IOReady) implemented; check for space
	  in last line of multiline response; use is_ascii_digit;
	  use ReconnectAllowed and NextTry.
	* FileAccess.h: (IOReady) new method.

1999-11-26  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.h, FileAccess.cc, complete.cc: add Glob::NoMatchPeriod
	  method; use it for completion.

1999-11-22  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc: try_time=0 is the same as DontSleep().
	* Http.cc: support ConnectVerify; remove some unneeded statements.
	* NetAccess.cc, NetAccess.h: move ConnectVerify here; remove
	  relookup-always setting.
	* resource.cc, ftpclass.cc, ftpclass.h: remove relookup-always.

1999-11-19  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: move aborted_data_sock along with control_sock.
	* FtpGlob.cc: make sure to restore old cwd.

1999-11-16  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: don't decrease sync_wait on 1xx replies.

1999-11-15  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: (ExtractPWD) don't return alloca'ed space, strdup it.

1999-11-14  Alexander V. Lukyanov  <<EMAIL>>

	* CmdExec.cc, CmdExec.h: (SuspendJob) new method; (PrintStatus)
	  don't print all queued commands if not verbose, print current
	  queue command status if ^Z (v==0).

1999-11-11  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc, ftpclass.h: add site_chmod_supported flag.

1999-11-09  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: properly set info.size and info.time if mdtm or size
	  are not supported.
	* FtpListInfo.cc: fix infinite loop on bad symlinks.

1999-11-07  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc, ftpclass.h: allow moving a connection with buffered
	  data or expected responses; (sync_wait) new member for number of
	  commands in flight.

1999-11-04  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc, FileAccess.h: (RereadManual) new method to stop
	  the driver wasting data when REST is not supported;
	  (norest_manual) new member; (device_prefix_len) new protected method;
	  use it in Chdir.
	* FtpGlob.cc: implement files_only.
	* FtpSplitList.cc, HttpDir.cc: use RereadManual.
	* Http.cc: support norest_manual.
	* ascii_ctype.h: add to_ascii_lower.
	* buffer.cc, buffer.h: (Empty) new method.
	* complete.cc: use DirectoriesOnly for `cd' command.
	* ftpclass.cc, ftpclass.h: better support for vms; remember that
	  MDTM or SIZE is not supported; implement ftp:rest-stor setting;
	  add closure to ftp:rest-list setting.
	* resource.cc: ftp:rest-stor setting.

1999-11-02  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc, FileAccess.h, FtpGlob.cc, FtpGlob.h, FtpListInfo.cc,
	  FtpListInfo.h, FtpSplitList.cc, FtpSplitList.h, Makefile.am:
	  redesign FtpGlob class to use FtpListInfo class; FtpListInfo now
	  uses more specialized FtpSplitList instead of FtpGlob;
	  (Glob::dirs_only) new member, implemented simple type filter.

1999-10-22  Alexander V. Lukyanov  <<EMAIL>>

	* Http.h, Http.cc: (use_head) new member; hftp:use-head new setting;
	  use GET instead of HEAD if !use_head.
	* resource.cc: (hftp:use-head) new setting.

1999-10-21  Alexander V. Lukyanov  <<EMAIL>>

	* HttpDir.cc: compare ftp:// with hftp:// properly.

1999-10-20  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc: (AnonymousLogin) reset cwd and home.
	* Http.h, Http.cc: add HFtp::Login; don't send password in URI.
	* commands.cc: do Login after Connect, not reverse.

1999-10-19  Alexander V. Lukyanov  <<EMAIL>>

	* HttpDir.cc: set hour/minute to 0 if they are not available.
	* Http.cc: avoid memory leak in encode_string; add support for
	  non-anonymous ftp over http.
	* url.cc: (url::encode_string) don't malloc buffer;
	  quote all if quote==0.

1999-10-19  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: don't send data in STORE mode until header is sent.
	  (reported by Brian Ristuccia)

1999-10-13  Alexander V. Lukyanov  <<EMAIL>>

	* HttpDir.cc: don't skip line begin early; parse Mini-Proxy listings.
	* Filter.cc: try getfd() before utime(), it might create the file.
	  (this is a workaround for getting empty files, a full solution
	  would be to block on close of output file.)
	* complete.cc: (glob_quote) remove extra quoting. This fixes completion
	  for files with spaces in name.

1999-10-13  Alexander V. Lukyanov  <<EMAIL>>

	* ResMgr.cc: pass canonic resource name to Reconfig.

1999-10-12  Alexander V. Lukyanov  <<EMAIL>>

	* commands.cc, CmdExec.cc: call Reconfig after session change;
	  (do_cd) exit_code=0 if check is not needed.

1999-10-11  Alexander V. Lukyanov  <<EMAIL>>

	* HttpDir.cc: check file info for validity.
	* HttpDir.cc: don't dump core on eol==0.

1999-10-10  Alexander V. Lukyanov  <<EMAIL>>

	* GetJob.cc: don't open local file early if !cont.

1999-10-09  Alexander V. Lukyanov  <<EMAIL>>

	* misc.cc: call mkdir(...,0777) to let user choose umask.
	* ftpclass.cc: prepare for better handling of unsupported SIZE/MDTM.

1999-10-08  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc, FileAccess.h, ftpclass.cc: low level ascii-mode
	  support.

1999-10-07  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: don't free peer, it is freed in parent class, and so
	  don't free proxy* (the later was harmless since proxy was set to 0).
	* Http.cc: use ClearPeer.
	* CmdExec.cc: actually assign c.
	* HttpDir.cc: better handle symlinks.

1999-10-05  Alexander V. Lukyanov  <<EMAIL>>

	* HttpDir.cc: handle Netscape-proxy listing; handle parse errors
	  better.
	* lftp.cc: use SOCKS4 define.
	* CmdExec.cc: use URL closure on several settings.

1999-10-04  Alexander V. Lukyanov  <<EMAIL>>

	* ArgV.cc: (delarg) decrement ind if needed.
	* HttpDir.h, HttpDir.cc: (LsOptions) new class, use it, implement
	  -F ls option, accept -alC but ignore them for now.

1999-10-02  Alexander V. Lukyanov  <<EMAIL>>

	* CmdExec.cc: fix type mismatch on 64-bit machines.
	* Makefile.am: don't depend on rlstdc.h

1999-10-01  Alexander V. Lukyanov  <<EMAIL>>

	* Resolver.cc: cast ResValue to (const char *) to avoid ambiguity.

1999-09-30  Alexander V. Lukyanov  <<EMAIL>>

	* commands.cc: remove _() from "%s: %s\n".

1999-09-30  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc, FileAccess.h, ftpclass.cc, ftpclass.h, commands.cc:
	  (CleanupAll, CleanupThis) new methods; use it in cmd_close.

1999-09-30  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc, log.h: extra info in debug output when it goes
	  to a file. (suggested by Caleb Epstein <<EMAIL>>)

1999-09-30  Albert Chin-A-Young <<EMAIL>>

	* module.c: fix // comment

1999-09-29  Alexander V. Lukyanov  <<EMAIL>>

	* ResMgr.h: include time.h for time_t.

1999-09-27  Alexander V. Lukyanov  <<EMAIL>>

	* HttpDir.cc: strip leading slash in links to /~user.

1999-09-27  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: create rate_limit for STORE mode.
	* lftp.cc: close fd 0; call setsid.

1999-09-20  Alexander V. Lukyanov  <<EMAIL>>

	* MirrorJob.cc, MirrorJob.h: use TimeInterval.
	* misc.cc, misc.h: drop decode_delay.
	* ResMgr.cc: (TimeInterval ctor) handle one more error case.

1999-09-20  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: define _XOPEN_SOURCE 500 for linux.

1999-09-19  Alexander V. Lukyanov  <<EMAIL>>

	* MirrorJob.cc: don't print messages when lftp is not foreground
	  process.
	* ResMgr.h: add TimeInterval(time_t) constructor.
	* SleepJob.cc, SleepJob.h: use TimeInterval; support `sleep forever'.
	* commands.cc, LsCache.cc, LsCache.h: use TimeInterval.

1999-09-19  Alexander V. Lukyanov  <<EMAIL>>

	* ResMgr.cc, ResMgr.h: new resource type TimeInterval.
	* Resolver.cc, Resolver.h: settings cache-{enable,expire,size}.
	* commands.cc: (set) print more meaningful message for invalid values.

1999-09-18  Alexander V. Lukyanov  <<EMAIL>>

	* SleepJob.cc, SleepJob.h: add repeat functionality;
	  implement PrintStatus.
	* commands.cc: add repeat command.
	* module.c: another module alias.
	* XferJob.cc: remove unneeded poll call.
	* ftpclass.cc: remove unneeded error checks.

1999-09-15  Alexander V. Lukyanov  <<EMAIL>>

	* Job.cc: (SessionJob::PrintStatus) check if session!=0.

1999-09-14  Alexander V. Lukyanov  <<EMAIL>>

	* resource.cc: check if ipv6 is supported at runtime.
	* FileAccess.cc: better message for system errors, correctly adjust
	  str_allocated.
	* NetAccess.cc: (NextPeer) fix off-by-one error; set try_time.
	* Http.cc, ftpclass.cc: move reconnect delay before Resolve, to avoid
	  double lookup in case of relookup_always; handle error of socket
	  creation better, skip address and report error verbosely.

1999-09-14  Alexander V. Lukyanov  <<EMAIL>>

	* PutJob.cc: unsuspend session; few corrections in size handling;
	  don't try to use size of pipe.

1999-09-12  Alexander V. Lukyanov  <<EMAIL>>

	* NetAccess.cc: when adding first RateLimit, reset global pool.

1999-09-12  Alexander V. Lukyanov  <<EMAIL>>

	* NetAccess.cc, NetAccess.h: implement RateLimit class; use it.
	* ftpclass.cc, Http.cc: use RateLimit.
	* resource.cc: new resources net:limit-total-rate
	  and net:limit-total-max.
	* ftpget.cc: create Log::global.

1999-09-10  Alexander V. Lukyanov  <<EMAIL>>

	* Resolver.cc: recognize host names in the form proto,hostname and
	  lookup address in specified protocol family, e.g. inet6,hostname.com.
	  (Note: this won't work over proxies)
	* GetJob.cc: add xfer:clobber setting; don't overwrite files if it is
	  false.

1999-09-08  Alexander V. Lukyanov  <<EMAIL>>

	* NetAccess.cc, NetAccess.h: drop first_lookup.
	* MirrorJob.cc, MirrorJob.h: don't get just appeared files, check
	  that size/date has not changed before overwrite/append;
	  build local file set before remote one.
	* FileSet.cc, FileSet.h: (SubstractNotIn) new method; optimize
	  other Substract* methods; make FindByName const.

1999-08-31  Alexander V. Lukyanov  <<EMAIL>>

	* Resolver.cc: add missing \n
	* ftpclass.cc: fix previous change not to close data_sock.
	* Http.cc: call SayConnectingTo.
	* complete.cc, complete.h: move remote_completion here.
	* CmdExec.cc, CmdExec.h, lftp.cc, log.cc, log.h: make debug output
	  clear/redraw the prompt.

1999-08-26  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.cc, FileAccess.h, Http.cc, Http.h, LocalAccess.cc,
	  NetAccess.cc, ftpclass.cc, ftpclass.h: split off NetAccess class;
	  use Resolver cache; add Resolve method; move rate limiter to
	  NetAccess.
	* Resolver.cc, Resolver.h: implement cache.
	* LsCache.cc, LsCache.h: allow adding from Buffer class.
	* HttpDir.cc: use new LsCache::Add method varianth; handle symlinks.
	* commands.cc: remove debug level restriction.
	* buffer.cc, buffer.h: make GetSaved const.
	* Makefile.am: add NetAccess.
	* NetAccess.cc, NetAccess.h: new files.
	* SMTask.cc, SMTask.h, ResMgr.cc, CmdExec.cc, CmdExec.h:
	  Reconfig now takes arg.

1999-08-26  Alexander V. Lukyanov  <<EMAIL>>

	* complete.cc: improve completion of file names with wildcards.

1999-08-25  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: handle 225 and 226 replies in TransferCheck.
	* Http.cc: use cwd in error message if file=="".

1999-08-24  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: reset try_time on certain failures: greeting, user,
	  pass, to sleep proper time before reconnect.

1999-08-21  Alexander V. Lukyanov  <<EMAIL>>

	* HttpDir.cc: improve support for squid's ftp listings;
	  support symlinks.
	* Makefile.am: cmd-sleep.so target; use -nostdlib to reduce size,
	  this works unless there are static constructors.
	* SleepJob.cc, commands.cc: move at and sleep commands to SleepJob.cc
	  to allow modularity.
	* modconfig.h: add MODULE_CMD_SLEEP.

1999-08-20  Alexander V. Lukyanov  <<EMAIL>>

	* module.c: provide module aliases.
	* FileAccess.cc: revert last change.

1999-08-20  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc, Http.h, HttpDir.cc: implement hftp protocol (ftp over http);
	  parse squid's ftp listings.
	* FileAccess.cc: load proto-http when hftp is requested.
	* resource.cc: hftp resources added; set ftp:proxy if environment
	  variable ftp_proxy points to ftp proxy.

1999-08-18  Alexander V. Lukyanov  <<EMAIL>>

	* buffer.h, buffer.cc: allow saving data; (Save, GetSaved) new methods.
	* HttpDir.cc, HttpDir.h, FtpDirList.cc, FtpDirList.h: save data in
	  Buffer ubuf, members (upos, from_cache) become obsolete.

1999-08-18  Jim Pick <<EMAIL>>

	* LsCache.h, FtpDirList.cc, FtpDirList.h: don't try to cache
	  anything if cache is off; don't cache too large objects (by lav)

1999-08-18  Alexander V. Lukyanov  <<EMAIL>>

	* complete.cc: local completion after redirection.

1999-08-17  Alexander V. Lukyanov  <<EMAIL>>

	* Job.cc, Job.h: provide useful default ShowRunStatus method.
	  (e.g. helps `at' command)

1999-08-16  Alexander V. Lukyanov  <<EMAIL>>

	* ArgV.cc, ArgV.h: make several methods const.
	* CmdExec.cc, CmdExec.h, commands.cc: chain all CmdExec's; allow
	  several queues to exist; (FindQueue) new method; (FeedArgV) new
	  method; synchronize cwd's of queue and main CmdExec.

1999-08-16  Alexander V. Lukyanov  <<EMAIL>>

	* commands.cc: (cmd_queue) print usage; set exit_code.

1999-08-12  Alexander V. Lukyanov  <<EMAIL>>

	* CmdExec.h, CmdExec.cc, commands.cc: implement simple command queue;
	  (CmdExec::queue) new static member; (queue) new command.

1999-08-12  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: paranoia check.

1999-08-11  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: try to catch "Too many open files" server error;
	  check for the substring in file name too; remove old code for
	  switching to sync mode.

1999-08-11  Alexander V. Lukyanov  <<EMAIL>>

	* lftpget: options -c -v -d.
	* Http.cc: change level of Connecting message.

1999-08-10  Alexander V. Lukyanov  <<EMAIL>>

	* lftpget: turn off final rate printing.
	* MirrorJob.cc: don't reset time on unchanged files - it can be bad
	  if time-prec>0 and files have really changed but have the same size.
	  (reported by Oscar Y Takeshita)

1999-08-09  Alexander V. Lukyanov  <<EMAIL>>

	* HttpDir.cc: (HttpDirList::Do) use proper prefix for ls -f.

1999-08-08  Alexander V. Lukyanov  <<EMAIL>>

	* HttpDir.cc: only try to get info for href links.

1999-08-06  Alexander V. Lukyanov  <<EMAIL>>

	* commands.cc: (cmd_get) decode %xx in default output file name.
	* XferJob.h: disable non_strict_urls (too complex).

1999-08-06  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc, ftpclass.h, resource.cc: add ftp:rest-list setting.
	* Job.h, Job.cc: more useful Job::AcceptSig.
	* CmdExec.cc: improve AcceptSig.
	* SleepJob.cc: assign jobno and cmdline to sub-CmdExec.

1999-08-05  Alexander V. Lukyanov  <<EMAIL>>

	* modconfig.h: enable proto-ftp module for real.

1999-08-05  Alexander V. Lukyanov  <<EMAIL>>

	* ArgV.h, CmdExec.cc: (ArgV::CombineQuoted) new method.
	* commands.cc: fix `at' command to quote the command to execute.
	* lftp.cc: use CombineQuoted.

1999-08-05  Alexander V. Lukyanov  <<EMAIL>>

	* FtpListInfo.cc: fix NT listing parser for the case of file name
	  with spaces.

1999-08-04  Alexander V. Lukyanov  <<EMAIL>>

	* CmdExec.cc, FindJob.cc, FtpCopy.cc, LsJob.cc, MirrorJob.cc,
	  StatusLine.cc, StatusLine.h, TreatFileJob.cc, XferJob.cc,
	  log.cc, mkdirJob.cc, mvJob.cc: (StatusLine::Clear) new method;
	  use it everywhere, this avoids warning in new gcc.
	  Don't call Clear when Done(), it will be cleared anyway by upper
	  level job (it was race anyway).

1999-08-04  Alexander V. Lukyanov  <<EMAIL>>

	* StatusLine.h, StatusLine.cc, XferJob.cc, pgetJob.cc:
	  (StatusLine::GetWidthDelayed) new method; use it to reduce number
	  of ioctl calls.

1999-08-04  Alexander V. Lukyanov  <<EMAIL>>

	* LsJob.cc: (NoCache) open with proper mode.
	* HttpDir.cc: handle another listing format.

1999-08-02  Alexander V. Lukyanov  <<EMAIL>>

	* Resolver.cc, log.cc: declare res_search, vsnprintf if prototypes
	  are missing.

1999-07-29  Alexander V. Lukyanov  <<EMAIL>>

	* SignalHook.cc: ignore SIGXFSZ, want to get EFBIG.

1999-07-24  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: send `Accept: */*', it is needed for some http servers.

1999-07-20  Alexander V. Lukyanov  <<EMAIL>>

	* Resolver.cc: make it compile on sunos551.
	* FindJob.cc, FindJob.h: fix core dump on output stall;
	  add PrintStatus method.
	* lftpget: set verbose; unset at-exit; print usage.

1999-07-19  Alexander V. Lukyanov  <<EMAIL>>

	* lftpget: new file.
	* Makefile.am: install and distribute lftpget.

1999-07-16  Alexander V. Lukyanov  <<EMAIL>>

	* LsJob.cc: fixed bug caused by cut-n-paste. This fixes spinning
	  when output stalls.
	* Resolver.cc: include netinet/in.h

1999-07-15  Alexander V. Lukyanov  <<EMAIL>>

	* HttpDir.cc: fix handling of HREF=/

1999-07-15  Alexander V. Lukyanov  <<EMAIL>>

	* commands.cc: (glob) rewind args before calling original command.
	  (fixes coredump for mrm without args)

1999-07-12  Alexander V. Lukyanov  <<EMAIL>>

	* Resolver.cc: don't search for SRV RR if address is numeric.

1999-07-11  Alexander V. Lukyanov  <<EMAIL>>

	* lftp_rl.c: improve lftp_rl_clear.

1999-07-10  Alexander V. Lukyanov  <<EMAIL>>

	* CmdExec.h: (CmdFeeder::clear) new method; (CmdExec::feeder_called)
	  new member.
	* CmdExec.cc: set feeder_called; call feeder->clear in top_vfprintf.
	* MirrorJob.cc: (va_Report) print even in background.
	* complete.cc: redisplay before waiting for key.
	* lftp.cc: (ReadlineFeeder::clear) implemented.
	* lftp_rl.c, lftp_rl.h: (lftp_rl_clear, lftp_rl_redisplay_maybe) new
	  functions.

1999-07-10  Alexander V. Lukyanov  <<EMAIL>>

	* complete.cc: add nlist to list of commands taking remote file as
	  an argument.

1999-07-10  Alexander V. Lukyanov  <<EMAIL>>

	* Job.cc, Job.h: (GetConnectURL) virtual new method. (ListDoneJobs)
	  compare urls and print job url if different.

1999-07-10  Alexander V. Lukyanov  <<EMAIL>>

	* Job.cc, Job.h, CmdExec.cc, CmdExec.h: add top_vfprintf method;
	  modify vfprintf method to call top_vfprintf; in (class CmdExec)
	  redefine only top_vfprintf.

1999-07-06  Alexander V. Lukyanov  <<EMAIL>>

	* HttpDir.cc: support base href, refresh, take list of tags from wget;
	  show relative icon links in ls.
	* HttpDir.h: (class HttpDirList) add base_href member.
	* Http.cc: set home to / properly.

1999-07-05  Alexander V. Lukyanov  <<EMAIL>>

	* commands.cc: cd and lcd without arguments go to home.
	* Resolver.cc: (LookupOne) fix to actually use argument.

1999-07-04  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: use new Resolver constructor; fix relookup
	  in case of net:relookup-always=true to lookup only after
	  trying last address.
	* Resolver.cc: finish SRV support; don't close stderr.
	* XferJob.cc: don't include netdb.h

1999-07-04  Alexander V. Lukyanov  <<EMAIL>>

	* Resolver.cc, Resolver.h: about to support SRV dns records.
	* Http.cc: use new Resolver constructor.

1999-07-04  Darren Stuart Embry <<EMAIL>>

	* FileAccess.cc: (GetFileURL) prepend colon before port name.

1999-06-29  Artur Frysiak <<EMAIL>>

	* Http.cc: send User-Agnet header.

1999-06-26  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: organize a list of tag/link pairs; add AREA/HREF pair.

1999-06-26  Alexander V. Lukyanov  <<EMAIL>>

	* FileAccess.h, Http.h: (FA::UseCache) new method.
	* LsJob.cc, HttpDir.cc: use it in rels.
	* Http.cc: send Cache-Control too.

1999-06-25  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc, Http.h, resource.cc: implement http:cache (pragma:no-cache)

1999-06-25  Alexander V. Lukyanov  <<EMAIL>>

	* complete.cc: workaround for readline bug (reported by
	  <<EMAIL>>); make few functions static.
	* Makefile.am: add READLINE_DEPEND to lftp_DEPENDENCIES.

1999-06-24  Alexander V. Lukyanov  <<EMAIL>>

	* XferJob.cc: translate time units in one place; add optional verbose
	  eta reporting (from Ben Winslow <<EMAIL>>).

1999-06-23  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: fix never-ending-transfer problem with servers
	  not supporintg ranges.

1999-06-22  Alexander V. Lukyanov  <<EMAIL>>

	* HttpDir.cc, HttpDir.h: extract link target from <frame src=;
	  support `ls -f file.html' to extract links from file.

1999-06-19  Alexander V. Lukyanov  <<EMAIL>>

	* XferJob.cc: more precise eta reporting.
	* HttpDir.cc: prevent coredump on url.path==0.
	* MirrorJob.cc: add missing eprintf argument.

1999-06-18  Alexander V. Lukyanov  <<EMAIL>>

	* commands.cc: add sleep help

1999-06-15  Alexander V. Lukyanov  <<EMAIL>>

	* resource.cc: set http:proxy to getenv(http_proxy) initially.

1999-06-15  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc, ftpclass.h: remove prehistoric methods DataReady
	  and Block.

1999-06-08  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: fix for ftpcopy in passive mode.

1999-06-04  Alexander V. Lukyanov  <<EMAIL>>

	* commands.cc: prevent coredump when short_desc!=0 && long_desc==0.

1999-06-02  Alexander V. Lukyanov  <<EMAIL>>

	* module.c: handle case with RTLD_NOW undefined (SunOS4)
	* FtpListInfo.cc, HttpDir.cc: correct format string.

1999-06-01  Alexander V. Lukyanov  <<EMAIL>>

	* Makefile.am: remove module before its re-creation.
	* ascii_ctype.h: add is_ascii_xdigit.
	* Http.cc, Http.h: support for keep-alive getting info; support
	  for chunked transfer encoding.
	* url.cc: always encode control characters.

1999-05-31  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc, Http.h: hook up HttpListInfo
	* HttpDir.cc, HttpDir.h: HttpListInfo implemented
	* FtpListInfo.cc: fixed bug with setting precise file time.
	* FileAccess.cc: untranslate some messages which indicate bug in app.
	* complete.cc: completion for new commands; add cache completion;
	  reset SIGINT count so that interrupted completion does not clear
	  line; call Open from GetInfoArray, Rename to avoid code duplication.
	* PutJob.cc, PutJob.h: add entity_date member; set date after Open.
	* LocalAccess.cc: don't use Poll; set file time in StoreStatus.
	* CmdExec.h: extern bookmark_subcmd, cache_subcmd.
	* ftpclass.cc: don't save line for every 213 response - this fixes
	  quote stat .
	* CatJob.cc, CatJob.h: removed globbing support,
	  now glob cat can be used.
	* commands.cc: use string_alloca; add slash for new bookmarks.
	* FtpGlob.cc: fix workarounds to check original; move workaround
	  for "no /" bug here from complete.cc.

1999-05-26  Alexander V. Lukyanov  <<EMAIL>>

	* MirrorJob.cc, MirrorJob.h: do chmod on remote files.
	* commands.cc: (glob, chmod) new commands.
	* rmJob.cc, rmJob.h: use TreatFileJob as base class.
	* TreatFileJob.cc, TreatFileJob.h, ChmodJob.cc, ChmodJob.h: new files.
	* ArgV.cc, ArgV.h: (insarg) new method.
	* LocalAccess.cc: support chmod.
	* ftpclass.cc: support chmod; tune overloaded server recognition.
	* FileAccess.h, FileAccess.cc: CHANGE_MODE new open mode;
	  (Chmod) new method.
	* lftp.cc: quote cmd line before adding it to history.
	* CmdExec.cc, CmdExec.h: (BUILTIN_GLOB) new builtin;
	  (needs_quotation) new method.
	* FtpDirList.cc: (Status) implemented.
	* LsJob.cc: (ShowRunStatus) implemented.
	* HttpDir.cc: implement FtpDirList::Status; check ubuf==0 before usage.
	* Http.cc: handle chmod.
	* keyvalue.h: make Pair protected
	* ResMgr.cc, ResMgr.h: outline ResDecl constructor.
	* misc.cc: clean const usage; optimize.
	* SMTask.cc: use true/false for bool.

1999-05-22  Alexander V. Lukyanov  <<EMAIL>>

	* complete.cc: make better function pointer casts.
	* ResMgr.cc, ResMgr.h, keyvalue.cc, keyvalue.h: few fixes for other
	  compilers.
	* FileSet.cc, FileSet.h: initialize FileInfo::data; outline few funcs.
	* xmalloc.cc, xmalloc.h: outline xfree to simplify debugging.
	* misc.cc: (dir_file) handle file==0.
	* HttpDir.cc, HttpDir.h: remove prefix if present; workaround apache
	  bug.
	* history.cc: use NO_PATH.
	* FileAccess.cc, FileAccess.h: (GetFileURL) new method; rename NO_CWD
	  to NO_PATH.
	* url.cc, url.h: add proto_required option; don't assign const char *
	  to char *.

1999-05-20  Alexander V. Lukyanov  <<EMAIL>>

	* HttpDir.cc, HttpDir.h: new files.
	* lftp.cc: use const; readline wrappers.
	* complete.cc: include complete.h, lftp_rl.h
	* complete.h: renamed from lftp.h
	* lftp_rl.c, lftp_rl.h: new files.
	* commands.cc: use const; use FinderJob.
	* FindJob.cc, FindJob.h: rename class FindJob to FinderJob, to avoid
	  conflict with Job::FindJob method.
	* ArgV.cc, ArgV.h: don't call getopt, because its prototype is
	  uncertain.
	* GetJob.cc: remove unused variable
	* MirrorJob.cc: use const; satisfy non-gcc compilers in case(file->...)
	* GetPass.cc: don't include readline.
	* buffer.cc: close session in ~FileInputBuffer.
	* Http.cc, Http.h: fix LONG_LIST mode to restart transfer;
	  change SendRequest to accept file name; add Connect method;
	  add MakeDirList, MakeGlob methods.
	* CmdExec.h, CmdExec.cc: use const char * where needed; remove
	  CmdExec:: inside CmdExec definition.
	* ResMgr.cc, ResMgr.h: make ResMgr::Resource public; add
	  ResourceCompare method.
	* keyvalue.cc, keyvalue.h: make KeyValueDB::Pair public; add
	  KeyValueDB::KeyCompare method.
	* bookmark.cc, CatJob.cc, XferJob.cc, XferJob.h, pgetJob.cc,
	  pgetJob.h, FileFeeder.cc, FileFeeder.h, xmalloc.cc, CatJob.h,
	  history.cc, SysCmdJob.cc:
	  use const char * where needed.
	* netrc.cc: avoid gcc'ism.
	* Filter.h: kill a warning
	* FileSet.cc, FileSet.h: (Add) merge info if already added.
	  (SortByName) mew method; few consts; add associated data.
	* SignalHook.cc, SignalHook.h: declare signal_handler type.
	* LocalAccess.cc, StatusLine.cc: use alloca instead of variable size
	  array.
	* ftpclass.cc, ftpclass.h: fix sscanf to work with int's instead of
	  members of struct tm; add const to several pointers; declare
	  friend struct expected_response properly for other compilers.
	  Fix bug with wrong address family for data socket in rare cases.
	  Add more error messages; assertions.
	* LsCache.h: declare friend class ExpireHelper properly for other
	  compilers.
	* misc.cc, misc.h: add static for inline basename_ptr, use const as
	  main version.
	* FtpCopy.cc: no need for ctype.h
	* ascii_ctype.h: new file
	* ftpget.cc: use ArgV for LsJob
	* LsJob.cc, LsJob.h: use ArgV
	* FtpDirList.cc, FtpDirList.h: use ArgV
	* FileAccess.h: pass ArgV to MakeDirList; add const; kill a warning.

1999-05-19  Albert Chin-A-Young <<EMAIL>>

	* ftp-opie.c, module.c, getdate.y: include xalloca.h to allow
	  compilation by Sun C compiler.

1999-05-17  Alexander V. Lukyanov  <<EMAIL>>

	* XferJob.cc, XferJob.h: (CanShowRate) new function; optimize timeout.
	* FileAccess.cc: encode some unsafe chars in url
	* url.cc, url.h: take extra argument for unsafe chars.
	* mgetJob.cc, CatJob.cc, mrmJob.cc: use the changed Glob::Status.
	* FtpGlob.cc: change Status to include session status.
	* Resolver.cc: change timeout to fatal-timeout
	* FtpDirList.cc, FtpDirList.h: new files
	* LsCache.cc, LsCache.h: add const
	* LsJob.cc, LsJob.h: support for DirList.
	* Http.cc: unset hostname if resolution cannot be done.
	* ftpclass.cc, ftpclass.h: add FtpDirList support, unset hostname
	  if resolution cannot be done.
	* buffer.h, buffer.cc: allow for FileInputBuffer to read data from
	  a session; support suspend/resume.
	* SignalHook.h: fix for some compilers/systems
	* resource.cc: add net: variables; removed duplicates in ftp: and http:;
	  make ftp:anon-pass visible.
	* FileAccess.cc: check for socket errors every time;
	  query net:variable too.
	* FileAccess.h: (DirList::DirList) create the buffer.
	* ResMgr.h: (ResValue::is_nil) new method.

1999-04-30  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc, Http.h: complete support for put (untested).
	* ftpclass.cc: move DefaultAnonPass to resource.cc
	* commands.cc: add set -a, set -d support; last_bg is default for wait.
	* lftp.cc: use ResMgr::ClassInit
	* ftpget.cc: support SOCKS, use ResMgr::ClassInit
	* resource.cc: initialize default for ftp:anon-user;
	  add ResMgr::ClassInit.
	* pgetJob.cc: use total_xferred in CurrETA.
	* XferJob.cc, XferJob.h: add offset argument for CurrETA
	* CmdExec.cc: add last_bg member
	* FtpListInfo.cc: create empty FileSet to avoid NULL access; handle L
	  in file permissions.
	* MirrorJob.cc: run session after Chdir to avoid 'idle' status
	* FileAccess.h: add virtual class DirList
	* ResMgr.cc, ResMgr.h: support for printing with defaults/only defaults
	* ArgV.h: add getindex method

1999-04-22  Alexander V. Lukyanov  <<EMAIL>>

	* FindJob.cc: don't go uplevel if there are cd/list errors.
	* FtpListInfo.cc, FtpListInfo.h: change format-specific
	  parsers to process single line at a time; copy line
	  before processing.
	* PutJob.cc: remove poll on input file.
	* XferJob.cc, XferJob.h, pgetJob.cc: remove obsolete slow status
	  line update.
	* CmdExec.cc, CmdExec.h: remove obsolete ls-in-completion
	* StatusLine.cc, StatusLine.h: implement delayed update.
	* commands.cc: move res_save_passwords to top level.

1999-04-15  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: use INET6 instead of AF_INET6
	* MirrorJob.cc: check if LocalAccess has been created
	* CmdExec.cc: add missing \n at end of input file
	* complete.cc: add mv to list of remote commands
	* commands.cc: add lpwd command
	* mgetJob.cc: more verbose status
	* CatJob.cc: more verbose status; remove unused has_wildcards
	* mrmJob.cc: more verbose status
	* FileAccess.h: (ArrayInfoPercentDone) new method
	* FtpGlob.cc: more verbose status
	* FtpListInfo.cc: more verbose status

1999-03-29  Alexander V. Lukyanov  <<EMAIL>>

	* FtpGlob.cc: fix handling of long lists
	* FileAccess.cc, FileAccess.h: add Glob::add_force
	* Http.cc: handle cd errors, deal with documents with no length
	* CatJob.cc: (Do) fix stupid bug introduced by Glob change

1999-03-27  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc, Http.h: add Location handling, get whole header on errors,
	  misc improvements.
	* Resolver.cc, Resolver.h: use FileInputBuffer to be reliable.
	* mrmJob.cc, mgetJob.cc: use NoGlob as last resort.
	* CatJob.cc: do glob always (because FtpGlob is now smart)
	* complete.cc: support MakeGlob==0
	* FileAccess.h, FileAccess.cc: implemented Glob::HasWildcards,
	  Glob::UnquoteWildcards, new class NoGlob.
	* pgetJob.cc: correct status lines and rate calculations.
	* FtpGlob.cc, FtpGlob.h: fnmatch inside of add(), don't access ftp
	  server if there are no wildcards.
	* FtpListInfo.cc: list parsing restructured; added NT list parser
	* LocalAccess.cc: (Glob) do chdir to cwd before glob.

1999-03-17  Alexander V. Lukyanov  <<EMAIL>>

	* ftpget.cc: support modules
	* Http.cc, Http.h: many changes, still unfinished.
	* FtpGlob.cc, FtpGlob.h: new files
	* LocalAccess.cc, LocalAccess.h: implement LocalGlob; more verbose
	  error messages.
	* lftp.cc: don't call obsolete ListInfo::ClassInit
	* ftpclass.cc, ftpclass.h: always copy resolved address
	  to connected session along with socket; implement SameSiteAs,
	  MakeGlob.
	* LsCache.cc, LsCache.h: implemented partial cache flush.
	* FileAccess.cc, FileAccess.h: add class FileAccessOperation;
	  move ListInfo here; add class Glob; move class Protocol here;
	  call LsCache flushing routines on certain operations;
	  add SameSiteAs method (almost th same sa SameLocationAs,
	  but don't check cwd).
	* complete.cc: use Glob
	* XferJob.cc: use FileAccess::New, don't include obsolete headers
	* mputJob.cc, mputJob.cc: don't include rglob.h, localize
	* FtpListInfo.cc, FtpListInfo.h: use FtpGlob
	* CatJob.cc, CatJob.h, mgetJob.cc, mgetJob.h, mrmJob.cc, mrmJob.h:
	  use new class Glob.
	* MirrorJob.h, MirrorJob.cc: don't include ListInfo.h;
	  use FileAccess::New; explicitly use UseCache(false).
	* FindJob.h: don't include ListInfo.h
	* ListInfo.h, ListInfo.cc, ProtoList.cc, ProtoList.h: removed; contents
	  moved to FileAccess
	* pgetJob.cc: use only 40 chars for file name; print eta in jobs output
	* commands.cc: use FileAccess::New; add list address to version output;
	  add --help and --version to `help lftp' output.
	* SignalHook.cc, SignalHook.h: don't use static arrays; add ClassInit
	* FtpCopy.cc: use FileAccess::New

1999-02-23  Alexander V. Lukyanov  <<EMAIL>>

	* Http.cc: Basic auth, new resource usage
	* lftp.cc: modular proto-ftp support
	* LocalAccess.cc: use Protocol::Register
	* ftpclass.cc: module support, new resource usage
	* commands.cc: module command, --no-umask in help for mirror,
	  use single quotes in 'bookmark edit' shell command.
	* DummyProto.h: add Reconfig
	* ResMgr.cc, ResMgr.h: renamed Query to SimpleQuery, add complex
	  Query method.
	* Resolver.cc, Resolver.h: add ClassInit
	* resource.cc, resource.h: new files
	* FileAccess.cc, FileAccess.h: move traffic shaper here, add Query and
	  Reconfig methods; add closure member.
	* ProtoList.h: add static Register member
	* ListInfo.cc, ListInfo.h: add ClassInit
	* FtpCopy.cc: don't use StateToError
	* MirrorJob.cc, MirrorJob.h: implement --no-umask, apply umask
	  by default.
	* CmdExec.cc: use op instead of args->a0().

1999-02-04  Alexander V. Lukyanov  <<EMAIL>>

	* commands.cc, CmdExec.cc, CmdExec.h: module support
	* lftp.cc: module support
	* complete.cc: don't use cmd_table directly
	* misc.cc, misc.h: (decode_delay) moved here
	* MirrorJob.cc: don't create LocalListInfo directly, module support
	* LocalAccess.cc: module support
	* ProtoList.cc: module support
	* PutJob.cc: call SetTime and SetDate
	* module.cc, module.h, modconfig.h: new files
	* Http.cc, Http.h: new files
	* buffer.cc, buffer.h: (FileInputBuffer) new class; (Errno) new method;
	  (EventTime) new method.
	* FileAccess.cc, FileAccess.h: (default_cwd) new member; stuff from
	  ftpclass; (SetTime, SetDate) new methods.
	* ftpclass.h, ftpclass.cc: move traffic shaper and other common with
	  Http things to FileAccess.
	* SMTask.h: add Timeout and Block methods for convenience
	* SMTask.cc: don't run suspended tasks.

1999-01-23  Alexander V. Lukyanov  <<EMAIL>>

	* MirrorJob.cc: fix Report condition
	* ftpclass.cc: change format for EPSV response
	* Resolver.cc: set flags on pipe (O_NONBLOCK, FD_CLOEXEC); check
	  for non-fatal errors.

1999-01-21  Alexander V. Lukyanov  <<EMAIL>>

	* Resolver.cc, Resolver.h: use string portname
	* FileAccess.h, FileAccess.cc: use string portname; implement base
	  Connect.
	* FtpCopy.cc, commands.cc, ftpget.cc, XferJob.cc: use symbolic port
	  in Connect.
	* ftpclass.h, ftpclass.cc: use string portname and proxy_port;
	  print "connecting..." messages for ipv6; set peer_curr properly to
	  less than peer_num; parse EPSV response correctly - search for '('.
	* MirrorJob.cc: always report to non-tty
	* CmdExec.cc, CmdExec.h: add cmd:verbose, useful for scripts.
	* ftpclass.cc: set peer_curr properly <peer_num.

1999-01-19  Alexander V. Lukyanov  <<EMAIL>>

	* PollVec.cc: merge pollfd's with the same descriptor (workaround for
	  problems in some systems' poll)
	* ftpclass.cc: support for multiple addresses and ipv6; fixed bugs in
	  CloseRespQueue; don't check port in passive mode.
	* ftpclass.h: support for multiple addresses and ipv6
	* Resolver.cc, Resolver.h: support for multiple addresses, multiple
	  address families, dns:order variable.
	* url.cc: check for multiple colons (ipv6 numeric)

1999-01-08  Alexander V. Lukyanov  <<EMAIL>>

	* complete.cc: check for mirror -N
	* CmdExec.cc, CmdExec.h: add BUILTIN_NONE, reset builtin to it.
	* commands.cc: (do_open) (do_cd) don't verify host or cwd if background
	* ftpclass.cc, ftpclass.h: (FindLastCWD) new method;
	  remove target_cwd; (CloseRespQueue) handle CHECK_{USER,PASS}*
	  correctly, don't ignore them; fix waiting for cwd to
	  proper directory.

1998-12-22  Alexander V. Lukyanov  <<EMAIL>>

	* PutJob.cc: correct bytes count when position changes (negative
	  count is obviously incorrect)
	* plural.c: use absolute value
	* commands.cc: fix to exit if initial open fails (that from lftp call)
	* CmdExec.cc: cmd:fail-exit to exit if a command fails.

1998-12-19  Alexander V. Lukyanov  <<EMAIL>>

	* rglob.cc: (path_match) treat empty pattern specially
	* CmdExec.cc, CmdExec.h: (AtExit) new function, (cmd:at-exit) new
	  setting.
	* lftp.cc: call top_exec->AtExit() and wait before exit
	* commands.cc: (do_lftp) feed "||exit" only if have fed command;
	  (do_bookmark) don't add empty bookmark or bookmark with empty
	  name.

1998-12-17  Alexander V. Lukyanov  <<EMAIL>>

	* MirrorJob.cc: clear waiting properly

1998-12-04  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: use socklen_t instead of ADDRLEN_TYPE; correct Buffered.
	* FileAccess.cc: use socklen_t instead of ADDRLEN_TYPE.
	* XferJob.cc: (Offset) implemented, used; (CountBytes) use Buffered
	  and session_buffered to correct transfer rate.
	* XferJob.h: (Offset) new method, (session_buffered) new member
	* ftpclass.h, ftpclass.cc: (Buffered) new method
	* FileAccess.h: (Buffered) new virtual method

1998-12-03  Alexander V. Lukyanov  <<EMAIL>>

	* XferJob.cc: add i18n for K/s etc.
	* commands.cc: (open) create new session if current protocol is dummy
	* FileAccess.cc: (GetConnectURL) return empty url for dummy protocol
	* lftp.cc: (main) use DummyProto instead of Ftp.
	* DummyProto.cc, DummyProto.h: (new files) dummy protocol, will help
	  in future to support dynamic loadable protocols.
	* xmalloc.h: don't rely on memcpy return value
	* LocalAccess.cc: when follow_links don't remove dangling links

1998-11-27  Alexander V. Lukyanov  <<EMAIL>>

	* lftp.cc: remove extra \n
	* ftpclass.cc: (Reconfig) call ResetBytes to reset accumulated count
	* SMTask.cc: call UpdateNow in ReconfigAll

1998-11-25  Alexander V. Lukyanov  <<EMAIL>>

	* XferJob.cc: add i18n for eta
	* complete.cc, rglob.cc, rglob.h, Job.h, LsCache.h, XferJob.cc,
	  CmdExec.cc, mgetJob.cc, rmJob.h, rmJob.cc, QuoteJob.h, QuoteJob.cc,
	  LsJob.h, mvJob.cc, PutJob.cc, MirrorJob.cc, CatJob.cc, GetJob.cc,
	  LsJob.cc, mkdirJob.cc, mrmJob.cc, pgetJob.cc, ftpget.cc: remove
	  dependency on ftpclass.h, use FileAccess.h instead; use FA.
	* rmJob.cc: support i18n; use FA
	* FileAccess.h: add FA shortcut to FileAccess
	* log.cc: cast void* to char*
	* ResMgr.cc, netrc.cc, lftp.cc: use const char *, avoid error with
	  new egcs
	* ftpclass.cc, ftpclass.h: use enum for various response checks;
	  clear resp queue on Close; associate path with CWD response handle.
	* commands.cc: use &CmdExec::do_... to compile with recent egcs
	* CmdExec.cc: set exit_code to zero when cd or open is stopped.

1998-11-22  Alexander V. Lukyanov  <<EMAIL>>

	* CmdExec.h: add ~CmdFeeder destructor, this fixes problem with
	  feeders not destroyed, open fd's in particular.

1998-11-20  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc, ftpclass.h: don't try to handle anon users specially,
	  it did not work anyway; one has to use anon-user and anon-pass
	  settings.
	* netrc.cc: ignore default; handle comments
	* SMTask.cc: include sys/time.h
	* commands.cc: add `command' command; replace args->a0() with op in
	  several places.
	* CmdExec.cc, CmdExec.h: (BUILTIN_EXEC_RESTART) new builtin.
	* ArgV.cc, ArgV.h: (delarg) new method

1998-11-18  Alexander V. Lukyanov  <<EMAIL>>

	* XferJob.cc: add start_time_ms, end_time_ms; use now and now_ms.
	* LocalAccess.cc: check Poll return value for -1
	* commands.cc: updated help for rm, mirror.
	* ftpclass.cc, ftpclass.h: ftp:limit-rate, ftp:limit-max new settings;
	  traffic limit implemented (BytesAllowed, BytesUsed, BytesReset);
	  allow passing data peer address to data_address_ok for verification
	  of PASV response; scan PASV response for address (as recommended in
	  RFC1123; thanks to Michael Handler <<EMAIL>> for patch);
	  verify data peer address in passive mode; check return value of Poll
	  for -1; (AbortedClose, ControlClose) new functions; rearrange code
	  in Read; clear lookup_done in SetProxy only if proxy changed.
	* lftp.cc: log SIGTERM when in background.
	* Resolver.cc: use static now.
	* SMTask.cc, SMTask.h: add static members: now, now_ms; (UpdateNow)
	  new static method; call UpdateNow from Schedule.
	* FileAccess.cc: don't call Disconnect from CheckHangup; return -1
	  from Poll if hangup.

1998-11-12  Alexander V. Lukyanov  <<EMAIL>>

	* MirrorJob.cc: set time only on directories; get sets it on files;
	  don't print report messages if there is parent_mirror, this
	  fixes multiple messages in nested directories after fg.
	* FileSet.cc, FileSet.h: add option to set time for only directories.
	* ftpclass.cc, ftpclass.h: add aborted_data_sock member; don't
	  close data connection right after sending ABOR, wait for answer;
	  send only first byte before ABOR as OOB to workaround recv()
	  braindamage; clear lookup_done in Reconfig since proxy could change.
	* CmdExec.cc: set verify_path, verify_host; handle ^Z in open/cd
	* commands.cc: use verify_path, verify_host
	* CmdExec.h: (verify_path, verify_host) new members

1998-11-05  Alexander V. Lukyanov  <<EMAIL>>

	* FtpListInfo.cc, QuoteJob.cc, netrc.cc, Resolver.cc, misc.cc,
	  parsecmd.cc, rglob.cc, url.cc, StatusLine.cc, ftpget.cc, buffer.h,
	  xmalloc.h, lftp.cc: use xstring.h instead of string.h to improve
	  portability.
	* Job.cc: (Fg, Bg) protect against infinite recursion

1998-10-31  Alexander V. Lukyanov  <<EMAIL>>

	* log.cc: (Format) implemented, added sl_cleared flag.
	* ftpget.cc: enable log when verbose
	* ftpclass.cc: add state check in ACCEPTING_STATE, fixes rare coredump.
	  socket-maxseg experimental setting for TCP_MAXSEG. Print errors from
	  setsockopt.
	* PollVec.cc: add assert(fd>=0).

1998-10-27  Alexander V. Lukyanov  <<EMAIL>>

	* commands.cc: (do_mirror) fix include/exclude setting, add -L option
	* FtpListInfo.cc: fix follow_symlinks handling, clear MODE, SIZE, etc.
	* ftpclass.cc: check for 4xx response for REST
	* ftpclass.h: comment fix
	* FtpCopy.cc: fix setting dst_size (was -2, need 0)

1998-10-23  Alexander V. Lukyanov  <<EMAIL>>

	* FtpListInfo.cc: fix exclude/include, implement follow_symlinks
	* commands.cc: (do_lftp) call do_open only if no lftp-specific options
	  were found; (do_mirror) add --dereference (-L) option;
	  (do_at) use get_date function
	* log.cc: fix logging after tty disconnect, clear tty flag
	* LocalAccess.cc: implement follow_symlinks semantics
	* ListInfo.cc, ListInfo.h: add follow_symlinks flag
	* MirrorJob.cc, MirrorJob.h: add RETR_SYMLINKS flag
	* getdate.y, getdate.h: new files (replace parsetime*)

1998-10-13  Alexander V. Lukyanov  <<EMAIL>>

	* complete.cc: fix remote completion (automatic class was deleted too
	  early); fix typo.
	* commands.cc: (do_mirror) don't use ONLY_NEWER for reverse mirror.
	* FileSet.cc, FileSet.h: (SameAs) add ignore argument,
	  (SubtractSame) same.
	* MirrorJob.cc: ignore date in reverse mode. (HandleFile) skip: label;
	  report errors in mkdir
	* ftpclass.cc: check only first digit in response.
	* parsetime.y: use mktime_from_utc for portability

1998-10-07  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: prevent re-entering to Disconnect, this fixes stack
	  overflow. (FlushSendQueue) new method. close control_sock if connect
	  failed. check for try_time==0. Change default for verify-address,
	  seems quite a bit of servers fail the check.

1998-10-03  Alexander V. Lukyanov  <<EMAIL>>

	* complete.cc: bookmark completion. cleanup.
	* CmdExec.cc: make FeedQuoted insert quotes, (GetFullCommandName) new
	  method.
	* keyvalue.h: (CurrentKey) (CurrentValue) (Next) (Rewind) new methods,
	  (current) new member
	* bookmark.h, bookmark.cc: (Rewind) new method, comment unused List()
	* history.h: (Rewind) new method
	* PutJob.cc, PutJob.h: (delete_files) new member, use it.
	* FindJob.cc: quote arguments and use --
	* mputJob.cc: recognize -e
	* FtpCopy.cc: include url in status
	* commands.cc: remove unused unquote(), fix do_lftp to call do_open.
	* FileAccess.cc: encode url parts
	* url.h, url.cc: (url) new class, (url::decode_sctring)
	  (url::encode_string) new functions, use the functions.
	* ftpclass.cc: typo corrected
	* GetJob.cc, GetJob.h: (RemoveBackupFile) new function
	* pgetJob.cc: use RemoveBackupFile

1998-09-15  Alexander V. Lukyanov  <<EMAIL>>

	* FindJob.cc, FindJob.h: add FindJob_Cmd class;
	  (class FindJob) use_cache, init_dir - new members, NextDir - new
	  method; show status in WAIT state, don't show it in CD.
	* FileSet.cc, FileSet.h: outline next() and curr().
	* ArgV.cc, ArgV.h: make getcurr return 0 after getnext returns 0
	* commands.cc: add rm -r support
	* misc.cc, misc.h: (xgetcwd) new function
	* CmdExec.cc, CmdExec.h: don't execute commands if chdir failed;
	  (FeedQuoted, unquote) new methods
	* ftpclass.cc: disable timeout for ftpcopy, remove NoWait.
	* SleepJob.h, SleepJob.cc: save and restore cwd, set parent of exec
	* FindJob.cc: include config.h

1998-09-08  Alexander V. Lukyanov  <<EMAIL>>

	* CmdExec.cc: (MakePrompt) add \@ handling
	* commands.cc: limit debug level to 0-9 range
	* keyvalue.cc: (Format) add \0 for empty case

1998-09-06  Alexander V. Lukyanov  <<EMAIL>>

	* rglob.cc: don't ignore NO_FILE
	* ftpclass.cc, ftpclass.h: remove unused ChdirStatus,
	  ignore NO_FILE for NLST and simulate eof; don't set state
	  in SwitchToState if we have failed smth (or else infinite loop
	  is possible).
	* FileAccess.cc, FileAccess.h: add 'verify' to Chdir
	* FindJob.cc, FindJob.h: support depth-first search, don't verify cd's

1998-09-05  Alexander V. Lukyanov  <<EMAIL>>

	* ListInfo.cc, ListInfo.h: add 'need' and 'use_cache' members
	* XferJob.cc, LsCache.cc: use #l# for longs in plural()
	* lftp.cc: use ProcWait::Signal
	* buffer.cc, buffer.h: new files
	* FindJob.cc, FindJob.h: new files
	* SignalHook.cc, SignalHook.h: add Block, Unblock, Default methods
	* commands.cc: use decode_delay in sleep, add CMD(find)
	* MirrorJob.cc: request ALL_INFO
	* FileSet.h: add ALL_INFO
	* misc.cc: support dir_file(dir_file(),)
	* FtpListInfo.cc: handle 'need' and 'use_cache'. If there were no
	  errors in long list, don't get short one. count errors in
	  ParseLongList_UNIX.
	* FileAccess.h: add GetFile method, include ListInfo.h
	* Resolver.h: include string.h
	* CmdExec.h: add CMD(find)
	* ProcWait.h, ProcWait.cc: add Signal method
	* Filter.h, Filter.cc: add closed member to avoid spawning a process
	  when done and waiting for termination; block SIGCHLD while spawning
	  process to prevent races.
	* SysCmdJob.cc: same for SIGCHLD
	* FtpCopy.cc: fix rolling stick :)
	* FileSet.h: add ALL_INFO
	* mgetJob.cc: don't use cache for rglob

1998-08-31  Alexander V. Lukyanov  <<EMAIL>>

	* ftpclass.cc: fix connection leak in GetBetterConnection (missing
	  return); add conditions to DataAbort to avoid unneeded ABOR;
	  close data connection in Read on EOF to avoid ABOR;
	  SwitchToState - remove bogus condition for COPY_FAILED

1998-08-28  Alexander V. Lukyanov  <<EMAIL>>

	* FileXfer.cc: fix setting op when args->count()==1
	* ArgV.h: add GetV method
	* SignalHook.cc, SignalHook.h: move to posix sigaction; add old_saved[]
	* ProcWait.cc, ProcWait.h: add SIGCHLD_handler, chain, destructor
	* log.cc, log.h: new files (log facility)
	* lftp.cc: use SIGCHLD_handler, use Log
	* SleepJob.cc, SleepJob.h: new files (sleep and at commands)
	* parsetime.y, parsetime-lex.l, parsetime-i.h: new files (from at-3.1.7)
	* FtpCopy.cc, FtpCopy.h: new files (copy between ftp servers)
	* Job.cc: call Bg() and Fg() for waiting
	* complete.cc: avoid "total xxx" in completion
	* FtpListInfo.cc: use int for base_dir_len
	* FileAccess.cc, FileAccess.h: use new log facility
	* ftpget.cc: use new log facility
	* CmdExec.cc, CmdExec.h: get rid of old debugging, Bg, Fg;
	  add new commands
	* commands.cc: use new log facility; add new commands
	* XferJob.cc, XferJob.h, pgetJob.cc: add eta reporting
	  (<NAME_EMAIL>)
	* ftpclass.cc, ftpclass.h: add COPY_FAILED state, DataAbort method,
	  abort data connection properly, send QUIT before control socket
	  close, copy_* members for ftpcopy support, FlushSendQueue flushes
	  all optionally

1998-08-19  Alexander V. Lukyanov  <<EMAIL>>

	* lftp.cc: (ReadlineFeeder) added ctty (is control tty) member;
	  removed hung-up check, since readline() should report eof in
	  the case; modified check for background state.
	* SMTask.cc: (Schedule) fix block collecting to exclude already
	  running tasks.

1998-08-17  Alexander V. Lukyanov  <<EMAIL>>

	* lftp.cc: (ReadlineFeeder::NextCmd) check for hung-up terminal

1998-08-17  Alexander V. Lukyanov  <<EMAIL>>

	* fg.cc: add debug printf's
	* Job.h, Job.cc: make Fg() and Bg() outline virtual
	* lftp.cc: (ReadlineFeeder::NextCmd) don't call readline()
	  if in background; (main) call top_exec->Fg() to make it foreground.
	* CmdExec.cc, CmdExec.h: (Fg, Bg) new functions; call waiting->Fg()
	  for only foreground jobs

Fri Aug 14 19:00:54 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.h, commands.cc: (suspend) new command
	* GetJob.cc: rename old file to file~ if it exists, and remove
	  backup upon getting the new file; this is useful for accidently
	  forgotten 're' or '-c'.
	* ftpclass.cc: fix warning about old_type; improve
	  error reporting; ignore extra 2xx responses for buggy servers
	* fg.h, fg.cc: (cont) new function; (Fg) cont the process even
	  if we don't own the terminal.
	* SysCmdJob.cc, SysCmdJob.h: (AcceptSig) implemented; (~) call
	  Bg() to release terminal properly, kill process in destructor
	* ProcWait.cc: (Kill) check if process still running

Mon Aug  3 19:18:51 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: (mirror) increase verbose instead of setting to 1
	* ftpclass.cc: (PASV_Catch) fix for broken servers returning 0,0,0,0

Wed Jul 29 17:35:15 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc: (~CmdExec) clear debug_shell if destroying it
	* CmdExec.h: make debug_shell public
	* commands.cc: (do_debug) don't set debug_shell
	* lftp.cc: (main) set debug_shell to top_exec
	Note: debug subsystem must be rewritten
	* complete.cc: remote completion by default for mirror
	* ftpclass.cc, ftpclass.h: ReadyCheck, check for m$ server and turn on
	  sync mode if it was not; check for hostname==""

Fri Jul 24 16:45:41 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: verify address before port

Thu Jul 23 00:54:11 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* mkdirJob.cc: include plural.h, one more message is preprocessed
	  by plural().
	* FileAccess.cc: (expand_tilde) fix memmove bound violation in
	  shrink case

Mon Jul 20 21:04:06 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* Filter.cc, Filter.h: new broken() method
	* XferJob.cc: use it to prevent spinning when output is broken
	  and data is not available yet

Wed Jul  1 11:31:22 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h: rename some settings for better sorting;
	  (verify-port) new setting, (data_port_verify) new member;
	  set buffer size in Reconfig; some tweaks
	* commands.cc: set arg1 in cd command for status line when dir=="-"

Mon Jun 22 16:31:09 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h: fix data_address_ok DebugPrint's;
	  add ftp:socket-buffer variable to tune tcp window

Thu Jun 18 18:19:20 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* alias.cc, alias.h: (Format) new function
	* commands.cc: use it
	* complete.cc: ls hack - ignore "total xxx", don't add /

Sun Jun 14 14:28:09 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc: if file type is unknown and local directory exists,
	  assume the remote file is a directory
	* ResMgr.cc, ResMgr.h: (Format) new function
	* commands.cc: use ResMgr::Format
	* ftpclass.cc, ftpclass.h: address-verify - new setting;
	  (data_address_ok) new function

Thu Jun 11 16:55:57 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* Filter.cc: expand tilde in file name.
	* misc.cc, misc.h: (expand_home_relative) take and return const
	* commands.cc: use const for expand_home_relative
	* LocalAccess.cc: (Write) fix to adjust pos correctly
	* CmdExec.cc: (completion_use_ls) new member, new setting
	  cmd:ls-in-completion; use host name for ls-default.
	* complete.cc: several fixes, use complation_use_ls.

Tue Jun  9 19:25:50 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* XferJob.cc: plural support
	* MirrorJob.cc: report not deleted files if flag set;
	  (SetRX) new method, replaces SetInclude and SetExclude;
	  plural support
	* commands.cc: (do_mirror) support several -i/-x; add optional
	  argument for --verbose; use new interface to MirrorJob
	* lftp.cc: (ReadlineFeeder) fix memory leak
	* xmalloc.h: (xfree) not report xfree(0) when debugging

Fri Jun  5 17:24:04 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: update help for bookmark, fix bo import to look in
	  proper directory.

Fri Jun  5 15:19:31 MSD 1998

	* ftpclass.cc: (Ftp(Ftp*)) copy anon_user/anon_pass if available
	(StrError) don't translate string that can't be seen by user

Fri Jun  5 14:42:15 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: (do_pwd) print url, allow redirection,
	  (do_open) prepend cmd_to_exec before bookmark expansion,
	  (do_bookmark) add import subcommand, complain if bookmark to delete
	  does not exist.

Fri Jun  5 14:37:26 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* complete.cc: try to use cached LONG_LIST for completion
	* LsCache.cc: (Lookup) don't copy out contents of cache if d==NULL

Wed Jun  3 17:18:04 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc, CmdExec.h: (save_passwords) new member,
	  (bmk:save-passwords) new setting
	* commands.cc: use save_passwords, (echo) new command
	* CatJob.cc: if output==0 use stdout

Fri May 29 18:32:47 MSD 1998  Alexander V. Lukyanov <<EMAIL>>
	* XferJob.cc, XferJob.h: add xfer:use_urls, unstrict urls

Mon May 25 23:42:14 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* FtpListInfo.cc: close session in destructor (fixes coredump)

Mon May 25 23:24:09 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc: use status_line even if !interactive

Mon May 25 15:52:08 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.h, FileAccess.cc: add WITH_PASSWORD flag to GetConnectURL

Sun May 24 23:30:00 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.h: make print_cmd_help non static, (do_lftp) new
	  function
	* commands.cc: (do_lftp) new function, remove -f option from
	  do_open, print_cmd_help changes, `open' help change
	* lftp.cc: use do_lftp (indirectly)

Sun May 24 23:00:16 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* CmdExec.cc: don't reference status_line when it's zero,
	  change status for cd and open commands

Sat May 23 03:06:25 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* MirrorJob.cc: pass newer_than to child mirror jobs
	* FileSet.cc: (SubtractOlderThan) subtract only plain files

Thu May 21 22:56:22 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: (mktime_from_utc) new function (from wget)
	  (ConvertFtpDate) use mktime_from_utc instead of old hack.

Thu May 21 22:21:50 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* FileAccess.h: make Login() virtual
	* ftpclass.cc: (Login) new function, add anon-user/anon-pass,
	  (SameConnection) new function, differenciate connections with,
	  different anonymous login. (DefaultAnonUser) new function,
	  (ClassInit) set default anon-pass, (anon_user) new member
	* ftpclass.h: same

Thu May 21 13:55:20 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* PutJob.cc: fix for new buffer allocation

Tue May 19 16:53:36 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: (find_command) new function (from vars.cc)
	* vars.cc, vars.h: remove

Fri May 15 19:25:49 MSD 1998  Alexander V. Lukyanov <<EMAIL>>
	* commands.cc: don't use errno in GetPass() error message

Fri May 15 18:17:31 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* Resolver.cc: handle timeout, fix sleep, (Reconfig) new function
	* Resolver.h: (timeout) new member, (Reconfig) new decl

Fri May 15 18:11:44 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* ResMgr.cc: (ResDecl::Query) new function
	* ResMgr.h: (ResValue) new class
	* ftpclass.cc: (Reconfig) use it, (ResQuery) remove
	* ftpclass.h: (ResQuery) remove, (SetFlag) take bool arg
	* CmdExec.cc: (Reconfig) use it, (ResQuery) remove
	* CmdExec.h: (ResQuery) remove

Fri May 15 16:49:35 MSD 1998  Alexander V. Lukyanov <<EMAIL>>
	* SMTask.cc (ReconfigAll): add NoWait

Fri May 15 16:31:14 MSD 1998  Alexander V. Lukyanov <<EMAIL>>
	* LsJob.cc: Fix cached listing printing
	* LsJob.h: remove unused variable cache_buffer_ptr

Tue May 12 22:24:38 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc: move pass hiding	here, where it belongs, show pass
	  when s/key is used or anonymous
	* FileAccess.cc: ...and remove from here

Tue May 12 21:45:02 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* XferJob.cc, XferJob.h: add support for variable sized buffer
	* CatJob.cc, CatJob.h: add support for cat'ing from memory
	* command.cc: (bookmark) use CatJob for bookmark list
	* bookmark.cc, bookmark.h: add Format

Tue May 12 19:19:54 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h: tune memory use

Tue May 12 19:00:06 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* xmalloc.cc, xmalloc.h: support for debug

Tue May 12 16:31:07 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* keyvalue.cc: (Format) new function
	  (Write) use Format
	* keyvalue.h: ajust accordingly

Mon May 11 01:08:37 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* commands.cc: (open) use password from .netrc if user name matches

Sat May  9 01:47:41 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.c: add "S/Key MD5" string to list of skey strings
	  (make_skey_reply) use `result' to catch multiline responses.

Fri May  8 02:47:11 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc, ftpclass.h: add force_skey, allow_skey defaults to yes
	  (make_skey_reply) rewrite, add otp-md5 string
	* ftpclass.cc: wait for pass prompt only when user given (no anon)

Thu May  7 21:55:17 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* parsecmd.cc: fix alias_field calculation

Wed May  6 21:13:41 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc (make_skey_reply): implement (base from wget) and use it;
	  (skey_pass, allow_skey): new members of class Ftp
	* ftpclass.h: likewise
	* Makefile.am: add md5.c, md5.h, ftp-opie.c to sources

Wed May  6 20:38:55 MSD 1998  Alexander V. Lukyanov <<EMAIL>>

	* ftpclass.cc (LoginCheck): assume non-fatal error when
	got RESP_LOGIN_FAILED, but without "Login incorrect" string.

Wed May  6 15:19:00 MSD 1998 Alexander V. Lukyanov <<EMAIL>>

	* commands.cc (bookmark): add `bookmark edit' command
