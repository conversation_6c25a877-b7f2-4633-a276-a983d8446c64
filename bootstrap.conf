# Bootstrap configuration.

# Copyright (C) 2006-2015 Free Software Foundation, Inc.

# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.

# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.

COPYRIGHT_HOLDER='Alexander V. Lu<PERSON>v <<EMAIL>>'
MSGID_BUGS_ADDRESS=<EMAIL>

checkout_only_file=.travis.yml
gnulib_name=libgnu
SKIP_PO=t

# gnulib modules used by this package.
gnulib_modules="
  alloca-opt
  arpa_inet
  configmake
  crypto/md5
  crypto/sha1
  environ
  filemode
  fnmatch
  fnmatch-gnu
  getopt-gnu
  gettext
  gettimeofday
  git-version-gen
  glob
  human
  iconv_open
  inet_pton
  lchown
  longlong
  lstat
  mbswidth
  memcasecmp
  memmem
  mktime
  modechange
  nstrftime
  parse-datetime
  passfd
  poll
  readlink
  regex
  sockets
  socklen
  strdup-posix
  strptime
  strstr
  strtok_r
  unsetenv
  vsnprintf
  vsnprintf-posix
  wcwidth
"

# Additional xgettext options to use.  Use "\\\newline" to break lines.
XGETTEXT_OPTIONS=$XGETTEXT_OPTIONS' --c++\\\
 --keyword=plural --flag=plural:1:pass-c-format\\\
 --from-code=UTF-8\\\
 --flag=asprintf:2:c-format --flag=vasprintf:2:c-format\\\
 --flag=asnprintf:3:c-format --flag=vasnprintf:3:c-format\\\
 --flag=wrapf:1:c-format\\\
 --flag=format:1:c-format --flag=vformat:1:c-format\\\
 --flag=appendf:1:c-format --flag=setf:1:c-format\\\
 --flag=LogNote:2:c-format --flag=LogError:2:c-format\\\
'

# If "AM_GNU_GETTEXT(external" or "AM_GNU_GETTEXT([external]"
# appears in configure.ac, exclude some unnecessary files.
# Without grep's -E option (not portable enough, pre-configure),
# the following test is ugly.  Also, this depends on the existence
# of configure.ac, not the obsolescent-named configure.in.  But if
# you're using this infrastructure, you should care about such things.

gettext_external=0
grep '^[	 ]*AM_GNU_GETTEXT(external\>' configure.ac > /dev/null &&
  gettext_external=1
grep '^[	 ]*AM_GNU_GETTEXT(\[external\]' configure.ac > /dev/null &&
  gettext_external=1

if test $gettext_external = 1; then
  # Gettext supplies these files, but we don't need them since
  # we don't have an intl subdirectory.
  excluded_files='
      m4/glibc2.m4
      m4/intdiv0.m4
      m4/lcmessage.m4
      m4/lock.m4
      m4/printf-posix.m4
      m4/size_max.m4
      m4/uintmax_t.m4
      m4/ulonglong.m4
      m4/visibility.m4
      m4/xsize.m4
  '
fi

# Build prerequisites
buildreq="\
autoconf   2.59
automake   1.9.6
git        1.5.5
tar        -
"
