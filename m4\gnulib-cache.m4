# Copyright (C) 2002-2024 Free Software Foundation, Inc.
#
# This file is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This file is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this file.  If not, see <https://www.gnu.org/licenses/>.
#
# As a special exception to the GNU General Public License,
# this file may be distributed as part of a program that
# contains a configuration script generated by Autoconf, under
# the same distribution terms as the rest of that program.
#
# Generated by gnulib-tool.
#
# This file represents the specification of how gnulib-tool is used.
# It acts as a cache: It is written and read by gnulib-tool.
# In projects that use version control, this file is meant to be put under
# version control, like the configure.ac and various Makefile.am files.


# Specification in the form of a command-line invocation:
# gnulib-tool --import \
#  --local-dir=gl \
#  --lib=libgnu \
#  --source-base=lib \
#  --m4-base=m4 \
#  --doc-base=doc \
#  --tests-base=tests \
#  --aux-dir=build-aux \
#  --no-conditional-dependencies \
#  --libtool \
#  --macro-prefix=gl \
#  alloca-opt \
#  arpa_inet \
#  configmake \
#  crypto/md5 \
#  crypto/sha1 \
#  environ \
#  filemode \
#  fnmatch \
#  fnmatch-gnu \
#  getopt-gnu \
#  gettext-h \
#  gettimeofday \
#  git-version-gen \
#  glob \
#  human \
#  iconv_open \
#  inet_pton \
#  lchown \
#  lstat \
#  mbswidth \
#  memcasecmp \
#  memmem \
#  mktime \
#  modechange \
#  nstrftime \
#  parse-datetime \
#  passfd \
#  poll \
#  readlink \
#  regex \
#  sockets \
#  socklen \
#  strdup-posix \
#  strptime \
#  strstr \
#  strtok_r \
#  unsetenv \
#  vsnprintf \
#  vsnprintf-posix \
#  wcwidth

# Specification in the form of a few gnulib-tool.m4 macro invocations:
gl_LOCAL_DIR([gl])
gl_MODULES([
  alloca-opt
  arpa_inet
  configmake
  crypto/md5
  crypto/sha1
  environ
  filemode
  fnmatch
  fnmatch-gnu
  getopt-gnu
  gettext-h
  gettimeofday
  git-version-gen
  glob
  human
  iconv_open
  inet_pton
  lchown
  lstat
  mbswidth
  memcasecmp
  memmem
  mktime
  modechange
  nstrftime
  parse-datetime
  passfd
  poll
  readlink
  regex
  sockets
  socklen
  strdup-posix
  strptime
  strstr
  strtok_r
  unsetenv
  vsnprintf
  vsnprintf-posix
  wcwidth
])
gl_AVOID([])
gl_SOURCE_BASE([lib])
gl_M4_BASE([m4])
gl_PO_BASE([])
gl_DOC_BASE([doc])
gl_TESTS_BASE([tests])
gl_LIB([libgnu])
gl_MAKEFILE_NAME([])
gl_LIBTOOL
gl_MACRO_PREFIX([gl])
gl_PO_DOMAIN([])
gl_WITNESS_C_MACRO([])
