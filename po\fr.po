# translation of fr.po to Français
# French messages of lftp
# Copyright (C) 2000-2001,2003, 2005, 2007, 2008 Free Software Foundation, Inc.
#
# <PERSON> <<EMAIL>>, 2000-2003, 2005.
# <PERSON> <<EMAIL>>, 2007, 2008.
msgid ""
msgstr ""
"Project-Id-Version: fr\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2024-11-08 13:05+0300\n"
"PO-Revision-Date: 2008-04-12 12:40+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Français <<EMAIL>>\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: KBabel 1.11.4\n"

#: lib/argmatch.c:145
#, c-format
msgid "invalid argument %s for %s"
msgstr "argument %s invalide pour %s"

#: lib/argmatch.c:146
#, c-format
msgid "ambiguous argument %s for %s"
msgstr "Argument %s ambigu pour %s"

#: lib/argmatch.c:165
msgid "Valid arguments are:"
msgstr "Les arguments valides sont :"

#: lib/error.c:208
msgid "Unknown system error"
msgstr "Erreur système inconnue"

#: lib/getopt.c:282
#, fuzzy, c-format
msgid "%s: option '%s%s' is ambiguous\n"
msgstr "%s : l'option « -W %s » est ambiguë\n"

#: lib/getopt.c:288
#, fuzzy, c-format
msgid "%s: option '%s%s' is ambiguous; possibilities:"
msgstr "%s : l'option « %s » est ambiguë\n"

#: lib/getopt.c:322
#, fuzzy, c-format
msgid "%s: unrecognized option '%s%s'\n"
msgstr "%s : option « %c%s » non reconnue\n"

#: lib/getopt.c:348
#, fuzzy, c-format
msgid "%s: option '%s%s' doesn't allow an argument\n"
msgstr "%s : l'option « %c%s » ne permet pas d'argument\n"

#: lib/getopt.c:363
#, fuzzy, c-format
msgid "%s: option '%s%s' requires an argument\n"
msgstr "%s : l'option « %s » nécessite un argument\n"

#: lib/getopt.c:624
#, fuzzy, c-format
msgid "%s: invalid option -- '%c'\n"
msgstr "%s : option invalide -- %c\n"

#: lib/getopt.c:639 lib/getopt.c:685
#, fuzzy, c-format
msgid "%s: option requires an argument -- '%c'\n"
msgstr "%s: l'option nécessite un argument -- %c\n"

#. TRANSLATORS:
#. Get translations for open and closing quotation marks.
#. The message catalog should translate "`" to a left
#. quotation mark suitable for the locale, and similarly for
#. "'".  For example, a French Unicode local should translate
#. these to U+00AB (LEFT-POINTING DOUBLE ANGLE
#. QUOTATION MARK), and U+00BB (RIGHT-POINTING DOUBLE ANGLE
#. QUOTATION MARK), respectively.
#.
#. If the catalog has no translation, we will try to
#. use Unicode U+2018 (LEFT SINGLE QUOTATION MARK) and
#. Unicode U+2019 (RIGHT SINGLE QUOTATION MARK).  If the
#. current locale is not Unicode, locale_quoting_style
#. will quote 'like this', and clocale_quoting_style will
#. quote "like this".  You should always include translations
#. for "`" and "'" even if U+2018 and U+2019 are appropriate
#. for your locale.
#.
#. If you don't know what to put here, please see
#. <https://en.wikipedia.org/wiki/Quotation_marks_in_other_languages>
#. and use glyphs suitable for your language.
#: lib/quotearg.c:354
msgid "`"
msgstr "« "

#: lib/quotearg.c:355
msgid "'"
msgstr " »"

#: lib/xalloc-die.c:34
msgid "memory exhausted"
msgstr "Pas assez de mémoire"

#: src/ArgV.cc:107
#, fuzzy
msgid "option requires an argument"
msgstr "%s : l'option « %s » nécessite un argument\n"

#: src/ArgV.cc:109 src/ArgV.cc:118
#, fuzzy
msgid "invalid option"
msgstr "%s : option invalide -- %c\n"

#: src/ArgV.cc:114
#, fuzzy, c-format
msgid "option `%s' requires an argument"
msgstr "%s : l'option « %s » nécessite un argument\n"

#: src/ArgV.cc:116
#, fuzzy, c-format
msgid "unrecognized option `%s'"
msgstr "%s : option « %c%s » non reconnue\n"

#: src/attach.h:138
#, fuzzy, c-format
msgid "[%u] Attached to terminal %s. %s\n"
msgstr "[%lu] Terminé par le signal %d. %s"

#: src/attach.h:150
#, c-format
msgid "[%u] Attached to terminal.\n"
msgstr ""

#: src/buffer.cc:253 src/FileAccess.cc:866
msgid " [cached]"
msgstr " [caché]"

#: src/ChmodJob.cc:80
#, c-format
msgid "Failed to change mode of `%s' to %04o (%s).\n"
msgstr "Impossible de modifier le mode de « %s » en %04o (%s).\n"

#: src/ChmodJob.cc:83
#, c-format
msgid "Mode of `%s' changed to %04o (%s).\n"
msgstr "Le mode de « %s » a été modifié en %04o (%s).\n"

#: src/ChmodJob.cc:88
#, c-format
msgid "Failed to change mode of `%s' because no old mode is available.\n"
msgstr ""
"Impossible de modifier le mode de « %s » car il n'y a pas d'ancien mode.\n"

#: src/CmdExec.cc:105
#, c-format
msgid "Warning: chdir(%s) failed: %s\n"
msgstr "Attention : chdir(%s) a échoué : %s\n"

#: src/CmdExec.cc:207
#, c-format
msgid "Unknown command `%s'.\n"
msgstr "Commande inconnue « %s ».\n"

#: src/CmdExec.cc:209
#, c-format
msgid "Ambiguous command `%s'.\n"
msgstr "Commande ambiguë « %s ».\n"

#: src/CmdExec.cc:228
#, c-format
msgid "Module for command `%s' did not register the command.\n"
msgstr "Le module pour la commande « %s » n'a pas enregistré la commande.\n"

#: src/CmdExec.cc:384
#, c-format
msgid "cd ok, cwd=%s\n"
msgstr "cd ok, cwd=%s\n"

#: src/CmdExec.cc:405 src/MirrorJob.cc:736
#, c-format
msgid "%s: received redirection to `%s'\n"
msgstr "%s : reçu une redirection vers « %s »\n"

#: src/CmdExec.cc:408 src/FileCopy.cc:1230
msgid "Too many redirections"
msgstr "Trop de redirections"

#: src/CmdExec.cc:517 src/CmdExec.cc:574
msgid "Interrupt"
msgstr "Interrompu"

#: src/CmdExec.cc:639
#, c-format
msgid "Warning: discarding incomplete command\n"
msgstr "Attention : commande incomplète ignorée\n"

#: src/CmdExec.cc:737
#, c-format
msgid "\tExecuting builtin `%s' [%s]\n"
msgstr "\tExécution de la commande interne « %s » [%s]\n"

#: src/CmdExec.cc:742
msgid "Queue is stopped."
msgstr "La file d'attente est stoppée."

#: src/CmdExec.cc:747
msgid "Now executing:"
msgstr "En cours d'exécution :"

#: src/CmdExec.cc:758
#, c-format
msgid "\tWaiting for job [%d] to terminate\n"
msgstr "\tEn attente de la fin de la tâche [%d]\n"

#: src/CmdExec.cc:761
#, c-format
msgid "\tWaiting for termination of jobs: "
msgstr "\tEn attente de la fin des tâches : "

#: src/CmdExec.cc:770
msgid "\tRunning\n"
msgstr "\tEn cours d'exécution\n"

#: src/CmdExec.cc:772
msgid "\tWaiting for command\n"
msgstr "\tEn attente de la commande\n"

#: src/CmdExec.cc:1261
#, c-format
msgid "%s: command `%s' is not compiled in.\n"
msgstr "%s : la commande « %s » n'est pas compilée.\n"

#: src/CmdExec.cc:1278
#, fuzzy, c-format
msgid "Usage: %s cmd [args...]\n"
msgstr "Utilisation : %s module [args...]\n"

#: src/CmdExec.cc:1284
#, c-format
msgid "%s: cannot create local session\n"
msgstr ""

#: src/commands.cc:114
msgid "!<shell-command>"
msgstr "!<commande-shell>"

#: src/commands.cc:115
msgid "Launch shell or shell command\n"
msgstr "Lance un interpréteur de commande ou une commande shell\n"

#: src/commands.cc:116
msgid "(commands)"
msgstr "(commandes)"

#: src/commands.cc:117
msgid ""
"Group commands together to be executed as one command\n"
"You can launch such a group in background\n"
msgstr ""
"Regroupe des commandes pour être exécutées comme une seule\n"
"commande. Vous pouvez lancer un tel groupe en tâche de fond\n"

#: src/commands.cc:120
msgid "alias [<name> [<value>]]"
msgstr "alias [<nom> [<valeur>]]"

#: src/commands.cc:121
msgid ""
"Define or undefine alias <name>. If <value> omitted,\n"
"the alias is undefined, else is takes the value <value>.\n"
"If no argument is given the current aliases are listed.\n"
msgstr ""
"Définit ou efface l'alias <nom>. Si <valeur> est omise,\n"
"l'alias est effacé, sinon il prend la valeur <valeur>.\n"
"Si aucun argument n'est donné, alors les alias en cours\n"
"sont affichés.\n"

#: src/commands.cc:125
msgid "anon - login anonymously (by default)\n"
msgstr "anon - se connecter de manière anonyme (par défaut)\n"

#: src/commands.cc:127
msgid "bookmark [SUBCMD]"
msgstr "bookmark [SOUS-COMMANDE]"

#: src/commands.cc:128
msgid ""
"bookmark command controls bookmarks\n"
"\n"
"The following subcommands are recognized:\n"
"  add <name> [<loc>] - add current place or given location to bookmarks\n"
"                       and bind to given name\n"
"  del <name>         - remove bookmark with the name\n"
"  edit               - start editor on bookmarks file\n"
"  import <type>      - import foreign bookmarks\n"
"  list               - list bookmarks (default)\n"
msgstr ""
"La commande bookmark contrôle les signets\n"
"\n"
"Les sous-commandes suivantes sont valides :\n"
"  add <nom> [<url>]  - ajoute l'url en cours ou l'url indiquée aux signets\n"
"                       et l'associe au nom donné\n"
"  del <nom>          - supprime le signet\n"
"  edit               - démarre l'éditeur de signets\n"
"  import <type>      - importe un fichier de signets externe\n"
"  list               - affiche les signets (par défaut)\n"

#: src/commands.cc:137
msgid "cache [SUBCMD]"
msgstr "cache [SOUS-COMMANDE]"

#: src/commands.cc:138
msgid ""
"cache command controls local memory cache\n"
"\n"
"The following subcommands are recognized:\n"
"  stat        - print cache status (default)\n"
"  on|off      - turn on/off caching\n"
"  flush       - flush cache\n"
"  size <lim>  - set memory limit\n"
"  expire <Nx> - set cache expiration time to N seconds (x=s)\n"
"                minutes (x=m) hours (x=h) or days (x=d)\n"
msgstr ""
"La commande cache contrôle la mémoire cache locale\n"
"\n"
"Les sous-commandes suivantes sont valides :\n"
"  stat        - affiche l'état du cache (défaut)\n"
"  on|off      - active ou désactive le cache\n"
"  flush       - vide le cache\n"
"  size <lim>  - définit la limite de mémoire, -1 = illimité\n"
"  expire <Nx> - définit le temps d'expiration du cache à N,\n"
"                N étant en secondes (x=s) minutes (x=m)\n"
"                heures (x=h) ou jours (x=d)\n"

#: src/commands.cc:146
msgid "cat [-b] <files>"
msgstr "cat [-b] <fichiers>"

#: src/commands.cc:147
msgid ""
"cat - output remote files to stdout (can be redirected)\n"
" -b  use binary mode (ascii is the default)\n"
msgstr ""
"cat - affiche les fichiers distants sur stdout (peut être redirigé)\n"
" -b  utilise le mode binaire (ASCII par défaut)\n"

#: src/commands.cc:149
msgid "cd <rdir>"
msgstr "cd <rép-dis>"

#: src/commands.cc:150
msgid ""
"Change current remote directory to <rdir>. The previous remote directory\n"
"is stored as `-'. You can do `cd -' to change the directory back.\n"
"The previous directory for each site is also stored on disk, so you can\n"
"do `open site; cd -' even after lftp restart.\n"
msgstr ""
"Change le répertoire en cours pour <rep-dis>. Le répertoire précédent\n"
"est enregistré dans « - ». Vous pouvez faire « cd - » pour revenir au\n"
"répertoire précédent. Le répertoire précédent de chaque site est stocké\n"
"sur disque, donc vous pouvez faire « open site; cd - » même après avoir\n"
"redémarré lftp.\n"

#: src/commands.cc:154
msgid "chmod [OPTS] mode file..."
msgstr "chmod [OPTS] mode fichier..."

#: src/commands.cc:155
msgid ""
"Change the mode of each FILE to MODE.\n"
"\n"
" -c, --changes        - like verbose but report only when a change is made\n"
" -f, --quiet          - suppress most error messages\n"
" -v, --verbose        - output a diagnostic for every file processed\n"
" -R, --recursive      - change files and directories recursively\n"
"\n"
"MODE can be an octal number or symbolic mode (see chmod(1))\n"
msgstr ""
"Modifie le mode de chaque FICHIER en MODE.\n"
"\n"
" -c, --changes        - comme verbose mais n'affiche que les modifications\n"
" -f, --quiet          - supprime quasiment tous les messages d'erreurs\n"
" -v, --verbose        - affiche un message pour chaque fichier\n"
" -R, --recursive      - modifie les fichiers et répertoires récursivement\n"
"\n"
"MODE peut être un nombre octal ou un mode symbolique (voir chmod(1))\n"

#: src/commands.cc:164
msgid ""
"Close idle connections. By default only with current server.\n"
" -a  close idle connections with all servers\n"
msgstr ""
"Ferme les connexions inutiles. Par défaut, uniquement pour le serveur "
"actif.\n"
" -a  ferme les connexions inutiles de tous les serveurs\n"

#: src/commands.cc:166
msgid "[re]cls [opts] [path/][pattern]"
msgstr "[re]cls [opts] [chemin/][motif]"

#: src/commands.cc:167
#, fuzzy
msgid ""
"List remote files. You can redirect output of this command to file\n"
"or via pipe to external command.\n"
"\n"
" -1                   - single-column output\n"
" -a, --all            - show dot files\n"
" -B, --basename       - show basename of files only\n"
"     --block-size=SIZ - use SIZ-byte blocks\n"
" -d, --directory      - list directory entries instead of contents\n"
" -F, --classify       - append indicator (one of /@) to entries\n"
" -h, --human-readable - print sizes in human readable format (e.g., 1K)\n"
"     --si             - likewise, but use powers of 1000 not 1024\n"
" -k, --kilobytes      - like --block-size=1024\n"
" -l, --long           - use a long listing format\n"
" -q, --quiet          - don't show status\n"
" -s, --size           - print size of each file\n"
"     --filesize       - if printing size, only print size for files\n"
" -i, --nocase         - case-insensitive pattern matching\n"
" -I, --sortnocase     - sort names case-insensitively\n"
" -D, --dirsfirst      - list directories first\n"
"     --sort=OPT       - \"name\", \"size\", \"date\"\n"
" -S                   - sort by file size\n"
" --user, --group, --perms, --date, --linkcount, --links\n"
"                      - show individual fields\n"
" --time-style=STYLE   - use specified time format\n"
"\n"
"By default, cls output is cached, to see new listing use `recls' or\n"
"`cache flush'.\n"
"\n"
"The variables cls-default and cls-completion-default can be used to\n"
"specify defaults for cls listings and completion listings, respectively.\n"
"For example, to make completion listings show file sizes, set\n"
"cls-completion-default to \"-s\".\n"
"\n"
"Tips: Use --filesize with -D to pack the listing better.  If you don't\n"
"always want to see file sizes, --filesize in cls-default will affect the\n"
"-s flag on the commandline as well.  Add `-i' to cls-completion-default\n"
"to make filename completion case-insensitive.\n"
msgstr ""
"Affiche les fichiers distants. Vous pouvez rediriger la sortie de cette\n"
"commande vers un fichier ou une commande externe via un tube.\n"
"\n"
" -1                   - sortie sur une colonne\n"
" -B, --basename       - affiche uniquement les noms des fichiers\n"
"     --bloc-size=SIZ  - utilise des blocs de taille SIZ\n"
" -d, --directory      - affiche les entrées des répertoires au lieu de leur\n"
"                        contenu\n"
" -F, --classify       - ajoute un indicateur (« / » ou « @ ») aux entrées\n"
" -h, --human-readable - affiche les tailles dans un format intelligible\n"
"                        (par ex., 1K)\n"
"     --si             - même chose, en utilisant des puissances de 1000\n"
"                        au lieu des puissances de 1024 -k, --kilobytes      "
"- comme --block-size=1024\n"
" -l, --long           - utilise un format d'affichage long\n"
" -q, --quiet          - n'affiche pas d'état\n"
" -s, --size           - affiche la taille de chaque fichier\n"
"     --filesize       - avec -s, n'affiche que les tailles des fichiers\n"
" -i, --nocase         - la mise en correspondance est insensible à la casse\n"
" -I, --sortnocase     - trie les noms sans tenir compte de la casse\n"
" -D, --dirsfirst      - affiche les répertoires en premier\n"
"     --sort=OPT       - « name » (par nom), « size »(par taille)\n"
" -S                   - trie suivant la taille des fichiers\n"
" --user, --group, --perms, --date, --linkcount, --links\n"
"                      - affiche des champs individuels\n"
" --time-style=STYLE   - utilise le format de temps spécifié\n"
"\n"
"Par défaut, la sortie de « cls » est cachée. Pour voir un nouvel affichage,\n"
"utilisez « recls » ou « cache flush ».\n"
"\n"
"Les variables cls-default et cls-completion-default peuvent être utilisées\n"
"pour spécifier les valeurs par défaut des affichages cls et des affichages "
"de\n"
"complétion, respectivement. Par exemple, pour que les affichages de\n"
"complétion affichent les tailles de fichiers, mettez\n"
"cls-completion-default à « -s ».\n"
"\n"
"Astuces : utilisez « --filesize » et « -D » pour de meilleurs affichages.\n"
"Si vous ne voulez pas toujours voir les tailles des fichiers, « --filesize "
"»\n"
"dans cls-default affectera l'option « -s » de la ligne de commande.\n"
"Toutes les options fonctionnent pour cls-completion-default.\n"
"Ajoutez « -i » à cls-completion-default pour rendre la complétion\n"
"des fichiers insensible à la casse.\n"

#: src/commands.cc:217
#, fuzzy
msgid "debug [OPTS] [<level>|off]"
msgstr "debug [<niveau>|off] [-o <fichier>]"

#: src/commands.cc:218
#, fuzzy
msgid ""
"Set debug level to given value or turn debug off completely.\n"
" -o <file>  redirect debug output to the file\n"
" -c  show message context\n"
" -p  show PID\n"
" -t  show timestamps\n"
msgstr ""
"Définit le niveau de débogage à la valeur indiquée ou désactive le "
"débogage.\n"
" -o <fichier>  redirige la sortie du débogage vers un fichier.\n"

#: src/commands.cc:223
msgid "du [options] <dirs>"
msgstr "du [options] <répertoires>"

#: src/commands.cc:224
msgid ""
"Summarize disk usage.\n"
" -a, --all             write counts for all files, not just directories\n"
"     --block-size=SIZ  use SIZ-byte blocks\n"
" -b, --bytes           print size in bytes\n"
" -c, --total           produce a grand total\n"
" -d, --max-depth=N     print the total for a directory (or file, with --"
"all)\n"
"                       only if it is N or fewer levels below the command\n"
"                       line argument;  --max-depth=0 is the same as\n"
"                       --summarize\n"
" -F, --files           print number of files instead of sizes\n"
" -h, --human-readable  print sizes in human readable format (e.g., 1K 234M "
"2G)\n"
" -H, --si              likewise, but use powers of 1000 not 1024\n"
" -k, --kilobytes       like --block-size=1024\n"
" -m, --megabytes       like --block-size=1048576\n"
" -S, --separate-dirs   do not include size of subdirectories\n"
" -s, --summarize       display only a total for each argument\n"
"     --exclude=PAT     exclude files that match PAT\n"
msgstr ""
"Récapituler l'utilisation du disque.\n"
" -a, --all             affiche pour tous les fichiers,\n"
"                       pas seulement les répertoires\n"
"     --block-size=SIZ  utilise des blocs de taille SIZ\n"
" -b, --bytes           affiche la taille en octets\n"
" -c, --total           produit un grand total\n"
" -d, --max-depth=N     affiche le total pour un répertoire (ou un fichier\n"
"                       avec --all) seulement s'il y a N ou moins de niveaux\n"
"                       en dessous des arguments de la commande ;\n"
"                       --max-depth=0 fait la même chose que --summarize\n"
" -F, --files           affiche le nombre de fichiers au lieu des tailles\n"
" -h, --human-readable  affiche les tailles dans format intelligible\n"
"                       (par ex. : 1K, 234M, 2G)\n"
" -H, --si              même chose, avec des puissances de 1000 et non de "
"1024\n"
" -k, --kilobytes       identique à --block-size=1024\n"
" -m, --megabytes       identique à --block-size=1048576\n"
" -S, --separate-dirs   ne pas inclure la taille des sous-répertoires\n"
" -s, --summarize       affiche seulement un total pour chaque argument\n"
"     --exclude=PAT     exclut les fichiers correspondant à PAT\n"

#: src/commands.cc:242
#, fuzzy
msgid "edit [OPTS] <file>"
msgstr "mget [OPTS] <fichiers>"

#: src/commands.cc:243
msgid ""
"Retrieve remote file to a temporary location, run a local editor on it\n"
"and upload the file back if changed.\n"
" -k  keep the temporary file\n"
" -o <temp>  explicit temporary file location\n"
msgstr ""

#: src/commands.cc:248
msgid "exit [<code>|bg]"
msgstr "exit [<code>|bg]"

#: src/commands.cc:249
msgid ""
"exit - exit from lftp or move to background if jobs are active\n"
"\n"
"If no jobs active, the code is passed to operating system as lftp\n"
"termination status. If omitted, exit code of last command is used.\n"
"`bg' forces moving to background if cmd:move-background is false.\n"
msgstr ""
"exit - sort de lftp ou bascule en arrière-plan si des tâches sont actives\n"
"\n"
"Si aucun tâche n'est active, le code est passé au système d'exploitation\n"
"comme étant le code de sortie de lftp. Si le code est omis, le code de\n"
"sortie de la dernière commande sera utilisé.\n"
"« bg » force le basculement en arrière-plan si cmd:move-background est "
"faux.\n"

#: src/commands.cc:255
msgid ""
"Usage: find [OPTS] [directory]\n"
"Print contents of specified directory or current directory recursively.\n"
"Directories in the list are marked with trailing slash.\n"
"You can redirect output of this command.\n"
" -d, --maxdepth=LEVELS  Descend at most LEVELS of directories.\n"
msgstr ""
"Utilisation : find [OPTS] [répertoire]\n"
"Affiche, de manière récursive, le contenu du répertoire spécifié\n"
"ou du répertoire en cours. Les répertoires dans la liste sont marqués avec "
"une\n"
"barre oblique finale. Vous pouvez rediriger la sortie de cette commande.\n"
" -d, --maxdepth=LEVELS  Descend dans au plus LEVELS répertoires.\n"

#: src/commands.cc:260
msgid "get [OPTS] <rfile> [-o <lfile>]"
msgstr "get [OPTS] <fichier-distant> -o <fichier-local>]"

#: src/commands.cc:261
#, fuzzy
msgid ""
"Retrieve remote file <rfile> and store it to local file <lfile>.\n"
" -o <lfile> specifies local file name (default - basename of rfile)\n"
" -c  continue, resume transfer\n"
" -E  delete remote files after successful transfer\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Télécharge le fichier distant <fichier-distant> et l'enregistre dans\n"
"le fichier local <fichier-local>.\n"
" -o <fichier-local> spécifie le nom de fichier local (par défaut : fichier-"
"distant)\n"
" -c  continue (reget)\n"
" -E  efface le fichier distant après un transfert correctement effectué.\n"
" -a  utilise le mode ASCII (mode binaire par défaut)\n"
" -O <base> spécifie le répertoire de base ou l'URL où les fichiers doivent\n"
"     être placés.\n"

#: src/commands.cc:268
msgid "glob [OPTS] <cmd> <args>"
msgstr "glob [OPTS] <cmd> <args>"

#: src/commands.cc:270
#, fuzzy
msgid ""
"Expand wildcards and run specified command.\n"
"Options can be used to expand wildcards to list of files, directories,\n"
"or both types. Type selection is not very reliable and depends on server.\n"
"If entry type cannot be determined, it will be included in the list.\n"
" -f  plain files (default)\n"
" -d  directories\n"
" -a  all types\n"
" --exist      return zero exit code when the patterns expand to non-empty "
"list\n"
" --not-exist  return zero exit code when the patterns expand to an empty "
"list\n"
msgstr ""
"Développe les caractères génériques (wildcard) et lance la commande\n"
"spécifiée. Les options peuvent être utilisées pour développer les "
"caractères\n"
"génériques à une liste de fichiers, de répertoires ou des deux types.\n"
"La détermination du type n'est pas très fiable et dépend du serveur.\n"
"Si une entrée ne peut être déterminée, elle sera incluse dans la liste.\n"
" -f  fichiers (défaut)\n"
" -d  répertoires\n"
" -a  tous les types\n"

#: src/commands.cc:279
msgid "help [<cmd>]"
msgstr "help [<cmd>]"

#: src/commands.cc:280
msgid "Print help for command <cmd>, or list of available commands\n"
msgstr ""
"Affiche l'aide pour la commande <cmd>, ou la liste des commandes "
"disponibles\n"

#: src/commands.cc:282
#, fuzzy
msgid ""
"List running jobs. -v means verbose, several -v can be specified.\n"
"If <job_no> is specified, only list a job with that number.\n"
msgstr ""
"Affiche les tâches actives. -v pour le mode volubile,\n"
"l'option pouvant être spécifiée plusieurs fois\n"

#: src/commands.cc:284
msgid "kill all|<job_no>"
msgstr "kill all|<num>"

#: src/commands.cc:285
msgid "Delete specified job with <job_no> or all jobs\n"
msgstr "Supprime la tâche spécifiée par <num> ou toutes les tâches\n"

#: src/commands.cc:286
msgid "lcd <ldir>"
msgstr "lcd <répertoire-local>"

#: src/commands.cc:287
msgid ""
"Change current local directory to <ldir>. The previous local directory\n"
"is stored as `-'. You can do `lcd -' to change the directory back.\n"
msgstr ""
"Modifie le répertoire local courant en <répertoire-local>. Le répertoire "
"local\n"
"précédent est stocké dans « - ». Vous pouvez faire « lcd - » pour revenir\n"
"au répertoire précédent.\n"

#: src/commands.cc:289
msgid "lftp [OPTS] <site>"
msgstr "lftp [OPTS] <site>"

#: src/commands.cc:290
#, fuzzy
msgid ""
"`lftp' is the first command executed by lftp after rc files\n"
" -f <file>           execute commands from the file and exit\n"
" -c <cmd>            execute the commands and exit\n"
" --norc              don't execute rc files from the home directory\n"
" --help              print this help and exit\n"
" --version           print lftp version and exit\n"
"Other options are the same as in `open' command:\n"
" -e <cmd>            execute the command just after selecting\n"
" -u <user>[,<pass>]  use the user/password for authentication\n"
" -p <port>           use the port for connection\n"
" -s <slot>           assign the connection to this slot\n"
" -d                  switch on debugging mode\n"
" <site>              host name, URL or bookmark name\n"
msgstr ""
"« lftp » est la première commande exécutée par lftp après les fichiers « rc "
"»\n"
" -f <fichier>        exécute les commandes depuis le fichier et termine\n"
" -c <cmd>            exécute les commandes et termine\n"
" --help              affiche cette aide et termine\n"
" --version           affiche la version de lftp et termine\n"
"Les autres options sont les mêmes que celles de la commande « open »\n"
" -e <cmd>            exécute la commande juste après la sélection\n"
" -u <user>[,<pass>]  utilise user/pass pour l'authentification\n"
" -p <port>           utilise le port pour la connexion\n"
" <site>              nom du site, URL ou nom de signet\n"

#: src/commands.cc:303
#, fuzzy
msgid "ln [-s] <file1> <file2>"
msgstr "mv <fichier1> <fichier2>"

#: src/commands.cc:304
#, fuzzy
msgid "Link <file1> to <file2>\n"
msgstr "Renomme <fichier1> en <fichier2>\n"

#: src/commands.cc:308
msgid "ls [<args>]"
msgstr "ls [<args>]"

#: src/commands.cc:309
msgid ""
"List remote files. You can redirect output of this command to file\n"
"or via pipe to external command.\n"
"By default, ls output is cached, to see new listing use `rels' or\n"
"`cache flush'.\n"
"See also `help cls'.\n"
msgstr ""
"Affiche les fichiers distants. Vous pouvez rediriger la sortie de cette\n"
"commande vers un fichier ou une commande externe via un tube.\n"
"Par défaut, la sortie de ls est cachée. Pour avoir un nouvel affichage,\n"
"utilisez « rels » ou « cache flush ».\n"
"Voir aussi « help cls ».\n"

#: src/commands.cc:314
msgid "mget [OPTS] <files>"
msgstr "mget [OPTS] <fichiers>"

#: src/commands.cc:315
#, fuzzy
msgid ""
"Gets selected files with expanded wildcards\n"
" -c  continue, resume transfer\n"
" -d  create directories the same as in file names and get the\n"
"     files into them instead of current directory\n"
" -E  delete remote files after successful transfer\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Télécharge les fichiers sélectionnés après expansion des caractères\n"
"génériques (wildcard)\n"
" -c  continue (reget)\n"
" -d  crée des répertoires du même nom que les noms de fichiers et\n"
"     met les fichiers dans ces répertoires au lieu du répertoire courant\n"
" -E  efface le fichier distant après un transfert correctement accompli.\n"
" -a  utilise le mode ASCII (mode binaire par défaut)\n"
" -O <base> spécifie le répertoire de base ou l'URL où les fichiers doivent\n"
"     être placés.\n"

#: src/commands.cc:322
msgid "mirror [OPTS] [remote [local]]"
msgstr "mirror [OPTS] [distant [local]]"

#: src/commands.cc:323
#, fuzzy
msgid "mkdir [OPTS] <dirs>"
msgstr "mkdir [-p] <répertoires>"

#: src/commands.cc:324
#, fuzzy
msgid ""
"Make remote directories\n"
" -p  make all levels of path\n"
" -f  be quiet, suppress messages\n"
msgstr ""
"Crée les répertoires distants\n"
" -p  crée tous les niveaux de l'arborescence.\n"

#: src/commands.cc:327
msgid "module name [args]"
msgstr "module nom [args]"

#: src/commands.cc:328
msgid ""
"Load module (shared object). The module should contain function\n"
"   void module_init(int argc,const char *const *argv)\n"
"If name contains a slash, then the module is searched in current\n"
"directory, otherwise in directories specified by setting module:path.\n"
msgstr ""
"Charge le module (objet partagé). Ce module doit contenir la fonction\n"
"   void module_init(int argc, const char *const argv)\n"
"Si le nom contient une barre oblique, le module est recherché dans le\n"
"répertoire courant, autrement, la recherche s'effectue dans les répertoires\n"
"spécifiés par module:path.\n"

#: src/commands.cc:332
msgid "more <files>"
msgstr "more <fichiers>"

#: src/commands.cc:333
msgid "Same as `cat <files> | more'. if PAGER is set, it is used as filter\n"
msgstr ""
"Identique à « cat <fichiers> | more ». Si PAGER est défini, il est utilisé\n"
"en temps que filtre.\n"

#: src/commands.cc:334
msgid "mput [OPTS] <files>"
msgstr "mput [OPTS] <fichiers>"

#: src/commands.cc:335
msgid ""
"Upload files with wildcard expansion\n"
" -c  continue, reput\n"
" -d  create directories the same as in file names and put the\n"
"     files into them instead of current directory\n"
" -E  delete local files after successful transfer (dangerous)\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Téléverse les fichiers après expansion des caractères génériques (wildcard)\n"
" -c  continue (reput)\n"
" -d  crée des répertoires du même nom que les noms de fichiers et\n"
"     met les fichiers dans ces répertoires au lieu du répertoire courant\n"
" -E  efface les fichiers locaux après un transfert correctement effectué\n"
"     (dangeureux)\n"
" -a  utilise le mode ASCII (mode binaire par défaut)\n"
" -O <base> spécifie le répertoire de base ou l'URL où les fichiers doivent\n"
"     être placés.\n"

#: src/commands.cc:342
msgid "mrm <files>"
msgstr "mrm <fichiers>"

#: src/commands.cc:343
msgid "Removes specified files with wildcard expansion\n"
msgstr "Efface les fichiers après expansion des caractères génériques\n"

#: src/commands.cc:344
msgid "mv <file1> <file2>"
msgstr "mv <fichier1> <fichier2>"

#: src/commands.cc:345
msgid "Rename <file1> to <file2>\n"
msgstr "Renomme <fichier1> en <fichier2>\n"

#: src/commands.cc:346
#, fuzzy
msgid "mmv [OPTS] <files> <target-dir>"
msgstr "mget [OPTS] <fichiers>"

#: src/commands.cc:347
msgid ""
"Move <files> to <target-directory> with wildcard expansion\n"
" -O <dir>  specifies the target directory (alternative way)\n"
msgstr ""

#: src/commands.cc:349
msgid "[re]nlist [<args>]"
msgstr "[re]nlist [<args>]"

#: src/commands.cc:350
msgid ""
"List remote file names.\n"
"By default, nlist output is cached, to see new listing use `renlist' or\n"
"`cache flush'.\n"
msgstr ""
"Affiche les fichiers distants. Par défaut, la sortie de nlist est cachée.\n"
"Pour avoir un nouvel affichage, utilisez « renlist » ou « cache flush ».\n"

#: src/commands.cc:353
msgid "open [OPTS] <site>"
msgstr "open [OPTS] <site>"

#: src/commands.cc:354
#, fuzzy
msgid ""
"Select a server, URL or bookmark\n"
" -e <cmd>            execute the command just after selecting\n"
" -u <user>[,<pass>]  use the user/password for authentication\n"
" -p <port>           use the port for connection\n"
" -s <slot>           assign the connection to this slot\n"
" -d                  switch on debugging mode\n"
" <site>              host name, URL or bookmark name\n"
msgstr ""
"Sélectionne un serveur, une URL ou un signet\n"
" -e <cmd>            exécute la commande juste après la sélection.\n"
" -u <user>[,<pass>]  utilise user/pass pour l'authentification.\n"
" -p <port>           utilise le port pour la connexion\n"
" -s <slot>           affecte la connexion à cet emplacement\n"
" <site>              nom du site, URL ou nom de signet\n"

#: src/commands.cc:361
msgid "pget [OPTS] <rfile> [-o <lfile>]"
msgstr "pget [OPTS] <fichier-distant> [-o <fichier-local>]"

#: src/commands.cc:362
msgid ""
"Gets the specified file using several connections. This can speed up "
"transfer,\n"
"but loads the net heavily impacting other users. Use only if you really\n"
"have to transfer the file ASAP.\n"
"\n"
"Options:\n"
" -c  continue transfer. Requires <lfile>.lftp-pget-status file.\n"
" -n <maxconn>  set maximum number of connections (default is is taken from\n"
"     pget:default-n setting)\n"
" -O <base> specifies base directory where files should be placed\n"
msgstr ""
"Télécharge le fichier spécifié en utilisant plusieurs connexions.\n"
"Cela peut accélérer le transfert, mais cela charge le réseau au détriment\n"
"des autres utilisateurs. À n'utiliser que si vous devez transférer le\n"
"fichier le plus rapidement possible.\n"
"\n"
"Options :\n"
" -c  continuer le transfert. Nécessite le fichier <fichier-local>.lftp-pget-"
"status.\n"
" -n <maxconn>  définit le nombre maximum de connexions (la valeur par "
"défaut\n"
"    est obtenue dans le paramètre pget:default-n)\n"
" -O <base> définit le répertoire de base où seront placés les fichiers\n"

#: src/commands.cc:370
msgid "put [OPTS] <lfile> [-o <rfile>]"
msgstr "put [OPTS] <fichier-local> [-o <fichier-distant>]"

#: src/commands.cc:371
msgid ""
"Upload <lfile> with remote name <rfile>.\n"
" -o <rfile> specifies remote file name (default - basename of lfile)\n"
" -c  continue, reput\n"
"     it requires permission to overwrite remote files\n"
" -E  delete local files after successful transfer (dangerous)\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Téléverse <fichier-local> avec le nom distant <fichier-distant>.\n"
" -o <fichier-distant> spécifie le nom distant\n"
"     (par défaut : nom de fichier-local)\n"
" -c  continue (reput)\n"
"     cela nécessite la permission d'écraser les fichiers distants\n"
" -E  efface le fichier local après un transfert correctement effectué\n"
"     (dangeureux)\n"
" -a  utilise le mode ASCII (mode binaire par défaut)\n"
" -O <base> spécifie le répertoire de base ou l'URL où les fichiers doivent\n"
"     être placés.\n"

#: src/commands.cc:379
msgid ""
"Print current remote URL.\n"
" -p  show password\n"
msgstr ""
"Affiche l'URL distante courante.\n"
" -p  affiche le mot de passe\n"

#: src/commands.cc:381
msgid "queue [OPTS] [<cmd>]"
msgstr "queue [OPTS] [<cmd>]"

#: src/commands.cc:382
msgid ""
"\n"
"       queue [-n num] <command>\n"
"\n"
"Add the command to queue for current site. Each site has its own command\n"
"queue. `-n' adds the command before the given item in the queue. It is\n"
"possible to queue up a running job by using command `queue wait <jobno>'.\n"
"\n"
"       queue --delete|-d [index or wildcard expression]\n"
"\n"
"Delete one or more items from the queue. If no argument is given, the last\n"
"entry in the queue is deleted.\n"
"\n"
"       queue --move|-m <index or wildcard expression> [index]\n"
"\n"
"Move the given items before the given queue index, or to the end if no\n"
"destination is given.\n"
"\n"
"Options:\n"
" -q                  Be quiet.\n"
" -v                  Be verbose.\n"
" -Q                  Output in a format that can be used to re-queue.\n"
"                     Useful with --delete.\n"
msgstr ""
"\n"
"       queue [-n num] <commande>\n"
"\n"
"Ajoute la commande à la file d'attente pour le site courant. Chaque site a "
"sa\n"
"propre file de commandes. « -n » ajoute la commande avant l'élément indiqué\n"
"dans la file. Il est possible de rajouter en tête de file une tâche en "
"cours\n"
"avec la commande « queue wait <jobno> ».\n"
"\n"
"       queue --delete|-d [index ou expression de caractères génériques]\n"
"\n"
"Supprime un ou plusieurs éléments de la file. Si aucun argument n'est "
"donné,\n"
"le dernier élément de la file est supprimé.\n"
"\n"
"       queue --move|-m <index ou expression de caractères génériques> "
"[index]\n"
"\n"
"Déplace les éléments donnés avant la place indiquée dans la file, ou à la\n"
"fin si aucune destination n'est donnée.\n"
"\n"
"Options :\n"
" -q                  Mode discret.\n"
" -v                  Mode volubile.\n"
" -Q                  Affiche dans un format réutilisable pour remettre en\n"
"                     file d'attente. Utile avec --delete.\n"

#: src/commands.cc:403
msgid "quote <cmd>"
msgstr "quote <cmd>"

#: src/commands.cc:404
msgid ""
"Send the command uninterpreted. Use with caution - it can lead to\n"
"unknown remote state and thus will cause reconnect. You cannot\n"
"be sure that any change of remote state because of quoted command\n"
"is solid - it can be reset by reconnect at any time.\n"
msgstr ""
"Envoie la commande sans l'interpréter. À utiliser avec prudence :\n"
"ceci peut mener à un état distant inconnu et donc provoquer une\n"
"reconnexion. Vous ne pouvez être certain que tout changement provoqué\n"
"par une commande envoyée avec quote sera définitif : il peut être\n"
"remis à zéro à tout moment par une reconnexion.\n"

#: src/commands.cc:409
msgid ""
"recls [<args>]\n"
"Same as `cls', but don't look in cache\n"
msgstr ""
"recls [<args>]\n"
"Identique à « cls », sauf que le cache n'est pas utilisé\n"

#: src/commands.cc:412
msgid ""
"Usage: reget [OPTS] <rfile> [-o <lfile>]\n"
"Same as `get -c'\n"
msgstr ""
"Utilisation : reget [OPTS] <fichier-distant> [-o <fichier-local>]\n"
"Identique à « get -c »\n"

#: src/commands.cc:415
msgid ""
"Usage: rels [<args>]\n"
"Same as `ls', but don't look in cache\n"
msgstr ""
"Utilisation : rels [<args>]\n"
"Identique à « ls », sauf que le cache n'est pas utilisé\n"

#: src/commands.cc:418
msgid ""
"Usage: renlist [<args>]\n"
"Same as `nlist', but don't look in cache\n"
msgstr ""
"Utilisation : renlist [<args>]\n"
"Identique à « nlist », sauf que le cache n'est pas utilisé\n"

#: src/commands.cc:420
msgid "repeat [OPTS] [delay] [command]"
msgstr "repeat [OPTS] [délai] [commande]"

#: src/commands.cc:422
msgid ""
"Usage: reput <lfile> [-o <rfile>]\n"
"Same as `put -c'\n"
msgstr ""
"Utilisation : reput <fichier-local> [-o <fichier-distant>]\n"
"Identique à « put -c »\n"

#: src/commands.cc:424
msgid "rm [-r] [-f] <files>"
msgstr "rm [-r] [-f] <fichiers>"

#: src/commands.cc:425
msgid ""
"Remove remote files\n"
" -r  recursive directory removal, be careful\n"
" -f  work quietly\n"
msgstr ""
"Efface des fichiers distants\n"
" -r  effacement de répertoire de manière récursive, soyez prudent\n"
" -f  travaille silencieusement\n"

#: src/commands.cc:428
msgid "rmdir [-f] <dirs>"
msgstr "rmdir [-f] <répertoires>"

#: src/commands.cc:429
msgid "Remove remote directories\n"
msgstr "Efface les répertoires distants\n"

#: src/commands.cc:430
msgid "scache [<session_no>]"
msgstr "scache [<num_session>]"

#: src/commands.cc:431
msgid "List cached sessions or switch to specified session number\n"
msgstr "Affiche les sessions en cache ou bascule sur la session spécifiée\n"

#: src/commands.cc:432
msgid "set [OPT] [<var> [<val>]]"
msgstr "set [OPT] [<var> [<val>]]"

#: src/commands.cc:433
msgid ""
"Set variable to given value. If the value is omitted, unset the variable.\n"
"Variable name has format ``name/closure'', where closure can specify\n"
"exact application of the setting. See lftp(1) for details.\n"
"If set is called with no variable then only altered settings are listed.\n"
"It can be changed by options:\n"
" -a  list all settings, including default values\n"
" -d  list only default values, not necessary current ones\n"
msgstr ""
"Définit la variable à la valeur indiquée. Si la valeur est omise, cela\n"
"efface la variable. Le format d'un nom de variable est « nom/restriction »\n"
"où la restriction permet de spécifier l'application exacte de la "
"définition.\n"
"Voir lftp(1) pour les détails.\n"
"Si set est appelée sans nom de variable, alors seules les définitions\n"
"modifiées sont affichées. Cela peut être modifié par les options :\n"
" -a  affiche toutes les définitions, même les valeurs par défaut\n"
" -d  affiche uniquement les valeurs par défaut\n"

#: src/commands.cc:441
#, fuzzy
msgid "site <site-cmd>"
msgstr "site <cmd_site>"

#: src/commands.cc:442
msgid ""
"Execute site command <site_cmd> and output the result\n"
"You can redirect its output\n"
msgstr ""
"Exécute la commande site <cmd_site> et affiche le résultat.\n"
"Vous pouvez rediriger sa sortie\n"

#: src/commands.cc:446
msgid ""
"Usage: slot [<label>]\n"
"List assigned slots.\n"
"If <label> is specified, switch to the slot named <label>.\n"
msgstr ""
"Utilisation : slot [<étiquette>]\n"
"Affiche la liste des emplacements (slots) affectés.\n"
"Si <étiquette> est spécifié, basculer vers l'emplacement nommé <étiquette>.\n"

#: src/commands.cc:449
msgid "source <file>"
msgstr "source <fichier>"

#: src/commands.cc:450
msgid "Execute commands recorded in file <file>\n"
msgstr "Exécute les commandes enregistrées dans le fichier <fichier>\n"

#: src/commands.cc:452
#, fuzzy
msgid "torrent [OPTS] <file|URL>..."
msgstr "mget [OPTS] <fichiers>"

#: src/commands.cc:453
msgid "user <user|URL> [<pass>]"
msgstr "user <user|URL> [<pass>]"

#: src/commands.cc:454
msgid ""
"Use specified info for remote login. If you specify URL, the password\n"
"will be cached for future usage.\n"
msgstr ""
"Utilise les informations spécifiées pour la connexion. Si vous spécifiez\n"
"l'URL, le mot de passe sera enregistré pour une utilisation ultérieure.\n"

#: src/commands.cc:457
msgid "Shows lftp version\n"
msgstr "Affiche la version de lftp\n"

#: src/commands.cc:458
msgid "wait [<jobno>]"
msgstr "wait [<num_travail>]"

#: src/commands.cc:459
msgid ""
"Wait for specified job to terminate. If jobno is omitted, wait\n"
"for last backgrounded job.\n"
msgstr ""
"Attend que la tâche spécifiée se termine. Si num_travail est omis,\n"
"attend la dernière tâche mise en arrière-plan.\n"

#: src/commands.cc:461
msgid "zcat <files>"
msgstr "zcat <fichiers>"

#: src/commands.cc:462
msgid "Same as cat, but filter each file through zcat\n"
msgstr "Identique à cat, mais filtre chaque fichier à travers zcat\n"

#: src/commands.cc:463
msgid "zmore <files>"
msgstr "zmore <fichiers>"

#: src/commands.cc:464
msgid "Same as more, but filter each file through zcat\n"
msgstr "Identique à more, mais filtre chaque fichier à travers zcat\n"

#: src/commands.cc:466
msgid "Same as cat, but filter each file through bzcat\n"
msgstr "Identique à cat, mais filtre chaque fichier à travers bzcat\n"

#: src/commands.cc:468
msgid "Same as more, but filter each file through bzcat\n"
msgstr "Identique à more, mais filtre chaque fichier à travers bzcat\n"

#: src/commands.cc:524
#, c-format
msgid "Usage: %s local-dir\n"
msgstr "Utilisation : %s répertoire-local\n"

#: src/commands.cc:560
#, c-format
msgid "lcd ok, local cwd=%s\n"
msgstr "lcd ok, cwd local=%s\n"

#: src/commands.cc:576
#, c-format
msgid "Usage: cd remote-dir\n"
msgstr "Utilisation : cd répertoire-distant\n"

#: src/commands.cc:588
#, c-format
msgid "%s: no old directory for this site\n"
msgstr "%s : pas d'ancien répertoire pour ce site\n"

#: src/commands.cc:677
#, c-format
msgid "Usage: %s [<exit_code>]\n"
msgstr "Utilisation : %s [<code_sortie>]\n"

#: src/commands.cc:686
msgid ""
"There are running jobs and `cmd:move-background' is not set.\n"
"Use `exit bg' to force moving to background or `kill all' to terminate "
"jobs.\n"
msgstr ""
"Il y a des tâches en cours et « cmd:move-background » n'est pas défini.\n"
"Utilisez « exit bg » pour forcer le basculement en arrière-plan\n"
"ou « kill all » pour terminer toutes les tâches.\n"

#: src/commands.cc:703
msgid ""
"\n"
"lftp now tricks the shell to move it to background process group.\n"
"lftp continues to run in the background despite the `Stopped' message.\n"
"lftp will automatically terminate when all jobs are finished.\n"
"Use `fg' shell command to return to lftp if it is still running.\n"
msgstr ""

#: src/commands.cc:837 src/commands.cc:3616
#, c-format
msgid "Try `%s --help' for more information\n"
msgstr "Essayez « %s --help » pour plus d'informations\n"

#: src/commands.cc:856
#, c-format
msgid "%s: -c, -f, -v, -h conflict with other `open' options and arguments\n"
msgstr ""

#: src/commands.cc:956
#, c-format
msgid "Usage: %s [-e cmd] [-p port] [-u user[,pass]] <host|url>\n"
msgstr "Utilisation : %s [-e cmd] [-p port] [-u user[,pass]] <host|url>\n"

#: src/commands.cc:1046 src/commands.cc:2276 src/DummyProto.cc:65
#: src/MirrorJob.cc:2172 src/MirrorJob.cc:2194
msgid " - not supported protocol"
msgstr " - protocole non pris en charge"

#: src/commands.cc:1078 src/commands.cc:2260
msgid "Password: "
msgstr "Mot de passe : "

#: src/commands.cc:1080
#, c-format
msgid "%s: GetPass() failed -- assume anonymous login\n"
msgstr "%s : GetPass() a échoué -- passage en mode anonyme\n"

#: src/commands.cc:1191 src/commands.cc:1284 src/commands.cc:1660
#: src/commands.cc:1691 src/commands.cc:1829 src/commands.cc:1914
#: src/commands.cc:2187 src/commands.cc:2381 src/commands.cc:2543
#: src/commands.cc:2588 src/commands.cc:2616 src/commands.cc:2623
#: src/commands.cc:2930 src/commands.cc:2957 src/commands.cc:2964
#: src/commands.cc:3293 src/lftp.cc:267 src/MirrorJob.cc:2136
#: src/SleepJob.cc:144 src/SleepJob.cc:200 src/Torrent.cc:4169
#, c-format
msgid "Try `help %s' for more information.\n"
msgstr "Essayez « help %s » pour plus d'informations.\n"

#: src/commands.cc:1201
#, c-format
msgid "Usage: %s [OPTS] command args...\n"
msgstr "Utilisation : %s [OPTS] commande args...\n"

#: src/commands.cc:1254
#, c-format
msgid "%s: -n: positive number expected. "
msgstr "%s : -n : nombre positif attendu. "

#: src/commands.cc:1306
#, c-format
msgid "Created a stopped queue.\n"
msgstr "Crée une file d'attente stoppée.\n"

#: src/commands.cc:1349 src/commands.cc:1377
#, c-format
msgid "%s: No queue is active.\n"
msgstr "%s : aucune file d'attente active.\n"

#: src/commands.cc:1369
#, c-format
msgid "%s: -m: Number expected as second argument. "
msgstr "%s : -m : nombre attendu en tant que second argument. "

#: src/commands.cc:1421
#, c-format
msgid "Usage: %s <cmd>\n"
msgstr "Utilisation : %s <cmd>\n"

#: src/commands.cc:1527
msgid "invalid argument for `--sort'"
msgstr "argument invalide pour « --sort »"

#: src/commands.cc:1557
msgid "invalid block size"
msgstr "taille de bloc invalide"

#: src/commands.cc:1700
#, c-format
msgid "Usage: %s [OPTS] files...\n"
msgstr "Utilisation : %s [OPTS] fichier...\n"

#: src/commands.cc:1785 src/commands.cc:1814
#, fuzzy, c-format
msgid "%s: %s: Number expected. "
msgstr "%s : -n : Nombre attendu. "

#: src/commands.cc:1834
#, fuzzy, c-format
msgid "%s: --continue conflicts with --remove-target.\n"
msgstr "%s : summarize est en conflit avec --max-depth=%i\n"

#: src/commands.cc:1844 src/commands.cc:1920
#, c-format
msgid "File name missed. "
msgstr "Nom de fichier manquant. "

#: src/commands.cc:1989
#, c-format
msgid "Usage: %s %s[-f] files...\n"
msgstr "Utilisation : %s %s[-f] fichiers...\n"

#: src/commands.cc:2032
#, c-format
msgid "Usage: %s [-e] <file|command>\n"
msgstr "Utilisation : %s [-e] <fichier|commande>\n"

#: src/commands.cc:2079
#, c-format
msgid "Usage: %s [-v] [-v] ...\n"
msgstr "Utilisation : %s [-v] [-v] ...\n"

#: src/commands.cc:2093 src/commands.cc:2347 src/commands.cc:2469
#: src/commands.cc:2685 src/commands.cc:3101 src/commands.cc:3182
#: src/lftp.cc:279
#, c-format
msgid "%s: %s - not a number\n"
msgstr "%s : %s - n'est pas un nombre\n"

#: src/commands.cc:2100 src/commands.cc:2326 src/commands.cc:2356
#: src/commands.cc:2487
#, c-format
msgid "%s: %d - no such job\n"
msgstr "%s : %d - tâche inconnue\n"

#: src/commands.cc:2135
#, c-format
msgid "Usage: %s [-p]\n"
msgstr "Utilisation : %s [-p]\n"

#: src/commands.cc:2227
#, c-format
msgid "debug level is %d, output goes to %s\n"
msgstr "niveau de débogage %d, sortie sur %s\n"

#: src/commands.cc:2230
#, c-format
msgid "debug is off\n"
msgstr "débogage désactivé\n"

#: src/commands.cc:2241
#, c-format
msgid "Usage: %s <user|URL> [<pass>]\n"
msgstr "Utilisation : %s <user|URL> [<pass>]\n"

#: src/commands.cc:2316 src/commands.cc:2479
#, c-format
msgid "%s: no current job\n"
msgstr "%s : pas de tâche en cours\n"

#: src/commands.cc:2328
#, c-format
msgid "Usage: %s <jobno> ... | all\n"
msgstr "Utilisation : %s <num_tache> | all\n"

#: src/commands.cc:2409
#, c-format
msgid "%s: %s. Use `set -a' to look at all variables.\n"
msgstr "%s : %s. Utilisez « set -a » pour visualiser toutes les variables.\n"

#: src/commands.cc:2453
#, c-format
msgid "Usage: %s [<jobno>]\n"
msgstr "Utilisation : %s [<num_tache>]\n"

#: src/commands.cc:2492
#, c-format
msgid "%s: some other job waits for job %d\n"
msgstr "%s : d'autres tâches attendent la tâche %d\n"

#: src/commands.cc:2497
#, c-format
msgid "%s: wait loop detected\n"
msgstr "%s : boucle infinie détectée\n"

#: src/commands.cc:2553
#, fuzzy, c-format
msgid "Usage: %s [OPTS] <files> <target-dir>\n"
msgstr "Utilisation : %s [OPTS] fichier\n"

#: src/commands.cc:2615 src/commands.cc:2956
#, c-format
msgid "Invalid command. "
msgstr "Commande invalide. "

#: src/commands.cc:2622 src/commands.cc:2963
#, c-format
msgid "Ambiguous command. "
msgstr "Commande ambiguë. "

#: src/commands.cc:2641
#, c-format
msgid "%s: Operand missed for size\n"
msgstr "%s : la taille n'a pas été indiquée\n"

#: src/commands.cc:2658
#, c-format
msgid "%s: Operand missed for `expire'\n"
msgstr "%s : opérande manquant pour « expire »\n"

#: src/commands.cc:2691
#, c-format
msgid ""
"%s: %s - no such cached session. Use `scache' to look at session list.\n"
msgstr ""
"%s : %s - pas de telle session en cache.\n"
"Utilisez « scache » pour afficher la liste des sessions.\n"

#: src/commands.cc:2715
#, c-format
msgid "Sorry, no help for %s\n"
msgstr "Désolé, pas d'aide disponible pour %s\n"

#: src/commands.cc:2720
#, c-format
msgid "%s is a built-in alias for %s\n"
msgstr "%s est un alias interne pour %s\n"

#: src/commands.cc:2725
#, c-format
msgid "Usage: %s\n"
msgstr "Utilisation : %s\n"

#: src/commands.cc:2733
#, c-format
msgid "%s is an alias to `%s'\n"
msgstr "%s est un alias pour « %s »\n"

#: src/commands.cc:2737
#, c-format
msgid "No such command `%s'. Use `help' to see available commands.\n"
msgstr ""
"Commande « %s » inconnue.\n"
"Utilisez « help » pour afficher la liste des commandes disponibles.\n"

#: src/commands.cc:2739
#, c-format
msgid "Ambiguous command `%s'. Use `help' to see available commands.\n"
msgstr ""
"Commande « %s »ambiguë.\n"
"Utilisez « help » pour afficher la liste des commandes disponibles.\n"

#: src/commands.cc:2805
#, c-format
msgid "LFTP | Version %s | Copyright (c) 1996-%d Alexander V. Lukyanov\n"
msgstr "LFTP | Version %s | Copyright (c) 1996-%d Alexander V. Lukyanov\n"

#: src/commands.cc:2808
#, c-format
msgid ""
"LFTP is free software: you can redistribute it and/or modify\n"
"it under the terms of the GNU General Public License as published by\n"
"the Free Software Foundation, either version 3 of the License, or\n"
"(at your option) any later version.\n"
"\n"
"This program is distributed in the hope that it will be useful,\n"
"but WITHOUT ANY WARRANTY; without even the implied warranty of\n"
"MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n"
"GNU General Public License for more details.\n"
"\n"
"You should have received a copy of the GNU General Public License\n"
"along with LFTP.  If not, see <http://www.gnu.org/licenses/>.\n"
msgstr ""

#: src/commands.cc:2822
#, fuzzy, c-format
msgid "Send bug reports and questions to the mailing list <%s>.\n"
msgstr "Envoyez les rapports de bogues et les questions à <%s>.\n"

#: src/commands.cc:2828
msgid "Libraries used: "
msgstr "Bibliothèques utilisées : "

#: src/commands.cc:2979 src/commands.cc:3007
#, c-format
msgid "%s: bookmark name required\n"
msgstr "%s : nom de signet requis\n"

#: src/commands.cc:2996
#, c-format
msgid "%s: spaces in bookmark name are not allowed\n"
msgstr "%s : les espaces dans les noms de signets ne sont pas permises\n"

#: src/commands.cc:3009
#, c-format
msgid "%s: no such bookmark `%s'\n"
msgstr "%s : signet « %s » inconnu\n"

#: src/commands.cc:3032
#, c-format
msgid "%s: import type required (netscape,ncftp)\n"
msgstr "%s : type d'import requis (netscape,ncftp)\n"

#: src/commands.cc:3110
#, c-format
msgid "Usage: %s [-d #] dir\n"
msgstr "Utilisation : %s [-d #] répertoire\n"

#: src/commands.cc:3215
#, c-format
msgid "%s: invalid block size `%s'\n"
msgstr "%s : taille de bloc invalide « %s »\n"

#: src/commands.cc:3226
#, c-format
msgid "Usage: %s [options] <dirs>\n"
msgstr "Utilisation : %s [options] <répertoires>\n"

#: src/commands.cc:3232
#, c-format
msgid "%s: warning: summarizing is the same as using --max-depth=0\n"
msgstr "%s : attention : summarize revient à faire --max-depth=0\n"

#: src/commands.cc:3236
#, c-format
msgid "%s: summarizing conflicts with --max-depth=%i\n"
msgstr "%s : summarize est en conflit avec --max-depth=%i\n"

#: src/commands.cc:3280
#, c-format
msgid "Usage: %s command args...\n"
msgstr "Utilisation : %s commande args...\n"

#: src/commands.cc:3292
#, c-format
msgid "Usage: %s module [args...]\n"
msgstr "Utilisation : %s module [args...]\n"

#: src/commands.cc:3310 src/LocalAccess.cc:642
msgid "cannot get current directory"
msgstr "ne peut lire le répertoire en cours"

#: src/commands.cc:3374
#, c-format
msgid "Usage: %s [OPTS] mode file...\n"
msgstr "Utilisation : %s [OPTS] mode du fichier...\n"

#: src/commands.cc:3394
#, c-format
msgid "invalid mode string: %s\n"
msgstr "chaîne de mode invalide : %s\n"

#: src/commands.cc:3457 src/commands.cc:3466
msgid "Invalid range format. Format is min-max, e.g. 10-20."
msgstr "Format d'intervalle invalide. Le format est min-max, par ex. : 10-20."

#: src/commands.cc:3478
#, c-format
msgid "Usage: %s [OPTS] file\n"
msgstr "Utilisation : %s [OPTS] fichier\n"

#: src/CopyJob.cc:82
#, c-format
msgid "`%s' at %lld %s%s%s%s"
msgstr "« %s » à %lld %s%s%s%s"

#: src/CopyJob.cc:159
#, c-format
msgid "%lld $#ll#byte|bytes$ transferred in %ld $#l#second|seconds$"
msgstr "%lld octet$#ll# transféré|s transférés$ en %ld seconde$#l#|s$"

#: src/CopyJob.cc:167
#, c-format
msgid "%lld $#ll#byte|bytes$ transferred"
msgstr "%lld octet$#ll# transféré|s transférés"

#: src/CopyJob.cc:283
#, c-format
msgid "Transfer of %d of %d $file|files$ failed\n"
msgstr "Le transfert de %d sur %d fichier$|s$ a échoué\n"

#: src/CopyJob.cc:289
#, c-format
msgid "Total %d $file|files$ transferred\n"
msgstr "Total %d fichier$|s$ transférés\n"

#: src/FileAccess.cc:160
msgid "Access failed: "
msgstr "L'accès a échoué : "

#: src/FileAccess.cc:161
msgid "File cannot be accessed"
msgstr "Impossible d'accéder au fichier"

#: src/FileAccess.cc:163 src/Fish.cc:982 src/ftpclass.cc:4599
#: src/ftpclass.cc:4608 src/SFtp.cc:1339 src/Torrent.cc:3533
msgid "Not connected"
msgstr "Non connecté"

#: src/FileAccess.cc:166 src/FileAccess.cc:167
msgid "Fatal error"
msgstr "Erreur fatale"

#: src/FileAccess.cc:169
msgid "Store failed - you have to reput"
msgstr "Le stockage a échoué - vous devez renvoyer (reput)"

#: src/FileAccess.cc:172 src/FileAccess.cc:173
msgid "Login failed"
msgstr "L'authentification a échoué."

#: src/FileAccess.cc:176 src/FileAccess.cc:177
msgid "Operation not supported"
msgstr "Opération non prise en charge"

#: src/FileAccess.cc:180
msgid "File moved"
msgstr "Fichier déplacé"

#: src/FileAccess.cc:182
msgid "File moved to `"
msgstr "Fichier déplacé vers '"

#: src/FileCopy.cc:141
msgid "copy: destination file is already complete\n"
msgstr "copy : le fichier de destination est déjà terminé\n"

#: src/FileCopy.cc:182
msgid "copy: put is broken\n"
msgstr "copy: « put » brisé\n"

#: src/FileCopy.cc:201
msgid "seek failed"
msgstr "« seek » a échoué"

#: src/FileCopy.cc:207
msgid "no progress timeout"
msgstr ""

#: src/FileCopy.cc:235
msgid "cannot seek on data source"
msgstr "ne peut se déplacer sur la source"

#: src/FileCopy.cc:238
#, c-format
msgid "copy: put rolled back to %lld, seeking get accordingly\n"
msgstr "copy : « put » est revenu à %lld, « get » s'y ajuste\n"

#: src/FileCopy.cc:251
msgid "copy: all data received, but get rolled back\n"
msgstr "copy : toutes les données reçues, mais « get » est revenu en arrière\n"

#: src/FileCopy.cc:267
#, c-format
msgid "copy: get rolled back to %lld, seeking put accordingly\n"
msgstr "copy : « get » est revenu à %lld, « put » s'y ajuste\n"

#: src/FileCopy.cc:364
msgid "file size decreased during transfer"
msgstr ""

#: src/FileCopy.cc:1227
#, c-format
msgid "copy: received redirection to `%s'\n"
msgstr "copy : reçu une redirection vers « %s »\n"

#: src/FileCopy.cc:1290
#, fuzzy
#| msgid "saw file size in response"
msgid "file size increased during transfer"
msgstr "taille du fichier détectée dans la réponse"

#: src/FileCopy.cc:1390
msgid "file name missed in URL"
msgstr "nom de fichier manquant dans l'URL"

#: src/FileCopy.cc:2086
msgid "Verify command failed without a message"
msgstr "La commande verify a échoué sans message"

#: src/FileCopyFtp.cc:95
msgid "**** FXP: trying to reverse ftp:fxp-passive-source\n"
msgstr "**** FXP : tentative d'inversion de ftp:fxp-passive-source\n"

#: src/FileCopyFtp.cc:102
msgid "**** FXP: trying to reverse ftp:fxp-passive-sscn\n"
msgstr "**** FXP : tentative d'inversion de ftp:fxp-passive-sscn\n"

#: src/FileCopyFtp.cc:110
msgid "**** FXP: trying to reverse ftp:ssl-protect-fxp\n"
msgstr "**** FXP : tentative d'inversion de ftp:ssl-protect-fxp\n"

#: src/FileCopyFtp.cc:116
msgid "**** FXP: giving up, reverting to plain copy\n"
msgstr "**** FXP : abandon, retour à la copie brute\n"

#: src/FileCopyFtp.cc:125
msgid "ftp:fxp-force is set but FXP is not available"
msgstr "ftp:fxp-force est actif mais l'FXP n'est pas disponible"

#: src/FileCopy.h:285
msgid "Verifying..."
msgstr "Vérification..."

#: src/FileSetOutput.cc:230
msgid "non-option arguments found"
msgstr "certains arguments ne sont pas des options"

#: src/Filter.cc:166
msgid "pipe() failed: "
msgstr "pipe() a échoué : "

#: src/Filter.cc:200 src/PtyShell.cc:135
#, c-format
msgid "chdir(%s) failed: %s\n"
msgstr "chdir(%s) a échoué : %s\n"

#: src/Filter.cc:208
#, c-format
msgid "execvp(%s) failed: %s\n"
msgstr "execvp(%s) a échoué : %s\n"

#: src/Filter.cc:213 src/PtyShell.cc:147
#, c-format
msgid "execl(/bin/sh) failed: %s\n"
msgstr "execl(/bin/sh) a échoué : %s\n"

#: src/Filter.cc:415
#, fuzzy
msgid "file already exists and xfer:clobber is unset"
msgstr "%s : %s : le fichier existe déjà et xfer:clobber n'est pas défini\n"

#: src/FindJobDu.cc:101
msgid "total"
msgstr "total"

#: src/Fish.cc:75 src/ftpclass.cc:1292 src/Http.cc:1232 src/SFtp.cc:77
#, c-format
msgid "Closing idle connection"
msgstr "Fermeture de la connexion inutile"

#: src/Fish.cc:162 src/SFtp.cc:177
msgid "Running connect program"
msgstr "Exécution du programme de connexion"

#: src/Fish.cc:588 src/ftpclass.cc:2887 src/ftpclass.cc:3107 src/Http.cc:1510
#: src/SFtp.cc:742 src/SFtp.cc:1083 src/SFtp.cc:1084 src/SSH_Access.cc:167
#, c-format
msgid "Peer closed connection"
msgstr "Connexion interrompue par le tiers"

#: src/Fish.cc:634 src/ftpclass.cc:3037 src/ftpclass.cc:4219 src/SFtp.cc:1108
#, c-format
msgid "extra server response"
msgstr "réponse du serveur supplémentaire"

#: src/Fish.cc:987 src/ftpclass.cc:4611 src/Http.cc:2329 src/Http.cc:2336
#: src/SFtp.cc:1345 src/Torrent.cc:3536 src/TorrentTracker.cc:624
msgid "Connecting..."
msgstr "Connexion..."

#: src/Fish.cc:989 src/ftpclass.cc:4617 src/SFtp.cc:1347
msgid "Connected"
msgstr "Connecté"

#: src/Fish.cc:991 src/ftpclass.cc:4594 src/ftpclass.cc:4622
#: src/ftpclass.cc:4636 src/Http.cc:2338 src/SFtp.cc:1349
#: src/TorrentTracker.cc:626
msgid "Waiting for response..."
msgstr "En attente de réponse..."

#: src/Fish.cc:993 src/ftpclass.cc:4656 src/Http.cc:2341 src/SFtp.cc:1351
msgid "Receiving data"
msgstr "Réception des données"

#: src/Fish.cc:995 src/ftpclass.cc:4654 src/Http.cc:2334 src/SFtp.cc:1353
msgid "Sending data"
msgstr "Émission des données"

#: src/Fish.cc:997 src/SFtp.cc:1355
msgid "Done"
msgstr "Terminé"

#: src/Fish.cc:1136 src/FtpDirList.cc:123 src/HttpDir.cc:1384 src/SFtp.cc:2200
#: src/SFtp.cc:2320
#, c-format
msgid "Getting file list (%lld) [%s]"
msgstr "Récupération de la liste des fichiers (%lld) [%s]"

#: src/ftpclass.cc:197
#, c-format
msgid "Data connection peer has wrong port number"
msgstr "La connexion de données du tiers a un mauvais numéro de port"

#: src/ftpclass.cc:203
#, c-format
msgid "Data connection peer has mismatching address"
msgstr "La connexion de données du tiers a une adresse incorrecte"

#: src/ftpclass.cc:225 src/ftpclass.cc:250
#, c-format
msgid "Switching to NOREST mode"
msgstr "Passage en mode NOREST"

#: src/ftpclass.cc:425
#, c-format
msgid "Server reply matched ftp:retry-530, retrying"
msgstr "La réponse du serveur est de la forme ftp:retry-530, nouvel essai"

#: src/ftpclass.cc:433
#, c-format
msgid "Server reply matched ftp:retry-530-anonymous, retrying"
msgstr ""
"La réponse du serveur est de la forme ftp:retry-530-anonymous, nouvel essai"

#: src/ftpclass.cc:466
msgid "Account is required, set ftp:acct variable"
msgstr "Un compte est nécessaire, définissez la variable ftp:acct"

#: src/ftpclass.cc:483
msgid "ftp:skey-force is set and server does not support OPIE nor S/KEY"
msgstr "ftp:skey-force est défini et le serveur ne supporte pas OPIE ou S/KEY"

#: src/ftpclass.cc:501
#, c-format
msgid "assuming failed host name lookup"
msgstr "la résolution du nom de l'hôte a échoué"

#: src/ftpclass.cc:809 src/ftpclass.cc:810
#, c-format
msgid "cannot parse EPSV response"
msgstr "ne peut analyser la réponse EPSV"

#: src/ftpclass.cc:837 src/ftpclass.cc:838
#, fuzzy, c-format
#| msgid "cannot parse EPSV response"
msgid "cannot parse custom EPSV response"
msgstr "ne peut analyser la réponse EPSV"

#: src/ftpclass.cc:1122
#, c-format
msgid "Closing control socket"
msgstr "Fermeture du socket de contrôle"

#: src/ftpclass.cc:1341
msgid "MLSD is disabled by ftp:use-mlsd"
msgstr "MLSD est désactivée par ftp:use-mlsd"

#: src/ftpclass.cc:1411 src/Http.cc:1431 src/Torrent.cc:3204
#: src/Torrent.cc:3743 src/TorrentTracker.cc:395
#, c-format
msgid "cannot create socket of address family %d"
msgstr "ne peut créer un socket de la famille %d"

#: src/ftpclass.cc:1442 src/Http.cc:1457
#, c-format
msgid "Socket error (%s) - reconnecting"
msgstr "Erreur de socket (%s) - reconnexion"

#: src/ftpclass.cc:1715
#, fuzzy
msgid "MFF and SITE CHMOD are not supported by this site"
msgstr "SITE CHMOD n'est pas prise en charge par ce site"

#: src/ftpclass.cc:1720
msgid "MLST and MLSD are not supported by this site"
msgstr "MLST et MLSD ne sont pas prises en charge par ce site"

#: src/ftpclass.cc:2031
#, fuzzy
msgid "SITE SYMLINK is not supported by the server"
msgstr "MLST et MLSD ne sont pas prises en charge par ce site"

#: src/ftpclass.cc:2204
msgid "unsupported network protocol"
msgstr "protocole réseau non pris en charge"

#: src/ftpclass.cc:2253 src/ftpclass.cc:2355
#, fuzzy, c-format
msgid "Data socket error (%s) - reconnecting"
msgstr "Erreur de socket (%s) - reconnexion"

#: src/ftpclass.cc:2281
#, c-format
msgid "Accepted data connection from (%s) port %u"
msgstr "Connexion données acceptée du (%s) port %u"

#: src/ftpclass.cc:2330
#, c-format
msgid "Connecting data socket to (%s) port %u"
msgstr "Connexion du socket de données à (%s) port %u"

#: src/ftpclass.cc:2336
#, c-format
msgid "Connecting data socket to proxy %s (%s) port %u"
msgstr "Connexion du socket de données au proxy %s (%s) port %u"

#: src/ftpclass.cc:2358 src/ftpclass.cc:4414
#, c-format
msgid "Switching passive mode off"
msgstr "Désactivation du mode passif"

#: src/ftpclass.cc:2366
#, c-format
msgid "Data connection established"
msgstr "Connexion de donnée établie"

#: src/ftpclass.cc:2688 src/ftpclass.cc:4548
msgid "ftp:ssl-force is set and server does not support or allow SSL"
msgstr "ftp:ssl-force est défini et le serveur ne supporte pas SSL"

#: src/ftpclass.cc:3053
#, c-format
msgid "Persist and retry"
msgstr "Persiste et réessaye"

#: src/ftpclass.cc:3321
#, c-format
msgid "Closing data socket"
msgstr "Fermeture du socket de données"

#: src/ftpclass.cc:3343
#, c-format
msgid "Closing aborted data socket"
msgstr "Fermeture du socket de données avortée"

#: src/ftpclass.cc:4193
#, c-format
msgid "saw file size in response"
msgstr "taille du fichier détectée dans la réponse"

#: src/ftpclass.cc:4233 src/ftpclass.cc:4260
#, c-format
msgid "Turning on sync-mode"
msgstr "Activation de sync-mode"

#: src/ftpclass.cc:4434
#, c-format
msgid "Switching passive mode on"
msgstr "Activation du mode passif"

#: src/ftpclass.cc:4585
msgid "FEAT negotiation..."
msgstr "négociation FEAT..."

#: src/ftpclass.cc:4592
msgid "Sending commands..."
msgstr "Émission des commandes..."

#: src/ftpclass.cc:4596
msgid "Delaying before retry"
msgstr "Attente avant nouvelle tentative"

#: src/ftpclass.cc:4597 src/Http.cc:2331
msgid "Connection idle"
msgstr "Connexion stagnante"

#: src/ftpclass.cc:4604 src/Http.cc:2323 src/Resolver.cc:172
#: src/Resolver.cc:217 src/TorrentTracker.cc:622
#, c-format
msgid "Resolving host address..."
msgstr "Résolution de l'adresse..."

#: src/ftpclass.cc:4615
msgid "TLS negotiation..."
msgstr "Négociation TLS..."

#: src/ftpclass.cc:4619
msgid "Logging in..."
msgstr "Identification en cours..."

#: src/ftpclass.cc:4623
msgid "Making data connection..."
msgstr "Établissement de la connexion de données..."

#: src/ftpclass.cc:4626
msgid "Changing remote directory..."
msgstr "Changement de répertoire distant..."

#: src/ftpclass.cc:4632
msgid "Waiting for other copy peer..."
msgstr "En attente de la copie distante..."

#: src/ftpclass.cc:4634 src/ftpclass.cc:4658
msgid "Waiting for transfer to complete"
msgstr "En attente de la fin du transfert"

#: src/ftpclass.cc:4638
msgid "Waiting for TLS shutdown..."
msgstr "En attente de l'arrêt de TLS..."

#: src/ftpclass.cc:4640
msgid "Waiting for data connection..."
msgstr "En attente de la connexion de données..."

#: src/ftpclass.cc:4646
msgid "Sending data/TLS"
msgstr "Émission des données/TLS"

#: src/ftpclass.cc:4648
msgid "Receiving data/TLS"
msgstr "Réception des données/TLS"

#: src/Http.cc:230
#, c-format
msgid "Closing HTTP connection"
msgstr "Fermeture de la connexion HTTP"

#: src/Http.cc:240
msgid "POST method failed"
msgstr "échec de la méthode POST"

#: src/Http.cc:1381
msgid "ftp over http cannot work without proxy, set hftp:proxy."
msgstr ""
"ftp à travers http ne peut travailler sans proxy, définissez hftp:proxy."

#: src/Http.cc:1537
#, c-format
msgid "Sending request..."
msgstr "Émission des requêtes..."

#: src/Http.cc:1575
#, c-format
msgid "Hit EOF while fetching headers"
msgstr "Reçu une fin de fichier en lisant les entêtes"

#: src/Http.cc:1695
#, c-format
msgid "Could not parse HTTP status line"
msgstr "Ne peut analyser la ligne d'état HTTP"

#: src/Http.cc:1726
msgid "Object is not cached and http:cache-control has only-if-cached"
msgstr "L'objet n'est pas caché, et http:cache-control a only-if-cached"

#: src/Http.cc:1891
#, c-format
msgid "Receiving body..."
msgstr "Réception du corps..."

#: src/Http.cc:2092
#, c-format
msgid "Hit EOF"
msgstr "Fin de fichier atteinte"

#: src/Http.cc:2095
#, c-format
msgid "Received not enough data, retrying"
msgstr "Pas assez de données reçues, nouvelle tentative"

#: src/Http.cc:2106
#, c-format
msgid "Received all"
msgstr "Tout reçu"

#: src/Http.cc:2111
#, c-format
msgid "Received all (total)"
msgstr "Tout reçu (total)"

#: src/Http.cc:2135 src/Http.cc:2159
msgid "chunked format violated"
msgstr "format en « chunk » violé"

#: src/Http.cc:2145
#, c-format
msgid "Received last chunk"
msgstr "Dernier fragment reçu"

#: src/Http.cc:2339
msgid "Fetching headers..."
msgstr "Réception des entêtes..."

#: src/Job.cc:293
#, c-format
msgid "[%d] Done (%s)"
msgstr "[%d] Terminé (%s)"

#: src/lftp.cc:370
#, fuzzy, c-format
msgid "[%u] Terminated by signal %d. %s\n"
msgstr "[%lu] Terminé par le signal %d. %s"

#: src/lftp.cc:445
#, fuzzy, c-format
msgid "[%u] Started.  %s\n"
msgstr "[%lu] Démarré.  %s"

#: src/lftp.cc:463
#, fuzzy, c-format
msgid "[%u] Detaching from the terminal to complete transfers...\n"
msgstr "[%lu] Bascule en arrière-plan pour terminer les transferts...\n"

#: src/lftp.cc:465
#, c-format
msgid "[%u] Exiting and detaching from the terminal.\n"
msgstr ""

#: src/lftp.cc:470
#, c-format
msgid "[%u] Detached from terminal. %s\n"
msgstr ""

#: src/lftp.cc:480
#, fuzzy, c-format
msgid "[%u] Finished. %s\n"
msgstr "[%lu] Terminé. %s"

#: src/lftp.cc:484
#, fuzzy, c-format
msgid "[%u] Moving to background to complete transfers...\n"
msgstr "[%lu] Bascule en arrière-plan pour terminer les transferts...\n"

#: src/lftp.cc:553
msgid "history -w file|-r file|-c|-l [cnt]"
msgstr "historique : -w fichier|-r fichier|-c|-l [num]"

#: src/lftp.cc:554
msgid ""
" -w <file> Write history to file.\n"
" -r <file> Read history from file; appends to current history.\n"
" -c  Clear the history.\n"
" -l  List the history (default).\n"
"Optional argument cnt specifies the number of history lines to list,\n"
"or \"all\" to list all entries.\n"
msgstr ""
" -w <fichier> Écrit l'historique dans le fichier.\n"
" -r <fichier> Lit l'historique depuis le fichier      et l'ajoute à "
"l'historique en cours.\n"
" -c  Efface l'historique.\n"
" -l  Liste l'historique (défaut).\n"
"L'argument optionnel num spécifie le nombre de lignes d'historique à "
"afficher,\n"
"ou « all » pour afficher toutes les entrées.\n"

#: src/lftp.cc:561
msgid "Attach the terminal to specified backgrounded lftp process.\n"
msgstr ""

#: src/lftp_ssl.cc:1291
#, c-format
msgid "No certificate presented by %s.\n"
msgstr ""

#: src/LocalAccess.cc:604 src/NetAccess.cc:686
msgid "Getting directory contents"
msgstr "Récupération du contenu du répertoire"

#: src/LocalAccess.cc:606 src/NetAccess.cc:690
msgid "Getting files information"
msgstr "Lecture des informations de fichiers"

#: src/LsCache.cc:190
#, c-format
msgid "%ld $#l#byte|bytes$ cached"
msgstr "%ld octet$#l#|s$ dans le cache"

#: src/LsCache.cc:194
msgid ", no size limit"
msgstr ", aucune limite de taille"

#: src/LsCache.cc:196
#, c-format
msgid ", maximum size %ld\n"
msgstr ", taille maximum %ld\n"

#: src/mgetJob.cc:78
#, fuzzy, c-format
msgid "%s: %s: no files found\n"
msgstr "%s : aucun fichier trouvé\n"

#: src/MirrorJob.cc:100
#, c-format
msgid "%sTotal: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
msgstr ""
"%sTotal : %d répertoire$|s$, %d fichier$|s$, %d $lien symbolique|liens "
"symboliques$\n"

#: src/MirrorJob.cc:104
#, c-format
msgid "%sNew: %d file$|s$, %d symlink$|s$\n"
msgstr "%sNouveau : %d fichier$|s$, %d $lien symbolique|liens symboliques$\n"

#: src/MirrorJob.cc:108
#, c-format
msgid "%sModified: %d file$|s$, %d symlink$|s$\n"
msgstr "%sModifié : %d fichier$|s$, %d $lien symbolique|liens symboliques$\n"

#: src/MirrorJob.cc:115
#, c-format
msgid "%sRemoved: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
msgstr ""
"%sSupprimé : %d répertoire$|s$, %d fichier$|s$, %d $lien symbolique|liens "
"symboliques$\n"

#: src/MirrorJob.cc:120
#, c-format
msgid "%s%d error$|s$ detected\n"
msgstr "%s%d $erreur détectée|erreurs détectées$\n"

#: src/MirrorJob.cc:231
#, fuzzy, c-format
msgid "Finished %s"
msgstr "[%lu] Terminé. %s"

#: src/MirrorJob.cc:344 src/MirrorJob.cc:519 src/MirrorJob.cc:1218
#, c-format
msgid "Removing old file `%s'"
msgstr "Suppression de l'ancien fichier « %s »"

#: src/MirrorJob.cc:346
#, fuzzy, c-format
msgid "Overwriting old file `%s'"
msgstr "Suppression de l'ancien fichier « %s »"

#: src/MirrorJob.cc:355
#, c-format
msgid "Skipping file `%s' (only-existing)"
msgstr "On ignore le fichier « %s » (only-existing)"

#: src/MirrorJob.cc:361
#, c-format
msgid "Transferring file `%s'"
msgstr "Transfert du fichier « %s »"

#: src/MirrorJob.cc:439
#, c-format
msgid "Skipping directory `%s' (only-existing)"
msgstr "On ignore le répertoire « %s » (only-existing)"

#: src/MirrorJob.cc:461 src/MirrorJob.cc:550 src/MirrorJob.cc:902
#, c-format
msgid "Removing old local file `%s'"
msgstr "Suppression de l'ancien fichier local « %s »"

#: src/MirrorJob.cc:487
#, fuzzy, c-format
msgid "Scanning directory `%s'"
msgstr "Création du répertoire « %s »"

#: src/MirrorJob.cc:489
#, c-format
msgid "Mirroring directory `%s'"
msgstr "Duplication du répertoire « %s »"

#: src/MirrorJob.cc:525 src/MirrorJob.cc:567
#, c-format
msgid "Making symbolic link `%s' to `%s'"
msgstr "Création du lien symbolique « %s » vers « %s »"

#: src/MirrorJob.cc:562
#, c-format
msgid "Skipping symlink `%s' (only-existing)"
msgstr "On ignore le lien symbolique « %s » (only-existing)"

#: src/MirrorJob.cc:807
#, c-format
msgid "mirror: protocol `%s' is not suitable for mirror\n"
msgstr "mirror : le protocole « %s » ne convient pas pour une duplication\n"

#: src/MirrorJob.cc:923
#, c-format
msgid "Making directory `%s'"
msgstr "Création du répertoire « %s »"

#: src/MirrorJob.cc:1181
#, c-format
msgid "Old directory `%s' is not removed"
msgstr "L'ancien répertoire « %s » n'est pas supprimé"

#: src/MirrorJob.cc:1183
#, c-format
msgid "Old file `%s' is not removed"
msgstr "L'ancien fichier « %s » n'est pas supprimé"

#: src/MirrorJob.cc:1216
#, c-format
msgid "Removing old directory `%s'"
msgstr "Suppression de l'ancien répertoire « %s »"

#: src/MirrorJob.cc:1339
#, fuzzy, c-format
msgid "Removing source directory `%s'"
msgstr "Suppression de l'ancien répertoire « %s »"

#: src/MirrorJob.cc:1405
#, fuzzy, c-format
msgid "Removing source file `%s'"
msgstr "Suppression de l'ancien fichier « %s »"

#: src/MirrorJob.cc:1425
#, c-format
msgid "Retrying mirror...\n"
msgstr "Réessaye la commande mirror...\n"

#: src/MirrorJob.cc:1694
msgid "pattern is empty"
msgstr ""

#: src/MirrorJob.cc:1779
#, fuzzy, c-format
msgid "%s must be one of: %s"
msgstr "doit être C, S, E, P, ou vide"

#: src/MirrorJob.cc:2019
#, c-format
msgid ""
"%s: multiple --file or --directory options must have the same base "
"directory\n"
msgstr ""

#: src/MirrorJob.cc:2160
#, c-format
msgid "%s: ambiguous source directory (`%s' or `%s'?)\n"
msgstr ""

#: src/MirrorJob.cc:2182
#, fuzzy, c-format
msgid "%s: ambiguous target directory (`%s' or `%s'?)\n"
msgstr "Argument %s ambigu pour %s"

#: src/MirrorJob.cc:2223
#, c-format
msgid "%s: source directory is required (mirror:require-source is set)\n"
msgstr ""

#: src/MirrorJob.cc:2343
msgid ""
"\n"
"Mirror specified remote directory to local directory\n"
"\n"
" -R, --reverse          reverse mirror (put files)\n"
"Lots of other options are documented in the man page lftp(1).\n"
"\n"
"When using -R, the first directory is local and the second is remote.\n"
"If the second directory is omitted, basename of the first directory is "
"used.\n"
"If both directories are omitted, current local and remote directories are "
"used.\n"
"\n"
"See the man page lftp(1) for a complete documentation.\n"
msgstr ""

#: src/mkdirJob.cc:128
#, c-format
msgid "%s ok, `%s' created\n"
msgstr "%s ok, « %s » créé\n"

#: src/mkdirJob.cc:130 src/rmJob.cc:51
#, c-format
msgid "%s failed for %d of %d director$y|ies$\n"
msgstr "%s a échoué pour %d sur %d répertoire$|s$\n"

#: src/mkdirJob.cc:133
#, c-format
msgid "%s ok, %d director$y|ies$ created\n"
msgstr "%s ok, %d répertoire$ créé|s créés$\n"

#: src/module.cc:190
#, c-format
msgid "depend module `%s': %s\n"
msgstr "dépendance du module « %s » : %s\n"

#: src/module.cc:210
msgid "modules are not supported on this system"
msgstr "les modules ne sont pas pris en charge sur ce système"

#: src/mvJob.cc:92
#, c-format
msgid "rename successful\n"
msgstr "renommage réussi\n"

#: src/NetAccess.cc:168
#, c-format
msgid "Connecting to %s%s (%s) port %u"
msgstr "Connexion à %s%s (%s) port %u"

#: src/NetAccess.cc:224 src/Torrent.cc:3326
#, c-format
msgid "Timeout - reconnecting"
msgstr "Temps écoulé - reconnexion"

#: src/NetAccess.cc:323
msgid "Connection limit reached"
msgstr "Limite de connexions atteinte"

#: src/NetAccess.cc:330
msgid "Delaying before reconnect"
msgstr "Attente avant reconnexion"

#: src/NetAccess.cc:354 src/NetAccess.cc:356
msgid "max-retries exceeded"
msgstr "le nombre maximum de tentatives (max-retries) a été atteint"

#: src/OutputJob.cc:145
#, c-format
msgid "%s (filter)"
msgstr "%s (filtré)"

#: src/parsecmd.cc:290
msgid "parse: missing filter command\n"
msgstr "parse : commande de filtre manquante\n"

#: src/parsecmd.cc:292
msgid "parse: missing redirection filename\n"
msgstr "parse : fichier de redirection manquant\n"

#: src/PatternSet.cc:110
#, fuzzy, c-format
msgid "regular expression `%s': %s"
msgstr "%s : expression rationnelle « %s » : %s\n"

#: src/pgetJob.cc:127
msgid "pget: falling back to plain get"
msgstr "pget : recours à un « get » simple"

#: src/pgetJob.cc:131
msgid "the target file is remote"
msgstr "le fichier cible est distant"

#: src/pgetJob.cc:136
msgid "the source file size is unknown"
msgstr "la taille du fichier source est inconnue"

#: src/pgetJob.cc:173
#, c-format
msgid "pget: warning: space allocation for %s (%lld bytes) failed: %s\n"
msgstr ""

#: src/pgetJob.cc:234
#, c-format
msgid "`%s', got %lld of %lld (%d%%) %s%s"
msgstr "« %s », lu %lld sur %lld (%d%%) %s%s"

#: src/plural.c:96
msgid "=1 =0|>1"
msgstr "=1 =0|>1"

#: src/PollVec.cc:69
#, c-format
msgid "%s: BUG - deadlock detected\n"
msgstr "%s : BOGUE - boucle infinie détectée\n"

#: src/PtyShell.cc:68
msgid "pseudo-tty allocation failed: "
msgstr "l'allocation du pseudo-terminal a échoué : "

#: src/QueueFeeder.cc:68
msgid "Added job$|s$"
msgstr "Ajout $de la tâche|des tâches$"

#: src/QueueFeeder.cc:164 src/QueueFeeder.cc:185
#, c-format
msgid "No queued jobs.\n"
msgstr "Aucune tâche dans la file d'attente\n"

#: src/QueueFeeder.cc:166
#, c-format
msgid "No queued job #%i.\n"
msgstr "Aucune tâche #%i dans la file d'attente.\n"

#: src/QueueFeeder.cc:171 src/QueueFeeder.cc:192
msgid "Deleted job$|s$"
msgstr "$Tâche supprimée|Tâches supprimées$"

#: src/QueueFeeder.cc:187
#, c-format
msgid "No queued jobs match \"%s\".\n"
msgstr "Aucune tâche correspondant à « %s ».\n"

#: src/QueueFeeder.cc:212 src/QueueFeeder.cc:230
msgid "Moved job$|s$"
msgstr "$Tâche déplacée|Tâches déplacées$"

#: src/QueueFeeder.cc:354
msgid "Commands queued:"
msgstr "Commande en file d'attente :"

#: src/ResMgr.cc:123
msgid "no such variable"
msgstr "variable inconnue"

#: src/ResMgr.cc:127
msgid "ambiguous variable name"
msgstr "nom de variable ambigu"

#: src/ResMgr.cc:335
msgid "invalid boolean value"
msgstr "valeur booléenne invalide"

#: src/ResMgr.cc:357
msgid "invalid boolean/auto value"
msgstr "valeur booléenne/automatique invalide"

#: src/ResMgr.cc:402 src/ResMgr.cc:713
msgid "invalid number"
msgstr "nombre invalide"

#: src/ResMgr.cc:415
msgid "invalid floating point number"
msgstr "nombre en virgule flottante invalide"

#: src/ResMgr.cc:429
#, fuzzy
msgid "invalid unsigned number"
msgstr "nombre invalide"

#: src/ResMgr.cc:678
msgid "Invalid time unit letter, only [smhd] are allowed."
msgstr "Unité de temps invalide, seules [smhd] sont permises."

#: src/ResMgr.cc:686
msgid "Invalid time format. Format is <time><unit>, e.g. 2h30m."
msgstr ""
"Format de temps invalide. Le format est <temps><unité>, par ex. : 2h30m."

#: src/ResMgr.cc:718
#, fuzzy
msgid "integer overflow"
msgstr "Débordement de nombre entier"

#: src/ResMgr.cc:804
msgid "Invalid IPv4 numeric address"
msgstr "Adresse numérique IPv4 invalide"

#: src/ResMgr.cc:814
msgid "Invalid IPv6 numeric address"
msgstr "Adresse numérique IPv6 invalide"

#: src/ResMgr.cc:884 src/ResMgr.cc:888
msgid "this encoding is not supported"
msgstr "cet encodage n'est pas pris en charge"

#: src/ResMgr.cc:894
msgid "no closure defined for this setting"
msgstr "aucune restriction pour ce paramètre"

#: src/ResMgr.cc:900
#, fuzzy
msgid "a closure is required for this setting"
msgstr "aucune restriction pour ce paramètre"

#: src/Resolver.cc:236
msgid "host name resolve timeout"
msgstr "délai d'attente dépassé pour la résolution du nom de l'hôte"

#: src/Resolver.cc:282
#, c-format
msgid "%d address$|es$ found"
msgstr "%d: $adresse trouvée|adresses trouvées$"

#: src/Resolver.cc:327
msgid "Link-local IPv6 address should have a scope"
msgstr ""

#: src/Resolver.cc:765
msgid "DNS resolution not trusted."
msgstr ""

#: src/Resolver.cc:871
msgid "Host name lookup failure"
msgstr "La résolution du nom a échouée"

#: src/Resolver.cc:903
#, c-format
msgid "no such %s service"
msgstr "service %s inconnu"

#: src/Resolver.cc:930
msgid "No address found"
msgstr "Adresse non trouvée"

#: src/resource.cc:50 src/resource.cc:108
msgid "Proxy protocol unsupported"
msgstr "Protocole de proxy non pris en charge"

#: src/resource.cc:54
msgid "ftp:proxy password: "
msgstr "ftp:proxy mot de passe : "

#: src/resource.cc:70
#, c-format
msgid "%s must be one of: "
msgstr ""

#: src/resource.cc:72
#, fuzzy
msgid "must be one of: "
msgstr "doit être C, S, E, P, ou vide"

#: src/resource.cc:84
msgid ", or empty"
msgstr ""

#: src/resource.cc:116
msgid "only PUT and POST values allowed"
msgstr "seules les valeurs PUT et POST sont permises"

#: src/resource.cc:148
#, c-format
msgid "unknown address family `%s'"
msgstr "famille d'adresse « %s » inconnue"

#: src/rmJob.cc:47
#, c-format
msgid "%s ok, `%s' removed\n"
msgstr "%s ok, « %s » supprimé\n"

#: src/rmJob.cc:54
#, c-format
msgid "%s failed for %d of %d file$|s$\n"
msgstr "%s a échoué pour %d sur %d fichier$|s$\n"

#: src/rmJob.cc:60
#, c-format
msgid "%s ok, %d director$y|ies$ removed\n"
msgstr "%s ok, %d répertoire$ supprimé|s supprimés$\n"

#: src/rmJob.cc:63
#, c-format
msgid "%s ok, %d file$|s$ removed\n"
msgstr "%s ok, %d fichier$ supprimé|s supprimés$\n"

#: src/SFtp.cc:1099 src/SFtp.cc:1100
#, c-format
msgid "invalid server response format"
msgstr "format de réponse du serveur invalide"

#: src/SleepJob.cc:99
msgid "Sleeping forever"
msgstr ""

#: src/SleepJob.cc:100
msgid "Sleep time left: "
msgstr ""

#: src/SleepJob.cc:108
#, c-format
msgid "\tRepeat count: %d\n"
msgstr "\tNombre de répétitions : %d\n"

#: src/SleepJob.cc:142
#, c-format
msgid "%s: argument required. "
msgstr "%s : argument requis. "

#: src/SleepJob.cc:262
#, c-format
msgid "%s: date-time specification missed\n"
msgstr ""

#: src/SleepJob.cc:269
#, c-format
msgid "%s: date-time parse error\n"
msgstr ""

#: src/SleepJob.cc:303
msgid ""
"Usage: sleep <time>[unit]\n"
"Sleep for given amount of time. The time argument can be optionally\n"
"followed by unit specifier: d - days, h - hours, m - minutes, s - seconds.\n"
"By default time is assumed to be seconds.\n"
msgstr ""
"Utilisation : sleep <temps>[unité]\n"
"Sommeille pendant un temps donné. L'argument de temps peut être "
"éventuellement\n"
"suivi par une unité : d - jours, h - heures, m - minutes, s - secondes.\n"
"Par défaut, le temps est en seconde.\n"

#: src/SleepJob.cc:310
#, fuzzy
msgid ""
"Repeat specified command with a delay between iterations.\n"
"Default delay is one second, default command is empty.\n"
" -c <count>  maximum number of iterations\n"
" -d <delay>  delay between iterations\n"
" --while-ok  stop when command exits with non-zero code\n"
" --until-ok  stop when command exits with zero code\n"
" --weak      stop when lftp moves to background.\n"
msgstr ""
"Répète la commande spécifiée en respectant un délai entre chaque\n"
"tentative. Le délai par défaut est d'une seconde, la commande\n"
"par défaut est la commande vide.\n"
" -c <count>  nombre de tentatives\n"
" -d <delay>  délai entre chaque tentative.\n"

#: src/Speedometer.cc:90
#, c-format
msgid "%.0fb/s"
msgstr "%.0fo/s"

#: src/Speedometer.cc:93
#, c-format
msgid "%.1fK/s"
msgstr "%.1fKo/s"

#: src/Speedometer.cc:96
#, c-format
msgid "%.2fM/s"
msgstr "%.2fMo/s"

#: src/Speedometer.cc:103
#, fuzzy, c-format
msgid "%.0f B/s"
msgstr "%.0fo/s"

#: src/Speedometer.cc:105
#, fuzzy, c-format
msgid "%.1f KiB/s"
msgstr "%.1fKo/s"

#: src/Speedometer.cc:107
#, fuzzy, c-format
msgid "%.2f MiB/s"
msgstr "%.2fMo/s"

#: src/Speedometer.cc:129
msgid "eta:"
msgstr "reste :"

#: src/SSH_Access.cc:97
#, fuzzy
msgid "Password required"
msgstr "Mot de passe : "

#: src/SSH_Access.cc:102
msgid "Login incorrect"
msgstr ""

#: src/SSH_Access.cc:196
#, fuzzy, c-format
msgid "Disconnecting"
msgstr "Connexion..."

#: src/SysCmdJob.cc:73
#, c-format
msgid "execlp(%s) failed: %s\n"
msgstr "execlp(%s) a échoué : %s\n"

#: src/TimeDate.cc:155
msgid "day"
msgstr "jour"

#: src/TimeDate.cc:156
msgid "hour"
msgstr "heure"

#: src/TimeDate.cc:157
msgid "minute"
msgstr "minute"

#: src/TimeDate.cc:158
msgid "second"
msgstr "seconde"

#: src/Torrent.cc:585
msgid "announced via "
msgstr ""

#: src/Torrent.cc:599
#, c-format
msgid "next announce in %s"
msgstr ""

#: src/Torrent.cc:1142
#, c-format
msgid "%d file$|s$ found, now scanning %s"
msgstr ""

#: src/Torrent.cc:1143
#, fuzzy, c-format
msgid "%d file$|s$ found"
msgstr "%d: $adresse trouvée|adresses trouvées$"

#: src/Torrent.cc:2075 src/Torrent.cc:2085
#, c-format
msgid "Getting meta-data: %s"
msgstr ""

#: src/Torrent.cc:2077
#, c-format
msgid "Validation: %u/%u (%u%%) %s%s"
msgstr ""

#: src/Torrent.cc:2088
#, fuzzy
msgid "Waiting for meta-data..."
msgstr "\tEn attente de la commande\n"

#: src/Torrent.cc:2096
msgid "Shutting down: "
msgstr ""

#: src/Torrent.cc:3207
#, fuzzy, c-format
msgid "Connecting to peer %s port %u"
msgstr "Connexion à %s%s (%s) port %u"

#: src/Torrent.cc:3258 src/Torrent.cc:3375
#, fuzzy, c-format
msgid "peer unexpectedly closed connection after %s"
msgstr "Connexion interrompue par le tiers"

#: src/Torrent.cc:3259 src/Torrent.cc:3376
#, fuzzy
msgid "peer unexpectedly closed connection"
msgstr "Connexion interrompue par le tiers"

#: src/Torrent.cc:3261 src/Torrent.cc:3262
#, fuzzy, c-format
msgid "peer closed connection (before handshake)"
msgstr "Connexion interrompue par le tiers"

#: src/Torrent.cc:3265 src/Torrent.cc:3378 src/Torrent.cc:3379
#, fuzzy, c-format
msgid "invalid peer response format"
msgstr "format de réponse du serveur invalide"

#: src/Torrent.cc:3360 src/Torrent.cc:3361
#, fuzzy, c-format
msgid "peer closed connection"
msgstr "Connexion interrompue par le tiers"

#: src/Torrent.cc:3538
msgid "Handshaking..."
msgstr ""

#: src/Torrent.cc:3798
msgid "Cannot bind a socket for torrent:port-range"
msgstr ""

#: src/Torrent.cc:3858
#, fuzzy, c-format
msgid "Accepted connection from [%s]:%d"
msgstr "Connexion données acceptée du (%s) port %u"

#: src/Torrent.cc:3924
#, c-format
msgid "peer sent unknown info_hash=%s in handshake"
msgstr ""

#: src/Torrent.cc:3948
#, c-format
msgid "peer handshake timeout"
msgstr ""

#: src/Torrent.cc:3960
#, c-format
msgid "peer short handshake"
msgstr ""

#: src/Torrent.cc:3962
#, fuzzy, c-format
msgid "peer closed just accepted connection"
msgstr "Connexion interrompue par le tiers"

#: src/Torrent.cc:4013
#, fuzzy, c-format
msgid "Seeding in background...\n"
msgstr "Émission des commandes..."

#: src/Torrent.cc:4176
#, fuzzy, c-format
msgid "%s: --share conflicts with --output-directory.\n"
msgstr "%s : summarize est en conflit avec --max-depth=%i\n"

#: src/Torrent.cc:4180
#, fuzzy, c-format
msgid "%s: --share conflicts with --only-new.\n"
msgstr "%s : summarize est en conflit avec --max-depth=%i\n"

#: src/Torrent.cc:4184
#, fuzzy, c-format
msgid "%s: --share conflicts with --only-incomplete.\n"
msgstr "%s : summarize est en conflit avec --max-depth=%i\n"

#: src/Torrent.cc:4226
#, c-format
msgid "%s: Please specify a file or directory to share.\n"
msgstr ""

#: src/Torrent.cc:4228
#, c-format
msgid "%s: Please specify meta-info file or URL.\n"
msgstr ""

#: src/Torrent.cc:4258
msgid ""
"Start BitTorrent job for the given torrent-files, which can be a local "
"file,\n"
"URL, magnet link or plain info_hash written in hex or base32. Local "
"wildcards\n"
"are expanded. Options:\n"
" -O <base>      specifies base directory where files should be placed\n"
" --force-valid  skip file validation\n"
" --dht-bootstrap=<node>  bootstrap DHT by sending a query to the node\n"
" --share        share specified file or directory\n"
msgstr ""

#: src/TorrentTracker.cc:178
msgid "not started"
msgstr ""

#: src/TorrentTracker.cc:181
#, c-format
msgid "next request in %s"
msgstr ""

#: src/TorrentTracker.cc:284 src/TorrentTracker.cc:501
#, c-format
msgid "Received valid info about %d peer$|s$"
msgstr ""

#: src/TorrentTracker.cc:298
#, c-format
msgid "Received valid info about %d IPv6 peer$|s$"
msgstr ""

#, fuzzy
#~ msgid "%s: option '--%s' doesn't allow an argument\n"
#~ msgstr "%s : l'option « --%s » ne permet pas d'argument\n"

#, fuzzy
#~ msgid "%s: unrecognized option '--%s'\n"
#~ msgstr "%s : option « --%s » non reconnue\n"

#, fuzzy
#~ msgid "%s: option '-W %s' is ambiguous\n"
#~ msgstr "%s : l'option « -W %s » est ambiguë\n"

#, fuzzy
#~ msgid "%s: option '-W %s' doesn't allow an argument\n"
#~ msgstr "%s : l'option « -W %s » ne permet pas d'argument\n"

#, fuzzy
#~ msgid "%s: option '-W %s' requires an argument\n"
#~ msgstr "%s : l'option « %s » nécessite un argument\n"

#~ msgid "Usage: mv <file1> <file2>\n"
#~ msgstr "Utilisation : mv <fichier1> <fichier2>\n"

#, fuzzy
#~ msgid "number"
#~ msgstr "nombre invalide"

#, fuzzy
#~ msgid ""
#~ "\n"
#~ "Mirror specified remote directory to local directory\n"
#~ "\n"
#~ " -c, --continue         continue a mirror job if possible\n"
#~ " -e, --delete           delete files not present at remote site\n"
#~ "     --delete-first     delete old files before transferring new ones\n"
#~ " -s, --allow-suid       set suid/sgid bits according to remote site\n"
#~ "     --allow-chown      try to set owner and group on files\n"
#~ "     --ignore-time      ignore time when deciding whether to download\n"
#~ " -n, --only-newer       download only newer files (-c won't work)\n"
#~ " -r, --no-recursion     don't go to subdirectories\n"
#~ " -p, --no-perms         don't set file permissions\n"
#~ "     --no-umask         don't apply umask to file modes\n"
#~ " -R, --reverse          reverse mirror (put files)\n"
#~ " -L, --dereference      download symbolic links as files\n"
#~ " -N, --newer-than=SPEC  download only files newer than specified time\n"
#~ " -P, --parallel[=N]     download N files in parallel\n"
#~ " -i RX, --include RX    include matching files\n"
#~ " -x RX, --exclude RX    exclude matching files\n"
#~ "                        RX is extended regular expression\n"
#~ " -v, --verbose[=N]      verbose operation\n"
#~ "     --log=FILE         write lftp commands being executed to FILE\n"
#~ "     --script=FILE      write lftp commands to FILE, but don't execute "
#~ "them\n"
#~ "     --just-print, --dry-run    same as --script=-\n"
#~ "\n"
#~ "When using -R, the first directory is local and the second is remote.\n"
#~ "If the second directory is omitted, basename of first directory is used.\n"
#~ "If both directories are omitted, current local and remote directories are "
#~ "used.\n"
#~ "See lftp(1) for a complete list of options.\n"
#~ msgstr ""
#~ "\n"
#~ "Duplique le répertoire distant spécifié vers le répertoire local\n"
#~ "\n"
#~ " -c, --continue         continue, si c'est possible, un travail de "
#~ "duplication\n"
#~ " -e, --delete           efface les fichiers absents sur le site distant\n"
#~ "     --delete-first     efface les anciens fichiers avant d'en "
#~ "transférer                         de nouveaux\n"
#~ " -s, --allow-suid       duplique aussi les bits suid/sgid\n"
#~ "     --allow-chown      essaye de dupliquer aussi les propriétaires\n"
#~ "     --ignore-time      ignore l'heure pour décider ou non du "
#~ "téléchargement\n"
#~ " -n, --only-newer       télécharge uniquement les nouveaux fichiers\n"
#~ " -r, --no-recursion     n'entre pas dans les sous-répertoires\n"
#~ " -p, --no-perms         ne définit pas les permissions de fichier\n"
#~ "     --no-umask         n'applique pas umask aux modes de fichier\n"
#~ " -R, --reverse          duplication inversée (émet les fichiers)\n"
#~ " -L, --dereference      télécharge les liens symboliques comme des "
#~ "fichiers\n"
#~ " -N, --newer-than=SPEC  télécharge uniquement les fichiers plus jeunes "
#~ "que\n"
#~ "                        la date et l'heure spécifiées par SPEC\n"
#~ " -P, --parallel[=N]     télécharge N fichiers en parallèle\n"
#~ " -i RX, --include RX    inclut les fichiers correspondant à RX\n"
#~ " -x RX, --exclude RX    exclut les fichiers correspondant à RX\n"
#~ "                        RX est une expression régulière étendue\n"
#~ " -v, --verbose[=N]      mode volubile\n"
#~ "     --log=FILE         écrit dans FILE les commandes exécutées par lftp\n"
#~ "     --script=FILE      écrit dans FILE les commandes à exécuter par "
#~ "lftp\n"
#~ "                        mais ne les exécute pas.\n"
#~ "     --just-print, --dry-run    identique à --script=-\n"
#~ "\n"
#~ "Lorsque vous utilisez -R, le premier répertoire est local et le second "
#~ "est\n"
#~ "distant. Si le second répertoire est omis, le nom de base du premier est\n"
#~ "utilisé. Si les deux répertoires sont omis, les répertoires courants "
#~ "local\n"
#~ "et distant sont utilisés.\n"

#~ msgid "SITE CHMOD is disabled by ftp:use-site-chmod"
#~ msgstr "SITE CHMOD est désactivé par ftp:use-site-chmod"

#, fuzzy
#~ msgid ""
#~ "ftp:proxy-auth-type must be one of: user, joined, joined-acct, open, "
#~ "proxy-user@host"
#~ msgstr ""
#~ "ftp:proxy-auth-type doit être une valeur parmi : user, joined, joined-"
#~ "acct, open"

#~ msgid "ftp:ssl-auth must be one of: SSL, TLS, TLS-P, TLS-C"
#~ msgstr "ftp:ssl-auth doit être un de la liste : SSL, TLS, TLS-P, TLS-C"

#~ msgid "Invalid suffix. Valid suffixes are: k, M, G, T, P, E, Z, Y"
#~ msgstr ""
#~ "Suffixe invalide. Les suffixes valides sont : k, M, G, T, P, E, Z, Y"

#~ msgid "invalid pair of numbers"
#~ msgstr "paire de nombres invalide"

#~ msgid "Saw `unknown', assume failed login"
#~ msgstr "« unknow » détecté, échec de la connexion"

#~ msgid "Can only edit plain queues.\n"
#~ msgstr "Ne peut éditer que des files d'attente entières.\n"

#~ msgid "Couldn't create temporary file `%s': %s.\n"
#~ msgstr "Ne peut créer le fichier temporaire « %s » : %s.\n"

#~ msgid "%s: error writing %s: %s\n"
#~ msgstr "%s : erreur lors de l'écriture de %s : %s\n"

#~ msgid "%s: illegal option -- %c\n"
#~ msgstr "%s : option illégale -- %c\n"

#~ msgid ""
#~ "LFTP is free software, covered by the GNU General Public License, and you "
#~ "are\n"
#~ "welcome to change it and/or distribute copies of it under certain "
#~ "conditions.\n"
#~ "There is absolutely no warranty for LFTP.  See COPYING for details.\n"
#~ msgstr ""
#~ "LFTP est un logiciel libre, distribué sous la licence GNU General Public\n"
#~ "License, et vous êtes encouragé à le modifier et/ou en distribuer des\n"
#~ "copies sous certaines conditions. LFTP est distribué sans aucune "
#~ "garantie.\n"
#~ "Veuillez consulter le fichier COPYING pour les détails.\n"

#~ msgid "block size"
#~ msgstr "taille de bloc"
