Begin3
Title:		LFTP command line file transfer program
Version:	4.6.4
Entered-date:	2015-08-20
Description:	LFTP is a shell-like command line file transfer program. It is
		reliable: can retry operations and does reget automatically.
		It can do several transfers simultaneously in background.
		You can start a transfer in background and continue browsing
		the ftp site or another one. This all is done in one process.
		Background jobs will be completed in nohup mode if you exit
		or close modem connection. Lftp has reput, mirror, reverse
		mirror among its features. Since version 2.0 it also supports
		http protocol. Other features include: ipv6 support, context
		sensitive completion, output redirection to files or to pipe,
		SOCKS support (configure option), ftp and http proxy support,
		transfer rate throttling for each connection and for all
		connections in sum, job queueing, job execution at specified
		time, opie/skey support in ftp protocol, ssl for http and ftp,
		fxp transfers. Version 3.0 supports sftp v3 and v4 protocols.
		Version 4.0 supports BitTorrent protocol.
Keywords:	ftp, client, readline, reliable, background, parallel, http,
		protocol, network, fish, sftp, https, ftps, torrent
Author:		<EMAIL> (<PERSON>)
Maintained-by:	<EMAIL> (<PERSON>)
Primary-site:	ftp.yars.free.net /pub/source/lftp
		2593 kB lftp-4.6.4.tar.gz
Alternate-site:	metalab.unc.edu /pub/Linux/system/network/file-transfer
Platforms:	Unix, G++
Copying-policy: GNU GPL
End
