# Mensajes en castellano para LFTP.
# Copyright (C) 2002 Free Software Foundation, Inc.
# This file is distributed under the same license as the lftp package.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2000.
#
msgid ""
msgstr ""
"Project-Id-Version: lftp 2.6.0\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2024-11-08 13:05+0300\n"
"PO-Revision-Date: 2002-08-08 19:01+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Spanish <<EMAIL>>\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8-bit\n"

#: lib/argmatch.c:145
#, c-format
msgid "invalid argument %s for %s"
msgstr "%s argumento inválido para %s"

#: lib/argmatch.c:146
#, c-format
msgid "ambiguous argument %s for %s"
msgstr "%s argumento ambiguo para %s"

#: lib/argmatch.c:165
msgid "Valid arguments are:"
msgstr "Argumentos válidos son:"

#: lib/error.c:208
msgid "Unknown system error"
msgstr "Error de sistema desconocido"

#: lib/getopt.c:282
#, fuzzy, c-format
msgid "%s: option '%s%s' is ambiguous\n"
msgstr "%s: opción '%s' es ambigua\n"

#: lib/getopt.c:288
#, fuzzy, c-format
msgid "%s: option '%s%s' is ambiguous; possibilities:"
msgstr "%s: opción '%s' es ambigua; posibilidades:"

#: lib/getopt.c:322
#, fuzzy, c-format
msgid "%s: unrecognized option '%s%s'\n"
msgstr "%s: opción desconocida para `%c%s'\n"

#: lib/getopt.c:348
#, fuzzy, c-format
msgid "%s: option '%s%s' doesn't allow an argument\n"
msgstr "%s: opción '%c%s' no es válida como argumento\n"

#: lib/getopt.c:363
#, fuzzy, c-format
msgid "%s: option '%s%s' requires an argument\n"
msgstr "%s: opción '--%s' necesita un argumento\n"

#: lib/getopt.c:624
#, fuzzy, c-format
msgid "%s: invalid option -- '%c'\n"
msgstr "opción inválida"

#: lib/getopt.c:639 lib/getopt.c:685
#, c-format
msgid "%s: option requires an argument -- '%c'\n"
msgstr "%s: requiere un argumento -- '%c'\n"

#. TRANSLATORS:
#. Get translations for open and closing quotation marks.
#. The message catalog should translate "`" to a left
#. quotation mark suitable for the locale, and similarly for
#. "'".  For example, a French Unicode local should translate
#. these to U+00AB (LEFT-POINTING DOUBLE ANGLE
#. QUOTATION MARK), and U+00BB (RIGHT-POINTING DOUBLE ANGLE
#. QUOTATION MARK), respectively.
#.
#. If the catalog has no translation, we will try to
#. use Unicode U+2018 (LEFT SINGLE QUOTATION MARK) and
#. Unicode U+2019 (RIGHT SINGLE QUOTATION MARK).  If the
#. current locale is not Unicode, locale_quoting_style
#. will quote 'like this', and clocale_quoting_style will
#. quote "like this".  You should always include translations
#. for "`" and "'" even if U+2018 and U+2019 are appropriate
#. for your locale.
#.
#. If you don't know what to put here, please see
#. <https://en.wikipedia.org/wiki/Quotation_marks_in_other_languages>
#. and use glyphs suitable for your language.
#: lib/quotearg.c:354
msgid "`"
msgstr ""

#: lib/quotearg.c:355
msgid "'"
msgstr ""

#: lib/xalloc-die.c:34
msgid "memory exhausted"
msgstr "memoria agotada"

#: src/ArgV.cc:107
#, fuzzy
msgid "option requires an argument"
msgstr "%s: opción '--%s' necesita un argumento\n"

#: src/ArgV.cc:109 src/ArgV.cc:118
#, fuzzy
msgid "invalid option"
msgstr "opción inválida"

#: src/ArgV.cc:114
#, fuzzy, c-format
msgid "option `%s' requires an argument"
msgstr "%s: opción '--%s' necesita un argumento\n"

#: src/ArgV.cc:116
#, fuzzy, c-format
msgid "unrecognized option `%s'"
msgstr "%s: se recibió redirección a `%s'\n"

#: src/attach.h:138
#, fuzzy, c-format
msgid "[%u] Attached to terminal %s. %s\n"
msgstr "[%lu] Terminado por señal %d. %s"

#: src/attach.h:150
#, c-format
msgid "[%u] Attached to terminal.\n"
msgstr ""

#: src/buffer.cc:253 src/FileAccess.cc:866
#, fuzzy
msgid " [cached]"
msgstr "1 byte cacheado"

#: src/ChmodJob.cc:80
#, c-format
msgid "Failed to change mode of `%s' to %04o (%s).\n"
msgstr "Error al cambiar el modo de '%s' a '%04o' (%s).\n"

#: src/ChmodJob.cc:83
#, c-format
msgid "Mode of `%s' changed to %04o (%s).\n"
msgstr "Modo de '%s' cambiado a '%04o' (%s).\n"

#: src/ChmodJob.cc:88
#, c-format
msgid "Failed to change mode of `%s' because no old mode is available.\n"
msgstr ""
"Falló cambiar el modo de '%s' por no estar disponible el modo antiguo\n"

#: src/CmdExec.cc:105
#, c-format
msgid "Warning: chdir(%s) failed: %s\n"
msgstr "Aviso: falló chdir(%s): %s\n"

#: src/CmdExec.cc:207
#, c-format
msgid "Unknown command `%s'.\n"
msgstr "Orden desconocida `%s'.\n"

#: src/CmdExec.cc:209
#, c-format
msgid "Ambiguous command `%s'.\n"
msgstr "Orden ambigua `%s'.\n"

#: src/CmdExec.cc:228
#, c-format
msgid "Module for command `%s' did not register the command.\n"
msgstr "El módulo para la orden `%s' no registro esa orden.\n"

#: src/CmdExec.cc:384
#, c-format
msgid "cd ok, cwd=%s\n"
msgstr "cd ok, dir actual=%s\n"

#: src/CmdExec.cc:405 src/MirrorJob.cc:736
#, c-format
msgid "%s: received redirection to `%s'\n"
msgstr "%s: se recibió redirección a `%s'\n"

#: src/CmdExec.cc:408 src/FileCopy.cc:1230
msgid "Too many redirections"
msgstr "Demasiadas redirecciones"

#: src/CmdExec.cc:517 src/CmdExec.cc:574
msgid "Interrupt"
msgstr "Interrupción"

#: src/CmdExec.cc:639
#, c-format
msgid "Warning: discarding incomplete command\n"
msgstr "Aviso: descartando orden incompleta\n"

#: src/CmdExec.cc:737
#, c-format
msgid "\tExecuting builtin `%s' [%s]\n"
msgstr "\tEjecutando primitiva `%s' [%s]\n"

#: src/CmdExec.cc:742
msgid "Queue is stopped."
msgstr "Cola se ha detenido"

#: src/CmdExec.cc:747
msgid "Now executing:"
msgstr "Ejecutando ahora:"

#: src/CmdExec.cc:758
#, c-format
msgid "\tWaiting for job [%d] to terminate\n"
msgstr "\tEsperando que termine la tarea [%d]\n"

#: src/CmdExec.cc:761
#, c-format
msgid "\tWaiting for termination of jobs: "
msgstr "\tEsperando la finalización de las siguientes tareas: "

#: src/CmdExec.cc:770
msgid "\tRunning\n"
msgstr "\tEjecutando\n"

#: src/CmdExec.cc:772
msgid "\tWaiting for command\n"
msgstr "\tEsperando orden\n"

#: src/CmdExec.cc:1261
#, c-format
msgid "%s: command `%s' is not compiled in.\n"
msgstr "%s: la orden `%s' no fue compilada dentro del binario.\n"

#: src/CmdExec.cc:1278
#, fuzzy, c-format
msgid "Usage: %s cmd [args...]\n"
msgstr "Uso: %s módulo [args...]\n"

#: src/CmdExec.cc:1284
#, c-format
msgid "%s: cannot create local session\n"
msgstr "%s: no se pudo crear una sesión local\n"

#: src/commands.cc:114
msgid "!<shell-command>"
msgstr "!<orden-de-shell>"

#: src/commands.cc:115
msgid "Launch shell or shell command\n"
msgstr "Lanza un shell u órdenes de shell\n"

#: src/commands.cc:116
msgid "(commands)"
msgstr "(órdenes)"

#: src/commands.cc:117
msgid ""
"Group commands together to be executed as one command\n"
"You can launch such a group in background\n"
msgstr ""
"Agrupa órdenes para que sean ejecutadas como una sola.\n"
"Agrupadas así pueden lanzarse a correr en segundo plano.\n"

#: src/commands.cc:120
msgid "alias [<name> [<value>]]"
msgstr "alias [<nombre> [<valor>]]"

#: src/commands.cc:121
msgid ""
"Define or undefine alias <name>. If <value> omitted,\n"
"the alias is undefined, else is takes the value <value>.\n"
"If no argument is given the current aliases are listed.\n"
msgstr ""
"Define o borra la definición del alias <nombre>. Si se omite <valor>,\n"
"el alias queda indefinido, de otra manera toma el valor <valor>.\n"
"Si no se da un argumento muestra los alias actuales.\n"

#: src/commands.cc:125
msgid "anon - login anonymously (by default)\n"
msgstr "anon - se indentifica como anónimo (es así por omisión)\n"

#: src/commands.cc:127
msgid "bookmark [SUBCMD]"
msgstr "bookmark [SUBORDEN]"

#: src/commands.cc:128
msgid ""
"bookmark command controls bookmarks\n"
"\n"
"The following subcommands are recognized:\n"
"  add <name> [<loc>] - add current place or given location to bookmarks\n"
"                       and bind to given name\n"
"  del <name>         - remove bookmark with the name\n"
"  edit               - start editor on bookmarks file\n"
"  import <type>      - import foreign bookmarks\n"
"  list               - list bookmarks (default)\n"
msgstr ""
"La orden bookmark controla los señaladores\n"
"\n"
"Las siguientes son reconocidas:\n"
"  add <nombre> [<loc>] - agrega el lugar actual o la ubicación dada a los\n"
"                         señaladores y lo vincula con el nombre dado\n"
"  del <nombre>         - borra el señalador con ese nombre\n"
"  edit                 - lanza editor con el archivo de señaladores\n"
"  import <tipo>        - importa señaladores foráneos\n"
"  list                 - lista los señaladores (por omisión)\n"

#: src/commands.cc:137
msgid "cache [SUBCMD]"
msgstr "cache [SUBORDEN]"

#: src/commands.cc:138
#, fuzzy
msgid ""
"cache command controls local memory cache\n"
"\n"
"The following subcommands are recognized:\n"
"  stat        - print cache status (default)\n"
"  on|off      - turn on/off caching\n"
"  flush       - flush cache\n"
"  size <lim>  - set memory limit\n"
"  expire <Nx> - set cache expiration time to N seconds (x=s)\n"
"                minutes (x=m) hours (x=h) or days (x=d)\n"
msgstr ""
"La orden cache controla el caché local en memoria\n"
"\n"
"Las siguientes subórdenes son reconocidas:\n"
"  stat        - muestra el estado del caché (opción por omisión)\n"
"  on|off      - enciende/apaga el caché\n"
"  flush       - vacía el caché\n"
"  size <lím>  - asigna un límite de memoria, -1 significa sin límite\n"
"  expire <Nx> - asigna un tiempo de expiración en segundos (x=s)\n"
"                minutos (x=m) horas (x=h) o días (x=d)\n"

#: src/commands.cc:146
msgid "cat [-b] <files>"
msgstr "cat [-b] <archivos>"

#: src/commands.cc:147
msgid ""
"cat - output remote files to stdout (can be redirected)\n"
" -b  use binary mode (ascii is the default)\n"
msgstr ""
"cat - envía archivos remotos a la salida estándar (puede redireccionarse)\n"
" -b  usa modo binario (por omisión se usa modo ascii)\n"

#: src/commands.cc:149
msgid "cd <rdir>"
msgstr "cd <dir_remoto>"

#: src/commands.cc:150
msgid ""
"Change current remote directory to <rdir>. The previous remote directory\n"
"is stored as `-'. You can do `cd -' to change the directory back.\n"
"The previous directory for each site is also stored on disk, so you can\n"
"do `open site; cd -' even after lftp restart.\n"
msgstr ""
"Cambia el directorio remoto actual a <dir_r>. El directorio previo es\n"
"almacenado como `-'. Ud. puede hacer `cd -' para volver a ese directorio.\n"
"El directorio previo correspondiente a cada sitio se almacena en disco, de\n"
"forma que Ud. puede hacer `open sitio; cd -' incluso después de haber\n"
"reiniciado lftp.\n"

#: src/commands.cc:154
msgid "chmod [OPTS] mode file..."
msgstr "chmod [OPTS] modo archivo..."

#: src/commands.cc:155
msgid ""
"Change the mode of each FILE to MODE.\n"
"\n"
" -c, --changes        - like verbose but report only when a change is made\n"
" -f, --quiet          - suppress most error messages\n"
" -v, --verbose        - output a diagnostic for every file processed\n"
" -R, --recursive      - change files and directories recursively\n"
"\n"
"MODE can be an octal number or symbolic mode (see chmod(1))\n"
msgstr ""

#: src/commands.cc:164
msgid ""
"Close idle connections. By default only with current server.\n"
" -a  close idle connections with all servers\n"
msgstr ""
"Cierra conexiones ociosas. Por omisión sólo las del servidor actual.\n"
" -a  cierra las conexiones ociosas con todos los servidores\n"

#: src/commands.cc:166
msgid "[re]cls [opts] [path/][pattern]"
msgstr ""

#: src/commands.cc:167
msgid ""
"List remote files. You can redirect output of this command to file\n"
"or via pipe to external command.\n"
"\n"
" -1                   - single-column output\n"
" -a, --all            - show dot files\n"
" -B, --basename       - show basename of files only\n"
"     --block-size=SIZ - use SIZ-byte blocks\n"
" -d, --directory      - list directory entries instead of contents\n"
" -F, --classify       - append indicator (one of /@) to entries\n"
" -h, --human-readable - print sizes in human readable format (e.g., 1K)\n"
"     --si             - likewise, but use powers of 1000 not 1024\n"
" -k, --kilobytes      - like --block-size=1024\n"
" -l, --long           - use a long listing format\n"
" -q, --quiet          - don't show status\n"
" -s, --size           - print size of each file\n"
"     --filesize       - if printing size, only print size for files\n"
" -i, --nocase         - case-insensitive pattern matching\n"
" -I, --sortnocase     - sort names case-insensitively\n"
" -D, --dirsfirst      - list directories first\n"
"     --sort=OPT       - \"name\", \"size\", \"date\"\n"
" -S                   - sort by file size\n"
" --user, --group, --perms, --date, --linkcount, --links\n"
"                      - show individual fields\n"
" --time-style=STYLE   - use specified time format\n"
"\n"
"By default, cls output is cached, to see new listing use `recls' or\n"
"`cache flush'.\n"
"\n"
"The variables cls-default and cls-completion-default can be used to\n"
"specify defaults for cls listings and completion listings, respectively.\n"
"For example, to make completion listings show file sizes, set\n"
"cls-completion-default to \"-s\".\n"
"\n"
"Tips: Use --filesize with -D to pack the listing better.  If you don't\n"
"always want to see file sizes, --filesize in cls-default will affect the\n"
"-s flag on the commandline as well.  Add `-i' to cls-completion-default\n"
"to make filename completion case-insensitive.\n"
msgstr ""

#: src/commands.cc:217
#, fuzzy
msgid "debug [OPTS] [<level>|off]"
msgstr "debug [<nivel>|off] [-o <archivo>]"

#: src/commands.cc:218
#, fuzzy
msgid ""
"Set debug level to given value or turn debug off completely.\n"
" -o <file>  redirect debug output to the file\n"
" -c  show message context\n"
" -p  show PID\n"
" -t  show timestamps\n"
msgstr ""
"Asigna el nivel de depuración al valor dado, o la desconecta completamente.\n"
" -o <archivo>  redirecciona la salida de depuración al archivo.\n"

#: src/commands.cc:223
msgid "du [options] <dirs>"
msgstr ""

#: src/commands.cc:224
msgid ""
"Summarize disk usage.\n"
" -a, --all             write counts for all files, not just directories\n"
"     --block-size=SIZ  use SIZ-byte blocks\n"
" -b, --bytes           print size in bytes\n"
" -c, --total           produce a grand total\n"
" -d, --max-depth=N     print the total for a directory (or file, with --"
"all)\n"
"                       only if it is N or fewer levels below the command\n"
"                       line argument;  --max-depth=0 is the same as\n"
"                       --summarize\n"
" -F, --files           print number of files instead of sizes\n"
" -h, --human-readable  print sizes in human readable format (e.g., 1K 234M "
"2G)\n"
" -H, --si              likewise, but use powers of 1000 not 1024\n"
" -k, --kilobytes       like --block-size=1024\n"
" -m, --megabytes       like --block-size=1048576\n"
" -S, --separate-dirs   do not include size of subdirectories\n"
" -s, --summarize       display only a total for each argument\n"
"     --exclude=PAT     exclude files that match PAT\n"
msgstr ""

#: src/commands.cc:242
#, fuzzy
msgid "edit [OPTS] <file>"
msgstr "mget [OPCS] <archivos>"

#: src/commands.cc:243
msgid ""
"Retrieve remote file to a temporary location, run a local editor on it\n"
"and upload the file back if changed.\n"
" -k  keep the temporary file\n"
" -o <temp>  explicit temporary file location\n"
msgstr ""

#: src/commands.cc:248
msgid "exit [<code>|bg]"
msgstr "exit [<código>|bg]"

#: src/commands.cc:249
msgid ""
"exit - exit from lftp or move to background if jobs are active\n"
"\n"
"If no jobs active, the code is passed to operating system as lftp\n"
"termination status. If omitted, exit code of last command is used.\n"
"`bg' forces moving to background if cmd:move-background is false.\n"
msgstr ""
"exit - salir de lftp o pasar a segundo plano si hay tareas activas\n"
"\n"
"Si no hay tareas activas, el código es pasado al sistema operativo\n"
"como el eastado de terminación de lftp. Si se omite, se usará el código\n"
"de salida de la última orden.\n"
"`bg' fuerza el pasaje a segundo plano incluso siendo falso\n"
"el parámetro cmd:move-background.\n"

#: src/commands.cc:255
#, fuzzy
msgid ""
"Usage: find [OPTS] [directory]\n"
"Print contents of specified directory or current directory recursively.\n"
"Directories in the list are marked with trailing slash.\n"
"You can redirect output of this command.\n"
" -d, --maxdepth=LEVELS  Descend at most LEVELS of directories.\n"
msgstr ""
"Uso: find [OPTS] [directorio]\n"
"Muestra el contenido del directorio especificado, recursivamente.\n"
"Los directorios en la lista serán marcados con un '/' al final.\n"
"Se puede redireccionar la salida de esta orden.\n"
" -d, --maxdepth=N  Desciende N niveles de directorios como mucho.\n"

#: src/commands.cc:260
msgid "get [OPTS] <rfile> [-o <lfile>]"
msgstr "get [OPCS] <arch_r> [-o <arch_l>]"

#: src/commands.cc:261
#, fuzzy
msgid ""
"Retrieve remote file <rfile> and store it to local file <lfile>.\n"
" -o <lfile> specifies local file name (default - basename of rfile)\n"
" -c  continue, resume transfer\n"
" -E  delete remote files after successful transfer\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Obtiene el archivo remoto <arch_r> y lo almacena en el\n"
"archivo local <arch_l>.\n"
" -o <arch_l> se usará como nombre del archivo local (por omisión: sólo\n"
"     el componente nombre de arch_r)\n"
" -c  continúa (como con reget)\n"
" -E  borra los archivos remotos después de una transferencia exitosa\n"
" -a  usa modo ascii (por omisión se usa modo binario)\n"
" -O <base> especifica un directorio base, o un URL, en el que se colocarán\n"
"     los archivos\n"

#: src/commands.cc:268
msgid "glob [OPTS] <cmd> <args>"
msgstr ""

#: src/commands.cc:270
#, fuzzy
msgid ""
"Expand wildcards and run specified command.\n"
"Options can be used to expand wildcards to list of files, directories,\n"
"or both types. Type selection is not very reliable and depends on server.\n"
"If entry type cannot be determined, it will be included in the list.\n"
" -f  plain files (default)\n"
" -d  directories\n"
" -a  all types\n"
" --exist      return zero exit code when the patterns expand to non-empty "
"list\n"
" --not-exist  return zero exit code when the patterns expand to an empty "
"list\n"
msgstr ""
"Expande comodines y ejecuta la orden especificada.\n"
"Se pueden usar opciones para expandir los comodines a listas de archivos,\n"
"directorios, o a ambos tipos. La selección del tipo no es muy robusta\n"
"y depende del servidor. Si el tipo de una entrada no puede ser determinado,\n"
"esta será incluida en la lista.\n"
" -f  archivos comunes (por omisión)\n"
" -d  directorios\n"
" -a  todos los tipos\n"

#: src/commands.cc:279
msgid "help [<cmd>]"
msgstr "help [<ord>]"

#: src/commands.cc:280
msgid "Print help for command <cmd>, or list of available commands\n"
msgstr "Muestra ayuda para la orden <ord>, o lista las órdenes disponibles\n"

#: src/commands.cc:282
#, fuzzy
msgid ""
"List running jobs. -v means verbose, several -v can be specified.\n"
"If <job_no> is specified, only list a job with that number.\n"
msgstr ""
"Lista las tareas activas. -v para mayor detalle, puede\n"
"especificarse -v más de una vez.\n"

#: src/commands.cc:284
msgid "kill all|<job_no>"
msgstr "kill all|<númtarea>"

#: src/commands.cc:285
msgid "Delete specified job with <job_no> or all jobs\n"
msgstr "Elimina la tarea especificada por <númtarea> o todas ellas\n"

#: src/commands.cc:286
msgid "lcd <ldir>"
msgstr "lcd <dirlocal>"

#: src/commands.cc:287
msgid ""
"Change current local directory to <ldir>. The previous local directory\n"
"is stored as `-'. You can do `lcd -' to change the directory back.\n"
msgstr ""
"Cambia el directorio local actual a <dir_l>. El directorio local previo\n"
"se almacena como `-'. Usted puede hacer `lcd -' para volver a ese\n"
"directorio.\n"

#: src/commands.cc:289
msgid "lftp [OPTS] <site>"
msgstr "lftp [OPCS] <servidor>"

#: src/commands.cc:290
#, fuzzy
msgid ""
"`lftp' is the first command executed by lftp after rc files\n"
" -f <file>           execute commands from the file and exit\n"
" -c <cmd>            execute the commands and exit\n"
" --norc              don't execute rc files from the home directory\n"
" --help              print this help and exit\n"
" --version           print lftp version and exit\n"
"Other options are the same as in `open' command:\n"
" -e <cmd>            execute the command just after selecting\n"
" -u <user>[,<pass>]  use the user/password for authentication\n"
" -p <port>           use the port for connection\n"
" -s <slot>           assign the connection to this slot\n"
" -d                  switch on debugging mode\n"
" <site>              host name, URL or bookmark name\n"
msgstr ""
"`lftp' es la primera orden ejecutada por lftp después de los archivos\n"
"de configuración.\n"
" -f <archivo>           ejecuta órdenes del archivo y sale\n"
" -c <ords>              ejecuta las órdenes y sale\n"
" --help                 muestra esta ayuda y sale\n"
" --version              muestra la versión de lftp y sale\n"
"Las demás opciones son las mismas que en la orden `open'\n"
" -e <ord>               ejecuta la orden justo después de seleccionar\n"
" -u <usuario>[,<clave>] usa usuario/clave para autenticación\n"
" -p <puerto>            usa el puerto para una conexión\n"
" <sitio>                servidor, URL o nombre de señalador\n"

#: src/commands.cc:303
#, fuzzy
msgid "ln [-s] <file1> <file2>"
msgstr "mv <archivo1> <archivo2>"

#: src/commands.cc:304
#, fuzzy
msgid "Link <file1> to <file2>\n"
msgstr "Renombra <archivo1> a <archivo2>\n"

#: src/commands.cc:308
msgid "ls [<args>]"
msgstr ""

#: src/commands.cc:309
msgid ""
"List remote files. You can redirect output of this command to file\n"
"or via pipe to external command.\n"
"By default, ls output is cached, to see new listing use `rels' or\n"
"`cache flush'.\n"
"See also `help cls'.\n"
msgstr ""
"Lista archivos remotos. Puede redirigir la salida de este comando a un\n"
"archivo o, a través de un pipe, a una orden externa.\n"
"Por omisión la salida de ls se mantiene en un caché. Para ver un listado\n"
"nuevo use `rels' o `cache flush'.\n"
"Vea también `help cls'.\n"

#: src/commands.cc:314
msgid "mget [OPTS] <files>"
msgstr "mget [OPCS] <archivos>"

#: src/commands.cc:315
#, fuzzy
msgid ""
"Gets selected files with expanded wildcards\n"
" -c  continue, resume transfer\n"
" -d  create directories the same as in file names and get the\n"
"     files into them instead of current directory\n"
" -E  delete remote files after successful transfer\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Obtiene los archivos seleccionados mediante el patrón\n"
" -c  continúa (como con reget)\n"
" -d  crea directorios como los haya en los nombres de los archivos\n"
"     y coloca los archivos en ellos en vez de en el directorio actual\n"
" -E  borra los archivos remotos después de una transferencia exitosa\n"
" -a  usa modo ascii (por omisión se usa modo binario)\n"
" -O <base> especifica un directorio base, o un URL, en el que se colocarán\n"
"     los archivos\n"

#: src/commands.cc:322
msgid "mirror [OPTS] [remote [local]]"
msgstr "mirror [OPCS] [remoto [local]]"

#: src/commands.cc:323
#, fuzzy
msgid "mkdir [OPTS] <dirs>"
msgstr "mget [OPCS] <archivos>"

#: src/commands.cc:324
#, fuzzy
msgid ""
"Make remote directories\n"
" -p  make all levels of path\n"
" -f  be quiet, suppress messages\n"
msgstr ""
"Crea directorios remotos\n"
" -p  crea todos los niveles de una ruta\n"

#: src/commands.cc:327
msgid "module name [args]"
msgstr "module nombre [args]"

#: src/commands.cc:328
msgid ""
"Load module (shared object). The module should contain function\n"
"   void module_init(int argc,const char *const *argv)\n"
"If name contains a slash, then the module is searched in current\n"
"directory, otherwise in directories specified by setting module:path.\n"
msgstr ""
"Inserta módulo (objeto compartido). El módulo debe contener la función\n"
"   void module_init(int argc,const char *const *argv)\n"
"Si el nombre contiene una barra, el módulo es buscado en el directorio\n"
"actual, sino en los directorios especificados por la variable module:path.\n"

#: src/commands.cc:332
msgid "more <files>"
msgstr "more <archivos>"

#: src/commands.cc:333
msgid "Same as `cat <files> | more'. if PAGER is set, it is used as filter\n"
msgstr ""
"Igual que `cat <archivos> | more'.\n"
"Si PAGER está asignada, se usa como filtro\n"

#: src/commands.cc:334
msgid "mput [OPTS] <files>"
msgstr "mput [OPCS] <archivos>"

#: src/commands.cc:335
msgid ""
"Upload files with wildcard expansion\n"
" -c  continue, reput\n"
" -d  create directories the same as in file names and put the\n"
"     files into them instead of current directory\n"
" -E  delete local files after successful transfer (dangerous)\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Envía los archivos seleccionados mediante el patrón\n"
" -c  continúa (como con reput)\n"
" -d  crea directorios como los haya en los nombres de los archivos\n"
"     y coloca los archivos en ellos en vez de en el directorio actual\n"
" -E  borra los archivos locales después de una transferencia exitosa\n"
"     (peligroso)\n"
" -a  usa modo ascii (por omisión se usa modo binario)\n"
" -O <base> especifica un directorio base, o un URL, en el que se colocarán\n"
"     los archivos\n"

#: src/commands.cc:342
msgid "mrm <files>"
msgstr "mrm <archivos>"

#: src/commands.cc:343
msgid "Removes specified files with wildcard expansion\n"
msgstr "Borra los archivos especificados mediante la expansión del patrón\n"

#: src/commands.cc:344
msgid "mv <file1> <file2>"
msgstr "mv <archivo1> <archivo2>"

#: src/commands.cc:345
msgid "Rename <file1> to <file2>\n"
msgstr "Renombra <archivo1> a <archivo2>\n"

#: src/commands.cc:346
#, fuzzy
msgid "mmv [OPTS] <files> <target-dir>"
msgstr "mget [OPCS] <archivos>"

#: src/commands.cc:347
msgid ""
"Move <files> to <target-directory> with wildcard expansion\n"
" -O <dir>  specifies the target directory (alternative way)\n"
msgstr ""

#: src/commands.cc:349
msgid "[re]nlist [<args>]"
msgstr ""

#: src/commands.cc:350
msgid ""
"List remote file names.\n"
"By default, nlist output is cached, to see new listing use `renlist' or\n"
"`cache flush'.\n"
msgstr ""
"Lista archivos remotos.\n"
"Por omisión la salida de ls se mantiene en un caché. Para ver un listado\n"
"nuevo use `renlist' o `cache flush'.\n"

#: src/commands.cc:353
msgid "open [OPTS] <site>"
msgstr "open [OPCS] <servidor>"

#: src/commands.cc:354
#, fuzzy
msgid ""
"Select a server, URL or bookmark\n"
" -e <cmd>            execute the command just after selecting\n"
" -u <user>[,<pass>]  use the user/password for authentication\n"
" -p <port>           use the port for connection\n"
" -s <slot>           assign the connection to this slot\n"
" -d                  switch on debugging mode\n"
" <site>              host name, URL or bookmark name\n"
msgstr ""
"Selecciona un servidor, URL o señalador\n"
" -e <ord>               ejecuta la orden justo después de seleccionar\n"
" -u <usuario>[,<clave>] usa usuario/clave para autenticación\n"
" -p <puerto>            usa el puerto para una conexión\n"
" <sitio>                servidor, URL o nombre de señalador\n"

#: src/commands.cc:361
msgid "pget [OPTS] <rfile> [-o <lfile>]"
msgstr "pget [OPCS] <arch_r> [-o <arch_l>]"

#: src/commands.cc:362
#, fuzzy
msgid ""
"Gets the specified file using several connections. This can speed up "
"transfer,\n"
"but loads the net heavily impacting other users. Use only if you really\n"
"have to transfer the file ASAP.\n"
"\n"
"Options:\n"
" -c  continue transfer. Requires <lfile>.lftp-pget-status file.\n"
" -n <maxconn>  set maximum number of connections (default is is taken from\n"
"     pget:default-n setting)\n"
" -O <base> specifies base directory where files should be placed\n"
msgstr ""
"Obtiene el archivo especificado usando varias conexiones. Esto puede "
"acelerar\n"
"la transferencia, pero carga fuertemente la red impactando a otros "
"usuarios.\n"
"Úselo sólo si realmente tiene que transferir el archivo tan rápido como sea\n"
"posible, o algún otro usuario podría enojarse :)\n"
"\n"
"Opciones:\n"
" -n <maxcon>   asigna el número máximo de conexiones (5 por omisión)\n"

#: src/commands.cc:370
msgid "put [OPTS] <lfile> [-o <rfile>]"
msgstr "put [OPCS] <arch_l> [-o <arch_r>]"

#: src/commands.cc:371
msgid ""
"Upload <lfile> with remote name <rfile>.\n"
" -o <rfile> specifies remote file name (default - basename of lfile)\n"
" -c  continue, reput\n"
"     it requires permission to overwrite remote files\n"
" -E  delete local files after successful transfer (dangerous)\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Envía <arch_l> con nombre remoto <arch_r>.\n"
" -o <arch_r> especifica el nombre remoto del archivo (por omisión - nombre\n"
"    base de <arch_l>).\n"
" -c continuar, reput\n"
"    requiere tener permiso para sobreescribir archivos remotos.\n"
" -E  borra los archivos remotos después de una transferencia exitosa\n"
"     (peligroso)\n"
" -a  usa modo ascii (por omisión se usa modo binario)\n"
" -O <base> especifica un directorio base, o un URL, en el que se colocarán\n"
"     los archivos\n"

#: src/commands.cc:379
msgid ""
"Print current remote URL.\n"
" -p  show password\n"
msgstr ""
"Muestra el URL remoto actual.\n"
" -p  muestra claves\n"

#: src/commands.cc:381
msgid "queue [OPTS] [<cmd>]"
msgstr "queue [OPTS] [<cmd>]"

#: src/commands.cc:382
msgid ""
"\n"
"       queue [-n num] <command>\n"
"\n"
"Add the command to queue for current site. Each site has its own command\n"
"queue. `-n' adds the command before the given item in the queue. It is\n"
"possible to queue up a running job by using command `queue wait <jobno>'.\n"
"\n"
"       queue --delete|-d [index or wildcard expression]\n"
"\n"
"Delete one or more items from the queue. If no argument is given, the last\n"
"entry in the queue is deleted.\n"
"\n"
"       queue --move|-m <index or wildcard expression> [index]\n"
"\n"
"Move the given items before the given queue index, or to the end if no\n"
"destination is given.\n"
"\n"
"Options:\n"
" -q                  Be quiet.\n"
" -v                  Be verbose.\n"
" -Q                  Output in a format that can be used to re-queue.\n"
"                     Useful with --delete.\n"
msgstr ""
"\n"
"       queue [-n núm] <orden>\n"
"\n"
"Agrega la orden a la cola del sitio actual. Cada sitio tiene su propia\n"
"cola de órdenes. `-n' agrega la órden antes del ítem de la cola con ese\n"
"número. Es posible encolar una tarea activa mediante la órden\n"
"`queue wait <númtarea>'.\n"
"\n"
"       queue --delete|-d [índice o expresión comodín]\n"
"\n"
"Borra uno o más items de la cola. Si no se especifica un argumento, se\n"
"borra última entrada.\n"
"\n"
"       queue --move|-m <índice o expresión comodín> [índice]\n"
"\n"
"Mueve los items indicados adelante del índice de la cola dado, o al\n"
"final si no se especifica destino.\n"
"\n"
"Opciones:\n"
" -q                  Modo silencioso.\n"
" -v                  Modo informativo.\n"
" -Q                  Salida en un formato que se puede usar para re-"
"encolar.\n"
"                     Útil con --delete.\n"

#: src/commands.cc:403
msgid "quote <cmd>"
msgstr "quote <orden>"

#: src/commands.cc:404
msgid ""
"Send the command uninterpreted. Use with caution - it can lead to\n"
"unknown remote state and thus will cause reconnect. You cannot\n"
"be sure that any change of remote state because of quoted command\n"
"is solid - it can be reset by reconnect at any time.\n"
msgstr ""
"Envía la orden sin interpretar. Use con cuidado - puede colocar al\n"
"servidor en un estado desconocido, y eso causará una reconexión.\n"
"Ud. no puede estar seguro de que ningún cambio en el estado del servidor\n"
"remoto se mantenga, ya que se puede perder el cambio en cualquier\n"
"momento debido a una reconexión.\n"

#: src/commands.cc:409
msgid ""
"recls [<args>]\n"
"Same as `cls', but don't look in cache\n"
msgstr ""
"recls [<args>]\n"
"Igual a `cls', pero sin tomar en cuenta el caché\n"

#: src/commands.cc:412
msgid ""
"Usage: reget [OPTS] <rfile> [-o <lfile>]\n"
"Same as `get -c'\n"
msgstr ""
"Uso: reget [OPCS] <arch_r> [-o <arch_l>]\n"
"Igual a `get -c'\n"

#: src/commands.cc:415
msgid ""
"Usage: rels [<args>]\n"
"Same as `ls', but don't look in cache\n"
msgstr ""
"Uso: rels [<args>]\n"
"Igual a `ls', pero sin tomar en cuenta el caché\n"

#: src/commands.cc:418
msgid ""
"Usage: renlist [<args>]\n"
"Same as `nlist', but don't look in cache\n"
msgstr ""
"Uso: renlist [<args>]\n"
"Igual a `nlist', pero sin tomar en cuenta el caché\n"

#: src/commands.cc:420
#, fuzzy
msgid "repeat [OPTS] [delay] [command]"
msgstr "repeat [lapso] [órden]"

#: src/commands.cc:422
msgid ""
"Usage: reput <lfile> [-o <rfile>]\n"
"Same as `put -c'\n"
msgstr ""
"Uso: reput <arch_l> [-o <arch_r>]\n"
"Igual a `put -c'\n"

#: src/commands.cc:424
msgid "rm [-r] [-f] <files>"
msgstr "rm [-r] [-f] <archivos>"

#: src/commands.cc:425
msgid ""
"Remove remote files\n"
" -r  recursive directory removal, be careful\n"
" -f  work quietly\n"
msgstr ""
"Borra archivos remotos\n"
" -r  borrado recursivo de directorios, sea cuidadoso\n"
" -f  trabaja en silencio\n"

#: src/commands.cc:428
msgid "rmdir [-f] <dirs>"
msgstr ""

#: src/commands.cc:429
msgid "Remove remote directories\n"
msgstr "Borra directorios remotos\n"

#: src/commands.cc:430
msgid "scache [<session_no>]"
msgstr "scache [<núm_sesión>]"

#: src/commands.cc:431
msgid "List cached sessions or switch to specified session number\n"
msgstr "Lista sesiones cacheadas o cambia al número de sesión especificado\n"

#: src/commands.cc:432
msgid "set [OPT] [<var> [<val>]]"
msgstr "set [OPT] [<var> [<val>]]"

#: src/commands.cc:433
msgid ""
"Set variable to given value. If the value is omitted, unset the variable.\n"
"Variable name has format ``name/closure'', where closure can specify\n"
"exact application of the setting. See lftp(1) for details.\n"
"If set is called with no variable then only altered settings are listed.\n"
"It can be changed by options:\n"
" -a  list all settings, including default values\n"
" -d  list only default values, not necessary current ones\n"
msgstr ""
"Asigna la variable a un valor dado. Si se omite el valor, borra la "
"variable.\n"
"El nombre de variable tiene el formato ``nombre/closure'', donde closure\n"
"puede especificar la exacta función del valor. Vea lftp(1) para más "
"detalles.\n"
"Si set se invoca sin una asignación entonces se muestran sólo los valores\n"
"modificados. Esto se puede cambiar con las opciones:\n"
" -a  lista todos los valores, incluyendo los por omisión\n"
" -d  lista sólo los valores por emisión, no necesariamente los actuales\n"

#: src/commands.cc:441
#, fuzzy
msgid "site <site-cmd>"
msgstr "site <orden_directa_al_sitio>"

#: src/commands.cc:442
msgid ""
"Execute site command <site_cmd> and output the result\n"
"You can redirect its output\n"
msgstr ""
"Ejecuta la orden directa al sitio y muestra el resultado\n"
"Ud. puede redireccionar su salida\n"

#: src/commands.cc:446
msgid ""
"Usage: slot [<label>]\n"
"List assigned slots.\n"
"If <label> is specified, switch to the slot named <label>.\n"
msgstr ""

#: src/commands.cc:449
msgid "source <file>"
msgstr "source <archivo>"

#: src/commands.cc:450
msgid "Execute commands recorded in file <file>\n"
msgstr "Ejecuta las órdenes almacenadas en el archivo <archivo>\n"

#: src/commands.cc:452
#, fuzzy
msgid "torrent [OPTS] <file|URL>..."
msgstr "mget [OPCS] <archivos>"

#: src/commands.cc:453
msgid "user <user|URL> [<pass>]"
msgstr "user <usuario|URL> [<clave>]"

#: src/commands.cc:454
msgid ""
"Use specified info for remote login. If you specify URL, the password\n"
"will be cached for future usage.\n"
msgstr ""
"Usar la información especificada para el login remoto. Si se especifica un\n"
"URL, la clave será retenida y reusada en futuras oportunidades.\n"

#: src/commands.cc:457
msgid "Shows lftp version\n"
msgstr "Muestra la versión de lftp\n"

#: src/commands.cc:458
msgid "wait [<jobno>]"
msgstr "wait [<númtarea>]"

#: src/commands.cc:459
msgid ""
"Wait for specified job to terminate. If jobno is omitted, wait\n"
"for last backgrounded job.\n"
msgstr ""
"Espera que termine la tarea especificada. Si se omite el número de tarea,\n"
"espera que termine la última tarea enviada a segundo plano.\n"

#: src/commands.cc:461
msgid "zcat <files>"
msgstr "zcat <archivos>"

#: src/commands.cc:462
msgid "Same as cat, but filter each file through zcat\n"
msgstr "Igual que cat, pero filtra cada archivo a través de zcat\n"

#: src/commands.cc:463
msgid "zmore <files>"
msgstr "zmore <archivos>"

#: src/commands.cc:464
msgid "Same as more, but filter each file through zcat\n"
msgstr "Igual que more, pero filtra cada archivo a través de zcat\n"

#: src/commands.cc:466
msgid "Same as cat, but filter each file through bzcat\n"
msgstr "Igual que cat, pero filtra cada archivo a través de bzcat\n"

#: src/commands.cc:468
msgid "Same as more, but filter each file through bzcat\n"
msgstr "Igual que more, pero filtra cada archivo a través de bzcat\n"

#: src/commands.cc:524
#, c-format
msgid "Usage: %s local-dir\n"
msgstr "Uso: %s dir-local\n"

#: src/commands.cc:560
#, c-format
msgid "lcd ok, local cwd=%s\n"
msgstr "lcd ok, dir local actual=%s\n"

#: src/commands.cc:576
#, c-format
msgid "Usage: cd remote-dir\n"
msgstr "Uso: cd dir-remoto\n"

#: src/commands.cc:588
#, c-format
msgid "%s: no old directory for this site\n"
msgstr "%s: no hay antiguo directorio para este sitio\n"

#: src/commands.cc:677
#, c-format
msgid "Usage: %s [<exit_code>]\n"
msgstr "Uso: %s [<código_de_salida>]\n"

#: src/commands.cc:686
msgid ""
"There are running jobs and `cmd:move-background' is not set.\n"
"Use `exit bg' to force moving to background or `kill all' to terminate "
"jobs.\n"
msgstr ""
"Hay tareas corriendo y el parámetro `cmd:move-background' es falso.\n"
"Use `exit bg' para forzar el pasaje a segundo plano, o `kill all'\n"
"para terminar esas tareas.\n"

#: src/commands.cc:703
msgid ""
"\n"
"lftp now tricks the shell to move it to background process group.\n"
"lftp continues to run in the background despite the `Stopped' message.\n"
"lftp will automatically terminate when all jobs are finished.\n"
"Use `fg' shell command to return to lftp if it is still running.\n"
msgstr ""

#: src/commands.cc:837 src/commands.cc:3616
#, c-format
msgid "Try `%s --help' for more information\n"
msgstr "Pruebe `%s --help' para mayor información\n"

#: src/commands.cc:856
#, c-format
msgid "%s: -c, -f, -v, -h conflict with other `open' options and arguments\n"
msgstr ""

#: src/commands.cc:956
#, c-format
msgid "Usage: %s [-e cmd] [-p port] [-u user[,pass]] <host|url>\n"
msgstr "Uso: %s [-e cmd] [-p puerto] [-u usuario[,clave]] <servidor|url>\n"

#: src/commands.cc:1046 src/commands.cc:2276 src/DummyProto.cc:65
#: src/MirrorJob.cc:2172 src/MirrorJob.cc:2194
msgid " - not supported protocol"
msgstr " - protocolo no soportado"

#: src/commands.cc:1078 src/commands.cc:2260
msgid "Password: "
msgstr "Clave: "

#: src/commands.cc:1080
#, c-format
msgid "%s: GetPass() failed -- assume anonymous login\n"
msgstr "%s: GetPass() falló -- asumiendo identificación anónima\n"

#: src/commands.cc:1191 src/commands.cc:1284 src/commands.cc:1660
#: src/commands.cc:1691 src/commands.cc:1829 src/commands.cc:1914
#: src/commands.cc:2187 src/commands.cc:2381 src/commands.cc:2543
#: src/commands.cc:2588 src/commands.cc:2616 src/commands.cc:2623
#: src/commands.cc:2930 src/commands.cc:2957 src/commands.cc:2964
#: src/commands.cc:3293 src/lftp.cc:267 src/MirrorJob.cc:2136
#: src/SleepJob.cc:144 src/SleepJob.cc:200 src/Torrent.cc:4169
#, c-format
msgid "Try `help %s' for more information.\n"
msgstr "Pruebe `help %s' para mayor información.\n"

#: src/commands.cc:1201
#, c-format
msgid "Usage: %s [OPTS] command args...\n"
msgstr "Uso: %s [OPCS] orden parámetros ...\n"

#: src/commands.cc:1254
#, fuzzy, c-format
msgid "%s: -n: positive number expected. "
msgstr "%s: -n: Se esperaba un número. "

#: src/commands.cc:1306
#, c-format
msgid "Created a stopped queue.\n"
msgstr ""

#: src/commands.cc:1349 src/commands.cc:1377
#, c-format
msgid "%s: No queue is active.\n"
msgstr "%s: Ninguna cola está activa.\n"

#: src/commands.cc:1369
#, c-format
msgid "%s: -m: Number expected as second argument. "
msgstr "%s: -m: Se esperaba un número como segundo argumento. "

#: src/commands.cc:1421
#, c-format
msgid "Usage: %s <cmd>\n"
msgstr "Uso: %s <orden>\n"

#: src/commands.cc:1527
msgid "invalid argument for `--sort'"
msgstr "argumento inválido para `--sort'"

#: src/commands.cc:1557
msgid "invalid block size"
msgstr "tamaño de bloque inválido"

#: src/commands.cc:1700
#, c-format
msgid "Usage: %s [OPTS] files...\n"
msgstr "Uso: %s [OPCS] archivos...\n"

#: src/commands.cc:1785 src/commands.cc:1814
#, fuzzy, c-format
msgid "%s: %s: Number expected. "
msgstr "%s: -n: Se esperaba un número. "

#: src/commands.cc:1834
#, c-format
msgid "%s: --continue conflicts with --remove-target.\n"
msgstr ""

#: src/commands.cc:1844 src/commands.cc:1920
#, c-format
msgid "File name missed. "
msgstr "Nombre de archivo no encontrado. "

#: src/commands.cc:1989
#, c-format
msgid "Usage: %s %s[-f] files...\n"
msgstr "Uso: %s %s[-f] archivos...\n"

#: src/commands.cc:2032
#, fuzzy, c-format
msgid "Usage: %s [-e] <file|command>\n"
msgstr "Uso: %s <archivo>\n"

#: src/commands.cc:2079
#, c-format
msgid "Usage: %s [-v] [-v] ...\n"
msgstr "Uso: %s [-v] [-v] ...\n"

#: src/commands.cc:2093 src/commands.cc:2347 src/commands.cc:2469
#: src/commands.cc:2685 src/commands.cc:3101 src/commands.cc:3182
#: src/lftp.cc:279
#, c-format
msgid "%s: %s - not a number\n"
msgstr "%s: %s - no es un número\n"

#: src/commands.cc:2100 src/commands.cc:2326 src/commands.cc:2356
#: src/commands.cc:2487
#, c-format
msgid "%s: %d - no such job\n"
msgstr "%s: %d - no existe tal tarea\n"

#: src/commands.cc:2135
#, c-format
msgid "Usage: %s [-p]\n"
msgstr "Uso: %s [-p]\n"

#: src/commands.cc:2227
#, c-format
msgid "debug level is %d, output goes to %s\n"
msgstr "el nivel de depuración es %d, la salida va a %s\n"

#: src/commands.cc:2230
#, c-format
msgid "debug is off\n"
msgstr "la depuración está desactivada\n"

#: src/commands.cc:2241
#, fuzzy, c-format
msgid "Usage: %s <user|URL> [<pass>]\n"
msgstr "user <usuario|URL> [<clave>]"

#: src/commands.cc:2316 src/commands.cc:2479
#, c-format
msgid "%s: no current job\n"
msgstr "%s: no hay una tarea seleccionada\n"

#: src/commands.cc:2328
#, c-format
msgid "Usage: %s <jobno> ... | all\n"
msgstr "Uso: %s <númtarea> ... | all\n"

#: src/commands.cc:2409
#, c-format
msgid "%s: %s. Use `set -a' to look at all variables.\n"
msgstr "%s: %s. Use `set -a' para ver todas las variables.\n"

#: src/commands.cc:2453
#, c-format
msgid "Usage: %s [<jobno>]\n"
msgstr "Uso: %s [<númtarea>]\n"

#: src/commands.cc:2492
#, c-format
msgid "%s: some other job waits for job %d\n"
msgstr "%s: hay otra tarea esperando por la tarea %d\n"

#  Juaa... me divierte mucho esta traducción, y ma sí... =)
#: src/commands.cc:2497
#, c-format
msgid "%s: wait loop detected\n"
msgstr "%s: bucle de espera detectado\n"

#: src/commands.cc:2553
#, fuzzy, c-format
msgid "Usage: %s [OPTS] <files> <target-dir>\n"
msgstr "Uso: %s [OPCS] archivo\n"

#: src/commands.cc:2615 src/commands.cc:2956
#, c-format
msgid "Invalid command. "
msgstr "Orden inválida."

#: src/commands.cc:2622 src/commands.cc:2963
#, c-format
msgid "Ambiguous command. "
msgstr "Orden ambigua. "

#: src/commands.cc:2641
#, c-format
msgid "%s: Operand missed for size\n"
msgstr "%s: Falta operando para size\n"

#: src/commands.cc:2658
#, c-format
msgid "%s: Operand missed for `expire'\n"
msgstr "%s: Falta operando para `expire'\n"

#: src/commands.cc:2691
#, c-format
msgid ""
"%s: %s - no such cached session. Use `scache' to look at session list.\n"
msgstr "%s: %s - no hay tal sesión cacheada. Use `scache' para ver la lista.\n"

#: src/commands.cc:2715
#, c-format
msgid "Sorry, no help for %s\n"
msgstr "Lo siento, no hay ayuda para %s\n"

#: src/commands.cc:2720
#, c-format
msgid "%s is a built-in alias for %s\n"
msgstr "%s es un alias de fábrica para %s\n"

#: src/commands.cc:2725
#, c-format
msgid "Usage: %s\n"
msgstr "Uso: %s\n"

#: src/commands.cc:2733
#, c-format
msgid "%s is an alias to `%s'\n"
msgstr "%s es un alias para `%s'\n"

#: src/commands.cc:2737
#, c-format
msgid "No such command `%s'. Use `help' to see available commands.\n"
msgstr "No hay una orden `%s'. Use `help' para ver las órdenes disponibles.\n"

#: src/commands.cc:2739
#, c-format
msgid "Ambiguous command `%s'. Use `help' to see available commands.\n"
msgstr "Orden ambigua `%s'. Use `help' para ver las órdenes disponibles.\n"

#: src/commands.cc:2805
#, fuzzy, c-format
msgid "LFTP | Version %s | Copyright (c) 1996-%d Alexander V. Lukyanov\n"
msgstr "Lftp | Versión %s | Copyright (c) 1996-2002 Alexander V. Lukyanov\n"

#: src/commands.cc:2808
#, c-format
msgid ""
"LFTP is free software: you can redistribute it and/or modify\n"
"it under the terms of the GNU General Public License as published by\n"
"the Free Software Foundation, either version 3 of the License, or\n"
"(at your option) any later version.\n"
"\n"
"This program is distributed in the hope that it will be useful,\n"
"but WITHOUT ANY WARRANTY; without even the implied warranty of\n"
"MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n"
"GNU General Public License for more details.\n"
"\n"
"You should have received a copy of the GNU General Public License\n"
"along with LFTP.  If not, see <http://www.gnu.org/licenses/>.\n"
msgstr ""

#: src/commands.cc:2822
#, fuzzy, c-format
msgid "Send bug reports and questions to the mailing list <%s>.\n"
msgstr "Envíe reportes de bugs y preguntas a <%s>.\n"

#: src/commands.cc:2828
msgid "Libraries used: "
msgstr ""

#: src/commands.cc:2979 src/commands.cc:3007
#, c-format
msgid "%s: bookmark name required\n"
msgstr "%s: se requiere un nombre de señalador\n"

#: src/commands.cc:2996
#, c-format
msgid "%s: spaces in bookmark name are not allowed\n"
msgstr "%s: no se permiten espacios en el nombre de un señalador\n"

#: src/commands.cc:3009
#, c-format
msgid "%s: no such bookmark `%s'\n"
msgstr "%s: no existe señalador `%s'\n"

#: src/commands.cc:3032
#, c-format
msgid "%s: import type required (netscape,ncftp)\n"
msgstr "%s: se requiere el tipo a importar (netscape, ncftp)\n"

# Es la ayuda del comando find.
#: src/commands.cc:3110
#, c-format
msgid "Usage: %s [-d #] dir\n"
msgstr "Uso: %s [-d N] dir\n"

#: src/commands.cc:3215
#, c-format
msgid "%s: invalid block size `%s'\n"
msgstr "%s: tamaño de bloque inválido `%s'\n"

# Es la ayuda del comando find.
#: src/commands.cc:3226
#, c-format
msgid "Usage: %s [options] <dirs>\n"
msgstr "Uso: %s [opciones] <dirs>\n"

#: src/commands.cc:3232
#, c-format
msgid "%s: warning: summarizing is the same as using --max-depth=0\n"
msgstr ""

#: src/commands.cc:3236
#, c-format
msgid "%s: summarizing conflicts with --max-depth=%i\n"
msgstr ""

#: src/commands.cc:3280
#, c-format
msgid "Usage: %s command args...\n"
msgstr "Uso: %s orden parámetros ...\n"

#: src/commands.cc:3292
#, c-format
msgid "Usage: %s module [args...]\n"
msgstr "Uso: %s módulo [args...]\n"

#: src/commands.cc:3310 src/LocalAccess.cc:642
msgid "cannot get current directory"
msgstr "no se pudo obtener el directorio actual"

#: src/commands.cc:3374
#, c-format
msgid "Usage: %s [OPTS] mode file...\n"
msgstr "Uso: %s [OPCS] modo archivo...\n"

#: src/commands.cc:3394
#, c-format
msgid "invalid mode string: %s\n"
msgstr "cadena de modo inválida: %s\n"

#: src/commands.cc:3457 src/commands.cc:3466
msgid "Invalid range format. Format is min-max, e.g. 10-20."
msgstr "Formato del rango inválido. El formato es mín-máx, p.ej. 10-20."

#: src/commands.cc:3478
#, c-format
msgid "Usage: %s [OPTS] file\n"
msgstr "Uso: %s [OPCS] archivo\n"

#: src/CopyJob.cc:82
#, c-format
msgid "`%s' at %lld %s%s%s%s"
msgstr "`%s' en %lld %s%s%s%s"

#: src/CopyJob.cc:159
#, c-format
msgid "%lld $#ll#byte|bytes$ transferred in %ld $#l#second|seconds$"
msgstr "%lld byte$#ll#|s$ transferidos en %ld segundo$#l#|s$."

#: src/CopyJob.cc:167
#, fuzzy, c-format
msgid "%lld $#ll#byte|bytes$ transferred"
msgstr "%lld byte$#l#|s$ transferidos.\n"

#: src/CopyJob.cc:283
#, c-format
msgid "Transfer of %d of %d $file|files$ failed\n"
msgstr "Falló la transferencia de %d de %d archivo$|s$.\n"

#: src/CopyJob.cc:289
#, c-format
msgid "Total %d $file|files$ transferred\n"
msgstr "%d archivo$|s$ transferido$|s$ en total.\n"

#: src/FileAccess.cc:160
#, fuzzy
msgid "Access failed: "
msgstr "Falló el acceso: %s"

#: src/FileAccess.cc:161
msgid "File cannot be accessed"
msgstr "El archivo no puede ser accedido"

#: src/FileAccess.cc:163 src/Fish.cc:982 src/ftpclass.cc:4599
#: src/ftpclass.cc:4608 src/SFtp.cc:1339 src/Torrent.cc:3533
msgid "Not connected"
msgstr "No conectado"

#: src/FileAccess.cc:166 src/FileAccess.cc:167
msgid "Fatal error"
msgstr "Error fatal"

#: src/FileAccess.cc:169
msgid "Store failed - you have to reput"
msgstr "El envío falló - tiene que usar reput"

#: src/FileAccess.cc:172 src/FileAccess.cc:173
msgid "Login failed"
msgstr "Falló la identificación con el servidor"

#: src/FileAccess.cc:176 src/FileAccess.cc:177
msgid "Operation not supported"
msgstr "Operación no soportada"

#: src/FileAccess.cc:180
#, fuzzy
msgid "File moved"
msgstr "Archivo movido: %s"

#: src/FileAccess.cc:182
#, fuzzy
msgid "File moved to `"
msgstr "Archivo movido a `%s'"

#: src/FileCopy.cc:141
msgid "copy: destination file is already complete\n"
msgstr "copy: el archivo de destino está completo\n"

#: src/FileCopy.cc:182
msgid "copy: put is broken\n"
msgstr "copy: put está roto\n"

#: src/FileCopy.cc:201
msgid "seek failed"
msgstr "falló seek"

#: src/FileCopy.cc:207
#, fuzzy
msgid "no progress timeout"
msgstr "tiempo fuera remoto"

#: src/FileCopy.cc:235
msgid "cannot seek on data source"
msgstr "no se pudo hacer seek en la fuente de datos"

#: src/FileCopy.cc:238
#, c-format
msgid "copy: put rolled back to %lld, seeking get accordingly\n"
msgstr "copy: put en rolled back a %lld, solicitado por get\n"

#: src/FileCopy.cc:251
msgid "copy: all data received, but get rolled back\n"
msgstr "copy: toda la data recibida pero get un rolled back\n"

#: src/FileCopy.cc:267
#, c-format
msgid "copy: get rolled back to %lld, seeking put accordingly\n"
msgstr ""

#: src/FileCopy.cc:364
msgid "file size decreased during transfer"
msgstr "el tamaño del archivo disminuyo durante la transferencia"

#: src/FileCopy.cc:1227
#, c-format
msgid "copy: received redirection to `%s'\n"
msgstr "copy: se recibió redirección a `%s'\n"

#: src/FileCopy.cc:1290
#, fuzzy
#| msgid "file size decreased during transfer"
msgid "file size increased during transfer"
msgstr "el tamaño del archivo disminuyo durante la transferencia"

#: src/FileCopy.cc:1390
msgid "file name missed in URL"
msgstr "nombre de archivo no encontrado em URL"

#: src/FileCopy.cc:2086
msgid "Verify command failed without a message"
msgstr "Comando Verify fallo sin mensaje"

#: src/FileCopyFtp.cc:95
msgid "**** FXP: trying to reverse ftp:fxp-passive-source\n"
msgstr "**** FXP: intentando invertir ftp:fxp-passive-source\n"

#: src/FileCopyFtp.cc:102
#, fuzzy
msgid "**** FXP: trying to reverse ftp:fxp-passive-sscn\n"
msgstr "**** FXP: intentando invertir ftp:fxp-passive-source\n"

#: src/FileCopyFtp.cc:110
#, fuzzy
msgid "**** FXP: trying to reverse ftp:ssl-protect-fxp\n"
msgstr "**** FXP: intentando invertir ftp:fxp-passive-source\n"

#: src/FileCopyFtp.cc:116
msgid "**** FXP: giving up, reverting to plain copy\n"
msgstr "**** FXP: me rindo, se usará copiado simple\n"

#: src/FileCopyFtp.cc:125
msgid "ftp:fxp-force is set but FXP is not available"
msgstr "ftp:fxp-force está activado pero FXP no está disponible"

#: src/FileCopy.h:285
msgid "Verifying..."
msgstr "Verificando..."

#: src/FileSetOutput.cc:230
msgid "non-option arguments found"
msgstr "sin opcion para argumentos encontrada"

#: src/Filter.cc:166
#, fuzzy
msgid "pipe() failed: "
msgstr "falló pipe(): %s"

#: src/Filter.cc:200 src/PtyShell.cc:135
#, c-format
msgid "chdir(%s) failed: %s\n"
msgstr "falló chdir(%s): %s\n"

#: src/Filter.cc:208
#, fuzzy, c-format
msgid "execvp(%s) failed: %s\n"
msgstr "falló execlp(%s): %s\n"

#: src/Filter.cc:213 src/PtyShell.cc:147
#, c-format
msgid "execl(/bin/sh) failed: %s\n"
msgstr "falló execl(/bin/sh): %s\n"

#: src/Filter.cc:415
#, fuzzy
msgid "file already exists and xfer:clobber is unset"
msgstr "%s: %s: el archivo ya existe y xfer:clobber no está definido\n"

#: src/FindJobDu.cc:101
msgid "total"
msgstr ""

#: src/Fish.cc:75 src/ftpclass.cc:1292 src/Http.cc:1232 src/SFtp.cc:77
#, c-format
msgid "Closing idle connection"
msgstr "Cerrando conexión ociosa"

#: src/Fish.cc:162 src/SFtp.cc:177
msgid "Running connect program"
msgstr "Ejecutando programa de conección"

#: src/Fish.cc:588 src/ftpclass.cc:2887 src/ftpclass.cc:3107 src/Http.cc:1510
#: src/SFtp.cc:742 src/SFtp.cc:1083 src/SFtp.cc:1084 src/SSH_Access.cc:167
#, c-format
msgid "Peer closed connection"
msgstr "El otro extremo cerró la conexión"

#: src/Fish.cc:634 src/ftpclass.cc:3037 src/ftpclass.cc:4219 src/SFtp.cc:1108
#, c-format
msgid "extra server response"
msgstr "respuesta extra del servidor"

#: src/Fish.cc:987 src/ftpclass.cc:4611 src/Http.cc:2329 src/Http.cc:2336
#: src/SFtp.cc:1345 src/Torrent.cc:3536 src/TorrentTracker.cc:624
msgid "Connecting..."
msgstr "Conectándose..."

#: src/Fish.cc:989 src/ftpclass.cc:4617 src/SFtp.cc:1347
msgid "Connected"
msgstr "Conectado"

#: src/Fish.cc:991 src/ftpclass.cc:4594 src/ftpclass.cc:4622
#: src/ftpclass.cc:4636 src/Http.cc:2338 src/SFtp.cc:1349
#: src/TorrentTracker.cc:626
msgid "Waiting for response..."
msgstr "Esperando respuesta..."

#: src/Fish.cc:993 src/ftpclass.cc:4656 src/Http.cc:2341 src/SFtp.cc:1351
msgid "Receiving data"
msgstr "Recibiendo datos"

#: src/Fish.cc:995 src/ftpclass.cc:4654 src/Http.cc:2334 src/SFtp.cc:1353
msgid "Sending data"
msgstr "Enviando datos"

#: src/Fish.cc:997 src/SFtp.cc:1355
#, fuzzy
msgid "Done"
msgstr "\tHecho\n"

#: src/Fish.cc:1136 src/FtpDirList.cc:123 src/HttpDir.cc:1384 src/SFtp.cc:2200
#: src/SFtp.cc:2320
#, c-format
msgid "Getting file list (%lld) [%s]"
msgstr "Obteniendo lista de archivos (%lld) [%s]"

#: src/ftpclass.cc:197
#, c-format
msgid "Data connection peer has wrong port number"
msgstr ""
"El otro extremo de la conexión de datos tiene un número equivocado de puerto"

#: src/ftpclass.cc:203
#, c-format
msgid "Data connection peer has mismatching address"
msgstr "No coincide la dirección del otro extremo de la conexión de datos"

#: src/ftpclass.cc:225 src/ftpclass.cc:250
#, c-format
msgid "Switching to NOREST mode"
msgstr "Conectando modo NOREST"

#: src/ftpclass.cc:425
#, c-format
msgid "Server reply matched ftp:retry-530, retrying"
msgstr "La respuesta del servidor califica según ftp:retry-530, reintentando"

#: src/ftpclass.cc:433
#, c-format
msgid "Server reply matched ftp:retry-530-anonymous, retrying"
msgstr ""
"La respuesta del servidor califica según ftp:retry-530-anonymous, "
"reintentando"

#: src/ftpclass.cc:466
msgid "Account is required, set ftp:acct variable"
msgstr "Se necesita una cuenta, asigne la variable ftp:acct"

#: src/ftpclass.cc:483
msgid "ftp:skey-force is set and server does not support OPIE nor S/KEY"
msgstr "ftp:está definido skey-force y el servidor no soporta ni OPIE ni S/KEY"

#: src/ftpclass.cc:501
#, c-format
msgid "assuming failed host name lookup"
msgstr "asumiendo falla en la búsqueda del nombre de servidor"

#: src/ftpclass.cc:809 src/ftpclass.cc:810
#, c-format
msgid "cannot parse EPSV response"
msgstr "no se pudo interpretar la respuesta EPSV"

#: src/ftpclass.cc:837 src/ftpclass.cc:838
#, fuzzy, c-format
#| msgid "cannot parse EPSV response"
msgid "cannot parse custom EPSV response"
msgstr "no se pudo interpretar la respuesta EPSV"

#: src/ftpclass.cc:1122
#, c-format
msgid "Closing control socket"
msgstr "Cerrando socket de control"

#: src/ftpclass.cc:1341
#, fuzzy
msgid "MLSD is disabled by ftp:use-mlsd"
msgstr "SITE CHMOD está desabilitado por ftp:use-site-chmod"

#: src/ftpclass.cc:1411 src/Http.cc:1431 src/Torrent.cc:3204
#: src/Torrent.cc:3743 src/TorrentTracker.cc:395
#, c-format
msgid "cannot create socket of address family %d"
msgstr "no se pudo crear un socket de la familia %d de direcciones"

#: src/ftpclass.cc:1442 src/Http.cc:1457
#, c-format
msgid "Socket error (%s) - reconnecting"
msgstr "Error de socket (%s) - reconectando"

#: src/ftpclass.cc:1715
#, fuzzy
msgid "MFF and SITE CHMOD are not supported by this site"
msgstr "El servidor no soporta el comando SITE CHMOD"

#: src/ftpclass.cc:1720
#, fuzzy
msgid "MLST and MLSD are not supported by this site"
msgstr "El servidor no soporta el comando SITE CHMOD"

#: src/ftpclass.cc:2031
#, fuzzy
msgid "SITE SYMLINK is not supported by the server"
msgstr "El servidor no soporta el comando SITE CHMOD"

#: src/ftpclass.cc:2204
msgid "unsupported network protocol"
msgstr "protocolo de red no soportado"

#: src/ftpclass.cc:2253 src/ftpclass.cc:2355
#, fuzzy, c-format
msgid "Data socket error (%s) - reconnecting"
msgstr "Error de socket (%s) - reconectando"

#: src/ftpclass.cc:2281
#, fuzzy, c-format
msgid "Accepted data connection from (%s) port %u"
msgstr "Conectando socket de datos a (%s) puerto %u"

#: src/ftpclass.cc:2330
#, c-format
msgid "Connecting data socket to (%s) port %u"
msgstr "Conectando socket de datos a (%s) puerto %u"

#: src/ftpclass.cc:2336
#, fuzzy, c-format
msgid "Connecting data socket to proxy %s (%s) port %u"
msgstr "Conectando socket de datos a (%s) puerto %u"

#: src/ftpclass.cc:2358 src/ftpclass.cc:4414
#, c-format
msgid "Switching passive mode off"
msgstr "Desconectando modo pasivo"

#: src/ftpclass.cc:2366
#, fuzzy, c-format
msgid "Data connection established"
msgstr "Conexión de datos abierta"

#: src/ftpclass.cc:2688 src/ftpclass.cc:4548
msgid "ftp:ssl-force is set and server does not support or allow SSL"
msgstr ""
"Está asignado el parámetro ssl-force y el sistema no soporta o no permite SSL"

#: src/ftpclass.cc:3053
#, c-format
msgid "Persist and retry"
msgstr "Persistir y reintentar"

#: src/ftpclass.cc:3321
#, c-format
msgid "Closing data socket"
msgstr "Cerrando socket de datos"

#: src/ftpclass.cc:3343
#, fuzzy, c-format
msgid "Closing aborted data socket"
msgstr "Cerrando socket de datos"

#: src/ftpclass.cc:4193
#, c-format
msgid "saw file size in response"
msgstr "vi tamaño del archivo en la respuesta"

#: src/ftpclass.cc:4233 src/ftpclass.cc:4260
#, c-format
msgid "Turning on sync-mode"
msgstr "Activando sync-mode"

#: src/ftpclass.cc:4434
#, c-format
msgid "Switching passive mode on"
msgstr "Conectando modo pasivo"

#: src/ftpclass.cc:4585
#, fuzzy
msgid "FEAT negotiation..."
msgstr "Negociando TLS..."

#: src/ftpclass.cc:4592
msgid "Sending commands..."
msgstr "Enviando comandos..."

#: src/ftpclass.cc:4596
#, fuzzy
msgid "Delaying before retry"
msgstr "Esperando antes de reintentar"

#: src/ftpclass.cc:4597 src/Http.cc:2331
msgid "Connection idle"
msgstr "Conexión ociosa"

#: src/ftpclass.cc:4604 src/Http.cc:2323 src/Resolver.cc:172
#: src/Resolver.cc:217 src/TorrentTracker.cc:622
#, c-format
msgid "Resolving host address..."
msgstr "Resolviendo dirección del servidor..."

#: src/ftpclass.cc:4615
msgid "TLS negotiation..."
msgstr "Negociando TLS..."

#: src/ftpclass.cc:4619
msgid "Logging in..."
msgstr "Identificándose..."

#: src/ftpclass.cc:4623
msgid "Making data connection..."
msgstr "Estableciendo la conexión de datos..."

#: src/ftpclass.cc:4626
msgid "Changing remote directory..."
msgstr "Cambiando el directorio remoto..."

#: src/ftpclass.cc:4632
msgid "Waiting for other copy peer..."
msgstr "Esperando al otro extremo para copiar..."

#: src/ftpclass.cc:4634 src/ftpclass.cc:4658
msgid "Waiting for transfer to complete"
msgstr "Esperando que finalice la transferencia"

#: src/ftpclass.cc:4638
#, fuzzy
msgid "Waiting for TLS shutdown..."
msgstr "Esperando respuesta..."

#: src/ftpclass.cc:4640
msgid "Waiting for data connection..."
msgstr "Esperando la conexión de datos..."

#: src/ftpclass.cc:4646
msgid "Sending data/TLS"
msgstr "Enviando datos/TLS"

#: src/ftpclass.cc:4648
msgid "Receiving data/TLS"
msgstr "Recibiendo datos/TLS"

#: src/Http.cc:230
#, c-format
msgid "Closing HTTP connection"
msgstr "Cerrando conexión HTTP"

#: src/Http.cc:240
msgid "POST method failed"
msgstr "falló el método POST"

#: src/Http.cc:1381
msgid "ftp over http cannot work without proxy, set hftp:proxy."
msgstr "ftp no funciona sobre http sin usar un proxy, configure hftp:proxy."

#: src/Http.cc:1537
#, c-format
msgid "Sending request..."
msgstr "Enviando requerimiento..."

#: src/Http.cc:1575
#, c-format
msgid "Hit EOF while fetching headers"
msgstr "Encontré EOF mientras traía encabezados"

#: src/Http.cc:1695
#, c-format
msgid "Could not parse HTTP status line"
msgstr "No se pudo interpretar la línea de estado HTTP"

#: src/Http.cc:1726
msgid "Object is not cached and http:cache-control has only-if-cached"
msgstr "Objecto no es cacheado y http:cache-control tiene only-if-cached"

#: src/Http.cc:1891
#, c-format
msgid "Receiving body..."
msgstr "Recibiendo cuerpo..."

#: src/Http.cc:2092
#, c-format
msgid "Hit EOF"
msgstr "Encontré EOF"

#: src/Http.cc:2095
#, c-format
msgid "Received not enough data, retrying"
msgstr "No se recibió suficiente, reintentando"

#: src/Http.cc:2106
#, c-format
msgid "Received all"
msgstr "Se recibió todo"

#: src/Http.cc:2111
#, c-format
msgid "Received all (total)"
msgstr "Se recibió todo (total)"

#: src/Http.cc:2135 src/Http.cc:2159
msgid "chunked format violated"
msgstr "formato `chunked' violado"

#: src/Http.cc:2145
#, fuzzy, c-format
msgid "Received last chunk"
msgstr "Se recibió todo"

#: src/Http.cc:2339
msgid "Fetching headers..."
msgstr "Trayendo encabezados..."

#: src/Job.cc:293
#, c-format
msgid "[%d] Done (%s)"
msgstr "[%d] Hecho (%s)"

#: src/lftp.cc:370
#, fuzzy, c-format
msgid "[%u] Terminated by signal %d. %s\n"
msgstr "[%lu] Terminado por señal %d. %s"

#: src/lftp.cc:445
#, fuzzy, c-format
msgid "[%u] Started.  %s\n"
msgstr "[%lu] Comenzado.  %s"

#: src/lftp.cc:463
#, fuzzy, c-format
msgid "[%u] Detaching from the terminal to complete transfers...\n"
msgstr "[%lu] Pasando a segundo plano para completar las transferencias...\n"

#: src/lftp.cc:465
#, c-format
msgid "[%u] Exiting and detaching from the terminal.\n"
msgstr ""

#: src/lftp.cc:470
#, c-format
msgid "[%u] Detached from terminal. %s\n"
msgstr ""

#: src/lftp.cc:480
#, fuzzy, c-format
msgid "[%u] Finished. %s\n"
msgstr "[%lu] Finalizado. %s"

#: src/lftp.cc:484
#, fuzzy, c-format
msgid "[%u] Moving to background to complete transfers...\n"
msgstr "[%lu] Pasando a segundo plano para completar las transferencias...\n"

#: src/lftp.cc:553
msgid "history -w file|-r file|-c|-l [cnt]"
msgstr ""

#: src/lftp.cc:554
msgid ""
" -w <file> Write history to file.\n"
" -r <file> Read history from file; appends to current history.\n"
" -c  Clear the history.\n"
" -l  List the history (default).\n"
"Optional argument cnt specifies the number of history lines to list,\n"
"or \"all\" to list all entries.\n"
msgstr ""

#: src/lftp.cc:561
msgid "Attach the terminal to specified backgrounded lftp process.\n"
msgstr ""

#: src/lftp_ssl.cc:1291
#, c-format
msgid "No certificate presented by %s.\n"
msgstr ""

#: src/LocalAccess.cc:604 src/NetAccess.cc:686
#, fuzzy
msgid "Getting directory contents"
msgstr "Obteniendo el contenido del directorio (%lld) %s[%s]"

#: src/LocalAccess.cc:606 src/NetAccess.cc:690
#, fuzzy
msgid "Getting files information"
msgstr "Obteniendo información sobre los archivos (%d%%) [%s]"

#: src/LsCache.cc:190
#, c-format
msgid "%ld $#l#byte|bytes$ cached"
msgstr "%ld byte$#l#|s$ cacheados"

#: src/LsCache.cc:194
msgid ", no size limit"
msgstr ", sin límite de tamaño"

#: src/LsCache.cc:196
#, c-format
msgid ", maximum size %ld\n"
msgstr ", tamaño máximo %ld\n"

#: src/mgetJob.cc:78
#, fuzzy, c-format
msgid "%s: %s: no files found\n"
msgstr "%s: no se encontraron archivos\n"

# Is it possible to use the $|$ construct twice for the same data?
#: src/MirrorJob.cc:100
#, c-format
msgid "%sTotal: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
msgstr "%sTotal: %d directorio$|s$, %d archivo$|s$, %d enlace$|s$\n"

#: src/MirrorJob.cc:104
#, c-format
msgid "%sNew: %d file$|s$, %d symlink$|s$\n"
msgstr "%sNuevos: %d archivo$|s$, %d enlace$|s$\n"

#: src/MirrorJob.cc:108
#, c-format
msgid "%sModified: %d file$|s$, %d symlink$|s$\n"
msgstr "%sModificados: %d archivo$|s$, %d enlace$|s$\n"

#: src/MirrorJob.cc:115
#, c-format
msgid "%sRemoved: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
msgstr "%sBorrados: %d directorio$|s$, %d archivo$|s$, %d enlace$|s$\n"

#: src/MirrorJob.cc:120
#, fuzzy, c-format
msgid "%s%d error$|s$ detected\n"
msgstr "%s ok, %d directorio$|s$ creado$|s$\n"

#: src/MirrorJob.cc:231
#, fuzzy, c-format
msgid "Finished %s"
msgstr "[%lu] Finalizado. %s"

#: src/MirrorJob.cc:344 src/MirrorJob.cc:519 src/MirrorJob.cc:1218
#, c-format
msgid "Removing old file `%s'"
msgstr "Borrando archivo antiguo `%s'"

#: src/MirrorJob.cc:346
#, fuzzy, c-format
msgid "Overwriting old file `%s'"
msgstr "Borrando archivo antiguo `%s'"

#: src/MirrorJob.cc:355
#, c-format
msgid "Skipping file `%s' (only-existing)"
msgstr "Omitiendo archivo '%s'"

#: src/MirrorJob.cc:361
#, c-format
msgid "Transferring file `%s'"
msgstr "Enviando archivo `%s'"

#: src/MirrorJob.cc:439
#, fuzzy, c-format
msgid "Skipping directory `%s' (only-existing)"
msgstr "Creando directorio `%s'"

#: src/MirrorJob.cc:461 src/MirrorJob.cc:550 src/MirrorJob.cc:902
#, c-format
msgid "Removing old local file `%s'"
msgstr "Borrando archivo local antiguo `%s'"

#: src/MirrorJob.cc:487
#, fuzzy, c-format
msgid "Scanning directory `%s'"
msgstr "Creando directorio `%s'"

#: src/MirrorJob.cc:489
#, c-format
msgid "Mirroring directory `%s'"
msgstr "Replicando directorio `%s'"

#: src/MirrorJob.cc:525 src/MirrorJob.cc:567
#, c-format
msgid "Making symbolic link `%s' to `%s'"
msgstr "Creando enlace simbólico `%s' a `%s'"

#: src/MirrorJob.cc:562
#, c-format
msgid "Skipping symlink `%s' (only-existing)"
msgstr "Omitiendo enlace simbolico `%s'"

#: src/MirrorJob.cc:807
#, c-format
msgid "mirror: protocol `%s' is not suitable for mirror\n"
msgstr "mirror: el protocolo `%s' no es adecuado para mirror\n"

#: src/MirrorJob.cc:923
#, c-format
msgid "Making directory `%s'"
msgstr "Creando directorio `%s'"

#: src/MirrorJob.cc:1181
#, fuzzy, c-format
msgid "Old directory `%s' is not removed"
msgstr "No se borró el archivo `%s'"

#: src/MirrorJob.cc:1183
#, c-format
msgid "Old file `%s' is not removed"
msgstr "No se borró el archivo `%s'"

#: src/MirrorJob.cc:1216
#, c-format
msgid "Removing old directory `%s'"
msgstr "Borrando directorio antiguo `%s'"

#: src/MirrorJob.cc:1339
#, fuzzy, c-format
msgid "Removing source directory `%s'"
msgstr "Borrando directorio antiguo `%s'"

#: src/MirrorJob.cc:1405
#, fuzzy, c-format
msgid "Removing source file `%s'"
msgstr "Borrando archivo antiguo `%s'"

#: src/MirrorJob.cc:1425
#, c-format
msgid "Retrying mirror...\n"
msgstr "Recibiendo mirror...\n"

#: src/MirrorJob.cc:1694
msgid "pattern is empty"
msgstr ""

#: src/MirrorJob.cc:1779
#, c-format
msgid "%s must be one of: %s"
msgstr "%s debe ser uno de: %s"

#: src/MirrorJob.cc:2019
#, c-format
msgid ""
"%s: multiple --file or --directory options must have the same base "
"directory\n"
msgstr ""

#: src/MirrorJob.cc:2160
#, c-format
msgid "%s: ambiguous source directory (`%s' or `%s'?)\n"
msgstr "%s: ambiguo directorio de origen (¿`%s' o `%s'?)\n"

#: src/MirrorJob.cc:2182
#, c-format
msgid "%s: ambiguous target directory (`%s' or `%s'?)\n"
msgstr "%s: ambiguo directorio de destino (¿`%s' o `%s'?)\n"

#: src/MirrorJob.cc:2223
#, c-format
msgid "%s: source directory is required (mirror:require-source is set)\n"
msgstr ""
"%s: directorio origen es requerido (mirror:require-source está configurado)\n"

#: src/MirrorJob.cc:2343
msgid ""
"\n"
"Mirror specified remote directory to local directory\n"
"\n"
" -R, --reverse          reverse mirror (put files)\n"
"Lots of other options are documented in the man page lftp(1).\n"
"\n"
"When using -R, the first directory is local and the second is remote.\n"
"If the second directory is omitted, basename of the first directory is "
"used.\n"
"If both directories are omitted, current local and remote directories are "
"used.\n"
"\n"
"See the man page lftp(1) for a complete documentation.\n"
msgstr ""

#: src/mkdirJob.cc:128
#, c-format
msgid "%s ok, `%s' created\n"
msgstr "%s ok, `%s' creado\n"

#: src/mkdirJob.cc:130 src/rmJob.cc:51
#, c-format
msgid "%s failed for %d of %d director$y|ies$\n"
msgstr "%s falló para %d de %d directorio$|s$\n"

#: src/mkdirJob.cc:133
#, c-format
msgid "%s ok, %d director$y|ies$ created\n"
msgstr "%s ok, %d directorio$|s$ creado$|s$\n"

#: src/module.cc:190
#, c-format
msgid "depend module `%s': %s\n"
msgstr ""

#: src/module.cc:210
msgid "modules are not supported on this system"
msgstr "este sistema no soporta el uso de módulos"

#: src/mvJob.cc:92
#, c-format
msgid "rename successful\n"
msgstr "renombrado exitoso\n"

#: src/NetAccess.cc:168
#, c-format
msgid "Connecting to %s%s (%s) port %u"
msgstr "Conectándose a %s%s (%s) port %u"

#: src/NetAccess.cc:224 src/Torrent.cc:3326
#, c-format
msgid "Timeout - reconnecting"
msgstr "Tiempo fuera - reconectando"

#: src/NetAccess.cc:323
msgid "Connection limit reached"
msgstr "Límite de conexiones alcanzado"

#: src/NetAccess.cc:330
msgid "Delaying before reconnect"
msgstr "Esperando antes de reintentar"

#: src/NetAccess.cc:354 src/NetAccess.cc:356
msgid "max-retries exceeded"
msgstr "variable max-retries excedida"

#: src/OutputJob.cc:145
#, c-format
msgid "%s (filter)"
msgstr "%s (filtro)"

#: src/parsecmd.cc:290
msgid "parse: missing filter command\n"
msgstr "parse: falta orden de filtro\n"

#: src/parsecmd.cc:292
msgid "parse: missing redirection filename\n"
msgstr "parse: falta archivo de redirección\n"

#: src/PatternSet.cc:110
#, c-format
msgid "regular expression `%s': %s"
msgstr ""

#: src/pgetJob.cc:127
msgid "pget: falling back to plain get"
msgstr ""

#: src/pgetJob.cc:131
#, fuzzy
msgid "the target file is remote"
msgstr "Viejo archivo remoto `%s' no removido"

#: src/pgetJob.cc:136
msgid "the source file size is unknown"
msgstr ""

#: src/pgetJob.cc:173
#, c-format
msgid "pget: warning: space allocation for %s (%lld bytes) failed: %s\n"
msgstr ""

#: src/pgetJob.cc:234
#, c-format
msgid "`%s', got %lld of %lld (%d%%) %s%s"
msgstr "`%s', se obtuvieron %lld de %lld (%d%%) %s%s"

#: src/plural.c:96
msgid "=1 =0|>1"
msgstr "=1"

#  Juaa... me divierte mucho esta traducción, y ma sí... =)
#: src/PollVec.cc:69
#, c-format
msgid "%s: BUG - deadlock detected\n"
msgstr "%s: BUG - abrazo mortal detectado\n"

#: src/PtyShell.cc:68
msgid "pseudo-tty allocation failed: "
msgstr "pseudo-tty falló asignación"

#: src/QueueFeeder.cc:68
msgid "Added job$|s$"
msgstr "Agregado job$|s$"

#: src/QueueFeeder.cc:164 src/QueueFeeder.cc:185
#, c-format
msgid "No queued jobs.\n"
msgstr "No trabajos en cola.\n"

#: src/QueueFeeder.cc:166
#, fuzzy, c-format
msgid "No queued job #%i.\n"
msgstr "%s: Ninguna cola está activa.\n"

#: src/QueueFeeder.cc:171 src/QueueFeeder.cc:192
msgid "Deleted job$|s$"
msgstr "Elimitado job$|s$"

#: src/QueueFeeder.cc:187
#, c-format
msgid "No queued jobs match \"%s\".\n"
msgstr "No encontrado trabajdos en cola según \"%s\".\n"

#: src/QueueFeeder.cc:212 src/QueueFeeder.cc:230
msgid "Moved job$|s$"
msgstr "Movido job$s|s$"

#: src/QueueFeeder.cc:354
#, fuzzy
msgid "Commands queued:"
msgstr "\tComandos encolados:\n"

#: src/ResMgr.cc:123
msgid "no such variable"
msgstr "no existe tal variable"

#: src/ResMgr.cc:127
msgid "ambiguous variable name"
msgstr "nombre de variable ambiguo"

#: src/ResMgr.cc:335
msgid "invalid boolean value"
msgstr "valor lógico inválido"

#: src/ResMgr.cc:357
msgid "invalid boolean/auto value"
msgstr "valor lógico/auto inválido"

#: src/ResMgr.cc:402 src/ResMgr.cc:713
msgid "invalid number"
msgstr "número inválido"

#: src/ResMgr.cc:415
msgid "invalid floating point number"
msgstr "número de coma flotante inválido"

#: src/ResMgr.cc:429
#, fuzzy
msgid "invalid unsigned number"
msgstr "número inválido"

#: src/ResMgr.cc:678
msgid "Invalid time unit letter, only [smhd] are allowed."
msgstr "Unidad de tiempo inválida, sólo se permiten [smhd]."

#: src/ResMgr.cc:686
msgid "Invalid time format. Format is <time><unit>, e.g. 2h30m."
msgstr "Formato de tiempo inválido, debe ser <tiempo><unidad>, p.ej. 2h30m."

#: src/ResMgr.cc:718
msgid "integer overflow"
msgstr "overflow de integer"

#: src/ResMgr.cc:804
msgid "Invalid IPv4 numeric address"
msgstr "Dirección numérica IPv4 inválida"

#: src/ResMgr.cc:814
#, fuzzy
msgid "Invalid IPv6 numeric address"
msgstr "Dirección numérica IPv4 inválida"

#: src/ResMgr.cc:884 src/ResMgr.cc:888
#, fuzzy
msgid "this encoding is not supported"
msgstr "Operación no soportada"

#: src/ResMgr.cc:894
msgid "no closure defined for this setting"
msgstr "No se ha definido cierre para esta configuración"

#: src/ResMgr.cc:900
#, fuzzy
msgid "a closure is required for this setting"
msgstr "No se ha definido cierre para esta configuración"

#: src/Resolver.cc:236
msgid "host name resolve timeout"
msgstr "tiempo fuera resolviendo el nombre de host"

#: src/Resolver.cc:282
#, fuzzy, c-format
msgid "%d address$|es$ found"
msgstr "---- Se encontr$ó|aron$ %d direcci$ón|ones$\n"

#: src/Resolver.cc:327
msgid "Link-local IPv6 address should have a scope"
msgstr ""

#: src/Resolver.cc:765
msgid "DNS resolution not trusted."
msgstr "Resolución DNS no confiable"

#: src/Resolver.cc:871
msgid "Host name lookup failure"
msgstr "Falla en la resolución del nombre del servidor"

#: src/Resolver.cc:903
#, c-format
msgid "no such %s service"
msgstr "no existe un servicio %s"

#: src/Resolver.cc:930
msgid "No address found"
msgstr "No se encontró ninguna dirección"

#: src/resource.cc:50 src/resource.cc:108
msgid "Proxy protocol unsupported"
msgstr "Protocolo de proxy no soportado"

#: src/resource.cc:54
msgid "ftp:proxy password: "
msgstr "Clave ftp:proxy: "

#: src/resource.cc:70
#, c-format
msgid "%s must be one of: "
msgstr ""

#: src/resource.cc:72
msgid "must be one of: "
msgstr ""

#: src/resource.cc:84
msgid ", or empty"
msgstr ""

#: src/resource.cc:116
msgid "only PUT and POST values allowed"
msgstr "sólo se permiten los valores PUT y POST"

#: src/resource.cc:148
#, c-format
msgid "unknown address family `%s'"
msgstr "familia `%s' de direcciones desconocida"

#: src/rmJob.cc:47
#, c-format
msgid "%s ok, `%s' removed\n"
msgstr "%s ok, `%s' borrado\n"

#: src/rmJob.cc:54
#, c-format
msgid "%s failed for %d of %d file$|s$\n"
msgstr "%s falló para %d de %d archivo$|s$\n"

#: src/rmJob.cc:60
#, c-format
msgid "%s ok, %d director$y|ies$ removed\n"
msgstr "%s ok, %d directorio$|s$ borrado$|s$\n"

#: src/rmJob.cc:63
#, c-format
msgid "%s ok, %d file$|s$ removed\n"
msgstr "%s ok, %d archivo$|s$ borrado$|s$\n"

#: src/SFtp.cc:1099 src/SFtp.cc:1100
#, fuzzy, c-format
msgid "invalid server response format"
msgstr "respuesta extra del servidor"

#: src/SleepJob.cc:99
msgid "Sleeping forever"
msgstr "Durmiendo para siempre"

#: src/SleepJob.cc:100
msgid "Sleep time left: "
msgstr "Tiempo de Sleep faltante:"

#: src/SleepJob.cc:108
#, c-format
msgid "\tRepeat count: %d\n"
msgstr "\tRepeticiones: %d\n"

#: src/SleepJob.cc:142
#, c-format
msgid "%s: argument required. "
msgstr "%s: se requiere un argumento. "

#: src/SleepJob.cc:262
#, c-format
msgid "%s: date-time specification missed\n"
msgstr "%s: date-time falta\n"

#: src/SleepJob.cc:269
#, c-format
msgid "%s: date-time parse error\n"
msgstr "%s: date-time error de parseo\n"

#: src/SleepJob.cc:303
msgid ""
"Usage: sleep <time>[unit]\n"
"Sleep for given amount of time. The time argument can be optionally\n"
"followed by unit specifier: d - days, h - hours, m - minutes, s - seconds.\n"
"By default time is assumed to be seconds.\n"
msgstr ""
"Uso: sleep <lapso>[unidad]\n"
"Espera la cantidad de tiempo dada. El argumento lapso puede ser seguido\n"
"opcionalmente por una unidad: d - días, h - horas, m - minutos, s - "
"segundos.\n"
"Por omisión se asume que el tiempo se da en segundos.\n"

#: src/SleepJob.cc:310
#, fuzzy
msgid ""
"Repeat specified command with a delay between iterations.\n"
"Default delay is one second, default command is empty.\n"
" -c <count>  maximum number of iterations\n"
" -d <delay>  delay between iterations\n"
" --while-ok  stop when command exits with non-zero code\n"
" --until-ok  stop when command exits with zero code\n"
" --weak      stop when lftp moves to background.\n"
msgstr ""
"Repite la orden especificada con una demora entre iteraciones.\n"
"La demora por omisión es de un segundo, la orden por omisión es nada.\n"

#: src/Speedometer.cc:90
#, c-format
msgid "%.0fb/s"
msgstr ""

#: src/Speedometer.cc:93
#, c-format
msgid "%.1fK/s"
msgstr ""

#: src/Speedometer.cc:96
#, c-format
msgid "%.2fM/s"
msgstr ""

#: src/Speedometer.cc:103
#, c-format
msgid "%.0f B/s"
msgstr ""

#: src/Speedometer.cc:105
#, c-format
msgid "%.1f KiB/s"
msgstr ""

#: src/Speedometer.cc:107
#, c-format
msgid "%.2f MiB/s"
msgstr ""

#: src/Speedometer.cc:129
msgid "eta:"
msgstr "falta:"

#: src/SSH_Access.cc:97
#, fuzzy
msgid "Password required"
msgstr "Clave: "

#: src/SSH_Access.cc:102
msgid "Login incorrect"
msgstr ""

#: src/SSH_Access.cc:196
#, fuzzy, c-format
msgid "Disconnecting"
msgstr "Conectándose..."

#: src/SysCmdJob.cc:73
#, c-format
msgid "execlp(%s) failed: %s\n"
msgstr "falló execlp(%s): %s\n"

#: src/TimeDate.cc:155
msgid "day"
msgstr "día"

#: src/TimeDate.cc:156
msgid "hour"
msgstr "hora"

#: src/TimeDate.cc:157
msgid "minute"
msgstr "minuto"

#: src/TimeDate.cc:158
msgid "second"
msgstr "segundo"

#: src/Torrent.cc:585
msgid "announced via "
msgstr ""

#: src/Torrent.cc:599
#, c-format
msgid "next announce in %s"
msgstr ""

#: src/Torrent.cc:1142
#, c-format
msgid "%d file$|s$ found, now scanning %s"
msgstr ""

#: src/Torrent.cc:1143
#, fuzzy, c-format
msgid "%d file$|s$ found"
msgstr "---- Se encontr$ó|aron$ %d direcci$ón|ones$\n"

#: src/Torrent.cc:2075 src/Torrent.cc:2085
#, c-format
msgid "Getting meta-data: %s"
msgstr ""

#: src/Torrent.cc:2077
#, c-format
msgid "Validation: %u/%u (%u%%) %s%s"
msgstr ""

#: src/Torrent.cc:2088
#, fuzzy
msgid "Waiting for meta-data..."
msgstr "\tEsperando orden\n"

#: src/Torrent.cc:2096
msgid "Shutting down: "
msgstr ""

#: src/Torrent.cc:3207
#, fuzzy, c-format
msgid "Connecting to peer %s port %u"
msgstr "Conectándose a %s%s (%s) port %u"

#: src/Torrent.cc:3258 src/Torrent.cc:3375
#, fuzzy, c-format
msgid "peer unexpectedly closed connection after %s"
msgstr "el extremo remoto cerró la conexión"

#: src/Torrent.cc:3259 src/Torrent.cc:3376
#, fuzzy
msgid "peer unexpectedly closed connection"
msgstr "el extremo remoto cerró la conexión"

#: src/Torrent.cc:3261 src/Torrent.cc:3262
#, fuzzy, c-format
msgid "peer closed connection (before handshake)"
msgstr "El otro extremo cerró la conexión"

#: src/Torrent.cc:3265 src/Torrent.cc:3378 src/Torrent.cc:3379
#, fuzzy, c-format
msgid "invalid peer response format"
msgstr "respuesta extra del servidor"

#: src/Torrent.cc:3360 src/Torrent.cc:3361
#, fuzzy, c-format
msgid "peer closed connection"
msgstr "El otro extremo cerró la conexión"

#: src/Torrent.cc:3538
msgid "Handshaking..."
msgstr ""

#: src/Torrent.cc:3798
msgid "Cannot bind a socket for torrent:port-range"
msgstr ""

#: src/Torrent.cc:3858
#, fuzzy, c-format
msgid "Accepted connection from [%s]:%d"
msgstr "Conectando socket de datos a (%s) puerto %u"

#: src/Torrent.cc:3924
#, c-format
msgid "peer sent unknown info_hash=%s in handshake"
msgstr ""

#: src/Torrent.cc:3948
#, c-format
msgid "peer handshake timeout"
msgstr ""

#: src/Torrent.cc:3960
#, c-format
msgid "peer short handshake"
msgstr ""

#: src/Torrent.cc:3962
#, fuzzy, c-format
msgid "peer closed just accepted connection"
msgstr "El otro extremo cerró la conexión"

#: src/Torrent.cc:4013
#, fuzzy, c-format
msgid "Seeding in background...\n"
msgstr "Enviando comandos..."

#: src/Torrent.cc:4176
#, c-format
msgid "%s: --share conflicts with --output-directory.\n"
msgstr ""

#: src/Torrent.cc:4180
#, c-format
msgid "%s: --share conflicts with --only-new.\n"
msgstr ""

#: src/Torrent.cc:4184
#, c-format
msgid "%s: --share conflicts with --only-incomplete.\n"
msgstr ""

#: src/Torrent.cc:4226
#, c-format
msgid "%s: Please specify a file or directory to share.\n"
msgstr ""

#: src/Torrent.cc:4228
#, c-format
msgid "%s: Please specify meta-info file or URL.\n"
msgstr ""

#: src/Torrent.cc:4258
msgid ""
"Start BitTorrent job for the given torrent-files, which can be a local "
"file,\n"
"URL, magnet link or plain info_hash written in hex or base32. Local "
"wildcards\n"
"are expanded. Options:\n"
" -O <base>      specifies base directory where files should be placed\n"
" --force-valid  skip file validation\n"
" --dht-bootstrap=<node>  bootstrap DHT by sending a query to the node\n"
" --share        share specified file or directory\n"
msgstr ""

#: src/TorrentTracker.cc:178
msgid "not started"
msgstr ""

#: src/TorrentTracker.cc:181
#, c-format
msgid "next request in %s"
msgstr ""

#: src/TorrentTracker.cc:284 src/TorrentTracker.cc:501
#, c-format
msgid "Received valid info about %d peer$|s$"
msgstr ""

#: src/TorrentTracker.cc:298
#, c-format
msgid "Received valid info about %d IPv6 peer$|s$"
msgstr ""

#~ msgid "%s: option '--%s' doesn't allow an argument\n"
#~ msgstr "%s: opción '--%s' no es válida como argumento\n"

#~ msgid "%s: unrecognized option '--%s'\n"
#~ msgstr "%s: opción desconocida para `--%s'\n"

#~ msgid "%s: option '-W %s' is ambiguous\n"
#~ msgstr "%s: opción '-W %s' es ambigua\n"

#~ msgid "%s: option '-W %s' doesn't allow an argument\n"
#~ msgstr "%s: opción '-W %s' no permite argumento\n"

#~ msgid "%s: option '-W %s' requires an argument\n"
#~ msgstr "%s: opción '-W %s' require un argumento\n"

#~ msgid "Usage: mv <file1> <file2>\n"
#~ msgstr "Uso: mv <archivo1> <archivo2>\n"

#, fuzzy
#~ msgid "number"
#~ msgstr "número inválido"

#, fuzzy
#~ msgid "error: %s:%d\n"
#~ msgstr "Error fatal: %s"

#~ msgid ""
#~ "\n"
#~ "Mirror specified remote directory to local directory\n"
#~ "\n"
#~ " -c, --continue         continue a mirror job if possible\n"
#~ " -e, --delete           delete files not present at remote site\n"
#~ "     --delete-first     delete old files before transferring new ones\n"
#~ " -s, --allow-suid       set suid/sgid bits according to remote site\n"
#~ "     --allow-chown      try to set owner and group on files\n"
#~ "     --ignore-time      ignore time when deciding whether to download\n"
#~ " -n, --only-newer       download only newer files (-c won't work)\n"
#~ " -r, --no-recursion     don't go to subdirectories\n"
#~ " -p, --no-perms         don't set file permissions\n"
#~ "     --no-umask         don't apply umask to file modes\n"
#~ " -R, --reverse          reverse mirror (put files)\n"
#~ " -L, --dereference      download symbolic links as files\n"
#~ " -N, --newer-than=SPEC  download only files newer than specified time\n"
#~ " -P, --parallel[=N]     download N files in parallel\n"
#~ " -i RX, --include RX    include matching files\n"
#~ " -x RX, --exclude RX    exclude matching files\n"
#~ "                        RX is extended regular expression\n"
#~ " -v, --verbose[=N]      verbose operation\n"
#~ "     --log=FILE         write lftp commands being executed to FILE\n"
#~ "     --script=FILE      write lftp commands to FILE, but don't execute "
#~ "them\n"
#~ "     --just-print, --dry-run    same as --script=-\n"
#~ "\n"
#~ "When using -R, the first directory is local and the second is remote.\n"
#~ "If the second directory is omitted, basename of first directory is used.\n"
#~ "If both directories are omitted, current local and remote directories are "
#~ "used.\n"
#~ "See lftp(1) for a complete list of options.\n"
#~ msgstr ""
#~ "\n"
#~ "Duplica el directorio remoto especificado en uno local\n"
#~ "\n"
#~ " -c, --continue         si es posible continúa una tarea de duplicación\n"
#~ " -e, --delete           borra archivos ausentes en el sitio remoto\n"
#~ " -s, --allow-suid       asigna los bits suid/sgid según sea su estado en "
#~ "el\n"
#~ "                        sitio remoto\n"
#~ "     --allow-chown      intentar asignar el propietario y el grupo\n"
#~ " -n, --only-newer       transfiere sólo los archivos que sean más "
#~ "recientes\n"
#~ "                        (-c no funciona)\n"
#~ " -r, --no-recursion     no descender por los subdirectorios\n"
#~ " -p, --no-perms         no asignar permisos en archivos\n"
#~ "     --no-umask         no aplicar umask a los modos de los archivos\n"
#~ " -R, --reverse          duplicación invertida (llevar archivos)\n"
#~ " -L, --dereference      transfiere enlaces simbólicos como archivos "
#~ "comunes\n"
#~ " -N, --newer-than ARCH  transfiere sólo archivos más nuevos que ARCH\n"
#~ " -P, --parallel[=N]     transfiere N archivos en paralelo\n"
#~ " -i RX, --include RX    incluye archivos según patrón\n"
#~ " -x RX, --exclude RX    excluye archivos según patrón\n"
#~ "                        RX es una expresión regular extendida\n"
#~ " -v, --verbose[=N]      mayor detalle en la infomación\n"
#~ "     --use-cache        retener y reusar los los directorios obtenidos\n"
#~ "\n"
#~ "Al usar -R, el primer directorio es local y el segundo es remoto.\n"
#~ "Si se omite el segundo, se usa el nombre base del primero.\n"
#~ "Si se omiten ambos, se usan los directorios local y remoto actuales.\n"
#~ "Mira el manual lftp(1) para una completa lista de opciones.\n"

#~ msgid "SITE CHMOD is disabled by ftp:use-site-chmod"
#~ msgstr "SITE CHMOD está desabilitado por ftp:use-site-chmod"

#~ msgid "ftp:ssl-auth must be one of: SSL, TLS, TLS-P, TLS-C"
#~ msgstr "ftp:ssl-auth debe estar entre: SSL, TLS, TLS-P, TLS-C"

#~ msgid "invalid pair of numbers"
#~ msgstr "par de números inválido"

#~ msgid "Saw `unknown', assume failed login"
#~ msgstr "Vi `unknown', asumiendo identificación fallida"

#~ msgid "Couldn't create temporary file `%s': %s.\n"
#~ msgstr "No se pudo crear el archivo temporal `%s': %s.\n"

#~ msgid "%s: error writing %s: %s\n"
#~ msgstr "%s: error escribiendo %s: %s\n"

#, fuzzy
#~ msgid "block size"
#~ msgstr "tamaño de bloque inválido"

#~ msgid "%sTo be removed: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
#~ msgstr ""
#~ "%sSerán borrados: %d directorio$|s$, %d archivo$|s$, %d enlace$|s$\n"

#~ msgid "Usage: %s userid [pass]\n"
#~ msgstr "Uso: %s id_usuario [clave]\n"

#~ msgid "Cache is on"
#~ msgstr "El caché está encendido"

#~ msgid "Cache is off"
#~ msgstr "El caché está apagado"

#~ msgid "Cache entries do not expire"
#~ msgstr "Las entradas del caché no expiran"

#~ msgid "Cache entries expire in %ld $#l#second|seconds$\n"
#~ msgstr "Las entradas del caché expiran en %ld segundo$#l#|s$\n"

#~ msgid "Cache entries expire in %ld $#l#minute|minutes$\n"
#~ msgstr "Las entradas del caché expiran en %ld minuto$#l#|s$\n"

#~ msgid ""
#~ "This is free software with ABSOLUTELY NO WARRANTY. See COPYING for "
#~ "details.\n"
#~ msgstr ""
#~ "Esto es software libre SIN NINGUNA GARANTÍA. Vea COPYING para detalles.\n"

#~ msgid "dns cache hit\n"
#~ msgstr "acierto en el cache dns\n"

#~ msgid "child returned invalid data"
#~ msgstr "proceso hijo devolvió datos inválidos"

#, fuzzy
#~ msgid "%s: %s: %s\n"
#~ msgstr "Uso: %s\n"

#~ msgid "%s: Invalid number for size\n"
#~ msgstr "%s: Número inválido para size\n"

#~ msgid "Warning: getcwd() failed: %s\n"
#~ msgstr "Aviso: falló getcwd(): %s\n"

#~ msgid "No directory to execute commands in - terminating\n"
#~ msgstr "No hay directorio en el que ejecutar las órdenes - terminando\n"

#~ msgid "Usage: %s mode file...\n"
#~ msgstr "Uso: %s modo archivo...\n"

#~ msgid "%s: %s - not an octal number\n"
#~ msgstr "%s: %s - no es un número octal\n"

#~ msgid "Removing old remote file `%s'"
#~ msgstr "Borrando archivo remoto antiguo `%s'"

#~ msgid "Retrieving remote file `%s'"
#~ msgstr "Obteniendo archivo remoto `%s'"

#~ msgid "mirror: cannot create `file:' access object, installation error?\n"
#~ msgstr ""
#~ "mirror: no se pudo crear objeto de acceso `file:', ¿error de "
#~ "instalación?\n"

#~ msgid "List remote file names\n"
#~ msgstr "Lista nombres de archivos remotos\n"

#~ msgid "Same as `get -c'\n"
#~ msgstr "Igual que `get -c'\n"

#~ msgid "Same as `put -c'\n"
#~ msgstr "Igual que `put -c'\n"

#~ msgid "bzcat <files>"
#~ msgstr "bzcat <archivos>"

#~ msgid "bzmore <files>"
#~ msgstr "bzmore <archivos>"

#~ msgid ""
#~ "Usage: queue <command>\n"
#~ "Add the command to queue for current site. Each site has its own\n"
#~ "command queue. It is possible to queue up a running job by using\n"
#~ "command `queue wait <jobno>'.\n"
#~ msgstr ""
#~ "Uso: queue <orden>\n"
#~ "Añade la orden a la cola del sitio actual. Cada sitio tiene su propia\n"
#~ "cola de órdenes. Es posible encolar una tarea activa mediante la orden\n"
#~ "`queue wait <númtarea>'.\n"

#~ msgid "apache listing matched"
#~ msgstr "listado estilo apache"

#~ msgid "unusual apache listing matched"
#~ msgstr "listado estilo apache pero inusual"

#~ msgid "Netscape-Proxy 2.53 listing matched"
#~ msgstr "listado estilo Netscape-Proxy 2.53"

#~ msgid "Netscape-Proxy 2.53 listing matched (dir/symlink)"
#~ msgstr "listado estilo Netscape-Proxy 2.53 (dir/enlace simbólico)"

#~ msgid "squid EPLF listing matched"
#~ msgstr "listado estilo EPLF de squid"

#~ msgid "Mini-Proxy web server listing matched"
#~ msgstr "listado estilo Mini-Proxy web server"

#~ msgid "apache ftp over http proxy listing matched"
#~ msgstr "listado estilo apache haciendo proxy de ftp sobre http"

#~ msgid "Saw `Login incorrect', assume failed login"
#~ msgstr "Vi `Login incorrect', asumiendo indentificación fallida"

#~ msgid "%s: cannot add empty bookmark\n"
#~ msgstr "%s: no se puede agregar un señalador vacío\n"

#~ msgid "connect: %s"
#~ msgstr "connect: %s"

#~ msgid "%s: cannot get current directory\n"
#~ msgstr "%s: no se pudo obtener el directorio actual\n"

#~ msgid "%s failed for %d of %d directories\n"
#~ msgstr "%s falló para %d de %d directorios\n"

#~ msgid "Fatal protocol error occured"
#~ msgstr "Ocurrió un error fatal de protocolo"

#~ msgid "Usage: %s files...\n"
#~ msgstr "Uso: %s archivos...\n"

#~ msgid "Use specified info for remote login\n"
#~ msgstr ""
#~ "Usa la información especificada para la identificación con el servidor\n"

#~ msgid "Sorry, %s can work with only ftp protocol\n"
#~ msgstr "Lo siento, %s sólo puede funcionar con el protocolo ftp\n"

#~ msgid "Usage: %s [-c] [-p] <source> <dest>\n"
#~ msgstr "Uso: %s [-c] [-p] <origen> <destino>\n"

#~ msgid "Getting size of `%s' [%s]"
#~ msgstr "Obteniendo tamaño de `%s' [%s]"

#~ msgid "Copying of `%s' in progress (%c)"
#~ msgstr "Copiado de `%s' en progreso (%c)"

#~ msgid "%s: no such files\n"
#~ msgstr "%s: no se encontraron esos archivos\n"

#~ msgid " - not supported protocol\n"
#~ msgstr " - protocolo no soportado\n"

#~ msgid "%s: %s - not supported protocol\n"
#~ msgstr "%s: %s - protocolo no soportado\n"

#~ msgid "remote rm(%s) - %s\n"
#~ msgstr "rm(%s) remoto - %s\n"

#~ msgid "\tNo files transferred successfully :(\n"
#~ msgstr "\tNingún archivo fue transferido exitosamente :(\n"

#~ msgid "Average transfer rate %g bytes/s\n"
#~ msgstr "Tasa promedio de transferencia %g bytes/s\n"

#~ msgid "%s: cannot write -- disk full?\n"
#~ msgstr "%s: no se puede escribir -- ¿disco lleno?\n"

#~ msgid "mget [-c] [-d] [-e] <files>"
#~ msgstr "mget [-c] [-d] [-e] <archivos>"

#~ msgid ""
#~ "Upload files with wildcard expansion\n"
#~ " -c  continue, reput\n"
#~ " -d  create directories the same as in file names and put the\n"
#~ "     files into them instead of current directory\n"
#~ msgstr ""
#~ "Envía los archivos seleccionados mediante el patrón\n"
#~ " -c  continúa, `reput'\n"
#~ " -d  crea directorios como los haya en los nombres de los archivos\n"
#~ "     y coloca los archivos en ellos en vez de en el directorio actual\n"

#~ msgid "Wait for specified job to terminate.\n"
#~ msgstr "Espera que termine la tarea especificada.\n"

#~ msgid "Usage: site <site_cmd>\n"
#~ msgstr "Uso: site <orden_directa_al_sitio>\n"

#~ msgid ""
#~ "FtpGet | Version %s | Copyright (C) 1996-1999 Alexander V. Lukyanov\n"
#~ msgstr ""
#~ "FtpGet | Versión %s | Copyright (C) 1996-1999 Alexander V. Lukyanov\n"

#~ msgid ""
#~ "Usage: ftpget [OPTIONS] host filename [-o local] [filename...]\n"
#~ "\n"
#~ "-p  --port         set port number\n"
#~ "-u  --user         login as user using pass as password\n"
#~ "-l  --list         get listing of specified directory(ies)\n"
#~ "-c  --continue     reget specified file(s)\n"
#~ "-q  --quiet        quiet (no output)\n"
#~ "-v  --verbose      verbose (lots of output)\n"
#~ "    --async-mode   use asynchronous mode (faster)\n"
#~ "    --sync-mode    use synchronous mode (compatible with bugs)\n"
#~ "\n"
#~ "-o  output to local file `local' (default - base name of filename)\n"
#~ msgstr ""
#~ "Uso: ftpget [OPCIONES] host archivo [-o local] [archivo...]\n"
#~ "\n"
#~ "-p  --port         asigna el número de port\n"
#~ "-u  --user         se identifica como ususario usando clav como clave\n"
#~ "-l  --list         obtiene el listado de los directorios especificados\n"
#~ "-c  --continue     continúa el get de archivos\n"
#~ "-q  --quiet        operación silenciosa (sin mensajes de salida)\n"
#~ "-v  --verbose      verborrágico (muchos mensajes)\n"
#~ "    --async-mode   usa modo asíncrono (más rápido)\n"
#~ "    --sync-mode    usa modo síncrónico (compatible con bugs)\n"
#~ "\n"
#~ "-o  salida hacia el archivo local (por omisión: nombre base de cada "
#~ "archivo)\n"

#~ msgid "Usage: %s [-c] [-d] [-e] pattern ...\n"
#~ msgstr "Uso: %s [-c] [-d] [-e] patrón ...\n"

#~ msgid "\tat %ld (%d%%) [%s]\n"
#~ msgstr "\ten %ld (%d%%) [%s]\n"

#~ msgid "%s: %s - invalid time precision\n"
#~ msgstr "%s: %s - precisión de tiempo inválida\n"

#~ msgid ""
#~ "%s: Invalid expire period (use Ns - in sec, Nm - in min, Nh - in hours)\n"
#~ msgstr ""
#~ "%s: Expiración inválida (use Ns - en segs, Nm - en mins, Nh - en horas)\n"

#~ msgid "%s: invalid delay. "
#~ msgstr "%s: demora inválida. "

#~ msgid "Timeout - trying sync mode (is it windoze?)"
#~ msgstr ""
#~ "Tiempo fuera - probando con modo `sync' (¿es windoze del otro lado?)"

#~ msgid "Operation is in progress"
#~ msgstr "Operación en progreso"

#~ msgid "Class is not Open()ed"
#~ msgstr "La clase no se inicializó con Open()"

#~ msgid "%s: %s - no such tcp service, using default\n"
#~ msgstr "%s: %s - no existe tal servicio tcp, usando valor por omisión\n"

#~ msgid ""
#~ "Set variable to given value. If the value is omitted, unset the "
#~ "variable.\n"
#~ "If called with no variable, currently set variables are listed.\n"
#~ msgstr ""
#~ "Asigna la variable con un valor dado. Si se omite el valor, borra la\n"
#~ "asignación previa. Si se llama sin una variable, se listan las variables\n"
#~ "actualmente asignadas.\n"

#~ msgid "`%s', got %lu of %lu (%d%%) %s"
#~ msgstr "`%s', se obtuvieron %lu de %lu (%d%%) %s"

#~ msgid "Remove remote files\n"
#~ msgstr "Borra archivos remotos\n"

#~ msgid "SwitchToState called with invalid state\n"
#~ msgstr "SwitchToState llamada con estado inválido\n"

# , c-format
#~ msgid "Usage: %s <variable> [<value>]\n"
#~ msgstr "Uso: %s <variable> [<valor>]\n"

#~ msgid "Store failed - reput is needed"
#~ msgstr "Envío fallido - se necesita reput"

#~ msgid "Cache entries expire in 1 second"
#~ msgstr "Las entradas del caché expiran en 1 segundo"

#~ msgid "Cache entries expire in 1 minute"
#~ msgstr "Las entradas del caché expiran en 1 minuto"

# , c-format
#~ msgid "%d files total\n"
#~ msgstr "%d archivos en total\n"

#~ msgid ""
#~ "open - select an ftp server\n"
#~ "Usage: open [-e cmd] [-u user[,pass]] [-p port] <host|url>\n"
#~ msgstr ""
#~ "open - selecciona un servidor ftp\n"
#~ "Uso: open [-e cmd] [-p puerto] [-u usuario[,clave]] <servidor|url>\n"

#~ msgid "source: %s\n"
#~ msgstr "fuente: %s\n"

#~ msgid "wait: <jobno> must be a number\n"
#~ msgstr "wait: <númtarea> debe ser un número\n"

# , c-format
#~ msgid "%s: %s - should be a number\n"
#~ msgstr "%s: %s - debe ser un número\n"

#~ msgid "ON"
#~ msgstr "ENCENDIDO"

#~ msgid "OFF"
#~ msgstr "APAGADO"
