# translation of de.po to deutsch
# translation of de.po to
# LFTP -  deutsche Übersetzung
# Korrekturen, KRITIK SEHR willkommen
# COPYRIGHT:
# GPL
# <PERSON><PERSON>-<PERSON> <<EMAIL>>, 1999,2000,2001,2002,2003, 2004, 2008.
msgid ""
msgstr ""
"Project-Id-Version: de\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2024-11-08 13:05+0300\n"
"PO-Revision-Date: 2008-03-29 14:27+0100\n"
"Last-Translator: <PERSON><PERSON>-<PERSON> <<EMAIL>>\n"
"Language-Team: German <<EMAIL>>\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: KAider 0.1\n"
"Plural-Forms:  nplurals=2; plural=(n != 1);\n"

#: lib/argmatch.c:145
#, c-format
msgid "invalid argument %s for %s"
msgstr "ungültiges Argument %s für »%s«"

#: lib/argmatch.c:146
#, c-format
msgid "ambiguous argument %s for %s"
msgstr "mehrdeutiges Argument  %s für %s"

#: lib/argmatch.c:165
msgid "Valid arguments are:"
msgstr "Gültige Argumente sind:"

#: lib/error.c:208
msgid "Unknown system error"
msgstr "Unbekannter Systemfehler"

#: lib/getopt.c:282
#, fuzzy, c-format
msgid "%s: option '%s%s' is ambiguous\n"
msgstr "%s: Option »-W %s« ist mehrdeutig\n"

#: lib/getopt.c:288
#, fuzzy, c-format
msgid "%s: option '%s%s' is ambiguous; possibilities:"
msgstr "%s: Option »%s« ist mehrdeutig\n"

#: lib/getopt.c:322
#, fuzzy, c-format
msgid "%s: unrecognized option '%s%s'\n"
msgstr "%s: nicht erkannte Option »--%c%s«\n"

#: lib/getopt.c:348
#, fuzzy, c-format
msgid "%s: option '%s%s' doesn't allow an argument\n"
msgstr "%s: Option »%c%s« erlaubt kein Argument\n"

#: lib/getopt.c:363
#, fuzzy, c-format
msgid "%s: option '%s%s' requires an argument\n"
msgstr "%s: Option »%s« erfordert einen Parameter\n"

#: lib/getopt.c:624
#, fuzzy, c-format
msgid "%s: invalid option -- '%c'\n"
msgstr "%s: Ungültige Option --%c\n"

#: lib/getopt.c:639 lib/getopt.c:685
#, fuzzy, c-format
msgid "%s: option requires an argument -- '%c'\n"
msgstr "%s: Option erfordert einen Parameter --%c\n"

#. TRANSLATORS:
#. Get translations for open and closing quotation marks.
#. The message catalog should translate "`" to a left
#. quotation mark suitable for the locale, and similarly for
#. "'".  For example, a French Unicode local should translate
#. these to U+00AB (LEFT-POINTING DOUBLE ANGLE
#. QUOTATION MARK), and U+00BB (RIGHT-POINTING DOUBLE ANGLE
#. QUOTATION MARK), respectively.
#.
#. If the catalog has no translation, we will try to
#. use Unicode U+2018 (LEFT SINGLE QUOTATION MARK) and
#. Unicode U+2019 (RIGHT SINGLE QUOTATION MARK).  If the
#. current locale is not Unicode, locale_quoting_style
#. will quote 'like this', and clocale_quoting_style will
#. quote "like this".  You should always include translations
#. for "`" and "'" even if U+2018 and U+2019 are appropriate
#. for your locale.
#.
#. If you don't know what to put here, please see
#. <https://en.wikipedia.org/wiki/Quotation_marks_in_other_languages>
#. and use glyphs suitable for your language.
#: lib/quotearg.c:354
msgid "`"
msgstr "»"

#: lib/quotearg.c:355
msgid "'"
msgstr "«"

#: lib/xalloc-die.c:34
msgid "memory exhausted"
msgstr "Speicher erschöpft"

#: src/ArgV.cc:107
#, fuzzy
msgid "option requires an argument"
msgstr "%s: Option »%s« erfordert einen Parameter\n"

#: src/ArgV.cc:109 src/ArgV.cc:118
#, fuzzy
msgid "invalid option"
msgstr "%s: Ungültige Option --%c\n"

#: src/ArgV.cc:114
#, fuzzy, c-format
msgid "option `%s' requires an argument"
msgstr "%s: Option »%s« erfordert einen Parameter\n"

#: src/ArgV.cc:116
#, fuzzy, c-format
msgid "unrecognized option `%s'"
msgstr "%s: nicht erkannte Option »--%c%s«\n"

#: src/attach.h:138
#, fuzzy, c-format
msgid "[%u] Attached to terminal %s. %s\n"
msgstr "[%lu] Beendet durch Signal %d. %s"

#: src/attach.h:150
#, c-format
msgid "[%u] Attached to terminal.\n"
msgstr ""

#: src/buffer.cc:253 src/FileAccess.cc:866
msgid " [cached]"
msgstr "[zwischengespeichert]"

#: src/ChmodJob.cc:80
#, c-format
msgid "Failed to change mode of `%s' to %04o (%s).\n"
msgstr "Konnte Modus nicht von »%s« zu %04o (%s) ändern.\n"

#: src/ChmodJob.cc:83
#, c-format
msgid "Mode of `%s' changed to %04o (%s).\n"
msgstr "Modus von »%s«  nach %04o (%s) geändert.\n"

#: src/ChmodJob.cc:88
#, c-format
msgid "Failed to change mode of `%s' because no old mode is available.\n"
msgstr ""
"Konnte den Modus von »%s« nicht ändern, da kein alter Modus zugänglich war.\n"

#: src/CmdExec.cc:105
#, c-format
msgid "Warning: chdir(%s) failed: %s\n"
msgstr "Warnung: chdir(%s) fehlgeschlagen: %s\n"

#: src/CmdExec.cc:207
#, c-format
msgid "Unknown command `%s'.\n"
msgstr "Unbekannter Befehl »%s«.\n"

#: src/CmdExec.cc:209
#, c-format
msgid "Ambiguous command `%s'.\n"
msgstr "Mehrdeutiger Befehl »%s«.\n"

#: src/CmdExec.cc:228
#, c-format
msgid "Module for command `%s' did not register the command.\n"
msgstr "Das Modul für den Befehl »%s« hat den Befehl nicht registriert.\n"

#: src/CmdExec.cc:384
#, c-format
msgid "cd ok, cwd=%s\n"
msgstr "Verzeichniswechsel OK, cwd=%s\n"

#: src/CmdExec.cc:405 src/MirrorJob.cc:736
#, c-format
msgid "%s: received redirection to `%s'\n"
msgstr "%s: Umleitung nach »%s« empfangen\n"

#: src/CmdExec.cc:408 src/FileCopy.cc:1230
msgid "Too many redirections"
msgstr "Zu viele Umleitungen"

#: src/CmdExec.cc:517 src/CmdExec.cc:574
msgid "Interrupt"
msgstr "Abbruch"

#: src/CmdExec.cc:639
#, c-format
msgid "Warning: discarding incomplete command\n"
msgstr "Warnung: unvollständiger Befehl verworfen\n"

#: src/CmdExec.cc:737
#, c-format
msgid "\tExecuting builtin `%s' [%s]\n"
msgstr "\tEingebautes»%s« wird ausgeführt [%s]\n"

#: src/CmdExec.cc:742
msgid "Queue is stopped."
msgstr "Warteschlange wurde angehalten."

#: src/CmdExec.cc:747
msgid "Now executing:"
msgstr "Momentan läuft:"

#: src/CmdExec.cc:758
#, c-format
msgid "\tWaiting for job [%d] to terminate\n"
msgstr "\t Warte auf Beendigung von Job [%d]\n"

#: src/CmdExec.cc:761
#, c-format
msgid "\tWaiting for termination of jobs: "
msgstr "\tWarte auf Erledigung der Jobs: "

#: src/CmdExec.cc:770
msgid "\tRunning\n"
msgstr "\tWird ausgeführt\n"

#: src/CmdExec.cc:772
msgid "\tWaiting for command\n"
msgstr "\tWarten auf Befehl\n"

#: src/CmdExec.cc:1261
#, c-format
msgid "%s: command `%s' is not compiled in.\n"
msgstr "%s: Befehl »%s« wurde nicht mitkompiliert.\n"

#: src/CmdExec.cc:1278
#, fuzzy, c-format
msgid "Usage: %s cmd [args...]\n"
msgstr "Benutzung: %s module [Argumente..]\n"

#: src/CmdExec.cc:1284
#, c-format
msgid "%s: cannot create local session\n"
msgstr ""

#: src/commands.cc:114
msgid "!<shell-command>"
msgstr "!<Shell-Befehl>"

#: src/commands.cc:115
msgid "Launch shell or shell command\n"
msgstr "Starte Shell oder Shell Befehl\n"

#: src/commands.cc:116
msgid "(commands)"
msgstr "(Befehle)"

#: src/commands.cc:117
msgid ""
"Group commands together to be executed as one command\n"
"You can launch such a group in background\n"
msgstr ""
"Befehle zu Gruppen zusammenfassen um sie mit einem Kommando auszuführen\n"
"Es ist möglich derartige Gruppen im Hintergrund laufen zu lassen\n"

#: src/commands.cc:120
msgid "alias [<name> [<value>]]"
msgstr "alias [<Name> [<Wert>]]"

#: src/commands.cc:121
msgid ""
"Define or undefine alias <name>. If <value> omitted,\n"
"the alias is undefined, else is takes the value <value>.\n"
"If no argument is given the current aliases are listed.\n"
msgstr ""
"Definiert oder löscht alias <Name>. Wenn <Wert> fehlt,\n"
"wird der Alias gelöscht, sonst nimmt er den Wert <Wert> an.\n"
"Wird kein Argument angegeben, so werden die bestehenden Aliase aufgelistet.\n"

#: src/commands.cc:125
msgid "anon - login anonymously (by default)\n"
msgstr "anon - anonymes Login (Vorgabe)\n"

#: src/commands.cc:127
msgid "bookmark [SUBCMD]"
msgstr "bookmark [UNTERBEFEHL]"

#: src/commands.cc:128
msgid ""
"bookmark command controls bookmarks\n"
"\n"
"The following subcommands are recognized:\n"
"  add <name> [<loc>] - add current place or given location to bookmarks\n"
"                       and bind to given name\n"
"  del <name>         - remove bookmark with the name\n"
"  edit               - start editor on bookmarks file\n"
"  import <type>      - import foreign bookmarks\n"
"  list               - list bookmarks (default)\n"
msgstr ""
"Der bookmark Befehl verwaltet die Lesezeichen\n"
"\n"
"Die folgenden Unterbefehle werden erkannt:\n"
"  add <Name> [<Ort>] - momentanen Verzeichnis oder angegebenes Verzeichnis\n"
"                       als Lesezeichen ablegen und dem Namen zuordnen\n"
"  del <Name>         - Lesezeichen diesen Namens entfernen\n"
"  edit               - Lesezeichendatei mit Editor bearbeiten\n"
"  import <Typ>       - fremdformatige Lesezeichen einlesen\n"
"  list               - Lesezeichen auflisten (Vorgabe)\n"

#: src/commands.cc:137
msgid "cache [SUBCMD]"
msgstr "cache [UNTERBEFEHL]"

#: src/commands.cc:138
msgid ""
"cache command controls local memory cache\n"
"\n"
"The following subcommands are recognized:\n"
"  stat        - print cache status (default)\n"
"  on|off      - turn on/off caching\n"
"  flush       - flush cache\n"
"  size <lim>  - set memory limit\n"
"  expire <Nx> - set cache expiration time to N seconds (x=s)\n"
"                minutes (x=m) hours (x=h) or days (x=d)\n"
msgstr ""
"Der cache Befehl kontrolliert den lokalen Zwischenspeiche (Cache)\n"
"\n"
"Die folgenden Unterbefehle werden erkannt:\n"
"  stat        - Status des Cache ausgeben (Vorgabe)\n"
"  on|off      - Cache an- oder ausschalten\n"
"  flush       - Cache leeren\n"
"  size <Lim>  - Speichergrenze setzen\n"
"  expire <Nx> - Ablaufzeit des Cache setzen auf N Sekunden (x=s)\n"
"                Minuten (x=m), Stunden (x=h) oder Tage (x=d)\n"

#: src/commands.cc:146
msgid "cat [-b] <files>"
msgstr "cat [-b] <Dateien>"

#: src/commands.cc:147
msgid ""
"cat - output remote files to stdout (can be redirected)\n"
" -b  use binary mode (ascii is the default)\n"
msgstr ""
"cat - nichtlokale Dateien an STDOUT ausgeben. (Kann umgeleitet werden)\n"
" -b Binärmodus verwenden (Textmodus ASCII ist der Standardwert)\n"

#: src/commands.cc:149
msgid "cd <rdir>"
msgstr "cd <verzeichnis>"

#: src/commands.cc:150
msgid ""
"Change current remote directory to <rdir>. The previous remote directory\n"
"is stored as `-'. You can do `cd -' to change the directory back.\n"
"The previous directory for each site is also stored on disk, so you can\n"
"do `open site; cd -' even after lftp restart.\n"
msgstr ""
"Wechsle in das nichtlokale Verzeichnis. Das vorherige Verzeichnis auf dem "
"anderen\n"
"Rechner wird unter »-« gespeichert. Man kann mit »cd -« wieder zurückgehen.\n"
"Das vorherige Verzeichnis für jeden Server wird auch auf Festplatte "
"gesichert, so\n"
"dass man auch bei einem Neustart von Lftp »open Server; cd -« benutzen "
"kann.\n"

#: src/commands.cc:154
msgid "chmod [OPTS] mode file..."
msgstr "chmod [OPTIONEN] modus Datei..."

#: src/commands.cc:155
msgid ""
"Change the mode of each FILE to MODE.\n"
"\n"
" -c, --changes        - like verbose but report only when a change is made\n"
" -f, --quiet          - suppress most error messages\n"
" -v, --verbose        - output a diagnostic for every file processed\n"
" -R, --recursive      - change files and directories recursively\n"
"\n"
"MODE can be an octal number or symbolic mode (see chmod(1))\n"
msgstr ""
"Den Modus jeder Datei auf MODUS ändern.\n"
"\n"
" -c, --changes        - entspricht »verbose«, aber meldet nur Änderungen\n"
" -f, --quiet          - die meisten Fehlermeldungen abschalten\n"
" -v, --verbose        - Bericht für jede bearbeitete Datei ausgeben\n"
" -R, --recursive      - Dateien und Verzeichnisse rekursiv ändern\n"
"\n"
"Der MODUS kann binär oder symbolisch angegeben werden (vgl. chmod(1))\n"

#: src/commands.cc:164
msgid ""
"Close idle connections. By default only with current server.\n"
" -a  close idle connections with all servers\n"
msgstr ""
"Schließe unbenutzte Verbindungen. Standardmäßig nur mit dem aktuellen "
"Server.\n"
" -a  unbenutzte Verbindungen auf allen Servern schließen\n"

#: src/commands.cc:166
msgid "[re]cls [opts] [path/][pattern]"
msgstr "[re]cls [Optionen] [Pfad/][Muster]"

#: src/commands.cc:167
#, fuzzy
msgid ""
"List remote files. You can redirect output of this command to file\n"
"or via pipe to external command.\n"
"\n"
" -1                   - single-column output\n"
" -a, --all            - show dot files\n"
" -B, --basename       - show basename of files only\n"
"     --block-size=SIZ - use SIZ-byte blocks\n"
" -d, --directory      - list directory entries instead of contents\n"
" -F, --classify       - append indicator (one of /@) to entries\n"
" -h, --human-readable - print sizes in human readable format (e.g., 1K)\n"
"     --si             - likewise, but use powers of 1000 not 1024\n"
" -k, --kilobytes      - like --block-size=1024\n"
" -l, --long           - use a long listing format\n"
" -q, --quiet          - don't show status\n"
" -s, --size           - print size of each file\n"
"     --filesize       - if printing size, only print size for files\n"
" -i, --nocase         - case-insensitive pattern matching\n"
" -I, --sortnocase     - sort names case-insensitively\n"
" -D, --dirsfirst      - list directories first\n"
"     --sort=OPT       - \"name\", \"size\", \"date\"\n"
" -S                   - sort by file size\n"
" --user, --group, --perms, --date, --linkcount, --links\n"
"                      - show individual fields\n"
" --time-style=STYLE   - use specified time format\n"
"\n"
"By default, cls output is cached, to see new listing use `recls' or\n"
"`cache flush'.\n"
"\n"
"The variables cls-default and cls-completion-default can be used to\n"
"specify defaults for cls listings and completion listings, respectively.\n"
"For example, to make completion listings show file sizes, set\n"
"cls-completion-default to \"-s\".\n"
"\n"
"Tips: Use --filesize with -D to pack the listing better.  If you don't\n"
"always want to see file sizes, --filesize in cls-default will affect the\n"
"-s flag on the commandline as well.  Add `-i' to cls-completion-default\n"
"to make filename completion case-insensitive.\n"
msgstr ""
"Dateien der Gegenstelle auflisten. Man kann die Ausgabe des Befehls\n"
"in eine Datei umleiten oder mittels einer Pipe in ein Programm einspeisen.\n"
"\n"
" -1                   - einspaltige Ausgabe\n"
" -B, --basename       - nur die Namenswurzel anzeigen\n"
"   --block-size=GRÖßE - Blocks in der GRÖßE in Bytes\n"
" -d, --directory      - Verzeichnisnamen statt Verzeichnisinhalt ausgeben\n"
" -F, --classify       - Indikator (einen aus /@) an Einträge hängen\n"
" -h, --human-readable - Dateigrößen in verständlichem Format ausgeben(z.B. "
"1k)\n"
"     --si             - ebenso, aber 1000 statt 1024 als Grundlage nehmen\n"
" -k  --kilobytes      - entspricht --block-size=1024\n"
" -l, --long           - ausführliches Format der Listenausgabe\n"
" -q, --quiet          - keine Statusmeldungen\n"
" -s, --size           - Größe jeder Datei ausgeben\n"
"     --filesize       - Größe wenn überhaupt nur für Dateien ausgeben\n"
" -i, --nocase         - Groß/Kleinschreibung für Joker nicht beachten\n"
" -I, --sortnocase     - Groß/Kleinschreibung beim Sortieren nicht beachten\n"
"  -D, --dirsfirst      - Verzeichnisse zuerst aufführen\n"
"      --sort=OPT       - \"name\", \"size\", \"date\"\n"
" -S                   - nach Dateigröße sortieren\n"
" --user, --group, --perms, --date, --linkcount, --links\n"
"                      - einzelne Felder ausgeben\n"
" --time-style=STYLE   - angegebenes Zeitformat nutzen\n"
"\n"
"Standardmäßig wird die Ausgabe von cls zwischengespeichert. Um eine neue\n"
"Liste anzufordern kann man »recls« oder »cache flush« verwenden\n"
"\n"
"Die Variablen cls-default und cls-completion-default können verwendet \n"
"werden, um die Einstellung jeweils für die Ausgabe von cls und die Ausgabe\n"
"der automatischen Vervollständigung einzustellen.\n"
"Um zum Beispiel bei letzterem die Dateigröße mit auszugeben, setzt man\n"
"cls-completion-default auf »-s«.\n"
"\n"
"Tips: --filesize und -D verwenden um eine kompaktere Ausgabe zu erzielen.\n"
"Wenn Sie nicht immer Dateigrößen sehen wollen: --filesize in cls-default\n"
"erfaßt auch die -s Option bei der Befehlseingabe. Alle Optionen "
"funktionieren\n"
"in cls-completion-default: -i  in cls-completion-default macht die\n"
"automatische Vervollständigung unabhängig von Groß/Kleinschreibung.\n"

#: src/commands.cc:217
#, fuzzy
msgid "debug [OPTS] [<level>|off]"
msgstr "debug [<Stufe>|off] [-o <Datei>]"

#: src/commands.cc:218
#, fuzzy
msgid ""
"Set debug level to given value or turn debug off completely.\n"
" -o <file>  redirect debug output to the file\n"
" -c  show message context\n"
" -p  show PID\n"
" -t  show timestamps\n"
msgstr ""
"Fehlersuche (Debug) auf angegebene Stufe stellen oder ganz abschalten "
"(off).\n"
" -o <Datei> Fehlerausgabe in die Datei umleiten.\n"

#: src/commands.cc:223
msgid "du [options] <dirs>"
msgstr "du [Optionen] <verzeichnisse>"

#: src/commands.cc:224
msgid ""
"Summarize disk usage.\n"
" -a, --all             write counts for all files, not just directories\n"
"     --block-size=SIZ  use SIZ-byte blocks\n"
" -b, --bytes           print size in bytes\n"
" -c, --total           produce a grand total\n"
" -d, --max-depth=N     print the total for a directory (or file, with --"
"all)\n"
"                       only if it is N or fewer levels below the command\n"
"                       line argument;  --max-depth=0 is the same as\n"
"                       --summarize\n"
" -F, --files           print number of files instead of sizes\n"
" -h, --human-readable  print sizes in human readable format (e.g., 1K 234M "
"2G)\n"
" -H, --si              likewise, but use powers of 1000 not 1024\n"
" -k, --kilobytes       like --block-size=1024\n"
" -m, --megabytes       like --block-size=1048576\n"
" -S, --separate-dirs   do not include size of subdirectories\n"
" -s, --summarize       display only a total for each argument\n"
"     --exclude=PAT     exclude files that match PAT\n"
msgstr ""
"Plattenspeicherverbrauch anzeigen.\n"
" -a, --all             Angaben für alle Dateien und nicht nur Verzeichnisse\n"
"                       ausgeben.\n"
"     --block-size=GRÖßE\n"
"                       Blocks der angegebenen GRÖßE in Bytes verwenden\n"
" -b, --bytes           Größe in Bytes ausgeben\n"
" -c, --total           Gesamtsumme des Speicherbedarfs ausgeben\n"
" -d, --max-depth=N     Summe des Speicherbedarfs für ein Verzeichnis\n"
"                       angeben (oder für eine Datei mit --all) soweit es\n"
"                       N oder weniger Ebenen unter dem Befehlsargument "
"liegt;\n"
"                       --max-depth=0 entspricht --summarize\n"
" -F  --files           Anzahl der Dateien statt der Größe angeben\n"
" -h, --human-readable  Größen in verständlichem Format ausgeben.\n"
"                       (z.B. 1K 234M 2G)\n"
" -H, --si              ähnlich, aber verwendet Vielfache von 1000 nicht "
"1024\n"
" -k, --kilobytes       wie --block-size=1024\n"
" -m, --megabytes       wie --block-size=1048576\n"
" -S, --separate-dirs   Größe der Unterverzeichnisse nicht mit einrechnen\n"
" -s, --summarize       Nur die Summe für jedes Argument angeben\n"
"     --exclude=MUSTER  Dateien die MUSTER entsprechen nicht mitzählen\n"

#: src/commands.cc:242
#, fuzzy
msgid "edit [OPTS] <file>"
msgstr "mget [OPTS] <Dateien>"

#: src/commands.cc:243
msgid ""
"Retrieve remote file to a temporary location, run a local editor on it\n"
"and upload the file back if changed.\n"
" -k  keep the temporary file\n"
" -o <temp>  explicit temporary file location\n"
msgstr ""

#: src/commands.cc:248
msgid "exit [<code>|bg]"
msgstr "exit [<code>|bg]"

#: src/commands.cc:249
msgid ""
"exit - exit from lftp or move to background if jobs are active\n"
"\n"
"If no jobs active, the code is passed to operating system as lftp\n"
"termination status. If omitted, exit code of last command is used.\n"
"`bg' forces moving to background if cmd:move-background is false.\n"
msgstr ""
"exit - lftp verlassen oder in den Hintergrund schicken, falls noch Jobs "
"laufen\n"
"\n"
"Wenn keine Jobs aktiv sind, wird der code als Beendigungsstatus an\n"
"das Betriebssystem weitergegeben. Ohne Angabe wird der Status des letzten\n"
"Befehls benutzt.\n"

#: src/commands.cc:255
msgid ""
"Usage: find [OPTS] [directory]\n"
"Print contents of specified directory or current directory recursively.\n"
"Directories in the list are marked with trailing slash.\n"
"You can redirect output of this command.\n"
" -d, --maxdepth=LEVELS  Descend at most LEVELS of directories.\n"
msgstr ""
"Verwendung: find [OPTIONEN] [Verzeichnis]\n"
"Gibt rekursiv den Inhalt des angegebenen oder des aktuellen Verzeichnisses \n"
"aus. Verzeichnisse werden in der Liste durch einen angehängten "
"Schrägstrich \n"
"(/) gekennzeichnet. Man kann die Ausgabe dieses Befehls umleiten.\n"
"       -d, --maxdepth=EBENEN Maximal bis zu EBENEN tief in "
"Verzeichnisstruktur\n"
"        eindringen\n"

#: src/commands.cc:260
msgid "get [OPTS] <rfile> [-o <lfile>]"
msgstr "get [OPTS] <nldatei> [-o <ldatei>"

#: src/commands.cc:261
#, fuzzy
msgid ""
"Retrieve remote file <rfile> and store it to local file <lfile>.\n"
" -o <lfile> specifies local file name (default - basename of rfile)\n"
" -c  continue, resume transfer\n"
" -E  delete remote files after successful transfer\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Nichtlokale Datei <nldatei> holen und es als lokale Datei <ldatei> "
"speichern.\n"
" -o <ldatei> gibt den Namen der lokalen Datei an (Vorgabe - Name der "
"nldatei)\n"
" -c continue, nach Abbruch an alter Stelle fortfahren\n"
" -E Dateien auf dem Server nach erfolgreicher Übertragung löschen. \n"
" -a ascii / Textmodus verwenden (Binärmodus ist der Standardwert)\n"
" -O <basis> gibt das Oberverzeichnis oder die URL an, wo die Dateien\n"
"    gespeichert werden sollen.\n"

#: src/commands.cc:268
msgid "glob [OPTS] <cmd> <args>"
msgstr "glob [OPTIONEN] <Befehle> <Argumente>"

#: src/commands.cc:270
#, fuzzy
msgid ""
"Expand wildcards and run specified command.\n"
"Options can be used to expand wildcards to list of files, directories,\n"
"or both types. Type selection is not very reliable and depends on server.\n"
"If entry type cannot be determined, it will be included in the list.\n"
" -f  plain files (default)\n"
" -d  directories\n"
" -a  all types\n"
" --exist      return zero exit code when the patterns expand to non-empty "
"list\n"
" --not-exist  return zero exit code when the patterns expand to an empty "
"list\n"
msgstr ""
"Platzhalter (Joker) ausfüllen und angegebenen Befehl ausführen.\n"
"Optionen können die Joker auf Verzeichnisse, Dateien oder beides "
"erstrecken.\n"
"Die Unterscheidung der Typen ist unzuverlässig und hängt von der Gegenstelle "
"ab.\n"
"Wenn keine Unterscheidung getroffen werden kann, wird ein  Eintrag im \n"
"Zweifel in die Liste aufgenommen.\n"
"  -f  einfache Dateien (Standardwert)\n"
"  -d  Verzeichnisse\n"
"  -a  alle Einträge\n"

#: src/commands.cc:279
msgid "help [<cmd>]"
msgstr "help [<Befehl>]"

#: src/commands.cc:280
msgid "Print help for command <cmd>, or list of available commands\n"
msgstr "Zeige die Hilfe für den Befehl <cmd>, sonst zeige verfügbare Befehle\n"

#: src/commands.cc:282
#, fuzzy
msgid ""
"List running jobs. -v means verbose, several -v can be specified.\n"
"If <job_no> is specified, only list a job with that number.\n"
msgstr ""
"Aktive Jobs auflisten. -v bedeutet mit Details, mehrere -v sind möglich.\n"

#: src/commands.cc:284
msgid "kill all|<job_no>"
msgstr "kill all|<job_nr.>"

#: src/commands.cc:285
msgid "Delete specified job with <job_no> or all jobs\n"
msgstr "Löscht den angegebenen Job mit <job_nr.> oder alle Jobs\n"

#: src/commands.cc:286
msgid "lcd <ldir>"
msgstr "lcd <lverz>"

#: src/commands.cc:287
msgid ""
"Change current local directory to <ldir>. The previous local directory\n"
"is stored as `-'. You can do `lcd -' to change the directory back.\n"
msgstr ""
"Wechsle das geltende lokale Verzeichnis zu <lverz>. Das letzte lokale "
"Verzeichnis\n"
"wird als »-« gespeichert. Man kommt mit »lcd -« wieder dorthin zurück.\n"

#: src/commands.cc:289
msgid "lftp [OPTS] <site>"
msgstr "lftp [OPTS] <site>"

#: src/commands.cc:290
#, fuzzy
msgid ""
"`lftp' is the first command executed by lftp after rc files\n"
" -f <file>           execute commands from the file and exit\n"
" -c <cmd>            execute the commands and exit\n"
" --norc              don't execute rc files from the home directory\n"
" --help              print this help and exit\n"
" --version           print lftp version and exit\n"
"Other options are the same as in `open' command:\n"
" -e <cmd>            execute the command just after selecting\n"
" -u <user>[,<pass>]  use the user/password for authentication\n"
" -p <port>           use the port for connection\n"
" -s <slot>           assign the connection to this slot\n"
" -d                  switch on debugging mode\n"
" <site>              host name, URL or bookmark name\n"
msgstr ""
"»lftp« ist der erste Befehl, der von lftp nach den rc-Dateien ausgeführt "
"wird.\n"
" -f <Datei>          Befehle aus dieser Datei ausführen und beenden\n"
" -c <Befehl>         Befehle ausführen und beenden\n"
"Die anderen Optionen sind die selben wie beim »open« Befehl\n"
" -e <Befehl>         Den Befehl direkt nach der Auswahl ausführen\n"
" -u <user>[,<pass>]  Benutze Benutzer und Passwort zur Authentifikation\n"
" -p <Port>           Benutze den angegebenen Port zur Verbindung\n"
" <site>              Name des Hosts, URL oder Name eines Lesezeichen\n"

#: src/commands.cc:303
#, fuzzy
msgid "ln [-s] <file1> <file2>"
msgstr "mv <datei1> <datei2>"

#: src/commands.cc:304
#, fuzzy
msgid "Link <file1> to <file2>\n"
msgstr "<datei1> in <datei2> umbenennen\n"

#: src/commands.cc:308
msgid "ls [<args>]"
msgstr "ls [<args>]"

#: src/commands.cc:309
msgid ""
"List remote files. You can redirect output of this command to file\n"
"or via pipe to external command.\n"
"By default, ls output is cached, to see new listing use `rels' or\n"
"`cache flush'.\n"
"See also `help cls'.\n"
msgstr ""
"Nichtlokale Dateien auflisten. Man kann die Ausgabe dieses Befehls in\n"
"eine Datei oder mittels einer Pipe zu einem externen Befehl umleiten.\n"
"Als Standardvorgabe, wird die Ausgabe zwischengespeichert, um die Liste zu "
"erneuern\n"
"dient entweder »rels« oder »cache flush«.\n"
"Siehe auch »help cls«.\n"

#: src/commands.cc:314
msgid "mget [OPTS] <files>"
msgstr "mget [OPTS] <Dateien>"

#: src/commands.cc:315
#, fuzzy
msgid ""
"Gets selected files with expanded wildcards\n"
" -c  continue, resume transfer\n"
" -d  create directories the same as in file names and get the\n"
"     files into them instead of current directory\n"
" -E  delete remote files after successful transfer\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Holt mittels Platzhaltern (Joker) ausgewählte Dateien\n"
" -c  nach Abbruch fortfahren, erneut laden\n"
" -d  Verzeichnisse mit dem Namen der ausgewählten Dateien erstellen und\n"
"     die Dateien dort ablegen statt im normalen Verzeichnis\n"
" -E  Dateien auf dem Server nach erfolgreicher Übertragung löschen\n"
" -a ASCII / Textmodus verwenden (Binärmodus ist die Voreinstellung)\n"
" -O <basis> gibt das Oberverzeichnis bzw. die URL an, wo Dateien abgelegt\n"
"    werden sollten\n"

#: src/commands.cc:322
msgid "mirror [OPTS] [remote [local]]"
msgstr "mirror [OPTS] [nichtlokal [lokal]]"

#: src/commands.cc:323
#, fuzzy
msgid "mkdir [OPTS] <dirs>"
msgstr "mkdir [-p] <verzeichnis>"

#: src/commands.cc:324
#, fuzzy
msgid ""
"Make remote directories\n"
" -p  make all levels of path\n"
" -f  be quiet, suppress messages\n"
msgstr ""
"Nichtlokale Verzeichnisse anlegen\n"
" -p alle Ebenen des Pfades (gesamten Pfad) anlegen\n"

#: src/commands.cc:327
msgid "module name [args]"
msgstr "module name [argumente]"

#: src/commands.cc:328
msgid ""
"Load module (shared object). The module should contain function\n"
"   void module_init(int argc,const char *const *argv)\n"
"If name contains a slash, then the module is searched in current\n"
"directory, otherwise in directories specified by setting module:path.\n"
msgstr ""
"Lade Modul (shared object). Das Modul sollte folgende Funktion enthalten\n"
"   void module_init(int argc,const char *const *argv\n"
"Wenn der Name einen Querstrich enthält, wird das Modul im aktuellen \n"
"Verzeichnis gesucht, andernfalls wird in den via module:path angegebenen\n"
"Verzeichnissen gesucht\n"

#: src/commands.cc:332
msgid "more <files>"
msgstr "more <Dateien>"

#: src/commands.cc:333
msgid "Same as `cat <files> | more'. if PAGER is set, it is used as filter\n"
msgstr ""
"Entspricht `cat <dateien> | more'. Wenn PAGER gesetzt ist, wird dieser als "
"Filter benutzt.\n"

#: src/commands.cc:334
msgid "mput [OPTS] <files>"
msgstr "mput [OPTS] <Dateien>"

#: src/commands.cc:335
msgid ""
"Upload files with wildcard expansion\n"
" -c  continue, reput\n"
" -d  create directories the same as in file names and put the\n"
"     files into them instead of current directory\n"
" -E  delete local files after successful transfer (dangerous)\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Speichert mittels Platzhaltern (Joker) ausgewählte Dateien auf dem Server\n"
" -c  nach Abbruch fortfahren, erneut hochladen\n"
" -d  Verzeichnisse mit dem Namen der ausgewählten Dateien erstellen und\n"
"     die Dateien dorthin hochladen statt im normalen Verzeichnis\n"
" -e  lokale Dateien nach erfolgreicher Übertragung löschen (Vorsicht!!)\n"
" -a  ASCII / Textmodus verwenden (Binärmodus ist die Vorgabe)\n"
" -O  <basis> gibt das Oberverzeichnis bzw. die URL an, wo die Dateien\n"
"     abgelegt werden sollen.\n"

#: src/commands.cc:342
msgid "mrm <files>"
msgstr "mrm <dateien>"

#: src/commands.cc:343
msgid "Removes specified files with wildcard expansion\n"
msgstr "Löscht die mit Platzhaltern(Joker) ausgewählten Dateien\n"

#: src/commands.cc:344
msgid "mv <file1> <file2>"
msgstr "mv <datei1> <datei2>"

#: src/commands.cc:345
msgid "Rename <file1> to <file2>\n"
msgstr "<datei1> in <datei2> umbenennen\n"

#: src/commands.cc:346
#, fuzzy
msgid "mmv [OPTS] <files> <target-dir>"
msgstr "mget [OPTS] <Dateien>"

#: src/commands.cc:347
msgid ""
"Move <files> to <target-directory> with wildcard expansion\n"
" -O <dir>  specifies the target directory (alternative way)\n"
msgstr ""

#: src/commands.cc:349
msgid "[re]nlist [<args>]"
msgstr "[re]nlist [<args>]"

#: src/commands.cc:350
msgid ""
"List remote file names.\n"
"By default, nlist output is cached, to see new listing use `renlist' or\n"
"`cache flush'.\n"
msgstr ""
"Nichtlokale Dateien auflisten.\n"
"Als Standardvorgabe, wird die Ausgabe zwischengespeichert, um die Liste zu "
"erneuern\n"
"dient entweder »rels« oder »cache flush«.\n"

#: src/commands.cc:353
msgid "open [OPTS] <site>"
msgstr "open [OPTS] <site>"

#: src/commands.cc:354
#, fuzzy
msgid ""
"Select a server, URL or bookmark\n"
" -e <cmd>            execute the command just after selecting\n"
" -u <user>[,<pass>]  use the user/password for authentication\n"
" -p <port>           use the port for connection\n"
" -s <slot>           assign the connection to this slot\n"
" -d                  switch on debugging mode\n"
" <site>              host name, URL or bookmark name\n"
msgstr ""
"Server, URL oder Lesezeichen auswählen\n"
" -e <Befehl>         <Befehl> sofort nach der Auswahl ausführen\n"
" -u <user>[,<pass>]  angegebenen Benutzer und Passwort zu Authentifikation "
"verwenden\n"
" -p <Port>           angegebenen Port zur Verbindung benutzen\n"
" -s <slot>           angegebenen Slot zur Verbindung benutzen\n"
" <site>              Name eines Hosts, URL oder Lesezeichen\n"

#: src/commands.cc:361
msgid "pget [OPTS] <rfile> [-o <lfile>]"
msgstr "pget [OPTS] <nlfile> [-o <lfile>]"

#: src/commands.cc:362
msgid ""
"Gets the specified file using several connections. This can speed up "
"transfer,\n"
"but loads the net heavily impacting other users. Use only if you really\n"
"have to transfer the file ASAP.\n"
"\n"
"Options:\n"
" -c  continue transfer. Requires <lfile>.lftp-pget-status file.\n"
" -n <maxconn>  set maximum number of connections (default is is taken from\n"
"     pget:default-n setting)\n"
" -O <base> specifies base directory where files should be placed\n"
msgstr ""
"Holt die angegebene Datei über mehrere Verbindungen gleichzeitig\n"
"Dies kann die Übertragung beschleunigen, aber führt zu hoher Netzlast,\n"
"die andere Benutzer beeinträchtigt. Nur verwenden, wenn eine Datei wirklich\n"
"SOFORT übertragen werden muß.)\n"
"\n"
"Optionen:\n"
" -c  Übertragung fortsetzen. Erfordert <lfile>.lftp-pget-status Datei.\n"
" -n <maxconn>  Höchstzahl an Verbindungen angeben\n"
"     (Vorgabe aus pget:default-n setting)\n"
" -O <base> Verzeichnis angeben, in den Dateien abgelegt werden\n"

#: src/commands.cc:370
msgid "put [OPTS] <lfile> [-o <rfile>]"
msgstr "put [OPTS] <lfile> [-o <nlfile>]"

#: src/commands.cc:371
msgid ""
"Upload <lfile> with remote name <rfile>.\n"
" -o <rfile> specifies remote file name (default - basename of lfile)\n"
" -c  continue, reput\n"
"     it requires permission to overwrite remote files\n"
" -E  delete local files after successful transfer (dangerous)\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Lade <ldatei> hoch unter dem Namen <nldatei>.\n"
" -o <nldatei> gibt den Namen der nichtlokalen Datei an. \n"
"    (Vorgabe - Name von ldatei)\n"
" -c  nach Abbruch fortfahren, erneut schicken\n"
"     Man braucht die Berechtigung Dateien auf dem Server zu überschreiben.\n"
" -e  Lokal vorliegende Dateien nach erfolgreicher Übertragung löschen \n"
"    (Vorsicht!! Riskant!)\n"
" -a  ASCII / Textmodus verwenden (Binärmodus ist die Vorgabe)\n"
" -O <basis> gibt das Oberverzeichnis bzw. die URL an, wo die Dateien\n"
"    gespeichert werden sollen.\n"

#: src/commands.cc:379
msgid ""
"Print current remote URL.\n"
" -p  show password\n"
msgstr ""
"Drucke momentane URL der Gegenstelle.\n"
" -p  Passwort zeigen\n"

#: src/commands.cc:381
msgid "queue [OPTS] [<cmd>]"
msgstr "queue [OPTIONEN] [<Befehl>]"

#: src/commands.cc:382
msgid ""
"\n"
"       queue [-n num] <command>\n"
"\n"
"Add the command to queue for current site. Each site has its own command\n"
"queue. `-n' adds the command before the given item in the queue. It is\n"
"possible to queue up a running job by using command `queue wait <jobno>'.\n"
"\n"
"       queue --delete|-d [index or wildcard expression]\n"
"\n"
"Delete one or more items from the queue. If no argument is given, the last\n"
"entry in the queue is deleted.\n"
"\n"
"       queue --move|-m <index or wildcard expression> [index]\n"
"\n"
"Move the given items before the given queue index, or to the end if no\n"
"destination is given.\n"
"\n"
"Options:\n"
" -q                  Be quiet.\n"
" -v                  Be verbose.\n"
" -Q                  Output in a format that can be used to re-queue.\n"
"                     Useful with --delete.\n"
msgstr ""
"\n"
"        queue [-n num] <Befehl>\n"
"\n"
"Den Befehl in die Warteschlange für die aktuelle Gegenstelle einfügen.\n"
"Jede Gegenstelle hat ihre eigene Warteschlange für Befehle.\n"
"»-n« fügt den Befehl vor dem angegebenen Eintrag in die Warteschlange ein. "
"Es\n"
"ist möglich einen bereits laufenden Job wieder in die Warteschlange zu "
"stellen\n"
"mit »queue wait <Jobnr.>«.\n"
"\n"
"        queue --delete|-d <Index oder Joker-Ausdruck>\n"
"\n"
"Ein oder mehrere Einträge in der Warteschlange löschen. Ohne genaue Angabe\n"
"wird der letzte Eintrag entfernt.\n"
"\n"
"        queue --move|-m <Index oder Joker-Ausdruck> [Index]\n"
"\n"
"Die angegebenen Einträge vor den angegebenen Warteschlangen-Index stellen,\n"
"oder an das Ende, wenn kein Ziel angegeben wurde.\n"
"\n"
"Optionen:\n"
" -q                   wenige Ausgaben\n"
" -v                   ausführliche Ausgaben\n"
" -Q                   Ausgabe in einem Format, das zum Wiedereinreihen in\n"
"                      die Warteschlange verwendet werden kann.\n"
"                      Nützlich in Verbindung mit --delete.\n"

#: src/commands.cc:403
msgid "quote <cmd>"
msgstr "quote <cmd>"

#: src/commands.cc:404
msgid ""
"Send the command uninterpreted. Use with caution - it can lead to\n"
"unknown remote state and thus will cause reconnect. You cannot\n"
"be sure that any change of remote state because of quoted command\n"
"is solid - it can be reset by reconnect at any time.\n"
msgstr ""
"Den angegebenen Befehl ohne Interpretation/Bearbeitung schicken. Mit\n"
"Vorsicht zu genießen, da dies zu einem unbekanntem Zustand auf dem anderen "
"Rechner\n"
"und zum Verbindungsneuaufbau führen kann. Man sollte sich nicht auf den "
"Zustand\n"
"des nichtlokalen Rechners verlassen, da das geschickte Kommando jederzeit "
"durch einen\n"
"Verbindungsneuaufbau überschrieben werden kann.\n"

#: src/commands.cc:409
msgid ""
"recls [<args>]\n"
"Same as `cls', but don't look in cache\n"
msgstr ""
"recls [<Argumente>]\n"
"Entspricht »ls«, aber schaut nicht in den Zwischenspeicher\n"

#: src/commands.cc:412
msgid ""
"Usage: reget [OPTS] <rfile> [-o <lfile>]\n"
"Same as `get -c'\n"
msgstr ""
"Verwendung: reget [OPTIONEN] <nichtlokale Datei> [-o <lokale Datei>]\n"
"Identisch zu »get -c«\n"

#: src/commands.cc:415
msgid ""
"Usage: rels [<args>]\n"
"Same as `ls', but don't look in cache\n"
msgstr ""
"Verwendung: rels [<Argumente>]\n"
"Entspricht »ls«, aber schaut nicht in den Cache\n"

#: src/commands.cc:418
msgid ""
"Usage: renlist [<args>]\n"
"Same as `nlist', but don't look in cache\n"
msgstr ""
"Verwendung: renlist [<Argumente]\n"
"Entspricht »nlist«, aber schaut nicht in den Cache\n"

#: src/commands.cc:420
msgid "repeat [OPTS] [delay] [command]"
msgstr "repeat [OPTIONEN][Verzögerung] [Befehl]"

#: src/commands.cc:422
msgid ""
"Usage: reput <lfile> [-o <rfile>]\n"
"Same as `put -c'\n"
msgstr ""
"Verwendung: reput <lokale Datei> [-o <nichtlokale Datei>]\n"
"Dasselbe wie »put -c«\n"

#: src/commands.cc:424
msgid "rm [-r] [-f] <files>"
msgstr "rm [-r] [-f] <dateien>"

#: src/commands.cc:425
msgid ""
"Remove remote files\n"
" -r  recursive directory removal, be careful\n"
" -f  work quietly\n"
msgstr ""
"Lösche nichtlokale Dateien\n"
" -r  rekursive Entfernung von Verzeichnissen, Vorsicht\n"
" -f  ohne jegliche Rückmeldung löschen\n"

#: src/commands.cc:428
msgid "rmdir [-f] <dirs>"
msgstr "mkdir [-f] <verzeichnisse>"

#: src/commands.cc:429
msgid "Remove remote directories\n"
msgstr "Entferne nichtlokale Verzeichnisse\n"

#: src/commands.cc:430
msgid "scache [<session_no>]"
msgstr "scache [<sitzungs_nr.>]"

#: src/commands.cc:431
msgid "List cached sessions or switch to specified session number\n"
msgstr "Zeigt gespeicherte Sitzungen oder wechselt zur angegebenen Nummer\n"

#: src/commands.cc:432
msgid "set [OPT] [<var> [<val>]]"
msgstr "set [OPTION] [<variable> [<wert>]] "

#: src/commands.cc:433
msgid ""
"Set variable to given value. If the value is omitted, unset the variable.\n"
"Variable name has format ``name/closure'', where closure can specify\n"
"exact application of the setting. See lftp(1) for details.\n"
"If set is called with no variable then only altered settings are listed.\n"
"It can be changed by options:\n"
" -a  list all settings, including default values\n"
" -d  list only default values, not necessary current ones\n"
msgstr ""
"Variable auf angegebenen Wert setzen. Wenn die Wertangabe ausgelassen wird,\n"
"wird die Variable gelöscht.\n"
"Der Variablenname hat das Format »name/closure«, wobei closure die genaue\n"
"Anwendung der Einstellung angibt. Details finden sich unter lftp(1).\n"
"Wenn set ohne Angabe einer Variablen aufgerufen wird, dann werden nur die\n"
"veränderten Werte angezeigt.\n"
"Folgende Optionen ändern dies Verhalten:\n"
" -a  zeigt alle Variablen einschließlich der unveränderten Standardvorgaben\n"
" -d  zeigt nur die Standardvorgaben, nicht unbedingt die aktuellen Werte.\n"

#: src/commands.cc:441
#, fuzzy
msgid "site <site-cmd>"
msgstr "site <site_befehl>"

#: src/commands.cc:442
msgid ""
"Execute site command <site_cmd> and output the result\n"
"You can redirect its output\n"
msgstr ""
"Führt den Befehl auf dem Host aus und gibt das Resultat aus\n"
"Ausgabeumleitung ist möglich\n"

#: src/commands.cc:446
msgid ""
"Usage: slot [<label>]\n"
"List assigned slots.\n"
"If <label> is specified, switch to the slot named <label>.\n"
msgstr ""
"Verwendung: slot [<label>]\n"
"Zugewiesene Slots anzeigen.\n"
"Wenn <label> angegeben wird, zum Slot namens <label> wechseln.\n"

#: src/commands.cc:449
msgid "source <file>"
msgstr "source <datei>"

#: src/commands.cc:450
msgid "Execute commands recorded in file <file>\n"
msgstr "Führe die in der Datei gespeicherten Befehle aus\n"

#: src/commands.cc:452
#, fuzzy
msgid "torrent [OPTS] <file|URL>..."
msgstr "mget [OPTS] <Dateien>"

#: src/commands.cc:453
msgid "user <user|URL> [<pass>]"
msgstr "user <user|URL> [<passwt>]"

#: src/commands.cc:454
msgid ""
"Use specified info for remote login. If you specify URL, the password\n"
"will be cached for future usage.\n"
msgstr ""
"Angegebene Info für das Einloggen verwenden. Wird eine URL angegeben,\n"
"so wird das Passwort für spätere Verwendung zwischengespeichert.\n"

#: src/commands.cc:457
msgid "Shows lftp version\n"
msgstr "Gibt die Lftp - Version an\n"

#: src/commands.cc:458
msgid "wait [<jobno>]"
msgstr "wait <jobnr.>"

#: src/commands.cc:459
msgid ""
"Wait for specified job to terminate. If jobno is omitted, wait\n"
"for last backgrounded job.\n"
msgstr ""
"Darauf warten, dass der angegebene Job beendet ist. Wird keine Jobnr. "
"angegeben,\n"
"wartet lftp auf den letzten in den Hintergrund gestellten Job.\n"

#: src/commands.cc:461
msgid "zcat <files>"
msgstr "zcat <Dateien>"

#: src/commands.cc:462
msgid "Same as cat, but filter each file through zcat\n"
msgstr "Entspricht cat, aber filtert jede Datei durch zcat\n"

#: src/commands.cc:463
msgid "zmore <files>"
msgstr "zmore <Dateien>"

#: src/commands.cc:464
msgid "Same as more, but filter each file through zcat\n"
msgstr "Entspricht more, aber benutzt zcat als Filter\n"

#: src/commands.cc:466
msgid "Same as cat, but filter each file through bzcat\n"
msgstr "Entspricht cat, aber filtert jede Datei durch bzcat\n"

#: src/commands.cc:468
msgid "Same as more, but filter each file through bzcat\n"
msgstr "Entspricht more, aber benutzt bzcat als Filter\n"

#: src/commands.cc:524
#, c-format
msgid "Usage: %s local-dir\n"
msgstr "Benutzung: %s lokales-Verzeichnis\n"

#: src/commands.cc:560
#, c-format
msgid "lcd ok, local cwd=%s\n"
msgstr "lcd OK, lokales cwd=%s\n"

#: src/commands.cc:576
#, c-format
msgid "Usage: cd remote-dir\n"
msgstr "Benutzung: cd nichtlokales-Verzeichnis\n"

#: src/commands.cc:588
#, c-format
msgid "%s: no old directory for this site\n"
msgstr "%s: kein vorheriges Verzeichnis an diesem Ort\n"

#: src/commands.cc:677
#, c-format
msgid "Usage: %s [<exit_code>]\n"
msgstr "Benutzung:%s [<Exit_kode>]\n"

#: src/commands.cc:686
msgid ""
"There are running jobs and `cmd:move-background' is not set.\n"
"Use `exit bg' to force moving to background or `kill all' to terminate "
"jobs.\n"
msgstr ""
"Jobs laufen noch und »cmd:move-background« ist nicht eingeschaltet.\n"
"Bitte »exit bg« verwenden um im Hintergrund fortzufahren oder\n"
"»kill all« um alle Jobs zu beenden.\n"

#: src/commands.cc:703
msgid ""
"\n"
"lftp now tricks the shell to move it to background process group.\n"
"lftp continues to run in the background despite the `Stopped' message.\n"
"lftp will automatically terminate when all jobs are finished.\n"
"Use `fg' shell command to return to lftp if it is still running.\n"
msgstr ""

#: src/commands.cc:837 src/commands.cc:3616
#, c-format
msgid "Try `%s --help' for more information\n"
msgstr "»%s --help' gibt weitere Informationen aus\n"

#: src/commands.cc:856
#, c-format
msgid "%s: -c, -f, -v, -h conflict with other `open' options and arguments\n"
msgstr ""

#: src/commands.cc:956
#, c-format
msgid "Usage: %s [-e cmd] [-p port] [-u user[,pass]] <host|url>\n"
msgstr "Benutzung: %s [-e Befehl] [-p Port] [-u user[,passwort]] <Host|URL>\n"

#: src/commands.cc:1046 src/commands.cc:2276 src/DummyProto.cc:65
#: src/MirrorJob.cc:2172 src/MirrorJob.cc:2194
msgid " - not supported protocol"
msgstr " - Protokoll nicht unterstützt"

#: src/commands.cc:1078 src/commands.cc:2260
msgid "Password: "
msgstr "Passwort: "

#: src/commands.cc:1080
#, c-format
msgid "%s: GetPass() failed -- assume anonymous login\n"
msgstr "%s: GetPass() fehlgeschlagen -- benutze anonymen Login\n"

#: src/commands.cc:1191 src/commands.cc:1284 src/commands.cc:1660
#: src/commands.cc:1691 src/commands.cc:1829 src/commands.cc:1914
#: src/commands.cc:2187 src/commands.cc:2381 src/commands.cc:2543
#: src/commands.cc:2588 src/commands.cc:2616 src/commands.cc:2623
#: src/commands.cc:2930 src/commands.cc:2957 src/commands.cc:2964
#: src/commands.cc:3293 src/lftp.cc:267 src/MirrorJob.cc:2136
#: src/SleepJob.cc:144 src/SleepJob.cc:200 src/Torrent.cc:4169
#, c-format
msgid "Try `help %s' for more information.\n"
msgstr "»help %s« gibt mehr Informationen aus.\n"

#: src/commands.cc:1201
#, c-format
msgid "Usage: %s [OPTS] command args...\n"
msgstr "Benutzung: %s Befehl Argumente..\n"

#: src/commands.cc:1254
#, c-format
msgid "%s: -n: positive number expected. "
msgstr "%s: -n: positive Zahl erwartet. "

#: src/commands.cc:1306
#, c-format
msgid "Created a stopped queue.\n"
msgstr "Angehaltene Warteschlange erzeugt.\n"

#: src/commands.cc:1349 src/commands.cc:1377
#, c-format
msgid "%s: No queue is active.\n"
msgstr "%s: Keine Warteschlange aktiv.\n"

#: src/commands.cc:1369
#, c-format
msgid "%s: -m: Number expected as second argument. "
msgstr "%s: -m: Zahl als zweite Angabe erwartet. "

#: src/commands.cc:1421
#, c-format
msgid "Usage: %s <cmd>\n"
msgstr "Benutzung: %s <Befehl>\n"

#: src/commands.cc:1527
msgid "invalid argument for `--sort'"
msgstr "ungültiges Argument für »--sort«"

#: src/commands.cc:1557
msgid "invalid block size"
msgstr "Unzulässige Blockgrösse"

#: src/commands.cc:1700
#, c-format
msgid "Usage: %s [OPTS] files...\n"
msgstr "Benutzung: %s [OPTS] Dateien ...\n"

#: src/commands.cc:1785 src/commands.cc:1814
#, fuzzy, c-format
msgid "%s: %s: Number expected. "
msgstr "%s: -n: Zahl erwartet. "

#: src/commands.cc:1834
#, fuzzy, c-format
msgid "%s: --continue conflicts with --remove-target.\n"
msgstr "%s: »summarize« kann nicht mit --max-depth=%i kombiniert werden\n"

#: src/commands.cc:1844 src/commands.cc:1920
#, c-format
msgid "File name missed. "
msgstr "Dateiname fehlt. "

#: src/commands.cc:1989
#, c-format
msgid "Usage: %s %s[-f] files...\n"
msgstr "Benutzung: %s %s [-f] Dateien...\n"

#: src/commands.cc:2032
#, c-format
msgid "Usage: %s [-e] <file|command>\n"
msgstr "Benutzung: %s  [-e] <datei|befehl>\n"

#: src/commands.cc:2079
#, c-format
msgid "Usage: %s [-v] [-v] ...\n"
msgstr "Benutzung: %s [-v] [-v] ...\n"

#: src/commands.cc:2093 src/commands.cc:2347 src/commands.cc:2469
#: src/commands.cc:2685 src/commands.cc:3101 src/commands.cc:3182
#: src/lftp.cc:279
#, c-format
msgid "%s: %s - not a number\n"
msgstr "%s: %s - keine Zahl\n"

#: src/commands.cc:2100 src/commands.cc:2326 src/commands.cc:2356
#: src/commands.cc:2487
#, c-format
msgid "%s: %d - no such job\n"
msgstr "%s: %d - kein derartiger Job\n"

#: src/commands.cc:2135
#, c-format
msgid "Usage: %s [-p]\n"
msgstr "Benutzung: %s [-p]\n"

#: src/commands.cc:2227
#, c-format
msgid "debug level is %d, output goes to %s\n"
msgstr "Debug Stufe ist %d, Ausgabe geht nach %s\n"

#: src/commands.cc:2230
#, c-format
msgid "debug is off\n"
msgstr "Debug ist abgeschaltet\n"

#: src/commands.cc:2241
#, c-format
msgid "Usage: %s <user|URL> [<pass>]\n"
msgstr "Verwendung: %s user <user|URL> [<passwt>]\n"

#: src/commands.cc:2316 src/commands.cc:2479
#, c-format
msgid "%s: no current job\n"
msgstr "%s: - kein derartiger Job\n"

#: src/commands.cc:2328
#, c-format
msgid "Usage: %s <jobno> ... | all\n"
msgstr "Benutzung: %s <jobnr.> ... | all\n"

#: src/commands.cc:2409
#, c-format
msgid "%s: %s. Use `set -a' to look at all variables.\n"
msgstr "%s: %s. »set -a« zeigt alle Variablen.\n"

#: src/commands.cc:2453
#, c-format
msgid "Usage: %s [<jobno>]\n"
msgstr "Benutzung: %s [<Jobnr.>]\n"

#: src/commands.cc:2492
#, c-format
msgid "%s: some other job waits for job %d\n"
msgstr "%s: ein anderer Job wartet auf die Beendigung des Jobs %d\n"

#: src/commands.cc:2497
#, c-format
msgid "%s: wait loop detected\n"
msgstr "%s: Warteschleife entdeckt\n"

#: src/commands.cc:2553
#, fuzzy, c-format
msgid "Usage: %s [OPTS] <files> <target-dir>\n"
msgstr "Benutzung: %s [OPTS] <Datei>\n"

#: src/commands.cc:2615 src/commands.cc:2956
#, c-format
msgid "Invalid command. "
msgstr "Ungültiger Befehl. "

#: src/commands.cc:2622 src/commands.cc:2963
#, c-format
msgid "Ambiguous command. "
msgstr "Mehrdeutiger Befehl. "

#: src/commands.cc:2641
#, c-format
msgid "%s: Operand missed for size\n"
msgstr "%s: Größenangabe fehlt\n"

#: src/commands.cc:2658
#, c-format
msgid "%s: Operand missed for `expire'\n"
msgstr "%s: Angabe für »expire«(Ablauf) fehlt\n"

#: src/commands.cc:2691
#, c-format
msgid ""
"%s: %s - no such cached session. Use `scache' to look at session list.\n"
msgstr ""
"%s: %s - keine derartige Sitzung zwischengespeichert. »scache« zeigt eine "
"Liste.\n"

#: src/commands.cc:2715
#, c-format
msgid "Sorry, no help for %s\n"
msgstr "Tut mir Leid, keine Hilfe für %s\n"

#: src/commands.cc:2720
#, c-format
msgid "%s is a built-in alias for %s\n"
msgstr "%s ist ein eingebauter alias für %s\n"

#: src/commands.cc:2725
#, c-format
msgid "Usage: %s\n"
msgstr "Benutzung: %s\n"

#: src/commands.cc:2733
#, c-format
msgid "%s is an alias to `%s'\n"
msgstr "%s ist ein alias für »%s«\n"

#: src/commands.cc:2737
#, c-format
msgid "No such command `%s'. Use `help' to see available commands.\n"
msgstr "Kein derartiger Befehl »%s«. »help« zeigt alle verfügbaren Befehle.\n"

#: src/commands.cc:2739
#, c-format
msgid "Ambiguous command `%s'. Use `help' to see available commands.\n"
msgstr "Zweideutiger Befehl »%s«. »help« zeigt alle verfügbaren Befehle. \n"

#: src/commands.cc:2805
#, c-format
msgid "LFTP | Version %s | Copyright (c) 1996-%d Alexander V. Lukyanov\n"
msgstr "LFTP | Version %s | Copyright (c) 1996-%d Alexander V. Lukyanov\n"

#: src/commands.cc:2808
#, c-format
msgid ""
"LFTP is free software: you can redistribute it and/or modify\n"
"it under the terms of the GNU General Public License as published by\n"
"the Free Software Foundation, either version 3 of the License, or\n"
"(at your option) any later version.\n"
"\n"
"This program is distributed in the hope that it will be useful,\n"
"but WITHOUT ANY WARRANTY; without even the implied warranty of\n"
"MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n"
"GNU General Public License for more details.\n"
"\n"
"You should have received a copy of the GNU General Public License\n"
"along with LFTP.  If not, see <http://www.gnu.org/licenses/>.\n"
msgstr ""

#: src/commands.cc:2822
#, fuzzy, c-format
msgid "Send bug reports and questions to the mailing list <%s>.\n"
msgstr "Fehlerbericht und Fragen bitte an <%s>.\n"

#: src/commands.cc:2828
msgid "Libraries used: "
msgstr "Verwendete Libraries:"

#: src/commands.cc:2979 src/commands.cc:3007
#, c-format
msgid "%s: bookmark name required\n"
msgstr "%s: Name des Lesezeichens notwendig\n"

#: src/commands.cc:2996
#, c-format
msgid "%s: spaces in bookmark name are not allowed\n"
msgstr "%s: Leerstellen sind im Namen von Lesezeichen nicht zulässig\n"

#: src/commands.cc:3009
#, c-format
msgid "%s: no such bookmark `%s'\n"
msgstr "%s: kein derartiges Lesezeichen »%s«\n"

#: src/commands.cc:3032
#, c-format
msgid "%s: import type required (netscape,ncftp)\n"
msgstr "%s: Importtyp muss angegeben werden (Netscape,ncftp)\n"

#: src/commands.cc:3110
#, c-format
msgid "Usage: %s [-d #] dir\n"
msgstr "Benutzung: %s [-d #] dir\n"

#: src/commands.cc:3215
#, c-format
msgid "%s: invalid block size `%s'\n"
msgstr "%s: unzulässige Blockgrösse »%s«\n"

#: src/commands.cc:3226
#, c-format
msgid "Usage: %s [options] <dirs>\n"
msgstr "Benutzung: %s [Optionen] <verzeichnisse>\n"

#: src/commands.cc:3232
#, c-format
msgid "%s: warning: summarizing is the same as using --max-depth=0\n"
msgstr "%s: Achtung: »summarize« (Zusammenzählen) entspricht --max-depth=0\n"

#: src/commands.cc:3236
#, c-format
msgid "%s: summarizing conflicts with --max-depth=%i\n"
msgstr "%s: »summarize« kann nicht mit --max-depth=%i kombiniert werden\n"

#: src/commands.cc:3280
#, c-format
msgid "Usage: %s command args...\n"
msgstr "Benutzung: %s Befehl Argumente..\n"

#: src/commands.cc:3292
#, c-format
msgid "Usage: %s module [args...]\n"
msgstr "Benutzung: %s module [Argumente..]\n"

#: src/commands.cc:3310 src/LocalAccess.cc:642
msgid "cannot get current directory"
msgstr "Kann aktuelles Verzeichnis nicht feststellen"

#: src/commands.cc:3374
#, c-format
msgid "Usage: %s [OPTS] mode file...\n"
msgstr "Benutzung: %s [OPTS] Modus Dateien ...\n"

#: src/commands.cc:3394
#, c-format
msgid "invalid mode string: %s\n"
msgstr "Ungültige Modusangabe: %s\n"

#: src/commands.cc:3457 src/commands.cc:3466
msgid "Invalid range format. Format is min-max, e.g. 10-20."
msgstr "Ungültige Angabe der Spanne. Das Format ist min-max - z.B. 10-20."

#: src/commands.cc:3478
#, c-format
msgid "Usage: %s [OPTS] file\n"
msgstr "Benutzung: %s [OPTS] <Datei>\n"

#: src/CopyJob.cc:82
#, c-format
msgid "`%s' at %lld %s%s%s%s"
msgstr "»%s« bei %lld %s%s%s%s"

#: src/CopyJob.cc:159
#, c-format
msgid "%lld $#ll#byte|bytes$ transferred in %ld $#l#second|seconds$"
msgstr "%lld $#ll#Byte|Bytes$ übertragen in %ld $#l#Sekunde|Sekunden$"

#: src/CopyJob.cc:167
#, c-format
msgid "%lld $#ll#byte|bytes$ transferred"
msgstr "%lld $#ll#Byte|Bytes$ übertragen"

#: src/CopyJob.cc:283
#, c-format
msgid "Transfer of %d of %d $file|files$ failed\n"
msgstr "Übertragung von %d der %d $Datei|Dateien$ fehlgeschlagen\n"

#: src/CopyJob.cc:289
#, c-format
msgid "Total %d $file|files$ transferred\n"
msgstr "Insgesamt %d $Datei|Dateien$ übertragen\n"

#: src/FileAccess.cc:160
msgid "Access failed: "
msgstr "Zugriff nicht möglich:"

#: src/FileAccess.cc:161
msgid "File cannot be accessed"
msgstr "Zugriff auf Datei nicht möglich"

#: src/FileAccess.cc:163 src/Fish.cc:982 src/ftpclass.cc:4599
#: src/ftpclass.cc:4608 src/SFtp.cc:1339 src/Torrent.cc:3533
msgid "Not connected"
msgstr "Keine Verbindung"

#: src/FileAccess.cc:166 src/FileAccess.cc:167
msgid "Fatal error"
msgstr "Fataler Fehler"

#: src/FileAccess.cc:169
msgid "Store failed - you have to reput"
msgstr "Speichern fehlgeschlagen - erneutes Hochladen notwendig"

#: src/FileAccess.cc:172 src/FileAccess.cc:173
msgid "Login failed"
msgstr "Login fehlgeschlagen"

#: src/FileAccess.cc:176 src/FileAccess.cc:177
msgid "Operation not supported"
msgstr "Operation wird nicht unterstützt"

#: src/FileAccess.cc:180
msgid "File moved"
msgstr "Datei verschoben"

#: src/FileAccess.cc:182
msgid "File moved to `"
msgstr "Datei verschoben nach »"

#: src/FileCopy.cc:141
msgid "copy: destination file is already complete\n"
msgstr "copy: Zieldatei ist bereits vollständig\n"

#: src/FileCopy.cc:182
msgid "copy: put is broken\n"
msgstr "copy: put funktioniert nicht\n"

#: src/FileCopy.cc:201
msgid "seek failed"
msgstr "Suche erfolglos"

#: src/FileCopy.cc:207
msgid "no progress timeout"
msgstr ""

#: src/FileCopy.cc:235
msgid "cannot seek on data source"
msgstr "Kann Datenquelle nicht durchsuchen"

#: src/FileCopy.cc:238
#, c-format
msgid "copy: put rolled back to %lld, seeking get accordingly\n"
msgstr "copy: put kehrte zu %lld zurück, suche entsprechendes get\n"

#: src/FileCopy.cc:251
msgid "copy: all data received, but get rolled back\n"
msgstr "copy: Alle Daten empfangen, aber get wurde zurückgesetzt\n"

#: src/FileCopy.cc:267
#, c-format
msgid "copy: get rolled back to %lld, seeking put accordingly\n"
msgstr "copy: get wurde zurückgesetzt zu %lld, suche dementsprechendes put\n"

#: src/FileCopy.cc:364
msgid "file size decreased during transfer"
msgstr ""

#: src/FileCopy.cc:1227
#, c-format
msgid "copy: received redirection to `%s'\n"
msgstr "copy: Umleitung nach »%s« empfangen\n"

#: src/FileCopy.cc:1290
#, fuzzy
#| msgid "saw file size in response"
msgid "file size increased during transfer"
msgstr "Dateigröße in Antwort empfangen"

#: src/FileCopy.cc:1390
msgid "file name missed in URL"
msgstr "Dateiname fehlt in der URL"

#: src/FileCopy.cc:2086
msgid "Verify command failed without a message"
msgstr "Überprüfung des Befehls fehlgeschlagen ohne Rückmeldung"

#: src/FileCopyFtp.cc:95
msgid "**** FXP: trying to reverse ftp:fxp-passive-source\n"
msgstr "**** FXP: Versuche ftp:fxp-passive-source umzukehren\n"

#: src/FileCopyFtp.cc:102
msgid "**** FXP: trying to reverse ftp:fxp-passive-sscn\n"
msgstr "**** FXP: Versuche ftp:fxp-passive-sscn umzukehren\n"

#: src/FileCopyFtp.cc:110
msgid "**** FXP: trying to reverse ftp:ssl-protect-fxp\n"
msgstr "**** FXP: Versuche ftp:ssl-protect-fxp umzukehren\n"

#: src/FileCopyFtp.cc:116
msgid "**** FXP: giving up, reverting to plain copy\n"
msgstr "**** FXP: gebe auf, kehre zu einfachem copy zurück\n"

#: src/FileCopyFtp.cc:125
msgid "ftp:fxp-force is set but FXP is not available"
msgstr "ftp:fxp-force ist eingeschaltet, aber FXP ist nicht verfügbar"

#: src/FileCopy.h:285
msgid "Verifying..."
msgstr "Überprüfe..."

#: src/FileSetOutput.cc:230
msgid "non-option arguments found"
msgstr "Parameter gefunden, die keine Option sind"

#: src/Filter.cc:166
msgid "pipe() failed: "
msgstr "pip() fehlgeschlagen:"

#: src/Filter.cc:200 src/PtyShell.cc:135
#, c-format
msgid "chdir(%s) failed: %s\n"
msgstr "chdir(%s) fehlgeschlagen: %s\n"

#: src/Filter.cc:208
#, c-format
msgid "execvp(%s) failed: %s\n"
msgstr "execvp(%s) fehlgeschlagen: %s\n"

#: src/Filter.cc:213 src/PtyShell.cc:147
#, c-format
msgid "execl(/bin/sh) failed: %s\n"
msgstr "execl(/bin/sh) fehlgeschlagen: %s\n"

#: src/Filter.cc:415
#, fuzzy
msgid "file already exists and xfer:clobber is unset"
msgstr ""
"%s: %s: Datei existiert bereits und xfer:clobber (=überschreiben) ist nicht "
"eingeschaltet\n"

#: src/FindJobDu.cc:101
msgid "total"
msgstr "insgesamt"

#: src/Fish.cc:75 src/ftpclass.cc:1292 src/Http.cc:1232 src/SFtp.cc:77
#, c-format
msgid "Closing idle connection"
msgstr "Schließe unbenutzte Verbindung"

#: src/Fish.cc:162 src/SFtp.cc:177
msgid "Running connect program"
msgstr "Starte Verbindungsprogramm"

#: src/Fish.cc:588 src/ftpclass.cc:2887 src/ftpclass.cc:3107 src/Http.cc:1510
#: src/SFtp.cc:742 src/SFtp.cc:1083 src/SFtp.cc:1084 src/SSH_Access.cc:167
#, c-format
msgid "Peer closed connection"
msgstr "Gegenüber hat Verbindung beendet"

#: src/Fish.cc:634 src/ftpclass.cc:3037 src/ftpclass.cc:4219 src/SFtp.cc:1108
#, c-format
msgid "extra server response"
msgstr "zusätzliche Server Antwort"

#: src/Fish.cc:987 src/ftpclass.cc:4611 src/Http.cc:2329 src/Http.cc:2336
#: src/SFtp.cc:1345 src/Torrent.cc:3536 src/TorrentTracker.cc:624
msgid "Connecting..."
msgstr "Verbinde..."

#: src/Fish.cc:989 src/ftpclass.cc:4617 src/SFtp.cc:1347
msgid "Connected"
msgstr "Verbunden"

#: src/Fish.cc:991 src/ftpclass.cc:4594 src/ftpclass.cc:4622
#: src/ftpclass.cc:4636 src/Http.cc:2338 src/SFtp.cc:1349
#: src/TorrentTracker.cc:626
msgid "Waiting for response..."
msgstr "Warte auf Antwort..."

#: src/Fish.cc:993 src/ftpclass.cc:4656 src/Http.cc:2341 src/SFtp.cc:1351
msgid "Receiving data"
msgstr "Empfange Daten"

#: src/Fish.cc:995 src/ftpclass.cc:4654 src/Http.cc:2334 src/SFtp.cc:1353
msgid "Sending data"
msgstr "Sende Daten"

#: src/Fish.cc:997 src/SFtp.cc:1355
msgid "Done"
msgstr "Fertig"

#: src/Fish.cc:1136 src/FtpDirList.cc:123 src/HttpDir.cc:1384 src/SFtp.cc:2200
#: src/SFtp.cc:2320
#, c-format
msgid "Getting file list (%lld) [%s]"
msgstr "Hole Dateiliste (%lld) [%s]"

#: src/ftpclass.cc:197
#, c-format
msgid "Data connection peer has wrong port number"
msgstr "Data connection peer hat die falsche Port Nummer"

#: src/ftpclass.cc:203
#, c-format
msgid "Data connection peer has mismatching address"
msgstr "Data connection peer hat nicht übereinstimmende Adresse"

#: src/ftpclass.cc:225 src/ftpclass.cc:250
#, c-format
msgid "Switching to NOREST mode"
msgstr "Schalte NOREST - Modus ein"

#: src/ftpclass.cc:425
#, c-format
msgid "Server reply matched ftp:retry-530, retrying"
msgstr "Server Antwort entspricht ftp:retry-530, neuer Versuch"

#: src/ftpclass.cc:433
#, c-format
msgid "Server reply matched ftp:retry-530-anonymous, retrying"
msgstr "Server Antwort entspricht ftp:retry-530-anonymous, neuer Versuch"

#: src/ftpclass.cc:466
msgid "Account is required, set ftp:acct variable"
msgstr "Eingerichteter Zugang ist erforderlich, ftp:acct Variable setzen"

#: src/ftpclass.cc:483
msgid "ftp:skey-force is set and server does not support OPIE nor S/KEY"
msgstr ""
"ftp:skey-force ist gesetzt und Gegenstelle unterstützt weder OPIE noch S/KEY"

#: src/ftpclass.cc:501
#, c-format
msgid "assuming failed host name lookup"
msgstr "nehme fehlgeschlagene Auflösung des Hostnamens an"

#: src/ftpclass.cc:809 src/ftpclass.cc:810
#, c-format
msgid "cannot parse EPSV response"
msgstr "Kann EPSV Antwort nicht auswerten"

#: src/ftpclass.cc:837 src/ftpclass.cc:838
#, fuzzy, c-format
#| msgid "cannot parse EPSV response"
msgid "cannot parse custom EPSV response"
msgstr "Kann EPSV Antwort nicht auswerten"

#: src/ftpclass.cc:1122
#, c-format
msgid "Closing control socket"
msgstr "Schließe den Kontroll - Socket"

#: src/ftpclass.cc:1341
msgid "MLSD is disabled by ftp:use-mlsd"
msgstr "MLSD wird abgeschaltet durch ftp:use-mlsd"

#: src/ftpclass.cc:1411 src/Http.cc:1431 src/Torrent.cc:3204
#: src/Torrent.cc:3743 src/TorrentTracker.cc:395
#, c-format
msgid "cannot create socket of address family %d"
msgstr "Kann kein Socket für die Adressfamilie %d anlegen"

#: src/ftpclass.cc:1442 src/Http.cc:1457
#, c-format
msgid "Socket error (%s) - reconnecting"
msgstr "Socket Fehler (%s) - erneutes Verbinden"

#: src/ftpclass.cc:1715
#, fuzzy
msgid "MFF and SITE CHMOD are not supported by this site"
msgstr "SITE CHMOD wird von dieser Gegenstelle nicht unterstützt"

#: src/ftpclass.cc:1720
msgid "MLST and MLSD are not supported by this site"
msgstr "MLST und MLSD werden von dieser Gegenstelle nicht unterstützt"

#: src/ftpclass.cc:2031
#, fuzzy
msgid "SITE SYMLINK is not supported by the server"
msgstr "MLST und MLSD werden von dieser Gegenstelle nicht unterstützt"

#: src/ftpclass.cc:2204
msgid "unsupported network protocol"
msgstr "nicht unterstütztes Netzwerkprotokoll"

#: src/ftpclass.cc:2253 src/ftpclass.cc:2355
#, fuzzy, c-format
msgid "Data socket error (%s) - reconnecting"
msgstr "Socket Fehler (%s) - erneutes Verbinden"

#: src/ftpclass.cc:2281
#, c-format
msgid "Accepted data connection from (%s) port %u"
msgstr "Datenverbindung von (%s) Port %u akzeptiert"

#: src/ftpclass.cc:2330
#, c-format
msgid "Connecting data socket to (%s) port %u"
msgstr "Verbinde Daten Socket mit (%s) Port %u"

#: src/ftpclass.cc:2336
#, c-format
msgid "Connecting data socket to proxy %s (%s) port %u"
msgstr "Datenverbindung mit Proxy %s (%s) Port %u"

#: src/ftpclass.cc:2358 src/ftpclass.cc:4414
#, c-format
msgid "Switching passive mode off"
msgstr "Schalte Passivmodus ab"

#: src/ftpclass.cc:2366
#, c-format
msgid "Data connection established"
msgstr "Datenverbindung hergestellt"

#: src/ftpclass.cc:2688 src/ftpclass.cc:4548
msgid "ftp:ssl-force is set and server does not support or allow SSL"
msgstr ""
"ftp:ssl-force ist gesetzt und Gegenstelle unterstützt oder erlaubt kein SSL"

#: src/ftpclass.cc:3053
#, c-format
msgid "Persist and retry"
msgstr "Verbleibe und versuche es weiter"

#: src/ftpclass.cc:3321
#, c-format
msgid "Closing data socket"
msgstr "Schließe den Daten Socket"

#: src/ftpclass.cc:3343
#, c-format
msgid "Closing aborted data socket"
msgstr "Schließe die Datenverbindung"

#: src/ftpclass.cc:4193
#, c-format
msgid "saw file size in response"
msgstr "Dateigröße in Antwort empfangen"

#: src/ftpclass.cc:4233 src/ftpclass.cc:4260
#, c-format
msgid "Turning on sync-mode"
msgstr "sync - Modus eingeschaltet"

#: src/ftpclass.cc:4434
#, c-format
msgid "Switching passive mode on"
msgstr "Schalte Passivmodus ein"

#: src/ftpclass.cc:4585
msgid "FEAT negotiation..."
msgstr "FEAT Verbindungsaufbau..."

#: src/ftpclass.cc:4592
msgid "Sending commands..."
msgstr "Schicke Befehle..."

#: src/ftpclass.cc:4596
msgid "Delaying before retry"
msgstr "Pausiere vor erneuter Verbindung"

#: src/ftpclass.cc:4597 src/Http.cc:2331
msgid "Connection idle"
msgstr "Verbindung ruht"

#: src/ftpclass.cc:4604 src/Http.cc:2323 src/Resolver.cc:172
#: src/Resolver.cc:217 src/TorrentTracker.cc:622
#, c-format
msgid "Resolving host address..."
msgstr "Löse Hostadresse auf..."

#: src/ftpclass.cc:4615
msgid "TLS negotiation..."
msgstr "TLS Verbindungsaufbau..."

#: src/ftpclass.cc:4619
msgid "Logging in..."
msgstr "Logge ein..."

#: src/ftpclass.cc:4623
msgid "Making data connection..."
msgstr "Stelle Datenverbindung her..."

#: src/ftpclass.cc:4626
msgid "Changing remote directory..."
msgstr "Wechsle nichtlokales Verzeichnis..."

#: src/ftpclass.cc:4632
msgid "Waiting for other copy peer..."
msgstr "Warte auf die Gegenstelle für die andere Kopie..."

#: src/ftpclass.cc:4634 src/ftpclass.cc:4658
msgid "Waiting for transfer to complete"
msgstr "Warte auf Übertragungsende"

#: src/ftpclass.cc:4638
msgid "Waiting for TLS shutdown..."
msgstr "Warte auf Beendung von LTS..."

#: src/ftpclass.cc:4640
msgid "Waiting for data connection..."
msgstr "Warte auf Datenverbindung..."

#: src/ftpclass.cc:4646
msgid "Sending data/TLS"
msgstr "Sende Daten/TLS"

#: src/ftpclass.cc:4648
msgid "Receiving data/TLS"
msgstr "Empfange Daten/TLS"

#: src/Http.cc:230
#, c-format
msgid "Closing HTTP connection"
msgstr "Schließe HTTP-Verbindung"

#: src/Http.cc:240
msgid "POST method failed"
msgstr "POST - Methode fehlgeschlagen"

#: src/Http.cc:1381
msgid "ftp over http cannot work without proxy, set hftp:proxy."
msgstr "FTP über HTTP funktioniert nur mit Proxy, hftp:proxy Option angeben."

#: src/Http.cc:1537
#, c-format
msgid "Sending request..."
msgstr "Schicke Anforderung..."

#: src/Http.cc:1575
#, c-format
msgid "Hit EOF while fetching headers"
msgstr "Dateiende beim Lesen der Kopfzeilen erhalten"

#: src/Http.cc:1695
#, c-format
msgid "Could not parse HTTP status line"
msgstr "Konnte HTTP Statusanzeige nicht auswerten"

#: src/Http.cc:1726
msgid "Object is not cached and http:cache-control has only-if-cached"
msgstr ""
"Objekt ist nicht zwischengespeichert und http:cache-control bestimmt only-if-"
"cached (nur mit Zwischenspeicherung)"

#: src/Http.cc:1891
#, c-format
msgid "Receiving body..."
msgstr "Empfange BODY..."

#: src/Http.cc:2092
#, c-format
msgid "Hit EOF"
msgstr "Dateiende (EOF) angetroffen"

#: src/Http.cc:2095
#, c-format
msgid "Received not enough data, retrying"
msgstr "Nicht genug Daten erhalten, neuer Versuch"

#: src/Http.cc:2106
#, c-format
msgid "Received all"
msgstr "alles empfangen"

#: src/Http.cc:2111
#, c-format
msgid "Received all (total)"
msgstr "Alles empfangen (insgesamt)"

#: src/Http.cc:2135 src/Http.cc:2159
msgid "chunked format violated"
msgstr "Blockformatierung verletzt"

#: src/Http.cc:2145
#, c-format
msgid "Received last chunk"
msgstr "Letztes Teil empfangen"

#: src/Http.cc:2339
msgid "Fetching headers..."
msgstr "Hole Kopfzeilen..."

#: src/Job.cc:293
#, c-format
msgid "[%d] Done (%s)"
msgstr "[%d] Fertig (%s)"

#: src/lftp.cc:370
#, fuzzy, c-format
msgid "[%u] Terminated by signal %d. %s\n"
msgstr "[%lu] Beendet durch Signal %d. %s"

#: src/lftp.cc:445
#, fuzzy, c-format
msgid "[%u] Started.  %s\n"
msgstr "[%lu] Begonnen. %s"

#: src/lftp.cc:463
#, fuzzy, c-format
msgid "[%u] Detaching from the terminal to complete transfers...\n"
msgstr "[%lu] Gehe in den Hintergrund um Übertragungen zu beenden...\n"

#: src/lftp.cc:465
#, c-format
msgid "[%u] Exiting and detaching from the terminal.\n"
msgstr ""

#: src/lftp.cc:470
#, c-format
msgid "[%u] Detached from terminal. %s\n"
msgstr ""

#: src/lftp.cc:480
#, fuzzy, c-format
msgid "[%u] Finished. %s\n"
msgstr "[%lu] Beendet. %s"

#: src/lftp.cc:484
#, fuzzy, c-format
msgid "[%u] Moving to background to complete transfers...\n"
msgstr "[%lu] Gehe in den Hintergrund um Übertragungen zu beenden...\n"

#: src/lftp.cc:553
msgid "history -w file|-r file|-c|-l [cnt]"
msgstr "history -w Datei|-r Datei|-c|-l [Zahl]"

#: src/lftp.cc:554
msgid ""
" -w <file> Write history to file.\n"
" -r <file> Read history from file; appends to current history.\n"
" -c  Clear the history.\n"
" -l  List the history (default).\n"
"Optional argument cnt specifies the number of history lines to list,\n"
"or \"all\" to list all entries.\n"
msgstr ""
" -w <Datei> Verlauf in eine Datei schreiben.\n"
" -r <Datei> Verlauf aus einer Datei lesen, an aktuellen Verlauf anhängen.\n"
" -c  Verlauf löschen.\n"
" -l  Verlauf anzeigen (Vorgabe).\n"
"Das entbehrliche Argument »Zahl« gibt die Anzahl der aufzulistenden\n"
"Verlaufszeilen an. »all« gibt alle Einträge aus.\n"

#: src/lftp.cc:561
msgid "Attach the terminal to specified backgrounded lftp process.\n"
msgstr ""

#: src/lftp_ssl.cc:1291
#, c-format
msgid "No certificate presented by %s.\n"
msgstr ""

#: src/LocalAccess.cc:604 src/NetAccess.cc:686
msgid "Getting directory contents"
msgstr "Lese Verzeichnisinhalt aus"

#: src/LocalAccess.cc:606 src/NetAccess.cc:690
msgid "Getting files information"
msgstr "Lese Dateiinformationen"

#: src/LsCache.cc:190
#, c-format
msgid "%ld $#l#byte|bytes$ cached"
msgstr "%ld $#l#Byte|Bytes$ zwischengespeichert"

#: src/LsCache.cc:194
msgid ", no size limit"
msgstr ", keine Größenbegrenzung"

#: src/LsCache.cc:196
#, c-format
msgid ", maximum size %ld\n"
msgstr ", Maximalgröße %ld\n"

#: src/mgetJob.cc:78
#, fuzzy, c-format
msgid "%s: %s: no files found\n"
msgstr "%s: keine Dateien gefunden\n"

#: src/MirrorJob.cc:100
#, c-format
msgid "%sTotal: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
msgstr "%sGesamt: %d Verzeichni$s|sse$, %d Datei$|en$, %d Verknüpfungen$|s$\n"

#: src/MirrorJob.cc:104
#, c-format
msgid "%sNew: %d file$|s$, %d symlink$|s$\n"
msgstr "%sNeu: %d Datei$|en$, %d Verknüpfungen$|s$\n"

#: src/MirrorJob.cc:108
#, c-format
msgid "%sModified: %d file$|s$, %d symlink$|s$\n"
msgstr "%sVerändert: %d Datei$|en$, %d Symlink$|s$\n"

#: src/MirrorJob.cc:115
#, c-format
msgid "%sRemoved: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
msgstr "%sGelöscht: %d Verzeichnis$|sse$, %d Datei$|en$, %d Symlink$|s$\n"

#: src/MirrorJob.cc:120
#, c-format
msgid "%s%d error$|s$ detected\n"
msgstr "%s%d Fehler gefunden\n"

#: src/MirrorJob.cc:231
#, fuzzy, c-format
msgid "Finished %s"
msgstr "[%lu] Beendet. %s"

#: src/MirrorJob.cc:344 src/MirrorJob.cc:519 src/MirrorJob.cc:1218
#, c-format
msgid "Removing old file `%s'"
msgstr "Entferne alte Datei »%s«"

#: src/MirrorJob.cc:346
#, fuzzy, c-format
msgid "Overwriting old file `%s'"
msgstr "Entferne alte Datei »%s«"

#: src/MirrorJob.cc:355
#, c-format
msgid "Skipping file `%s' (only-existing)"
msgstr "Überspringe Datei »%s« (only-existing)"

#: src/MirrorJob.cc:361
#, c-format
msgid "Transferring file `%s'"
msgstr "Sende Datei »%s«"

#: src/MirrorJob.cc:439
#, c-format
msgid "Skipping directory `%s' (only-existing)"
msgstr "Lege Verzeichnis »%s« an (only-existing)"

#: src/MirrorJob.cc:461 src/MirrorJob.cc:550 src/MirrorJob.cc:902
#, c-format
msgid "Removing old local file `%s'"
msgstr "Entferne alte lokale Datei »%s«"

#: src/MirrorJob.cc:487
#, fuzzy, c-format
msgid "Scanning directory `%s'"
msgstr "Lege Verzeichnis »%s« an"

#: src/MirrorJob.cc:489
#, c-format
msgid "Mirroring directory `%s'"
msgstr "Spiegele Verzeichnis »%s«"

#: src/MirrorJob.cc:525 src/MirrorJob.cc:567
#, c-format
msgid "Making symbolic link `%s' to `%s'"
msgstr "Erzeuge symbolische Verknüpfung »%s« nach »%s«"

#: src/MirrorJob.cc:562
#, c-format
msgid "Skipping symlink `%s' (only-existing)"
msgstr "Überspringe Verknüpfung »%s« (only-existing)"

#: src/MirrorJob.cc:807
#, c-format
msgid "mirror: protocol `%s' is not suitable for mirror\n"
msgstr "mirror: Protokoll »%s« ist nicht für das spiegeln geeignet\n"

#: src/MirrorJob.cc:923
#, c-format
msgid "Making directory `%s'"
msgstr "Lege Verzeichnis »%s« an"

#: src/MirrorJob.cc:1181
#, c-format
msgid "Old directory `%s' is not removed"
msgstr "Bestehendes Verzeichnis »%s« wird nicht entfernt"

#: src/MirrorJob.cc:1183
#, c-format
msgid "Old file `%s' is not removed"
msgstr "Bestehende Datei »%s« wird nicht entfernt"

#: src/MirrorJob.cc:1216
#, c-format
msgid "Removing old directory `%s'"
msgstr "Entferne alte Datei »%s«"

#: src/MirrorJob.cc:1339
#, fuzzy, c-format
msgid "Removing source directory `%s'"
msgstr "Entferne alte Datei »%s«"

#: src/MirrorJob.cc:1405
#, fuzzy, c-format
msgid "Removing source file `%s'"
msgstr "Entferne alte Datei »%s«"

#: src/MirrorJob.cc:1425
#, c-format
msgid "Retrying mirror...\n"
msgstr "Versuche Spiegelserver...\n"

#: src/MirrorJob.cc:1694
msgid "pattern is empty"
msgstr ""

#: src/MirrorJob.cc:1779
#, fuzzy, c-format
msgid "%s must be one of: %s"
msgstr "muss eins der folgenden sein: C, S, E, P oder leer"

#: src/MirrorJob.cc:2019
#, c-format
msgid ""
"%s: multiple --file or --directory options must have the same base "
"directory\n"
msgstr ""

#: src/MirrorJob.cc:2160
#, c-format
msgid "%s: ambiguous source directory (`%s' or `%s'?)\n"
msgstr ""

#: src/MirrorJob.cc:2182
#, fuzzy, c-format
msgid "%s: ambiguous target directory (`%s' or `%s'?)\n"
msgstr "mehrdeutiges Argument  %s für %s"

#: src/MirrorJob.cc:2223
#, c-format
msgid "%s: source directory is required (mirror:require-source is set)\n"
msgstr ""

#: src/MirrorJob.cc:2343
msgid ""
"\n"
"Mirror specified remote directory to local directory\n"
"\n"
" -R, --reverse          reverse mirror (put files)\n"
"Lots of other options are documented in the man page lftp(1).\n"
"\n"
"When using -R, the first directory is local and the second is remote.\n"
"If the second directory is omitted, basename of the first directory is "
"used.\n"
"If both directories are omitted, current local and remote directories are "
"used.\n"
"\n"
"See the man page lftp(1) for a complete documentation.\n"
msgstr ""

#: src/mkdirJob.cc:128
#, c-format
msgid "%s ok, `%s' created\n"
msgstr "%s OK, »%s« erzeugt\n"

#: src/mkdirJob.cc:130 src/rmJob.cc:51
#, c-format
msgid "%s failed for %d of %d director$y|ies$\n"
msgstr "%s fehlgeschlagen für %d von %d Verzeichnis$|en$\n"

#: src/mkdirJob.cc:133
#, c-format
msgid "%s ok, %d director$y|ies$ created\n"
msgstr "%s OK, %d Verzeichnis$|se$ angelegt\n"

#: src/module.cc:190
#, c-format
msgid "depend module `%s': %s\n"
msgstr "Voraussetzung Modul »%s«: %s\n"

#: src/module.cc:210
msgid "modules are not supported on this system"
msgstr "Module sind auf diesem System nicht verwendbar"

#: src/mvJob.cc:92
#, c-format
msgid "rename successful\n"
msgstr "Umbenennung erfolgreich\n"

#: src/NetAccess.cc:168
#, c-format
msgid "Connecting to %s%s (%s) port %u"
msgstr "Verbinde mit %s%s (%s) Port %u"

#: src/NetAccess.cc:224 src/Torrent.cc:3326
#, c-format
msgid "Timeout - reconnecting"
msgstr "Zeitablauf - neu verbinden"

#: src/NetAccess.cc:323
msgid "Connection limit reached"
msgstr "Zahl der Verbindungen hat Grenze erreicht"

#: src/NetAccess.cc:330
msgid "Delaying before reconnect"
msgstr "Pausiere vor erneuter Verbindung"

#: src/NetAccess.cc:354 src/NetAccess.cc:356
msgid "max-retries exceeded"
msgstr "max-retries überschritten"

#: src/OutputJob.cc:145
#, c-format
msgid "%s (filter)"
msgstr "%s (Filter)"

#: src/parsecmd.cc:290
msgid "parse: missing filter command\n"
msgstr "parse: Filterbefehl fehlt\n"

#: src/parsecmd.cc:292
msgid "parse: missing redirection filename\n"
msgstr "parse: Dateiname für Umleitung fehlt\n"

#: src/PatternSet.cc:110
#, fuzzy, c-format
msgid "regular expression `%s': %s"
msgstr "%s: Regulärer Ausdruck (regular expression) %s: %s\n"

#: src/pgetJob.cc:127
msgid "pget: falling back to plain get"
msgstr "pget: falle auf einfaches get zurück"

#: src/pgetJob.cc:131
msgid "the target file is remote"
msgstr "Die Zieldatei ist nicht lokal"

#: src/pgetJob.cc:136
msgid "the source file size is unknown"
msgstr "die Größe der Quelldatei ist unbekannt"

#: src/pgetJob.cc:173
#, c-format
msgid "pget: warning: space allocation for %s (%lld bytes) failed: %s\n"
msgstr ""

#: src/pgetJob.cc:234
#, c-format
msgid "`%s', got %lld of %lld (%d%%) %s%s"
msgstr "»%s«, empfange %lld von %lld (%d%%) %s %s"

#: src/plural.c:96
msgid "=1 =0|>1"
msgstr "<2 >1"

#: src/PollVec.cc:69
#, c-format
msgid "%s: BUG - deadlock detected\n"
msgstr "%s: Fehler - Schleife (deadlock) entdeckt\n"

#: src/PtyShell.cc:68
msgid "pseudo-tty allocation failed: "
msgstr "Allokation eines Pseudo-tty fehlgeschlagen:"

#: src/QueueFeeder.cc:68
msgid "Added job$|s$"
msgstr "Job$|s$  hinzugefügt"

#: src/QueueFeeder.cc:164 src/QueueFeeder.cc:185
#, c-format
msgid "No queued jobs.\n"
msgstr "Keine Jobs in der Warteschlange.\n"

#: src/QueueFeeder.cc:166
#, c-format
msgid "No queued job #%i.\n"
msgstr "Keine Job in der Warteschlange #%i.\n"

#: src/QueueFeeder.cc:171 src/QueueFeeder.cc:192
msgid "Deleted job$|s$"
msgstr "Gelöschte  Job$|s$"

#: src/QueueFeeder.cc:187
#, c-format
msgid "No queued jobs match \"%s\".\n"
msgstr "Kein Job in der Warteschlange passt zu  »%s«.\n"

#: src/QueueFeeder.cc:212 src/QueueFeeder.cc:230
msgid "Moved job$|s$"
msgstr "Job$|s$ verschoben"

#: src/QueueFeeder.cc:354
msgid "Commands queued:"
msgstr "Befehle in Warteschlange:"

#: src/ResMgr.cc:123
msgid "no such variable"
msgstr "keine derartige Variable"

#: src/ResMgr.cc:127
msgid "ambiguous variable name"
msgstr "Mehrdeutiger Name für Variable"

#: src/ResMgr.cc:335
msgid "invalid boolean value"
msgstr "unzulässiger boolscher Wert"

#: src/ResMgr.cc:357
msgid "invalid boolean/auto value"
msgstr "unzulässiger boolscher/automatischer Wert"

#: src/ResMgr.cc:402 src/ResMgr.cc:713
msgid "invalid number"
msgstr "Unzulässige Zahl"

#: src/ResMgr.cc:415
msgid "invalid floating point number"
msgstr "Unzulässige Gleitkommazahl"

#: src/ResMgr.cc:429
#, fuzzy
msgid "invalid unsigned number"
msgstr "Unzulässige Zahl"

#: src/ResMgr.cc:678
msgid "Invalid time unit letter, only [smhd] are allowed."
msgstr "Ungültige Zeiteinheit, nur [smhd] sind zulässig."

#: src/ResMgr.cc:686
msgid "Invalid time format. Format is <time><unit>, e.g. 2h30m."
msgstr "Ungültige Zeitangabe. Das Format ist <Zeit><Einheit> - z.B. 2h30m."

#: src/ResMgr.cc:718
#, fuzzy
msgid "integer overflow"
msgstr "Integerüberlauf"

#: src/ResMgr.cc:804
msgid "Invalid IPv4 numeric address"
msgstr "Ungültige IPv4 Adressierungsnummer"

#: src/ResMgr.cc:814
msgid "Invalid IPv6 numeric address"
msgstr "Ungültige IPv6 Adressierungsnummer"

#: src/ResMgr.cc:884 src/ResMgr.cc:888
msgid "this encoding is not supported"
msgstr "Diese Zeichenkodierung wird nicht unterstützt"

#: src/ResMgr.cc:894
msgid "no closure defined for this setting"
msgstr "kein Gültigkeitsbereich (closure) für diese Einstellung definiert"

#: src/ResMgr.cc:900
#, fuzzy
msgid "a closure is required for this setting"
msgstr "kein Gültigkeitsbereich (closure) für diese Einstellung definiert"

#: src/Resolver.cc:236
msgid "host name resolve timeout"
msgstr "Zeitablauf: Rechnernamen nicht aufgelöst"

#: src/Resolver.cc:282
#, c-format
msgid "%d address$|es$ found"
msgstr "%d  Adresse$|n$ gefunden"

#: src/Resolver.cc:327
msgid "Link-local IPv6 address should have a scope"
msgstr ""

#: src/Resolver.cc:765
msgid "DNS resolution not trusted."
msgstr ""

#: src/Resolver.cc:871
msgid "Host name lookup failure"
msgstr "Nachschlagen des Hostnamen fehlgeschlagen"

#: src/Resolver.cc:903
#, c-format
msgid "no such %s service"
msgstr "kein derartiger %s Dienst"

#: src/Resolver.cc:930
msgid "No address found"
msgstr "Keine Adresse gefunden"

#: src/resource.cc:50 src/resource.cc:108
msgid "Proxy protocol unsupported"
msgstr "Proxy Protokoll wird nicht unterstützt"

#: src/resource.cc:54
msgid "ftp:proxy password: "
msgstr "ftp:proxy Passwort: "

#: src/resource.cc:70
#, c-format
msgid "%s must be one of: "
msgstr ""

#: src/resource.cc:72
#, fuzzy
msgid "must be one of: "
msgstr "muss eins der folgenden sein: C, S, E, P oder leer"

#: src/resource.cc:84
msgid ", or empty"
msgstr ""

#: src/resource.cc:116
msgid "only PUT and POST values allowed"
msgstr "nur PUT und POST Werte sind zulässig"

#: src/resource.cc:148
#, c-format
msgid "unknown address family `%s'"
msgstr "unbekannte Adress Familie »%s«"

#: src/rmJob.cc:47
#, c-format
msgid "%s ok, `%s' removed\n"
msgstr "%s OK, »%s« gelöscht\n"

#: src/rmJob.cc:54
#, c-format
msgid "%s failed for %d of %d file$|s$\n"
msgstr "%s fehlgeschlagen für %d von %d Datei$|en$\n"

#: src/rmJob.cc:60
#, c-format
msgid "%s ok, %d director$y|ies$ removed\n"
msgstr "%s OK, %d Verzeichnis$|se$ entfernt\n"

#: src/rmJob.cc:63
#, c-format
msgid "%s ok, %d file$|s$ removed\n"
msgstr "%s OK, %d Datei$|en$ gelöscht\n"

#: src/SFtp.cc:1099 src/SFtp.cc:1100
#, c-format
msgid "invalid server response format"
msgstr "unzulässiges Format der Serverantwort"

#: src/SleepJob.cc:99
msgid "Sleeping forever"
msgstr ""

#: src/SleepJob.cc:100
msgid "Sleep time left: "
msgstr ""

#: src/SleepJob.cc:108
#, c-format
msgid "\tRepeat count: %d\n"
msgstr "\tWiederhole Zählung: %d\n"

#: src/SleepJob.cc:142
#, c-format
msgid "%s: argument required. "
msgstr "%s: Argument vorgeschrieben. "

#: src/SleepJob.cc:262
#, c-format
msgid "%s: date-time specification missed\n"
msgstr ""

#: src/SleepJob.cc:269
#, c-format
msgid "%s: date-time parse error\n"
msgstr ""

#: src/SleepJob.cc:303
msgid ""
"Usage: sleep <time>[unit]\n"
"Sleep for given amount of time. The time argument can be optionally\n"
"followed by unit specifier: d - days, h - hours, m - minutes, s - seconds.\n"
"By default time is assumed to be seconds.\n"
msgstr ""
"Verwendung: sleep <Zeitdauer>[Zeiteinheit]\n"
"Schlafe für die angegebene Zeitdauer. Das Zeitargument kann mit der Angabe \n"
"einer Einheit ergänzt werden: d - Tage, h - Stunden, m - Minuten, s - "
"Sekunden.\n"
"Ohne Angabe wird die Zeitangabe als Sekunden interpretiert.\n"

#: src/SleepJob.cc:310
#, fuzzy
msgid ""
"Repeat specified command with a delay between iterations.\n"
"Default delay is one second, default command is empty.\n"
" -c <count>  maximum number of iterations\n"
" -d <delay>  delay between iterations\n"
" --while-ok  stop when command exits with non-zero code\n"
" --until-ok  stop when command exits with zero code\n"
" --weak      stop when lftp moves to background.\n"
msgstr ""
"Den angegebenen Befehl mit der angegebenen Verzögerung wiederholt "
"ausführen.\n"
"Standardverzögerung ist 1 Sekunde. Standardbefehl ist leer.\n"
" -c <anzahl> Anzahl der Wiederholungen\n"
" -d >Verzögerung zwischen einzelnen Wiederholungen.\n"

#: src/Speedometer.cc:90
#, c-format
msgid "%.0fb/s"
msgstr "%.0fb/s"

#: src/Speedometer.cc:93
#, c-format
msgid "%.1fK/s"
msgstr "%.1fK/s"

#: src/Speedometer.cc:96
#, c-format
msgid "%.2fM/s"
msgstr "%.2fM/s"

#: src/Speedometer.cc:103
#, fuzzy, c-format
msgid "%.0f B/s"
msgstr "%.0fb/s"

#: src/Speedometer.cc:105
#, fuzzy, c-format
msgid "%.1f KiB/s"
msgstr "%.1fK/s"

#: src/Speedometer.cc:107
#, fuzzy, c-format
msgid "%.2f MiB/s"
msgstr "%.2fM/s"

#: src/Speedometer.cc:129
msgid "eta:"
msgstr "ca. fertig:"

#: src/SSH_Access.cc:97
#, fuzzy
msgid "Password required"
msgstr "Passwort: "

#: src/SSH_Access.cc:102
msgid "Login incorrect"
msgstr ""

#: src/SSH_Access.cc:196
#, fuzzy, c-format
msgid "Disconnecting"
msgstr "Verbinde..."

#: src/SysCmdJob.cc:73
#, c-format
msgid "execlp(%s) failed: %s\n"
msgstr "execlp(%s) fehlgeschlagen: %s\n"

#: src/TimeDate.cc:155
msgid "day"
msgstr "Tag"

#: src/TimeDate.cc:156
msgid "hour"
msgstr "Stunde"

#: src/TimeDate.cc:157
msgid "minute"
msgstr "Minute"

#: src/TimeDate.cc:158
msgid "second"
msgstr "Sekunde"

#: src/Torrent.cc:585
msgid "announced via "
msgstr ""

#: src/Torrent.cc:599
#, c-format
msgid "next announce in %s"
msgstr ""

#: src/Torrent.cc:1142
#, c-format
msgid "%d file$|s$ found, now scanning %s"
msgstr ""

#: src/Torrent.cc:1143
#, fuzzy, c-format
msgid "%d file$|s$ found"
msgstr "%d  Adresse$|n$ gefunden"

#: src/Torrent.cc:2075 src/Torrent.cc:2085
#, c-format
msgid "Getting meta-data: %s"
msgstr ""

#: src/Torrent.cc:2077
#, c-format
msgid "Validation: %u/%u (%u%%) %s%s"
msgstr ""

#: src/Torrent.cc:2088
#, fuzzy
msgid "Waiting for meta-data..."
msgstr "\tWarten auf Befehl\n"

#: src/Torrent.cc:2096
msgid "Shutting down: "
msgstr ""

#: src/Torrent.cc:3207
#, fuzzy, c-format
msgid "Connecting to peer %s port %u"
msgstr "Verbinde mit %s%s (%s) Port %u"

#: src/Torrent.cc:3258 src/Torrent.cc:3375
#, fuzzy, c-format
msgid "peer unexpectedly closed connection after %s"
msgstr "Gegenüber hat Verbindung beendet"

#: src/Torrent.cc:3259 src/Torrent.cc:3376
#, fuzzy
msgid "peer unexpectedly closed connection"
msgstr "Gegenüber hat Verbindung beendet"

#: src/Torrent.cc:3261 src/Torrent.cc:3262
#, fuzzy, c-format
msgid "peer closed connection (before handshake)"
msgstr "Gegenüber hat Verbindung beendet"

#: src/Torrent.cc:3265 src/Torrent.cc:3378 src/Torrent.cc:3379
#, fuzzy, c-format
msgid "invalid peer response format"
msgstr "unzulässiges Format der Serverantwort"

#: src/Torrent.cc:3360 src/Torrent.cc:3361
#, fuzzy, c-format
msgid "peer closed connection"
msgstr "Gegenüber hat Verbindung beendet"

#: src/Torrent.cc:3538
msgid "Handshaking..."
msgstr ""

#: src/Torrent.cc:3798
msgid "Cannot bind a socket for torrent:port-range"
msgstr ""

#: src/Torrent.cc:3858
#, fuzzy, c-format
msgid "Accepted connection from [%s]:%d"
msgstr "Datenverbindung von (%s) Port %u akzeptiert"

#: src/Torrent.cc:3924
#, c-format
msgid "peer sent unknown info_hash=%s in handshake"
msgstr ""

#: src/Torrent.cc:3948
#, c-format
msgid "peer handshake timeout"
msgstr ""

#: src/Torrent.cc:3960
#, c-format
msgid "peer short handshake"
msgstr ""

#: src/Torrent.cc:3962
#, fuzzy, c-format
msgid "peer closed just accepted connection"
msgstr "Gegenüber hat Verbindung beendet"

#: src/Torrent.cc:4013
#, fuzzy, c-format
msgid "Seeding in background...\n"
msgstr "Schicke Befehle..."

#: src/Torrent.cc:4176
#, fuzzy, c-format
msgid "%s: --share conflicts with --output-directory.\n"
msgstr "%s: »summarize« kann nicht mit --max-depth=%i kombiniert werden\n"

#: src/Torrent.cc:4180
#, fuzzy, c-format
msgid "%s: --share conflicts with --only-new.\n"
msgstr "%s: »summarize« kann nicht mit --max-depth=%i kombiniert werden\n"

#: src/Torrent.cc:4184
#, fuzzy, c-format
msgid "%s: --share conflicts with --only-incomplete.\n"
msgstr "%s: »summarize« kann nicht mit --max-depth=%i kombiniert werden\n"

#: src/Torrent.cc:4226
#, c-format
msgid "%s: Please specify a file or directory to share.\n"
msgstr ""

#: src/Torrent.cc:4228
#, c-format
msgid "%s: Please specify meta-info file or URL.\n"
msgstr ""

#: src/Torrent.cc:4258
msgid ""
"Start BitTorrent job for the given torrent-files, which can be a local "
"file,\n"
"URL, magnet link or plain info_hash written in hex or base32. Local "
"wildcards\n"
"are expanded. Options:\n"
" -O <base>      specifies base directory where files should be placed\n"
" --force-valid  skip file validation\n"
" --dht-bootstrap=<node>  bootstrap DHT by sending a query to the node\n"
" --share        share specified file or directory\n"
msgstr ""

#: src/TorrentTracker.cc:178
msgid "not started"
msgstr ""

#: src/TorrentTracker.cc:181
#, c-format
msgid "next request in %s"
msgstr ""

#: src/TorrentTracker.cc:284 src/TorrentTracker.cc:501
#, c-format
msgid "Received valid info about %d peer$|s$"
msgstr ""

#: src/TorrentTracker.cc:298
#, c-format
msgid "Received valid info about %d IPv6 peer$|s$"
msgstr ""

#, fuzzy
#~ msgid "%s: option '--%s' doesn't allow an argument\n"
#~ msgstr "%s: Option »--%s« erlaubt kein Argument\n"

#, fuzzy
#~ msgid "%s: unrecognized option '--%s'\n"
#~ msgstr "%s: nicht erkannte Option »--%s«\n"

#, fuzzy
#~ msgid "%s: option '-W %s' is ambiguous\n"
#~ msgstr "%s: Option »-W %s« ist mehrdeutig\n"

#, fuzzy
#~ msgid "%s: option '-W %s' doesn't allow an argument\n"
#~ msgstr "%s: Option »-W %s« erlaubt keine Parameter\n"

#, fuzzy
#~ msgid "%s: option '-W %s' requires an argument\n"
#~ msgstr "%s: Option »%s« erfordert einen Parameter\n"

#~ msgid "Usage: mv <file1> <file2>\n"
#~ msgstr "Benutzung: mv <datei1> <datei2>\n"

#, fuzzy
#~ msgid "number"
#~ msgstr "Unzulässige Zahl"

#, fuzzy
#~ msgid "error: %s:%d\n"
#~ msgstr "Fataler Fehler: %s"

#, fuzzy
#~ msgid ""
#~ "\n"
#~ "Mirror specified remote directory to local directory\n"
#~ "\n"
#~ " -c, --continue         continue a mirror job if possible\n"
#~ " -e, --delete           delete files not present at remote site\n"
#~ "     --delete-first     delete old files before transferring new ones\n"
#~ " -s, --allow-suid       set suid/sgid bits according to remote site\n"
#~ "     --allow-chown      try to set owner and group on files\n"
#~ "     --ignore-time      ignore time when deciding whether to download\n"
#~ " -n, --only-newer       download only newer files (-c won't work)\n"
#~ " -r, --no-recursion     don't go to subdirectories\n"
#~ " -p, --no-perms         don't set file permissions\n"
#~ "     --no-umask         don't apply umask to file modes\n"
#~ " -R, --reverse          reverse mirror (put files)\n"
#~ " -L, --dereference      download symbolic links as files\n"
#~ " -N, --newer-than=SPEC  download only files newer than specified time\n"
#~ " -P, --parallel[=N]     download N files in parallel\n"
#~ " -i RX, --include RX    include matching files\n"
#~ " -x RX, --exclude RX    exclude matching files\n"
#~ "                        RX is extended regular expression\n"
#~ " -v, --verbose[=N]      verbose operation\n"
#~ "     --log=FILE         write lftp commands being executed to FILE\n"
#~ "     --script=FILE      write lftp commands to FILE, but don't execute "
#~ "them\n"
#~ "     --just-print, --dry-run    same as --script=-\n"
#~ "\n"
#~ "When using -R, the first directory is local and the second is remote.\n"
#~ "If the second directory is omitted, basename of first directory is used.\n"
#~ "If both directories are omitted, current local and remote directories are "
#~ "used.\n"
#~ "See lftp(1) for a complete list of options.\n"
#~ msgstr ""
#~ "\n"
#~ "Das angegebene nichtlokale Verzeichnis in ein lokales Verzeichnis "
#~ "spiegeln\n"
#~ "\n"
#~ " -c, --continue         mit dem Spiegeln fortfahren, wenn möglich\n"
#~ " -e, --delete           Dateien die nicht auf dem entfernten "
#~ "Verzeichnis \n"
#~ "                        liegen löschen\n"
#~ "     --delete-first     alte Dateien vor der Übertragung neuer Dateien "
#~ "löschen\n"
#~ " -s, --allow-suid       SUID/SGID Berechtigungen setzen wie auf dem\n"
#~ "                        Server\n"
#~ "     --allow-chown      wenn möglich Benutzer und Gruppe setzen\n"
#~ " -n, --only-newer       nur neuere Dateien herunterladen (schließt -c "
#~ "aus)\n"
#~ " -r, --no-recursion     Unterverzeichnisse nicht mit einschließen\n"
#~ " -p, --no-perms         keine Dateiberechtigungen setzen\n"
#~ "     --no-umask         umask nicht auf Dateiattribute anwenden\n"
#~ " -R, --reverse          umgedrehter Spiegel (Dateien schicken)\n"
#~ " -L, --dereference      symbolische Links als Files herunterladen\n"
#~ " -N, --newer-than DATEI nur Dateien die neuer sind als DATEI "
#~ "herunterladen\n"
#~ " -P, --parallel[=N]     N Dateien gleichzeitig herunterladen\n"
#~ " -i RA, --include RA    zum regulären Ausdruck passende Dateien "
#~ "einschließen\n"
#~ " -x RA, --exclude RA    zum regulären Ausdruck passende Dateien\n"
#~ "                        ausschließen\n"
#~ "                        RA sind erweiterte reguläre Ausdrücke (Joker)\n"
#~ " -v, --verbose[=N]      mehr Informationen ausgeben\n"
#~ "     --log=DATEI        ausgeführte lftp Befehle in DATEI protokollieren\n"
#~ "     --script=DATEI     lftp Befehle in DATEI protkollieren, aber nicht\n"
#~ "                        ausführen\n"
#~ "     --jusz.print, --dry-run    entspricht »--script=-«\n"
#~ "\n"
#~ "Gibt man -R an, so ist das erste Verzeichnis lokal und das andere auf "
#~ "dem \n"
#~ "Gegenstelle.\n"
#~ "Wird das zweite Verzeichnis nicht angegeben, wird die "
#~ "Namenswurzel(basename)\n"
#~ "des ersten Verzeichnisses verwendet.\n"
#~ "Werden beide Verzeichnisse weggelassen, so werden das aktuelle lokale "
#~ "und\n"
#~ "nichtlokale Verzeichnis verwendet.\n"

#~ msgid "SITE CHMOD is disabled by ftp:use-site-chmod"
#~ msgstr "SITE CHMOD abgeschaltet durch ftp:use-site-chmod"

#, fuzzy
#~ msgid ""
#~ "ftp:proxy-auth-type must be one of: user, joined, joined-acct, open, "
#~ "proxy-user@host"
#~ msgstr ""
#~ "ftp:proxy-auth-type muss eins der folgenden sein: user, joined, joined-"
#~ "acct, open"

#~ msgid "ftp:ssl-auth must be one of: SSL, TLS, TLS-P, TLS-C"
#~ msgstr "ftp:ssl-auth muss eines der folgenden sein: SSL, TLS, TLS-P, TLS-C"

#~ msgid "Invalid suffix. Valid suffixes are: k, M, G, T, P, E, Z, Y"
#~ msgstr ""
#~ "Unzulässiger Endung (suffix). Zulässige Endungen sind: k, M, G, T, P, E, "
#~ "Z, Y"

#~ msgid "invalid pair of numbers"
#~ msgstr "Unzulässiges Zahlenpaar"

#~ msgid "Saw `unknown', assume failed login"
#~ msgstr "»Unknown« empfangen, gehe von fehlgeschlagenem Login aus"

#~ msgid "Can only edit plain queues.\n"
#~ msgstr "Kann nur einfache Warteschlangen editieren.\n"

#~ msgid "Couldn't create temporary file `%s': %s.\n"
#~ msgstr "Konnte temporäre Datei `%s': %s nicht anlegen.\n"

#~ msgid "%s: error writing %s: %s\n"
#~ msgstr "%s: Fehler beim Schreiben von %s: %s\n"

#~ msgid "%s: illegal option -- %c\n"
#~ msgstr "%s: unzulässige Option --%c\n"

#~ msgid ""
#~ "LFTP is free software, covered by the GNU General Public License, and you "
#~ "are\n"
#~ "welcome to change it and/or distribute copies of it under certain "
#~ "conditions.\n"
#~ "There is absolutely no warranty for LFTP.  See COPYING for details.\n"
#~ msgstr ""
#~ "LFTP ist freie Software und steht unter der GNU General Public License.\n"
#~ "Es steht Ihnen frei, die Software zu verändern und/oder  unter "
#~ "bestimmten\n"
#~ "Bedingungen Kopien zu verbreiten.\n"
#~ "Es gibt absolut keine Gewährleistung für LFTP. \n"
#~ "Bitte konsultieren Sie COPYING für Einzelheiten.\n"

#~ msgid "block size"
#~ msgstr "Blockgrösse"

#~ msgid "%sTo be removed: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
#~ msgstr ""
#~ "%sZur Löschung vorgesehen: %d Verzeichnis$|se$, %d Datei$|en$, %d "
#~ "Symlink$|s$\n"

#~ msgid "Usage: %s userid [pass]\n"
#~ msgstr "Benutzung: %s User-ID [passwort]\n"

#~ msgid "Cache is on"
#~ msgstr "Cache ist an"

#~ msgid "Cache is off"
#~ msgstr "Cache ist aus"

#~ msgid "Cache entries do not expire"
#~ msgstr "Cache Einträge verfallen nicht"

#~ msgid "Cache entries expire in %ld $#l#second|seconds$\n"
#~ msgstr ""
#~ "Einträge im Zwischenspeicher verfallen in %ld $#l#Sekunde|Sekunden$\n"

#~ msgid "Cache entries expire in %ld $#l#minute|minutes$\n"
#~ msgstr "Einträge im Zwischenspeicher verfallen in %ld $#l#Minute|Minuten$\n"

#~ msgid ""
#~ "This is free software with ABSOLUTELY NO WARRANTY. See COPYING for "
#~ "details.\n"
#~ msgstr ""
#~ "Dies ist freie Software OHNE IRGENDWELCHE GARANTIEN ODER "
#~ "GEWÄHRLEISTUNGEN.\n"
#~ "Die Datei COPYING enthält Details.\n"
