/*
 * lftp - file transfer program
 *
 * Copyright (c) 1996-2012 by <PERSON> (<EMAIL>)
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

#ifndef FILEFEEDER_H
#define FILEFEEDER_H

#include "CmdExec.h"

class FileFeeder : public CmdFeeder
{
   Ref<FDStream> in;
   Ref<FgData> fg_data;
   char buffer[0x1001];
public:
   const char *NextCmd(CmdExec *exec,const char *prompt);
   FileFeeder(FDStream *in);
   ~FileFeeder();
   void Fg() { if(fg_data) fg_data->Fg(); }
   void Bg() { if(fg_data) fg_data->Bg(); }
};

#endif//FILEFEEDER_H
