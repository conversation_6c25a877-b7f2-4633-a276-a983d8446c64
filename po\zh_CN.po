# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR <PERSON> <<EMAIL>>
# This file is distributed under the same license as the lftp package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: lftp 4.8.3-dirty\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2024-11-08 13:05+0300\n"
"PO-Revision-Date: 2018-03-21 02:41+0800\n"
"Last-Translator: wwj402@<PERSON> <<EMAIL>>\n"
"Language-Team: zh <<EMAIL>>\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Language: zh_CN\n"
"X-Source-Language: C\n"
"X-Generator: Poedit 2.0.2\n"

#: lib/argmatch.c:145
#, c-format
msgid "invalid argument %s for %s"
msgstr "无效的参数 %s 用于 %s"

#: lib/argmatch.c:146
#, c-format
msgid "ambiguous argument %s for %s"
msgstr "不明确的参数 %s 用于 %s"

#: lib/argmatch.c:165
msgid "Valid arguments are:"
msgstr "有效的参数是："

#: lib/error.c:208
msgid "Unknown system error"
msgstr "未知的系统错误"

#: lib/getopt.c:282
#, c-format
msgid "%s: option '%s%s' is ambiguous\n"
msgstr "%s：选项 '%s%s' 不明确\n"

#: lib/getopt.c:288
#, c-format
msgid "%s: option '%s%s' is ambiguous; possibilities:"
msgstr "%s：选项 '%s%s' 不明确；可能是："

#: lib/getopt.c:322
#, c-format
msgid "%s: unrecognized option '%s%s'\n"
msgstr "%s：无法识别的选项 '%s%s'\n"

#: lib/getopt.c:348
#, c-format
msgid "%s: option '%s%s' doesn't allow an argument\n"
msgstr "%s：选项 '%s%s' 不允许参数\n"

#: lib/getopt.c:363
#, c-format
msgid "%s: option '%s%s' requires an argument\n"
msgstr "%s：选项 '%s%s' 需要一个参数\n"

#: lib/getopt.c:624
#, c-format
msgid "%s: invalid option -- '%c'\n"
msgstr "%s：无效选项 -- '%c'\n"

#: lib/getopt.c:639 lib/getopt.c:685
#, c-format
msgid "%s: option requires an argument -- '%c'\n"
msgstr "%s：选项需要一个参数 -- '%c'\n"

#. TRANSLATORS:
#. Get translations for open and closing quotation marks.
#. The message catalog should translate "`" to a left
#. quotation mark suitable for the locale, and similarly for
#. "'".  For example, a French Unicode local should translate
#. these to U+00AB (LEFT-POINTING DOUBLE ANGLE
#. QUOTATION MARK), and U+00BB (RIGHT-POINTING DOUBLE ANGLE
#. QUOTATION MARK), respectively.
#.
#. If the catalog has no translation, we will try to
#. use Unicode U+2018 (LEFT SINGLE QUOTATION MARK) and
#. Unicode U+2019 (RIGHT SINGLE QUOTATION MARK).  If the
#. current locale is not Unicode, locale_quoting_style
#. will quote 'like this', and clocale_quoting_style will
#. quote "like this".  You should always include translations
#. for "`" and "'" even if U+2018 and U+2019 are appropriate
#. for your locale.
#.
#. If you don't know what to put here, please see
#. <https://en.wikipedia.org/wiki/Quotation_marks_in_other_languages>
#. and use glyphs suitable for your language.
#: lib/quotearg.c:354
msgid "`"
msgstr ""

#: lib/quotearg.c:355
msgid "'"
msgstr ""

#: lib/xalloc-die.c:34
msgid "memory exhausted"
msgstr "内存耗尽"

#: src/ArgV.cc:107
msgid "option requires an argument"
msgstr "选项需要一个参数"

#: src/ArgV.cc:109 src/ArgV.cc:118
msgid "invalid option"
msgstr "无效的选项"

#: src/ArgV.cc:114
#, c-format
msgid "option `%s' requires an argument"
msgstr "选项 `%s' 需要一个参数"

#: src/ArgV.cc:116
#, c-format
msgid "unrecognized option `%s'"
msgstr "无法识别的选项 `%s'"

#: src/attach.h:138
#, c-format
msgid "[%u] Attached to terminal %s. %s\n"
msgstr "[%u] 附加到终端 %s。%s\n"

#: src/attach.h:150
#, c-format
msgid "[%u] Attached to terminal.\n"
msgstr "[%u] 附加到终端。\n"

#: src/buffer.cc:253 src/FileAccess.cc:866
msgid " [cached]"
msgstr " [缓存]"

#: src/ChmodJob.cc:80
#, c-format
msgid "Failed to change mode of `%s' to %04o (%s).\n"
msgstr "无法将 `%s' 的模式更改为 %04o (%s)。\n"

#: src/ChmodJob.cc:83
#, c-format
msgid "Mode of `%s' changed to %04o (%s).\n"
msgstr "`%ss' 的模式更改为 %04o (%s)。\n"

#: src/ChmodJob.cc:88
#, c-format
msgid "Failed to change mode of `%s' because no old mode is available.\n"
msgstr "无法更改 `%s'的模式，因为没有旧模式可用。\n"

#: src/CmdExec.cc:105
#, c-format
msgid "Warning: chdir(%s) failed: %s\n"
msgstr "警告: chdir(%s) 失败: %s\n"

#: src/CmdExec.cc:207
#, c-format
msgid "Unknown command `%s'.\n"
msgstr "未知命令 `%s'。\n"

#: src/CmdExec.cc:209
#, c-format
msgid "Ambiguous command `%s'.\n"
msgstr "不明确的命令 `%s'。\n"

#: src/CmdExec.cc:228
#, c-format
msgid "Module for command `%s' did not register the command.\n"
msgstr "命令 `%s' 的模块没有注册该命令。\n"

#: src/CmdExec.cc:384
#, c-format
msgid "cd ok, cwd=%s\n"
msgstr "cd 成功, 当前目录=%s\n"

#: src/CmdExec.cc:405 src/MirrorJob.cc:736
#, c-format
msgid "%s: received redirection to `%s'\n"
msgstr "%s：收到重定向到 `%s'\n"

#: src/CmdExec.cc:408 src/FileCopy.cc:1230
msgid "Too many redirections"
msgstr "太多的重定向"

#: src/CmdExec.cc:517 src/CmdExec.cc:574
msgid "Interrupt"
msgstr "中断"

#: src/CmdExec.cc:639
#, c-format
msgid "Warning: discarding incomplete command\n"
msgstr "警告: 放弃未完成的命令\n"

#: src/CmdExec.cc:737
#, c-format
msgid "\tExecuting builtin `%s' [%s]\n"
msgstr "\t执行内建的 `%s' [%s]\n"

#: src/CmdExec.cc:742
msgid "Queue is stopped."
msgstr "队列被停止。"

#: src/CmdExec.cc:747
msgid "Now executing:"
msgstr "正在执行："

#: src/CmdExec.cc:758
#, c-format
msgid "\tWaiting for job [%d] to terminate\n"
msgstr "\t等待任务 [%d] 中止\n"

#: src/CmdExec.cc:761
#, c-format
msgid "\tWaiting for termination of jobs: "
msgstr "\t等待终止任务： "

#: src/CmdExec.cc:770
msgid "\tRunning\n"
msgstr "\t正在运行\n"

#: src/CmdExec.cc:772
msgid "\tWaiting for command\n"
msgstr "\t等待命令\n"

#: src/CmdExec.cc:1261
#, c-format
msgid "%s: command `%s' is not compiled in.\n"
msgstr "%s：命令 '%s' 未被编译进去。\n"

#: src/CmdExec.cc:1278
#, c-format
msgid "Usage: %s cmd [args...]\n"
msgstr "用法：%s cmd [args...]\n"

#: src/CmdExec.cc:1284
#, c-format
msgid "%s: cannot create local session\n"
msgstr "%s：无法创建本地会话\n"

#: src/commands.cc:114
msgid "!<shell-command>"
msgstr ""

#: src/commands.cc:115
msgid "Launch shell or shell command\n"
msgstr "启动 shell 或 shell 命令\n"

#: src/commands.cc:116
msgid "(commands)"
msgstr ""

#: src/commands.cc:117
msgid ""
"Group commands together to be executed as one command\n"
"You can launch such a group in background\n"
msgstr ""
"把多个要执行的命令编为一组成为一个命令\n"
"你可以在后台启动这样一个组\n"

#: src/commands.cc:120
msgid "alias [<name> [<value>]]"
msgstr ""

#: src/commands.cc:121
msgid ""
"Define or undefine alias <name>. If <value> omitted,\n"
"the alias is undefined, else is takes the value <value>.\n"
"If no argument is given the current aliases are listed.\n"
msgstr ""
"定义或取消别名 <name>。如果 <value> 被忽略，\n"
"则取消别名定义，否则定义其值为 <value>。\n"
"如果没有参数，列出当前的所有别名。\n"

#: src/commands.cc:125
msgid "anon - login anonymously (by default)\n"
msgstr "anon - 匿名登录(默认)\n"

#: src/commands.cc:127
msgid "bookmark [SUBCMD]"
msgstr ""

#: src/commands.cc:128
msgid ""
"bookmark command controls bookmarks\n"
"\n"
"The following subcommands are recognized:\n"
"  add <name> [<loc>] - add current place or given location to bookmarks\n"
"                       and bind to given name\n"
"  del <name>         - remove bookmark with the name\n"
"  edit               - start editor on bookmarks file\n"
"  import <type>      - import foreign bookmarks\n"
"  list               - list bookmarks (default)\n"
msgstr ""
"bookmark 命令控制书签\n"
"\n"
"支持下面的子命令:\n"
"  add <name> [<loc>] - 把当前位置或指定的位置加入书签,并使用给定名字\n"
"  del <name>         - 删除指定名字的书签\n"
"  edit               - 编辑书签文件\n"
"  import <type>      - 导入其他程序的书签\n"
"  list               - 列出书签 (默认)\n"

#: src/commands.cc:137
msgid "cache [SUBCMD]"
msgstr ""

#: src/commands.cc:138
msgid ""
"cache command controls local memory cache\n"
"\n"
"The following subcommands are recognized:\n"
"  stat        - print cache status (default)\n"
"  on|off      - turn on/off caching\n"
"  flush       - flush cache\n"
"  size <lim>  - set memory limit\n"
"  expire <Nx> - set cache expiration time to N seconds (x=s)\n"
"                minutes (x=m) hours (x=h) or days (x=d)\n"
msgstr ""
"cache 命令控制本地的缓存\n"
"\n"
"支持下列子命令:\n"
"  stat        - 显示缓存的状态 (默认)\n"
"  on|ff       - 打开/关闭缓存\n"
"  flush       - 清除缓存\n"
"  size <lim>  - 设置内存限制, -1 表示没有限制\n"
"  expire <Nx> - 设置缓存过期的时间为 N 秒 (x=s)\n"
"                分钟 (x=m) 小时 (x=h) 或天(x=d)\n"

#: src/commands.cc:146
msgid "cat [-b] <files>"
msgstr ""

#: src/commands.cc:147
msgid ""
"cat - output remote files to stdout (can be redirected)\n"
" -b  use binary mode (ascii is the default)\n"
msgstr ""
"cat - 输出远程文件到标准输出(可以重定向)\n"
" -b  使用二进制模式(默认是 ascii)\n"

#: src/commands.cc:149
msgid "cd <rdir>"
msgstr ""

#: src/commands.cc:150
msgid ""
"Change current remote directory to <rdir>. The previous remote directory\n"
"is stored as `-'. You can do `cd -' to change the directory back.\n"
"The previous directory for each site is also stored on disk, so you can\n"
"do `open site; cd -' even after lftp restart.\n"
msgstr ""
"改变当前的远程目录到 <rdir>。先前的远程目录被存为 `-'。你可以用 `cd -'\n"
"回到先前的目录。\n"
"每个站点的先前目录保存在磁盘上，即使在 lftp 重启动后，你也可以用\n"
"`open site; cd -'，回到先前的目录\n"

#: src/commands.cc:154
msgid "chmod [OPTS] mode file..."
msgstr ""

#: src/commands.cc:155
msgid ""
"Change the mode of each FILE to MODE.\n"
"\n"
" -c, --changes        - like verbose but report only when a change is made\n"
" -f, --quiet          - suppress most error messages\n"
" -v, --verbose        - output a diagnostic for every file processed\n"
" -R, --recursive      - change files and directories recursively\n"
"\n"
"MODE can be an octal number or symbolic mode (see chmod(1))\n"
msgstr ""
"将每个 FILE 的模式更改为 MODE。\n"
"\n"
" -c, --changes        - 如同 verbose，但仅在做出更改时才报告\n"
" -f, --quiet          - 禁止大多数错误消息\n"
" -v, --verbose        - 为每个处理的文件输出一个诊断\n"
" -R, --recursive      - 递归地更改文件和目录\n"
"\n"
"MODE 可以是八进制数或符号模式(请参阅 chmod (1))\n"

#: src/commands.cc:164
msgid ""
"Close idle connections. By default only with current server.\n"
" -a  close idle connections with all servers\n"
msgstr ""
"关闭空闲连接。默认情况下仅当前服务器。\n"
" -a  关闭所有服务器的空闲连接\n"

#: src/commands.cc:166
msgid "[re]cls [opts] [path/][pattern]"
msgstr ""

#: src/commands.cc:167
msgid ""
"List remote files. You can redirect output of this command to file\n"
"or via pipe to external command.\n"
"\n"
" -1                   - single-column output\n"
" -a, --all            - show dot files\n"
" -B, --basename       - show basename of files only\n"
"     --block-size=SIZ - use SIZ-byte blocks\n"
" -d, --directory      - list directory entries instead of contents\n"
" -F, --classify       - append indicator (one of /@) to entries\n"
" -h, --human-readable - print sizes in human readable format (e.g., 1K)\n"
"     --si             - likewise, but use powers of 1000 not 1024\n"
" -k, --kilobytes      - like --block-size=1024\n"
" -l, --long           - use a long listing format\n"
" -q, --quiet          - don't show status\n"
" -s, --size           - print size of each file\n"
"     --filesize       - if printing size, only print size for files\n"
" -i, --nocase         - case-insensitive pattern matching\n"
" -I, --sortnocase     - sort names case-insensitively\n"
" -D, --dirsfirst      - list directories first\n"
"     --sort=OPT       - \"name\", \"size\", \"date\"\n"
" -S                   - sort by file size\n"
" --user, --group, --perms, --date, --linkcount, --links\n"
"                      - show individual fields\n"
" --time-style=STYLE   - use specified time format\n"
"\n"
"By default, cls output is cached, to see new listing use `recls' or\n"
"`cache flush'.\n"
"\n"
"The variables cls-default and cls-completion-default can be used to\n"
"specify defaults for cls listings and completion listings, respectively.\n"
"For example, to make completion listings show file sizes, set\n"
"cls-completion-default to \"-s\".\n"
"\n"
"Tips: Use --filesize with -D to pack the listing better.  If you don't\n"
"always want to see file sizes, --filesize in cls-default will affect the\n"
"-s flag on the commandline as well.  Add `-i' to cls-completion-default\n"
"to make filename completion case-insensitive.\n"
msgstr ""
"列出远程文件。您可以将此命令的输出重定向到文件\n"
"或通过管道连接到外部命令。\n"
"\n"
" -1                   - 单列输出\n"
" -a, --all            - 显示点文件\n"
" -B, --basename       - 仅显示文件的基本名称\n"
"     --block-size=SIZ - 使用 SIZ 大小的字节块\n"
" -d, --directory      - 列出目录条目而不是内容\n"
" -F, --classify       - 将指示符(/@ 之一)附加到条目\n"
" -h, --human-readable - 打印便于可读格式的大小(例如1K)\n"
"     --si             - 同上，但使用1000而不是1024的幂\n"
" -k, --kilobytes      - 如同 --block-size=1024\n"
" -l, --long           - 使用长列表格式\n"
" -q, --quiet          - 不显示状态\n"
" -s, --size           - 打印每个文件的大小\n"
"     --filesize       - 如果打印大小，只打印文件大小\n"
" -i, --nocase         - 不区分大小写的模式匹配\n"
" -I, --sortnocase     - 不区分大小写地按名称排序\n"
" -D, --dirsfirst      - 首先列出目录\n"
"     --sort=OPT       - \"name\"，\"size\"，\"date\"\n"
" -S                   - 按文件大小排序\n"
" --user, --group, --perms, --date, --linkcount, --links\n"
"                      - 显示单个字段\n"
" --time-style=STYLE   - 使用指定的时间格式\n"
"\n"
"默认情况下，cls 的输出被缓存，要查看新的列表使用 `recls' 或\n"
"`cache flush'。\n"
"\n"
"可以使用变量 cls-default 和 cls-completion-default \n"
"分别指定 cls 列表和完成列表的默认值。\n"
"例如，要使用完成列表显示文件大小，请设置 \n"
"cls-completion-default 为“-s”。\n"
"\n"
"提示：使用 --filesize 加 -D 更好地打包列表。如果你不是\n"
"总希望看到文件大小，cls-default 中的 --filesize 也会\n"
"影响到命令行上的 -s 标志。将 `-i' 添加到 cls-completion-default\n"
" 中使文件名完成不区分大小写。\n"

#: src/commands.cc:217
msgid "debug [OPTS] [<level>|off]"
msgstr ""

#: src/commands.cc:218
msgid ""
"Set debug level to given value or turn debug off completely.\n"
" -o <file>  redirect debug output to the file\n"
" -c  show message context\n"
" -p  show PID\n"
" -t  show timestamps\n"
msgstr ""
"将调试级别设置为给定值或完全关闭调试。\n"
" -o <file>  将调试输出重定向到文件\n"
" -c  显示消息上下文\n"
" -p  显示 PID\n"
" -t  显示时间戳\n"

#: src/commands.cc:223
msgid "du [options] <dirs>"
msgstr ""

#: src/commands.cc:224
msgid ""
"Summarize disk usage.\n"
" -a, --all             write counts for all files, not just directories\n"
"     --block-size=SIZ  use SIZ-byte blocks\n"
" -b, --bytes           print size in bytes\n"
" -c, --total           produce a grand total\n"
" -d, --max-depth=N     print the total for a directory (or file, with --"
"all)\n"
"                       only if it is N or fewer levels below the command\n"
"                       line argument;  --max-depth=0 is the same as\n"
"                       --summarize\n"
" -F, --files           print number of files instead of sizes\n"
" -h, --human-readable  print sizes in human readable format (e.g., 1K 234M "
"2G)\n"
" -H, --si              likewise, but use powers of 1000 not 1024\n"
" -k, --kilobytes       like --block-size=1024\n"
" -m, --megabytes       like --block-size=1048576\n"
" -S, --separate-dirs   do not include size of subdirectories\n"
" -s, --summarize       display only a total for each argument\n"
"     --exclude=PAT     exclude files that match PAT\n"
msgstr ""
"总结磁盘使用情况。\n"
" -a, --all             所有文件都写入计数，而不仅仅是目录\n"
"     --block-size=SIZ  使用 SIZ 字节大小的字节块\n"
" -b, --bytes           以字节为单位打印大小\n"
" -c, --total           生成一个总计\n"
" -d, --max-depth=N     仅打印等于或低于命令行参数 N 层级的\n"
"                       目录(或文件，带有 --all)的总数\n"
"                       --max-depth=0 等于 --summarize。\n"
" -F, --files           打印文件数量而不是大小\n"
" -h, --human-readable  以便于可读格式打印大小(例如，1K 234M 2G)\n"
" -H, --si              同上，但使用1000而不是1024的幂\n"
" -k, --kilobytes       等同 --block-size=1024\n"
" -m, --megabytes       等同 --block-size=1048576\n"
" -S, --separate-dirs   不包括子目录的大小\n"
" -s, --summarize       对于每个参数仅显示一个总数\n"
"     --exclude=PAT     排除与 PAT 匹配的文件\n"

#: src/commands.cc:242
msgid "edit [OPTS] <file>"
msgstr ""

#: src/commands.cc:243
msgid ""
"Retrieve remote file to a temporary location, run a local editor on it\n"
"and upload the file back if changed.\n"
" -k  keep the temporary file\n"
" -o <temp>  explicit temporary file location\n"
msgstr ""
"将远程文件取回到临时位置，在其上运行本地编辑器\n"
"如果更改，则将文件上传回去。\n"
" -k  保留临时文件\n"
"-o <temp>  显式指定临时文件位置\n"

#: src/commands.cc:248
msgid "exit [<code>|bg]"
msgstr ""

#: src/commands.cc:249
msgid ""
"exit - exit from lftp or move to background if jobs are active\n"
"\n"
"If no jobs active, the code is passed to operating system as lftp\n"
"termination status. If omitted, exit code of last command is used.\n"
"`bg' forces moving to background if cmd:move-background is false.\n"
msgstr ""
"exit - 从 lftp 中退出，如果有任务活动则进入后台\n"
"\n"
"如果没有活动任务, code 会被传给操作系统作为 lftp 的退出状态。\n"
"如果省略，则使用上个命令的退出代码。\n"
"如果 cmd:move-background 为假，则 `bg' 强制进入后台。\n"

#: src/commands.cc:255
msgid ""
"Usage: find [OPTS] [directory]\n"
"Print contents of specified directory or current directory recursively.\n"
"Directories in the list are marked with trailing slash.\n"
"You can redirect output of this command.\n"
" -d, --maxdepth=LEVELS  Descend at most LEVELS of directories.\n"
msgstr ""
"用法：find [OPTS] [directory]\n"
"递归地打印指定目录或当前目录的内容。\n"
"列表中的目录标有尾部斜线。\n"
"您可以重定向该命令的输出。\n"
" -d, --maxdepth=LEVELS  最多 LEVELS 层级的目录。\n"

#: src/commands.cc:260
msgid "get [OPTS] <rfile> [-o <lfile>]"
msgstr ""

#: src/commands.cc:261
msgid ""
"Retrieve remote file <rfile> and store it to local file <lfile>.\n"
" -o <lfile> specifies local file name (default - basename of rfile)\n"
" -c  continue, resume transfer\n"
" -E  delete remote files after successful transfer\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"下载远程文件 <rfile> 并保存为本地文件 <lfile>。\n"
" -o <lfile> 指定本地文件名 (默认值 - rfile 的名字)\n"
" -c 继续，继续传输\n"
" -E 传输成功后删除远程文件\n"
" -a 使用 ascii 模式(二进制是默认模式)\n"
" -O <base> 指定应放置文件的基础目录或 URL\n"

#: src/commands.cc:268
msgid "glob [OPTS] <cmd> <args>"
msgstr ""

#: src/commands.cc:270
msgid ""
"Expand wildcards and run specified command.\n"
"Options can be used to expand wildcards to list of files, directories,\n"
"or both types. Type selection is not very reliable and depends on server.\n"
"If entry type cannot be determined, it will be included in the list.\n"
" -f  plain files (default)\n"
" -d  directories\n"
" -a  all types\n"
" --exist      return zero exit code when the patterns expand to non-empty "
"list\n"
" --not-exist  return zero exit code when the patterns expand to an empty "
"list\n"
msgstr ""
"扩展通配符并运行指定的命令。\n"
"选项可用于将通配符扩展到文件列表，目录，\n"
"或两种类型。类型选择不是很可靠，取决于服务器。\n"
"如果输入类型不能确定，它将包含在列表中。\n"
" -f  纯文件(默认)\n"
" -d  目录\n"
" -a  所有类型\n"
" --exist      当模式扩展到非空列表时，返回零退出代码\n"
" --not-exist  当模式扩展到空列表时，返回零退出代码\n"

#: src/commands.cc:279
msgid "help [<cmd>]"
msgstr ""

#: src/commands.cc:280
msgid "Print help for command <cmd>, or list of available commands\n"
msgstr "显示命令 <cmd> 的帮助信息, 或者列出可用的命令\n"

#: src/commands.cc:282
msgid ""
"List running jobs. -v means verbose, several -v can be specified.\n"
"If <job_no> is specified, only list a job with that number.\n"
msgstr ""
"列出运行的任务。 -v 表示更多信息，可以指定多个 -v。\n"
"如果指定 <job_no>，则只列出具有该号码的任务。\n"

#: src/commands.cc:284
msgid "kill all|<job_no>"
msgstr ""

#: src/commands.cc:285
msgid "Delete specified job with <job_no> or all jobs\n"
msgstr "删除指定的任务号为 <job_no> 的任务, 或者所有的任务\n"

#: src/commands.cc:286
msgid "lcd <ldir>"
msgstr ""

#: src/commands.cc:287
msgid ""
"Change current local directory to <ldir>. The previous local directory\n"
"is stored as `-'. You can do `lcd -' to change the directory back.\n"
msgstr ""
"改变当前本地目录到 <ldir>。先前的本地目录保存为 `-'。\n"
"你可以使用 `lcd -' 切换回先前的目录。\n"

#: src/commands.cc:289
msgid "lftp [OPTS] <site>"
msgstr ""

#: src/commands.cc:290
msgid ""
"`lftp' is the first command executed by lftp after rc files\n"
" -f <file>           execute commands from the file and exit\n"
" -c <cmd>            execute the commands and exit\n"
" --norc              don't execute rc files from the home directory\n"
" --help              print this help and exit\n"
" --version           print lftp version and exit\n"
"Other options are the same as in `open' command:\n"
" -e <cmd>            execute the command just after selecting\n"
" -u <user>[,<pass>]  use the user/password for authentication\n"
" -p <port>           use the port for connection\n"
" -s <slot>           assign the connection to this slot\n"
" -d                  switch on debugging mode\n"
" <site>              host name, URL or bookmark name\n"
msgstr ""
"`lftp' 是在 rc 文件执行后 lftp 执行的第一个命令\n"
" -f <file>           执行文件中的命令后退出\n"
" -c <cmd>            执行命令后退出\n"
" --help              显示帮助信息后退出\n"
" --version           显示 lftp 版本后退出\n"
" 其他的选项同 `open' 命令\n"
" -e <cmd>            在选择后执行命令\n"
" -u <user>[,<pass>]  使用指定的用户名/口令进行验证\n"
" -p <port>           连接指定的端口\n"
" <site>              主机名, URL 或书签的名字\n"

#: src/commands.cc:303
msgid "ln [-s] <file1> <file2>"
msgstr ""

#: src/commands.cc:304
msgid "Link <file1> to <file2>\n"
msgstr "链接 <file1> 到 <file2>\n"

#: src/commands.cc:308
msgid "ls [<args>]"
msgstr ""

#: src/commands.cc:309
msgid ""
"List remote files. You can redirect output of this command to file\n"
"or via pipe to external command.\n"
"By default, ls output is cached, to see new listing use `rels' or\n"
"`cache flush'.\n"
"See also `help cls'.\n"
msgstr ""
"列出远程文件。您可以将此命令的输出重定向到文件\n"
"或通过管道连接到外部命令。\n"
"默认情况下，ls 输出被缓存，要查看新列表使用 `rels' 或\n"
" `cache flush'。\n"
"另见 `help cls'。\n"

#: src/commands.cc:314
msgid "mget [OPTS] <files>"
msgstr ""

#: src/commands.cc:315
msgid ""
"Gets selected files with expanded wildcards\n"
" -c  continue, resume transfer\n"
" -d  create directories the same as in file names and get the\n"
"     files into them instead of current directory\n"
" -E  delete remote files after successful transfer\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"使用展开的通配符来下载选中的文件\n"
" -c  继续，续传\n"
" -d  建立文件名中包含的目录，把文件下载到这些目录中，而不是\n"
"     下载到当前目录\n"
" -E  传输成功后删除远程文件\n"
" -a  使用 ascii 模式(二进制是默认模式)\n"
" -O <base> 指定应放置文件的基础目录或 URL\n"

#: src/commands.cc:322
msgid "mirror [OPTS] [remote [local]]"
msgstr ""

#: src/commands.cc:323
msgid "mkdir [OPTS] <dirs>"
msgstr ""

#: src/commands.cc:324
msgid ""
"Make remote directories\n"
" -p  make all levels of path\n"
" -f  be quiet, suppress messages\n"
msgstr ""
"建立远程目录\n"
" -p  建立各级路径\n"
" -f  静默，不显示信息\n"

#: src/commands.cc:327
msgid "module name [args]"
msgstr ""

#: src/commands.cc:328
msgid ""
"Load module (shared object). The module should contain function\n"
"   void module_init(int argc,const char *const *argv)\n"
"If name contains a slash, then the module is searched in current\n"
"directory, otherwise in directories specified by setting module:path.\n"
msgstr ""
"装载模块 (共享对象). 模块应该包含函数\n"
"   void module_init(int argc,const char *const *argv)\n"
"如果名字中包含 `/'，那么模块在当前路径中寻找, 否则在\n"
"设置 module:path 中。\n"

#: src/commands.cc:332
msgid "more <files>"
msgstr ""

#: src/commands.cc:333
msgid "Same as `cat <files> | more'. if PAGER is set, it is used as filter\n"
msgstr "和 `cat <files> | more' 相同。如果设置了 PAGER, 它被用来做过滤\n"

#: src/commands.cc:334
msgid "mput [OPTS] <files>"
msgstr ""

#: src/commands.cc:335
msgid ""
"Upload files with wildcard expansion\n"
" -c  continue, reput\n"
" -d  create directories the same as in file names and put the\n"
"     files into them instead of current directory\n"
" -E  delete local files after successful transfer (dangerous)\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"使用展开的通配符来上传选定的文件\n"
" -c  继续, 重传\n"
" -d  建立文件名中包含的目录，把文件下载到这些目录中，而不是\n"
"     下载到当前目录\n"
" -E  传输成功后删除本地文件(危险)\n"
" -a  使用 ascii 模式(二进制是默认模式)\n"
" -O <base> 指定应放置文件的基础目录或 URL\n"

#: src/commands.cc:342
msgid "mrm <files>"
msgstr ""

#: src/commands.cc:343
msgid "Removes specified files with wildcard expansion\n"
msgstr "使用通配符展开来删除指定的文件\n"

#: src/commands.cc:344
msgid "mv <file1> <file2>"
msgstr ""

#: src/commands.cc:345
msgid "Rename <file1> to <file2>\n"
msgstr "把文件 <file1> 更名为 <file2>\n"

#: src/commands.cc:346
msgid "mmv [OPTS] <files> <target-dir>"
msgstr ""

#: src/commands.cc:347
msgid ""
"Move <files> to <target-directory> with wildcard expansion\n"
" -O <dir>  specifies the target directory (alternative way)\n"
msgstr ""
"使用通配符扩展将 <files> 移动到 <target-directory>\n"
" -O <dir>  指定目标目录(备选路径)\n"

#: src/commands.cc:349
msgid "[re]nlist [<args>]"
msgstr ""

#: src/commands.cc:350
msgid ""
"List remote file names.\n"
"By default, nlist output is cached, to see new listing use `renlist' or\n"
"`cache flush'.\n"
msgstr ""
"列出远程文件。\n"
"默认, nlist 的输出是缓存的，如果需要看到新的列表，使用 `renlist' 或\n"
"`cache flush'。\n"

#: src/commands.cc:353
msgid "open [OPTS] <site>"
msgstr ""

#: src/commands.cc:354
msgid ""
"Select a server, URL or bookmark\n"
" -e <cmd>            execute the command just after selecting\n"
" -u <user>[,<pass>]  use the user/password for authentication\n"
" -p <port>           use the port for connection\n"
" -s <slot>           assign the connection to this slot\n"
" -d                  switch on debugging mode\n"
" <site>              host name, URL or bookmark name\n"
msgstr ""
"选择一个服务器, URL 或书签\n"
" -e <cmd>            在选中后执行命令\n"
" -u <user>[,<pass>]  使用指定的用户名/口令进行验证\n"
" -p <port>           连接指定端口\n"
" -s <slot>           将连接分配给指定槽\n"
" -d                  打开调试模式\n"
" <site>              主机名，URL 或书签名\n"

#: src/commands.cc:361
msgid "pget [OPTS] <rfile> [-o <lfile>]"
msgstr ""

#: src/commands.cc:362
msgid ""
"Gets the specified file using several connections. This can speed up "
"transfer,\n"
"but loads the net heavily impacting other users. Use only if you really\n"
"have to transfer the file ASAP.\n"
"\n"
"Options:\n"
" -c  continue transfer. Requires <lfile>.lftp-pget-status file.\n"
" -n <maxconn>  set maximum number of connections (default is is taken from\n"
"     pget:default-n setting)\n"
" -O <base> specifies base directory where files should be placed\n"
msgstr ""
"使用多个连接下载指定的文件。这可以加快传输的速度，\n"
"但是给网络增加了负担，显著地影响其他用户。只在你真正\n"
"需要尽快下载文件的时候使用。\n"
"\n"
"选项:\n"
" -c  继续传输。需要 <lfile>。lftp-pget-status 文件\n"
" -n <maxconn>  设置最大连接数 (默认值取自\n"
"               pget:default-n 设置)\n"
" -O <base> 指定放置文件的基础目录\n"

#: src/commands.cc:370
msgid "put [OPTS] <lfile> [-o <rfile>]"
msgstr ""

#: src/commands.cc:371
msgid ""
"Upload <lfile> with remote name <rfile>.\n"
" -o <rfile> specifies remote file name (default - basename of lfile)\n"
" -c  continue, reput\n"
"     it requires permission to overwrite remote files\n"
" -E  delete local files after successful transfer (dangerous)\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"上传 <lfile> 成为远程文件 <rfile>。\n"
" -o <rfile> 指定远程文件名 (默认值 - lfile 的名字)\n"
" -c 继续，重传\n"
"    这需要有覆盖远程文件的权限\n"
" -E  传输成功后删除本地文件(危险)\n"
" -a  使用 ascii 模式(二进制是默认模式)\n"
" -O <base> 指定应放置文件的基础目录或 URL\n"

#: src/commands.cc:379
msgid ""
"Print current remote URL.\n"
" -p  show password\n"
msgstr ""
"打印当前的远程 URL。\n"
" -p  显示密码\n"

#: src/commands.cc:381
msgid "queue [OPTS] [<cmd>]"
msgstr ""

#: src/commands.cc:382
msgid ""
"\n"
"       queue [-n num] <command>\n"
"\n"
"Add the command to queue for current site. Each site has its own command\n"
"queue. `-n' adds the command before the given item in the queue. It is\n"
"possible to queue up a running job by using command `queue wait <jobno>'.\n"
"\n"
"       queue --delete|-d [index or wildcard expression]\n"
"\n"
"Delete one or more items from the queue. If no argument is given, the last\n"
"entry in the queue is deleted.\n"
"\n"
"       queue --move|-m <index or wildcard expression> [index]\n"
"\n"
"Move the given items before the given queue index, or to the end if no\n"
"destination is given.\n"
"\n"
"Options:\n"
" -q                  Be quiet.\n"
" -v                  Be verbose.\n"
" -Q                  Output in a format that can be used to re-queue.\n"
"                     Useful with --delete.\n"
msgstr ""
"\n"
"       queue [-n num] <command>\n"
"\n"
"将命令添加到当前站点队列中。每个站点都有自己的命令\n"
"队列.`-n' 在队列中的给定项之前添加命令。\n"
"使用命令 `queue wait <jobno>' 可以对正在运行的任务进行排队。\n"
"\n"
"       queue --delete|-d [index or wildcard expression]\n"
"\n"
"从队列中删除一个或多个项目。如果没有给定参数，则队列中的\n"
"最后一个条目将被删除。\n"
"\n"
"       queue --move|-m <index or wildcard expression> [index]\n"
"\n"
"移动给定的项目到给定的队列索引之前，或者在没有\n"
"给定目的地时到末尾。\n"
"\n"
"选项：\n"
" -q                  静默。\n"
" -v                  详细。\n"
" -Q                  以可用于重新排队的格式输出。\n"
"                     用于 --delete。\n"

#: src/commands.cc:403
msgid "quote <cmd>"
msgstr ""

#: src/commands.cc:404
msgid ""
"Send the command uninterpreted. Use with caution - it can lead to\n"
"unknown remote state and thus will cause reconnect. You cannot\n"
"be sure that any change of remote state because of quoted command\n"
"is solid - it can be reset by reconnect at any time.\n"
msgstr ""
"发出未经本地解释的命令。小心使用 - 它可能会导致远程的\n"
"未知状态，这样会引起重新连接。你不能保证 quote 命令\n"
"引起的远程状态的改变是一致的 - 可以通过重新连接来复位。\n"

#: src/commands.cc:409
msgid ""
"recls [<args>]\n"
"Same as `cls', but don't look in cache\n"
msgstr ""
"recls [<args>]\n"
"与 `cls' 相同，但不在缓存中查找\n"

#: src/commands.cc:412
msgid ""
"Usage: reget [OPTS] <rfile> [-o <lfile>]\n"
"Same as `get -c'\n"
msgstr ""
"用法：reget [OPTS] <rfile> [-o <lfile>]\n"
"与 `get -c' 相同\n"

#: src/commands.cc:415
msgid ""
"Usage: rels [<args>]\n"
"Same as `ls', but don't look in cache\n"
msgstr ""
"用法：rels [<args>]\n"
"与 `ls' 相同，但不在缓存中查找\n"

#: src/commands.cc:418
msgid ""
"Usage: renlist [<args>]\n"
"Same as `nlist', but don't look in cache\n"
msgstr ""
"用法：renlist [<args>]\n"
"与 `nlist' 相同，但不在缓存中查找\n"

#: src/commands.cc:420
msgid "repeat [OPTS] [delay] [command]"
msgstr ""

#: src/commands.cc:422
msgid ""
"Usage: reput <lfile> [-o <rfile>]\n"
"Same as `put -c'\n"
msgstr ""
"用法：reput <lfile> [-o <rfile>]\n"
"与 `put -c' 相同\n"

#: src/commands.cc:424
msgid "rm [-r] [-f] <files>"
msgstr ""

#: src/commands.cc:425
msgid ""
"Remove remote files\n"
" -r  recursive directory removal, be careful\n"
" -f  work quietly\n"
msgstr ""
"删除远程文件\n"
" -r  递归删除目录，请谨慎使用\n"
" -f  静默地工作\n"

#: src/commands.cc:428
msgid "rmdir [-f] <dirs>"
msgstr ""

#: src/commands.cc:429
msgid "Remove remote directories\n"
msgstr "删除远程目录\n"

#: src/commands.cc:430
msgid "scache [<session_no>]"
msgstr ""

#: src/commands.cc:431
msgid "List cached sessions or switch to specified session number\n"
msgstr "列出缓存的会话，或者切换到指定的会话\n"

#: src/commands.cc:432
msgid "set [OPT] [<var> [<val>]]"
msgstr ""

#: src/commands.cc:433
msgid ""
"Set variable to given value. If the value is omitted, unset the variable.\n"
"Variable name has format ``name/closure'', where closure can specify\n"
"exact application of the setting. See lftp(1) for details.\n"
"If set is called with no variable then only altered settings are listed.\n"
"It can be changed by options:\n"
" -a  list all settings, including default values\n"
" -d  list only default values, not necessary current ones\n"
msgstr ""
"设置变量为指定的值。如果值忽略，取消变量。\n"
"变量名的格式为 ``名字/约束''，约束可以指定设置的\n"
"应用范围。请查看 lftp(1) 获得细节。\n"
"如果不带变量使用 set，只有改动过的设置才被列出。\n"
"这可以通过下列选项来改变:\n"
" -a  列出所有的设置，包括默认设置\n"
" -d  只列出默认值，不一定是正在用的\n"

#: src/commands.cc:441
msgid "site <site-cmd>"
msgstr "site <site_cmd>"

#: src/commands.cc:442
msgid ""
"Execute site command <site_cmd> and output the result\n"
"You can redirect its output\n"
msgstr ""
"执行 site 命令 <site_cmd> 并输出结果\n"
"你可以重定向它的输出\n"

#: src/commands.cc:446
msgid ""
"Usage: slot [<label>]\n"
"List assigned slots.\n"
"If <label> is specified, switch to the slot named <label>.\n"
msgstr ""
"用法：slot [<label>]\n"
"列出分配的插槽。\n"
"如果指定 <label>，则切换到名为 <label> 的插槽。\n"

#: src/commands.cc:449
msgid "source <file>"
msgstr ""

#: src/commands.cc:450
msgid "Execute commands recorded in file <file>\n"
msgstr "执行文件 <file> 中的命令\n"

#: src/commands.cc:452
msgid "torrent [OPTS] <file|URL>..."
msgstr ""

#: src/commands.cc:453
msgid "user <user|URL> [<pass>]"
msgstr ""

#: src/commands.cc:454
msgid ""
"Use specified info for remote login. If you specify URL, the password\n"
"will be cached for future usage.\n"
msgstr ""
"使用指定的信息进行远程登录。如果您指定 URL，密码\n"
"将被缓存以备将来使用。\n"

#: src/commands.cc:457
msgid "Shows lftp version\n"
msgstr "显示 lftp 的版本\n"

#: src/commands.cc:458
msgid "wait [<jobno>]"
msgstr ""

#: src/commands.cc:459
msgid ""
"Wait for specified job to terminate. If jobno is omitted, wait\n"
"for last backgrounded job.\n"
msgstr ""
"等待指定的任务终止。如果省略 jobno，等待\n"
"最后的后台任务。\n"

#: src/commands.cc:461
msgid "zcat <files>"
msgstr ""

#: src/commands.cc:462
msgid "Same as cat, but filter each file through zcat\n"
msgstr "同 cat，但使用 zcat 过滤每个文件\n"

#: src/commands.cc:463
msgid "zmore <files>"
msgstr ""

#: src/commands.cc:464
msgid "Same as more, but filter each file through zcat\n"
msgstr "同 more，但使用 zcat 过滤每个文件\n"

#: src/commands.cc:466
msgid "Same as cat, but filter each file through bzcat\n"
msgstr "同 cat，但使用 bzcat 过滤每个文件\n"

#: src/commands.cc:468
msgid "Same as more, but filter each file through bzcat\n"
msgstr "同 more，但使用 bzcat 过滤每个文件\n"

#: src/commands.cc:524
#, c-format
msgid "Usage: %s local-dir\n"
msgstr "用法：%s local-dir\n"

#: src/commands.cc:560
#, c-format
msgid "lcd ok, local cwd=%s\n"
msgstr "lcd 成功，本地目录=%s\n"

#: src/commands.cc:576
#, c-format
msgid "Usage: cd remote-dir\n"
msgstr "用法：cd remote-dir\n"

#: src/commands.cc:588
#, c-format
msgid "%s: no old directory for this site\n"
msgstr "%s：这个站点没有保存的旧目录\n"

#: src/commands.cc:677
#, c-format
msgid "Usage: %s [<exit_code>]\n"
msgstr "用法：%s [<exit_code>]\n"

#: src/commands.cc:686
msgid ""
"There are running jobs and `cmd:move-background' is not set.\n"
"Use `exit bg' to force moving to background or `kill all' to terminate "
"jobs.\n"
msgstr ""
"有正在运行的任务，并且未设置 `cmd:move-background'。\n"
"使用 `exit bg' 强制移动到后台或 `kill all' 来终止任务。\n"

#: src/commands.cc:703
msgid ""
"\n"
"lftp now tricks the shell to move it to background process group.\n"
"lftp continues to run in the background despite the `Stopped' message.\n"
"lftp will automatically terminate when all jobs are finished.\n"
"Use `fg' shell command to return to lftp if it is still running.\n"
msgstr ""
"\n"
"lftp 现在会欺骗 shell 将其移动到后台进程组。\n"
"尽管有 `Stopped' 消息，lftp 仍会继续在后台运行。\n"
"所有任务完成后，lftp 将自动终止。\n"
"如果它仍在运行，请使用 `fg' shell 命令返回到 lftp。\n"

#: src/commands.cc:837 src/commands.cc:3616
#, c-format
msgid "Try `%s --help' for more information\n"
msgstr "请用 `%s --help' 获取更多的信息\n"

#: src/commands.cc:856
#, c-format
msgid "%s: -c, -f, -v, -h conflict with other `open' options and arguments\n"
msgstr "%s：-c，-f，-v，-h 与其他 `open' 选项和参数冲突\n"

#: src/commands.cc:956
#, c-format
msgid "Usage: %s [-e cmd] [-p port] [-u user[,pass]] <host|url>\n"
msgstr "用法：%s [-e cmd] [-p port] [-u user[,pass]] <host|url>\n"

#: src/commands.cc:1046 src/commands.cc:2276 src/DummyProto.cc:65
#: src/MirrorJob.cc:2172 src/MirrorJob.cc:2194
msgid " - not supported protocol"
msgstr " - 不支持的协议"

#: src/commands.cc:1078 src/commands.cc:2260
msgid "Password: "
msgstr "密码: "

#: src/commands.cc:1080
#, c-format
msgid "%s: GetPass() failed -- assume anonymous login\n"
msgstr "%s：GetPass() 失败 -- 使用匿名登录\n"

#: src/commands.cc:1191 src/commands.cc:1284 src/commands.cc:1660
#: src/commands.cc:1691 src/commands.cc:1829 src/commands.cc:1914
#: src/commands.cc:2187 src/commands.cc:2381 src/commands.cc:2543
#: src/commands.cc:2588 src/commands.cc:2616 src/commands.cc:2623
#: src/commands.cc:2930 src/commands.cc:2957 src/commands.cc:2964
#: src/commands.cc:3293 src/lftp.cc:267 src/MirrorJob.cc:2136
#: src/SleepJob.cc:144 src/SleepJob.cc:200 src/Torrent.cc:4169
#, c-format
msgid "Try `help %s' for more information.\n"
msgstr "请用 `help %s' 或得更多的信息。\n"

#: src/commands.cc:1201
#, c-format
msgid "Usage: %s [OPTS] command args...\n"
msgstr "用法：%s [OPTS] command args...\n"

#: src/commands.cc:1254
#, c-format
msgid "%s: -n: positive number expected. "
msgstr "%s：-n：预期的正数。 "

#: src/commands.cc:1306
#, c-format
msgid "Created a stopped queue.\n"
msgstr "创建了一个停止队列。\n"

#: src/commands.cc:1349 src/commands.cc:1377
#, c-format
msgid "%s: No queue is active.\n"
msgstr "%s：没有队列处于活动状态。\n"

#: src/commands.cc:1369
#, c-format
msgid "%s: -m: Number expected as second argument. "
msgstr "%s：-m：预期作为第二个参数的数字。 "

#: src/commands.cc:1421
#, c-format
msgid "Usage: %s <cmd>\n"
msgstr "用法：%s <cmd>\n"

#: src/commands.cc:1527
msgid "invalid argument for `--sort'"
msgstr "无效的参数用于 `--sort'"

#: src/commands.cc:1557
msgid "invalid block size"
msgstr "无效的块大小"

#: src/commands.cc:1700
#, c-format
msgid "Usage: %s [OPTS] files...\n"
msgstr "用法：%s [OPTS] files...\n"

#: src/commands.cc:1785 src/commands.cc:1814
#, c-format
msgid "%s: %s: Number expected. "
msgstr "%s：%s：预期数量。 "

#: src/commands.cc:1834
#, c-format
msgid "%s: --continue conflicts with --remove-target.\n"
msgstr "%s：--continue 与 --remove-target 冲突。\n"

#: src/commands.cc:1844 src/commands.cc:1920
#, c-format
msgid "File name missed. "
msgstr "缺少文件名。 "

#: src/commands.cc:1989
#, c-format
msgid "Usage: %s %s[-f] files...\n"
msgstr "用法：%s %s[-f] files...\n"

#: src/commands.cc:2032
#, c-format
msgid "Usage: %s [-e] <file|command>\n"
msgstr "用法：%s [-e] <file|command>\n"

#: src/commands.cc:2079
#, c-format
msgid "Usage: %s [-v] [-v] ...\n"
msgstr "用法：%s [-v] [-v] ...\n"

#: src/commands.cc:2093 src/commands.cc:2347 src/commands.cc:2469
#: src/commands.cc:2685 src/commands.cc:3101 src/commands.cc:3182
#: src/lftp.cc:279
#, c-format
msgid "%s: %s - not a number\n"
msgstr "%s：%s - 不是一个数字\n"

#: src/commands.cc:2100 src/commands.cc:2326 src/commands.cc:2356
#: src/commands.cc:2487
#, c-format
msgid "%s: %d - no such job\n"
msgstr "%s：%d - 没有这个任务\n"

#: src/commands.cc:2135
#, c-format
msgid "Usage: %s [-p]\n"
msgstr "用法：%s [-p]\n"

#: src/commands.cc:2227
#, c-format
msgid "debug level is %d, output goes to %s\n"
msgstr "调试级别为 %d，输出到 %s\n"

#: src/commands.cc:2230
#, c-format
msgid "debug is off\n"
msgstr "调试功能关闭\n"

#: src/commands.cc:2241
#, c-format
msgid "Usage: %s <user|URL> [<pass>]\n"
msgstr "用法：%s <user|URL> [<pass>]\n"

#: src/commands.cc:2316 src/commands.cc:2479
#, c-format
msgid "%s: no current job\n"
msgstr "%s：没有当前任务\n"

#: src/commands.cc:2328
#, c-format
msgid "Usage: %s <jobno> ... | all\n"
msgstr "用法：%s <jobno> ... | all\n"

#: src/commands.cc:2409
#, c-format
msgid "%s: %s. Use `set -a' to look at all variables.\n"
msgstr "%s：%s。使用 `set -a' 查看所有的变量。\n"

#: src/commands.cc:2453
#, c-format
msgid "Usage: %s [<jobno>]\n"
msgstr "用法：%s [<jobno>]\n"

#: src/commands.cc:2492
#, c-format
msgid "%s: some other job waits for job %d\n"
msgstr "%s：某个任务在等待任务 %d 结束\n"

#: src/commands.cc:2497
#, c-format
msgid "%s: wait loop detected\n"
msgstr "%s：检测到等待循环\n"

#: src/commands.cc:2553
#, c-format
msgid "Usage: %s [OPTS] <files> <target-dir>\n"
msgstr "用法：%s [OPTS] <files> <target-dir>\n"

#: src/commands.cc:2615 src/commands.cc:2956
#, c-format
msgid "Invalid command. "
msgstr "无效的命令。 "

#: src/commands.cc:2622 src/commands.cc:2963
#, c-format
msgid "Ambiguous command. "
msgstr "不明确的命令。 "

#: src/commands.cc:2641
#, c-format
msgid "%s: Operand missed for size\n"
msgstr "%s：size 缺少操作数\n"

#: src/commands.cc:2658
#, c-format
msgid "%s: Operand missed for `expire'\n"
msgstr "%s：`expire' 缺少操作数\n"

#: src/commands.cc:2691
#, c-format
msgid ""
"%s: %s - no such cached session. Use `scache' to look at session list.\n"
msgstr "%s：%s - 没有这个缓存的会话。使用 `scache' 列出会话列表。\n"

#: src/commands.cc:2715
#, c-format
msgid "Sorry, no help for %s\n"
msgstr "抱歉，没有针对 %s 的帮助信息\n"

#: src/commands.cc:2720
#, c-format
msgid "%s is a built-in alias for %s\n"
msgstr "%s 是 %s 的一个内建别名\n"

#: src/commands.cc:2725
#, c-format
msgid "Usage: %s\n"
msgstr "用法：%s\n"

#: src/commands.cc:2733
#, c-format
msgid "%s is an alias to `%s'\n"
msgstr "%s 是 `%s' 的别名\n"

#: src/commands.cc:2737
#, c-format
msgid "No such command `%s'. Use `help' to see available commands.\n"
msgstr "没有 `%s' 这个命令。请用 `help' 列出可用的命令。\n"

#: src/commands.cc:2739
#, c-format
msgid "Ambiguous command `%s'. Use `help' to see available commands.\n"
msgstr "不确切的命令 `%s'。请用 `help' 列出可用的命令。\n"

#: src/commands.cc:2805
#, c-format
msgid "LFTP | Version %s | Copyright (c) 1996-%d Alexander V. Lukyanov\n"
msgstr "LFTP | 版本 %s | 版权 (c) 1996-%d Alexander V. Lukyanov\n"

#: src/commands.cc:2808
#, c-format
msgid ""
"LFTP is free software: you can redistribute it and/or modify\n"
"it under the terms of the GNU General Public License as published by\n"
"the Free Software Foundation, either version 3 of the License, or\n"
"(at your option) any later version.\n"
"\n"
"This program is distributed in the hope that it will be useful,\n"
"but WITHOUT ANY WARRANTY; without even the implied warranty of\n"
"MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n"
"GNU General Public License for more details.\n"
"\n"
"You should have received a copy of the GNU General Public License\n"
"along with LFTP.  If not, see <http://www.gnu.org/licenses/>.\n"
msgstr ""
"LFTP 是免费软件：您可以重新发布和/或修改它\n"
"根据 GNU 通用公共许可证条款由自由软件基金会\n"
"发布，或许可证的第3版，或者(根据您的选择)\n"
"任何更新的版本。\n"
"\n"
"这个程序是可分发的，希望它会有用，\n"
"但没有任何担保；甚至没有对适销性或特定用途\n"
"适用性的隐含保证。有关详细信息\n"
"请参阅 GNU 通用公共许可证。\n"
"\n"
"您应该收到 GNU 通用公共许可证的副本\n"
"以及 LFTP。如果没有，请参阅 <http://www.gnu.org/licenses/>。\n"

#: src/commands.cc:2822
#, c-format
msgid "Send bug reports and questions to the mailing list <%s>.\n"
msgstr "将缺陷报告和问题发送到邮件列表 <%s>。\n"

#: src/commands.cc:2828
msgid "Libraries used: "
msgstr "使用的库: "

#: src/commands.cc:2979 src/commands.cc:3007
#, c-format
msgid "%s: bookmark name required\n"
msgstr "%s：需要书签名\n"

#: src/commands.cc:2996
#, c-format
msgid "%s: spaces in bookmark name are not allowed\n"
msgstr "%s：书签名称中的空格是不允许的\n"

#: src/commands.cc:3009
#, c-format
msgid "%s: no such bookmark `%s'\n"
msgstr "%s：没有 `%s' 这个书签\n"

#: src/commands.cc:3032
#, c-format
msgid "%s: import type required (netscape,ncftp)\n"
msgstr "%s：需要导入类型(netscape，ncftp)\n"

#: src/commands.cc:3110
#, c-format
msgid "Usage: %s [-d #] dir\n"
msgstr "用法：%s [-d #] dir\n"

#: src/commands.cc:3215
#, c-format
msgid "%s: invalid block size `%s'\n"
msgstr "%s：无效的块大小 `%s'\n"

#: src/commands.cc:3226
#, c-format
msgid "Usage: %s [options] <dirs>\n"
msgstr "用法：%s [options] <dirs>\n"

#: src/commands.cc:3232
#, c-format
msgid "%s: warning: summarizing is the same as using --max-depth=0\n"
msgstr "%s：警告：--summarize 与使用 --max-depth=0 相同\n"

#: src/commands.cc:3236
#, c-format
msgid "%s: summarizing conflicts with --max-depth=%i\n"
msgstr "%s：--summarize 与 --max-depth=%i 冲突\n"

#: src/commands.cc:3280
#, c-format
msgid "Usage: %s command args...\n"
msgstr "用法：%s command args...\n"

#: src/commands.cc:3292
#, c-format
msgid "Usage: %s module [args...]\n"
msgstr "用法：%s module [args...]\n"

#: src/commands.cc:3310 src/LocalAccess.cc:642
msgid "cannot get current directory"
msgstr "无法获取当前目录"

#: src/commands.cc:3374
#, c-format
msgid "Usage: %s [OPTS] mode file...\n"
msgstr "用法：%s [OPTS] mode file...\n"

#: src/commands.cc:3394
#, c-format
msgid "invalid mode string: %s\n"
msgstr "无效 mode 字符串：%s\n"

#: src/commands.cc:3457 src/commands.cc:3466
msgid "Invalid range format. Format is min-max, e.g. 10-20."
msgstr "范围格式无效。格式是 最小-最大，例如 10-20。"

#: src/commands.cc:3478
#, c-format
msgid "Usage: %s [OPTS] file\n"
msgstr "用法：%s [OPTS] file\n"

#: src/CopyJob.cc:82
#, c-format
msgid "`%s' at %lld %s%s%s%s"
msgstr "`%s' 在 %lld %s%s%s%s"

#: src/CopyJob.cc:159
#, c-format
msgid "%lld $#ll#byte|bytes$ transferred in %ld $#l#second|seconds$"
msgstr "%lld 字节，在 %ld 秒中传输完毕"

#: src/CopyJob.cc:167
#, c-format
msgid "%lld $#ll#byte|bytes$ transferred"
msgstr "传输 %lld 字节"

#: src/CopyJob.cc:283
#, c-format
msgid "Transfer of %d of %d $file|files$ failed\n"
msgstr "传输 %d/%d 文件失败\n"

#: src/CopyJob.cc:289
#, c-format
msgid "Total %d $file|files$ transferred\n"
msgstr "总共传输 %d 个文件\n"

#: src/FileAccess.cc:160
msgid "Access failed: "
msgstr "访问失败: "

#: src/FileAccess.cc:161
msgid "File cannot be accessed"
msgstr "文件无法访问"

#: src/FileAccess.cc:163 src/Fish.cc:982 src/ftpclass.cc:4599
#: src/ftpclass.cc:4608 src/SFtp.cc:1339 src/Torrent.cc:3533
msgid "Not connected"
msgstr "未连接"

#: src/FileAccess.cc:166 src/FileAccess.cc:167
msgid "Fatal error"
msgstr "严重错误"

#: src/FileAccess.cc:169
msgid "Store failed - you have to reput"
msgstr "保存失败 - 你必须重新上传"

#: src/FileAccess.cc:172 src/FileAccess.cc:173
msgid "Login failed"
msgstr "登录失败"

#: src/FileAccess.cc:176 src/FileAccess.cc:177
msgid "Operation not supported"
msgstr "不支持的操作"

#: src/FileAccess.cc:180
msgid "File moved"
msgstr "文件已移动"

#: src/FileAccess.cc:182
msgid "File moved to `"
msgstr "文件移至`"

#: src/FileCopy.cc:141
msgid "copy: destination file is already complete\n"
msgstr "复制：目标文件已经完成\n"

#: src/FileCopy.cc:182
msgid "copy: put is broken\n"
msgstr "复制：上传中断\n"

#: src/FileCopy.cc:201
msgid "seek failed"
msgstr "查找失败"

#: src/FileCopy.cc:207
msgid "no progress timeout"
msgstr "没有进度超时"

#: src/FileCopy.cc:235
msgid "cannot seek on data source"
msgstr "不能在数据源中查找"

#: src/FileCopy.cc:238
#, c-format
msgid "copy: put rolled back to %lld, seeking get accordingly\n"
msgstr "复制：上传会滚到 %lld，查找相应的 下载\n"

#: src/FileCopy.cc:251
msgid "copy: all data received, but get rolled back\n"
msgstr "复制：收到所有数据，但下载回滚\n"

#: src/FileCopy.cc:267
#, c-format
msgid "copy: get rolled back to %lld, seeking put accordingly\n"
msgstr "复制：下载回滚到 %lld，寻求查找相应上传\n"

#: src/FileCopy.cc:364
msgid "file size decreased during transfer"
msgstr "文件大小在传输期间减小"

#: src/FileCopy.cc:1227
#, c-format
msgid "copy: received redirection to `%s'\n"
msgstr "复制：接收重定向到 `%s'\n"

#: src/FileCopy.cc:1290
#, fuzzy
#| msgid "file size decreased during transfer"
msgid "file size increased during transfer"
msgstr "文件大小在传输期间减小"

#: src/FileCopy.cc:1390
msgid "file name missed in URL"
msgstr "在 URL中缺失文件名"

#: src/FileCopy.cc:2086
msgid "Verify command failed without a message"
msgstr "没有信息，验证命令失败"

#: src/FileCopyFtp.cc:95
msgid "**** FXP: trying to reverse ftp:fxp-passive-source\n"
msgstr "**** FXP：尝试反转 ftp:fxp-passive-source\n"

#: src/FileCopyFtp.cc:102
msgid "**** FXP: trying to reverse ftp:fxp-passive-sscn\n"
msgstr "**** FXP：尝试反转 ftp:fxp-passive-sscn\n"

#: src/FileCopyFtp.cc:110
msgid "**** FXP: trying to reverse ftp:ssl-protect-fxp\n"
msgstr "**** FXP：尝试反转 ftp:ssl-protect-fxp\n"

#: src/FileCopyFtp.cc:116
msgid "**** FXP: giving up, reverting to plain copy\n"
msgstr "**** FXP：放弃，恢复为普通复制\n"

#: src/FileCopyFtp.cc:125
msgid "ftp:fxp-force is set but FXP is not available"
msgstr "ftp:fxp-force 已设置，但 FXP 不可用"

#: src/FileCopy.h:285
msgid "Verifying..."
msgstr "验证中..."

#: src/FileSetOutput.cc:230
msgid "non-option arguments found"
msgstr "找到了非选项参数"

#: src/Filter.cc:166
msgid "pipe() failed: "
msgstr "pipe() 失败: "

#: src/Filter.cc:200 src/PtyShell.cc:135
#, c-format
msgid "chdir(%s) failed: %s\n"
msgstr "chdir(%s) 失败：%s\n"

#: src/Filter.cc:208
#, c-format
msgid "execvp(%s) failed: %s\n"
msgstr "execvp(%s) 失败：%s\n"

#: src/Filter.cc:213 src/PtyShell.cc:147
#, c-format
msgid "execl(/bin/sh) failed: %s\n"
msgstr "execl(/bin/sh) 失败：%s\n"

#: src/Filter.cc:415
msgid "file already exists and xfer:clobber is unset"
msgstr "文件已经存在，并且 xfer:clobber 未被设置"

#: src/FindJobDu.cc:101
msgid "total"
msgstr "总计"

#: src/Fish.cc:75 src/ftpclass.cc:1292 src/Http.cc:1232 src/SFtp.cc:77
#, c-format
msgid "Closing idle connection"
msgstr "关闭空闲连接"

#: src/Fish.cc:162 src/SFtp.cc:177
msgid "Running connect program"
msgstr "运行连接程序"

#: src/Fish.cc:588 src/ftpclass.cc:2887 src/ftpclass.cc:3107 src/Http.cc:1510
#: src/SFtp.cc:742 src/SFtp.cc:1083 src/SFtp.cc:1084 src/SSH_Access.cc:167
#, c-format
msgid "Peer closed connection"
msgstr "对端关闭连接"

#: src/Fish.cc:634 src/ftpclass.cc:3037 src/ftpclass.cc:4219 src/SFtp.cc:1108
#, c-format
msgid "extra server response"
msgstr "额外的服务器应答"

#: src/Fish.cc:987 src/ftpclass.cc:4611 src/Http.cc:2329 src/Http.cc:2336
#: src/SFtp.cc:1345 src/Torrent.cc:3536 src/TorrentTracker.cc:624
msgid "Connecting..."
msgstr "正在连接..."

#: src/Fish.cc:989 src/ftpclass.cc:4617 src/SFtp.cc:1347
msgid "Connected"
msgstr "已连接"

#: src/Fish.cc:991 src/ftpclass.cc:4594 src/ftpclass.cc:4622
#: src/ftpclass.cc:4636 src/Http.cc:2338 src/SFtp.cc:1349
#: src/TorrentTracker.cc:626
msgid "Waiting for response..."
msgstr "正等待应答..."

#: src/Fish.cc:993 src/ftpclass.cc:4656 src/Http.cc:2341 src/SFtp.cc:1351
msgid "Receiving data"
msgstr "正接收数据"

#: src/Fish.cc:995 src/ftpclass.cc:4654 src/Http.cc:2334 src/SFtp.cc:1353
msgid "Sending data"
msgstr "正发送数据"

#: src/Fish.cc:997 src/SFtp.cc:1355
msgid "Done"
msgstr "完成"

#: src/Fish.cc:1136 src/FtpDirList.cc:123 src/HttpDir.cc:1384 src/SFtp.cc:2200
#: src/SFtp.cc:2320
#, c-format
msgid "Getting file list (%lld) [%s]"
msgstr "正在获取文件列表 (%lld) [%s]"

#: src/ftpclass.cc:197
#, c-format
msgid "Data connection peer has wrong port number"
msgstr "数据连接对端的端口号错误"

#: src/ftpclass.cc:203
#, c-format
msgid "Data connection peer has mismatching address"
msgstr "数据连接对端的地址不匹配"

#: src/ftpclass.cc:225 src/ftpclass.cc:250
#, c-format
msgid "Switching to NOREST mode"
msgstr "切换到 NOREST 模式"

#: src/ftpclass.cc:425
#, c-format
msgid "Server reply matched ftp:retry-530, retrying"
msgstr "服务器回复匹配 ftp:retry-530，重试"

#: src/ftpclass.cc:433
#, c-format
msgid "Server reply matched ftp:retry-530-anonymous, retrying"
msgstr "服务器回复匹配 ftp:retry-530-anonymous，重试"

#: src/ftpclass.cc:466
msgid "Account is required, set ftp:acct variable"
msgstr "帐户是必需的，设置 ftp:acct 变量"

#: src/ftpclass.cc:483
msgid "ftp:skey-force is set and server does not support OPIE nor S/KEY"
msgstr "ftp:skey-force 已设置，服务器不支持 OPIE 和 S/KEY"

#: src/ftpclass.cc:501
#, c-format
msgid "assuming failed host name lookup"
msgstr "假设主机名称查找失败"

#: src/ftpclass.cc:809 src/ftpclass.cc:810
#, c-format
msgid "cannot parse EPSV response"
msgstr "无法解析 EPSV 响应"

#: src/ftpclass.cc:837 src/ftpclass.cc:838
#, fuzzy, c-format
#| msgid "cannot parse EPSV response"
msgid "cannot parse custom EPSV response"
msgstr "无法解析 EPSV 响应"

#: src/ftpclass.cc:1122
#, c-format
msgid "Closing control socket"
msgstr "关闭控制套接字"

#: src/ftpclass.cc:1341
msgid "MLSD is disabled by ftp:use-mlsd"
msgstr "MLSD 由 ftp:use-mlsd 禁用"

#: src/ftpclass.cc:1411 src/Http.cc:1431 src/Torrent.cc:3204
#: src/Torrent.cc:3743 src/TorrentTracker.cc:395
#, c-format
msgid "cannot create socket of address family %d"
msgstr "无法创建地址族 %d 的套接字"

#: src/ftpclass.cc:1442 src/Http.cc:1457
#, c-format
msgid "Socket error (%s) - reconnecting"
msgstr "套接字错误 (%s) - 重新连接"

#: src/ftpclass.cc:1715
msgid "MFF and SITE CHMOD are not supported by this site"
msgstr "本网站不支持 MFF 和 SITE CHMOD"

#: src/ftpclass.cc:1720
msgid "MLST and MLSD are not supported by this site"
msgstr "本网站不支持 MLST 和 MLSD"

#: src/ftpclass.cc:2031
msgid "SITE SYMLINK is not supported by the server"
msgstr "SITE SYMLINK 不受服务器支持"

#: src/ftpclass.cc:2204
msgid "unsupported network protocol"
msgstr "不受支持的网络协议"

#: src/ftpclass.cc:2253 src/ftpclass.cc:2355
#, c-format
msgid "Data socket error (%s) - reconnecting"
msgstr "数据套接字错误 (%s) - 重新连接"

#: src/ftpclass.cc:2281
#, c-format
msgid "Accepted data connection from (%s) port %u"
msgstr "已接受的数据连接从 (%s) 端口 %u"

#: src/ftpclass.cc:2330
#, c-format
msgid "Connecting data socket to (%s) port %u"
msgstr "将数据套接字连接到 (%s) 端口 %u"

#: src/ftpclass.cc:2336
#, c-format
msgid "Connecting data socket to proxy %s (%s) port %u"
msgstr "将数据套接字连接到代理服务器 %s (%s) 端口 %u"

#: src/ftpclass.cc:2358 src/ftpclass.cc:4414
#, c-format
msgid "Switching passive mode off"
msgstr "关闭被动模式"

#: src/ftpclass.cc:2366
#, c-format
msgid "Data connection established"
msgstr "数据连接已建立"

#: src/ftpclass.cc:2688 src/ftpclass.cc:4548
msgid "ftp:ssl-force is set and server does not support or allow SSL"
msgstr "ftp:ssl-force 已设置并且服务器不支持或不允许使用 SSL"

#: src/ftpclass.cc:3053
#, c-format
msgid "Persist and retry"
msgstr "持续并重试"

#: src/ftpclass.cc:3321
#, c-format
msgid "Closing data socket"
msgstr "关闭数据套接字"

#: src/ftpclass.cc:3343
#, c-format
msgid "Closing aborted data socket"
msgstr "关闭中止的数据套接字"

#: src/ftpclass.cc:4193
#, c-format
msgid "saw file size in response"
msgstr "在响应时查看文件大小"

#: src/ftpclass.cc:4233 src/ftpclass.cc:4260
#, c-format
msgid "Turning on sync-mode"
msgstr "打开同步模式"

#: src/ftpclass.cc:4434
#, c-format
msgid "Switching passive mode on"
msgstr "打开被动模式"

#: src/ftpclass.cc:4585
msgid "FEAT negotiation..."
msgstr "FEAT 协商..."

#: src/ftpclass.cc:4592
msgid "Sending commands..."
msgstr "正在发送命令..."

#: src/ftpclass.cc:4596
msgid "Delaying before retry"
msgstr "重新连接前延时"

#: src/ftpclass.cc:4597 src/Http.cc:2331
msgid "Connection idle"
msgstr "连接空闲"

#: src/ftpclass.cc:4604 src/Http.cc:2323 src/Resolver.cc:172
#: src/Resolver.cc:217 src/TorrentTracker.cc:622
#, c-format
msgid "Resolving host address..."
msgstr "正在解析主机地址..."

#: src/ftpclass.cc:4615
msgid "TLS negotiation..."
msgstr "TLS 协商..."

#: src/ftpclass.cc:4619
msgid "Logging in..."
msgstr "正在登录..."

#: src/ftpclass.cc:4623
msgid "Making data connection..."
msgstr "正在建立数据连接..."

#: src/ftpclass.cc:4626
msgid "Changing remote directory..."
msgstr "正在改变远程目录..."

#: src/ftpclass.cc:4632
msgid "Waiting for other copy peer..."
msgstr "正在等待其他副本对等方....."

#: src/ftpclass.cc:4634 src/ftpclass.cc:4658
msgid "Waiting for transfer to complete"
msgstr "正等待传输完成"

#: src/ftpclass.cc:4638
msgid "Waiting for TLS shutdown..."
msgstr "正在等待 TLS 关闭..."

#: src/ftpclass.cc:4640
msgid "Waiting for data connection..."
msgstr "正等待数据连接..."

#: src/ftpclass.cc:4646
msgid "Sending data/TLS"
msgstr "发送 data/TLS"

#: src/ftpclass.cc:4648
msgid "Receiving data/TLS"
msgstr "接收 data/TLS"

#: src/Http.cc:230
#, c-format
msgid "Closing HTTP connection"
msgstr "关闭 HTTP 连接"

#: src/Http.cc:240
msgid "POST method failed"
msgstr "POST 方法失败"

#: src/Http.cc:1381
msgid "ftp over http cannot work without proxy, set hftp:proxy."
msgstr "如果没有代理，http 上的 ftp 无法正常工作，请设置 hftp:proxy。"

#: src/Http.cc:1537
#, c-format
msgid "Sending request..."
msgstr "正在发送请求..."

#: src/Http.cc:1575
#, c-format
msgid "Hit EOF while fetching headers"
msgstr "获取头时匹配 EOF"

#: src/Http.cc:1695
#, c-format
msgid "Could not parse HTTP status line"
msgstr "无法解析 HTTP 状态行"

#: src/Http.cc:1726
msgid "Object is not cached and http:cache-control has only-if-cached"
msgstr "对象没有被缓存，并且 http:cache-control 只有 only-if-cached"

#: src/Http.cc:1891
#, c-format
msgid "Receiving body..."
msgstr "接收主体..."

#: src/Http.cc:2092
#, c-format
msgid "Hit EOF"
msgstr "匹配 EOF"

#: src/Http.cc:2095
#, c-format
msgid "Received not enough data, retrying"
msgstr "收到的数据不足，请重试"

#: src/Http.cc:2106
#, c-format
msgid "Received all"
msgstr "全部收到"

#: src/Http.cc:2111
#, c-format
msgid "Received all (total)"
msgstr "全部收到 (共计)"

#: src/Http.cc:2135 src/Http.cc:2159
msgid "chunked format violated"
msgstr "违反了块格式"

#: src/Http.cc:2145
#, c-format
msgid "Received last chunk"
msgstr "收到最后一块"

#: src/Http.cc:2339
msgid "Fetching headers..."
msgstr "正在获取头..."

#: src/Job.cc:293
#, c-format
msgid "[%d] Done (%s)"
msgstr "[%d] 完成 (%s)"

#: src/lftp.cc:370
#, c-format
msgid "[%u] Terminated by signal %d. %s\n"
msgstr "[%u] 由信号 %d 终止。%s\n"

#: src/lftp.cc:445
#, c-format
msgid "[%u] Started.  %s\n"
msgstr "[%u] 启动。 %s\n"

#: src/lftp.cc:463
#, c-format
msgid "[%u] Detaching from the terminal to complete transfers...\n"
msgstr "[%u] 从终端上分离完成传输...\n"

#: src/lftp.cc:465
#, c-format
msgid "[%u] Exiting and detaching from the terminal.\n"
msgstr "[%u] 退出和终端分离。\n"

#: src/lftp.cc:470
#, c-format
msgid "[%u] Detached from terminal. %s\n"
msgstr "[%u] 从终端分离。%s\n"

#: src/lftp.cc:480
#, c-format
msgid "[%u] Finished. %s\n"
msgstr "[%u] 已完成。%s\n"

#: src/lftp.cc:484
#, c-format
msgid "[%u] Moving to background to complete transfers...\n"
msgstr "[%u] 转到后台完成传输...\n"

#: src/lftp.cc:553
msgid "history -w file|-r file|-c|-l [cnt]"
msgstr ""

#: src/lftp.cc:554
msgid ""
" -w <file> Write history to file.\n"
" -r <file> Read history from file; appends to current history.\n"
" -c  Clear the history.\n"
" -l  List the history (default).\n"
"Optional argument cnt specifies the number of history lines to list,\n"
"or \"all\" to list all entries.\n"
msgstr ""
" -w <file> 将历史记录写入文件。\n"
" -r <file> 从文件读取历史记录；追加到当前历史记录。\n"
" -c  清除历史记录。\n"
" -l  列出历史(默认)。\n"
"可选参数 cnt 指定要列出的历史行数，\n"
"或 “all” 列出所有条目。\n"

#: src/lftp.cc:561
msgid "Attach the terminal to specified backgrounded lftp process.\n"
msgstr "将终端附加到指定的后台 lftp 进程。\n"

#: src/lftp_ssl.cc:1291
#, c-format
msgid "No certificate presented by %s.\n"
msgstr "没有由 %s 提供的证书。\n"

#: src/LocalAccess.cc:604 src/NetAccess.cc:686
msgid "Getting directory contents"
msgstr "获取目录内容"

#: src/LocalAccess.cc:606 src/NetAccess.cc:690
msgid "Getting files information"
msgstr "获取文件信息"

#: src/LsCache.cc:190
#, c-format
msgid "%ld $#l#byte|bytes$ cached"
msgstr "%ld 字节已缓存"

#: src/LsCache.cc:194
msgid ", no size limit"
msgstr "，没有大小限制"

#: src/LsCache.cc:196
#, c-format
msgid ", maximum size %ld\n"
msgstr "，最大大小为 %ld\n"

#: src/mgetJob.cc:78
#, c-format
msgid "%s: %s: no files found\n"
msgstr "%s：%s：找不到文件\n"

#: src/MirrorJob.cc:100
#, c-format
msgid "%sTotal: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
msgstr "%s全部：%d 个目录，%d 个文件，%d 个符号链接\n"

#: src/MirrorJob.cc:104
#, c-format
msgid "%sNew: %d file$|s$, %d symlink$|s$\n"
msgstr "%s新建：%d 个文件，%d 个符号链接\n"

#: src/MirrorJob.cc:108
#, c-format
msgid "%sModified: %d file$|s$, %d symlink$|s$\n"
msgstr "%s已修改：%d 个文件，%d 个符号链接\n"

#: src/MirrorJob.cc:115
#, c-format
msgid "%sRemoved: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
msgstr "%s已删除：%d 个目录，%d 个文件，%d 个符号链接\n"

#: src/MirrorJob.cc:120
#, c-format
msgid "%s%d error$|s$ detected\n"
msgstr "检测到 %s%d 个错误\n"

#: src/MirrorJob.cc:231
#, c-format
msgid "Finished %s"
msgstr "已完成 %s"

#: src/MirrorJob.cc:344 src/MirrorJob.cc:519 src/MirrorJob.cc:1218
#, c-format
msgid "Removing old file `%s'"
msgstr "正在删除旧文件 `%s'"

#: src/MirrorJob.cc:346
#, c-format
msgid "Overwriting old file `%s'"
msgstr "覆盖旧文件 `%s'"

#: src/MirrorJob.cc:355
#, c-format
msgid "Skipping file `%s' (only-existing)"
msgstr "跳过文件 `%s' (仅存在时)"

#: src/MirrorJob.cc:361
#, c-format
msgid "Transferring file `%s'"
msgstr "传输文件 '%s'"

#: src/MirrorJob.cc:439
#, c-format
msgid "Skipping directory `%s' (only-existing)"
msgstr "跳过目录 `%s' (仅存在时)"

#: src/MirrorJob.cc:461 src/MirrorJob.cc:550 src/MirrorJob.cc:902
#, c-format
msgid "Removing old local file `%s'"
msgstr "正在删除旧的本地文件 `%s'"

#: src/MirrorJob.cc:487
#, c-format
msgid "Scanning directory `%s'"
msgstr "扫描目录 `%s'"

#: src/MirrorJob.cc:489
#, c-format
msgid "Mirroring directory `%s'"
msgstr "镜像目录 `%s'"

#: src/MirrorJob.cc:525 src/MirrorJob.cc:567
#, c-format
msgid "Making symbolic link `%s' to `%s'"
msgstr "建立符号链接 `%s' 指向 `%s'"

#: src/MirrorJob.cc:562
#, c-format
msgid "Skipping symlink `%s' (only-existing)"
msgstr "跳过符号链接 `%s' (仅存在时)"

#: src/MirrorJob.cc:807
#, c-format
msgid "mirror: protocol `%s' is not suitable for mirror\n"
msgstr "镜像：协议 `%s' 不适合进行镜像\n"

#: src/MirrorJob.cc:923
#, c-format
msgid "Making directory `%s'"
msgstr "建立目录 `%s'"

#: src/MirrorJob.cc:1181
#, c-format
msgid "Old directory `%s' is not removed"
msgstr "旧的目录 `%s' 没有删除"

#: src/MirrorJob.cc:1183
#, c-format
msgid "Old file `%s' is not removed"
msgstr "旧的文件 `%s' 没有删除"

#: src/MirrorJob.cc:1216
#, c-format
msgid "Removing old directory `%s'"
msgstr "删除旧的目录 `%s'"

#: src/MirrorJob.cc:1339
#, c-format
msgid "Removing source directory `%s'"
msgstr "删除源目录 `%s'"

#: src/MirrorJob.cc:1405
#, c-format
msgid "Removing source file `%s'"
msgstr "删除源文件 `%s'"

#: src/MirrorJob.cc:1425
#, c-format
msgid "Retrying mirror...\n"
msgstr "重试镜像...\n"

#: src/MirrorJob.cc:1694
msgid "pattern is empty"
msgstr ""

#: src/MirrorJob.cc:1779
#, c-format
msgid "%s must be one of: %s"
msgstr "%s 必须是以下之一：%s"

#: src/MirrorJob.cc:2019
#, c-format
msgid ""
"%s: multiple --file or --directory options must have the same base "
"directory\n"
msgstr "%s：多个 --file 或 --directory 选项必须具有相同的基础目录\n"

#: src/MirrorJob.cc:2160
#, c-format
msgid "%s: ambiguous source directory (`%s' or `%s'?)\n"
msgstr "%s：不明确的源目录 (%s' 或 `%s'?)\n"

#: src/MirrorJob.cc:2182
#, c-format
msgid "%s: ambiguous target directory (`%s' or `%s'?)\n"
msgstr "%s：不明确的目标目录 (`%s' 或 `%s'?)\n"

#: src/MirrorJob.cc:2223
#, c-format
msgid "%s: source directory is required (mirror:require-source is set)\n"
msgstr "%s：源目录是必需的(mirror:require-source 已设置)\n"

#: src/MirrorJob.cc:2343
msgid ""
"\n"
"Mirror specified remote directory to local directory\n"
"\n"
" -R, --reverse          reverse mirror (put files)\n"
"Lots of other options are documented in the man page lftp(1).\n"
"\n"
"When using -R, the first directory is local and the second is remote.\n"
"If the second directory is omitted, basename of the first directory is "
"used.\n"
"If both directories are omitted, current local and remote directories are "
"used.\n"
"\n"
"See the man page lftp(1) for a complete documentation.\n"
msgstr ""
"\n"
"将指定的远程目录镜像到本地目录\n"
"\n"
" -R, --reverse          反向镜像 (上传文件)\n"
"许多其他选项都记录在手册页 lftp(1) 中。\n"
"\n"
"当使用 -R 时，第一个目录是本地的，第二个是远程的。\n"
"如果省略第二个目录，则使用第一个目录的基础名称。\n"
"如果两个目录都被省略，则使用当前的本地和远程目录。\n"
"\n"
"有关完整的文档，请参见手册页 lftp(1)。\n"

#: src/mkdirJob.cc:128
#, c-format
msgid "%s ok, `%s' created\n"
msgstr "%s 成功，建立 `%s'\n"

#: src/mkdirJob.cc:130 src/rmJob.cc:51
#, c-format
msgid "%s failed for %d of %d director$y|ies$\n"
msgstr "%s 失败，%d/%d 个目录\n"

#: src/mkdirJob.cc:133
#, c-format
msgid "%s ok, %d director$y|ies$ created\n"
msgstr "%s 成功，建立 %d 个目录\n"

#: src/module.cc:190
#, c-format
msgid "depend module `%s': %s\n"
msgstr "依赖模块`%s'：%s\n"

#: src/module.cc:210
msgid "modules are not supported on this system"
msgstr "这个系统不支持模块"

#: src/mvJob.cc:92
#, c-format
msgid "rename successful\n"
msgstr "重命名成功\n"

#: src/NetAccess.cc:168
#, c-format
msgid "Connecting to %s%s (%s) port %u"
msgstr "正在连接到 %s%s (%s) 端口 %u"

#: src/NetAccess.cc:224 src/Torrent.cc:3326
#, c-format
msgid "Timeout - reconnecting"
msgstr "超时 - 重新连接"

#: src/NetAccess.cc:323
msgid "Connection limit reached"
msgstr "已达到连接限制"

#: src/NetAccess.cc:330
msgid "Delaying before reconnect"
msgstr "重新连接前延时"

#: src/NetAccess.cc:354 src/NetAccess.cc:356
msgid "max-retries exceeded"
msgstr "达到最大重试次数"

#: src/OutputJob.cc:145
#, c-format
msgid "%s (filter)"
msgstr "%s (过滤器)"

#: src/parsecmd.cc:290
msgid "parse: missing filter command\n"
msgstr "parse：缺少过滤器命令\n"

#: src/parsecmd.cc:292
msgid "parse: missing redirection filename\n"
msgstr "parse：缺少重定向文件名\n"

#: src/PatternSet.cc:110
#, c-format
msgid "regular expression `%s': %s"
msgstr "正则表达式`%s'：%s"

#: src/pgetJob.cc:127
msgid "pget: falling back to plain get"
msgstr "pget：返回到简单下载"

#: src/pgetJob.cc:131
msgid "the target file is remote"
msgstr "目标文件是远程的"

#: src/pgetJob.cc:136
msgid "the source file size is unknown"
msgstr "源文件大小未知"

#: src/pgetJob.cc:173
#, c-format
msgid "pget: warning: space allocation for %s (%lld bytes) failed: %s\n"
msgstr "pget：警告：%s (%lld 字节) 的空间分配失败：%s\n"

#: src/pgetJob.cc:234
#, c-format
msgid "`%s', got %lld of %lld (%d%%) %s%s"
msgstr "`%s'，已下载 %lld/%lld (%d%%) %s%s"

#: src/plural.c:96
msgid "=1 =0|>1"
msgstr ""

#: src/PollVec.cc:69
#, c-format
msgid "%s: BUG - deadlock detected\n"
msgstr "%s：缺陷 - 检测到死锁\n"

#: src/PtyShell.cc:68
msgid "pseudo-tty allocation failed: "
msgstr "pseudo-tty 分配失败： "

#: src/QueueFeeder.cc:68
msgid "Added job$|s$"
msgstr "已添加的任务"

#: src/QueueFeeder.cc:164 src/QueueFeeder.cc:185
#, c-format
msgid "No queued jobs.\n"
msgstr "没有队列的任务。\n"

#: src/QueueFeeder.cc:166
#, c-format
msgid "No queued job #%i.\n"
msgstr "没有队列的任务 #%i。\n"

#: src/QueueFeeder.cc:171 src/QueueFeeder.cc:192
msgid "Deleted job$|s$"
msgstr "已删除的任务"

#: src/QueueFeeder.cc:187
#, c-format
msgid "No queued jobs match \"%s\".\n"
msgstr "没有队列的任务匹配 \"%s\"。\n"

#: src/QueueFeeder.cc:212 src/QueueFeeder.cc:230
msgid "Moved job$|s$"
msgstr "已移动的任务"

#: src/QueueFeeder.cc:354
msgid "Commands queued:"
msgstr "队列的命令:"

#: src/ResMgr.cc:123
msgid "no such variable"
msgstr "没有这个变量"

#: src/ResMgr.cc:127
msgid "ambiguous variable name"
msgstr "不确切的变量名"

#: src/ResMgr.cc:335
msgid "invalid boolean value"
msgstr "无效的布尔值"

#: src/ResMgr.cc:357
msgid "invalid boolean/auto value"
msgstr "无效的 布尔/自动 值"

#: src/ResMgr.cc:402 src/ResMgr.cc:713
msgid "invalid number"
msgstr "无效的数字"

#: src/ResMgr.cc:415
msgid "invalid floating point number"
msgstr "无效的浮点数"

#: src/ResMgr.cc:429
msgid "invalid unsigned number"
msgstr "无效的无符号数字"

#: src/ResMgr.cc:678
msgid "Invalid time unit letter, only [smhd] are allowed."
msgstr "无效的时间单位字母，只允许 [smhd]。"

#: src/ResMgr.cc:686
msgid "Invalid time format. Format is <time><unit>, e.g. 2h30m."
msgstr "无效的时间格式。格式是 <time><unit>，例如2h30m。"

#: src/ResMgr.cc:718
msgid "integer overflow"
msgstr "整数溢出"

#: src/ResMgr.cc:804
msgid "Invalid IPv4 numeric address"
msgstr "无效的 IPv4 数字地址"

#: src/ResMgr.cc:814
msgid "Invalid IPv6 numeric address"
msgstr "无效的 IPv6 数字地址"

#: src/ResMgr.cc:884 src/ResMgr.cc:888
msgid "this encoding is not supported"
msgstr "此编码不受支持"

#: src/ResMgr.cc:894
msgid "no closure defined for this setting"
msgstr "此设置没有定义约束"

#: src/ResMgr.cc:900
msgid "a closure is required for this setting"
msgstr "此设置需要一个约束"

#: src/Resolver.cc:236
msgid "host name resolve timeout"
msgstr "主机名解析超时"

#: src/Resolver.cc:282
#, c-format
msgid "%d address$|es$ found"
msgstr "找到 %d 个地址"

#: src/Resolver.cc:327
msgid "Link-local IPv6 address should have a scope"
msgstr "Link-local IPv6 地址应该有一个范围"

#: src/Resolver.cc:765
msgid "DNS resolution not trusted."
msgstr "DNS 解析不可信。"

#: src/Resolver.cc:871
msgid "Host name lookup failure"
msgstr "主机名称查找失败"

#: src/Resolver.cc:903
#, c-format
msgid "no such %s service"
msgstr "没有此 %s 服务"

#: src/Resolver.cc:930
msgid "No address found"
msgstr "未找到地址"

#: src/resource.cc:50 src/resource.cc:108
msgid "Proxy protocol unsupported"
msgstr "不支持代理协议"

#: src/resource.cc:54
msgid "ftp:proxy password: "
msgstr "ftp:proxy 密码： "

#: src/resource.cc:70
#, c-format
msgid "%s must be one of: "
msgstr "%s 必须是以下之一： "

#: src/resource.cc:72
msgid "must be one of: "
msgstr "必须是以下之一： "

#: src/resource.cc:84
msgid ", or empty"
msgstr "，或者为空"

#: src/resource.cc:116
msgid "only PUT and POST values allowed"
msgstr "只允许 PUT 和 POST 值"

#: src/resource.cc:148
#, c-format
msgid "unknown address family `%s'"
msgstr "未知的地址族 `%s'"

#: src/rmJob.cc:47
#, c-format
msgid "%s ok, `%s' removed\n"
msgstr "%s 成功，删除 `%s'\n"

#: src/rmJob.cc:54
#, c-format
msgid "%s failed for %d of %d file$|s$\n"
msgstr "%s 失败，%d/%d 个文件\n"

#: src/rmJob.cc:60
#, c-format
msgid "%s ok, %d director$y|ies$ removed\n"
msgstr "%s 成功，删除 %d 个目录\n"

#: src/rmJob.cc:63
#, c-format
msgid "%s ok, %d file$|s$ removed\n"
msgstr "%s 成功，删除 %d 个文件\n"

#: src/SFtp.cc:1099 src/SFtp.cc:1100
#, c-format
msgid "invalid server response format"
msgstr "服务器响应格式无效"

#: src/SleepJob.cc:99
msgid "Sleeping forever"
msgstr "永远睡眠"

#: src/SleepJob.cc:100
msgid "Sleep time left: "
msgstr "剩余的睡眠时间： "

#: src/SleepJob.cc:108
#, c-format
msgid "\tRepeat count: %d\n"
msgstr "\t重复次数：%d\n"

#: src/SleepJob.cc:142
#, c-format
msgid "%s: argument required. "
msgstr "%s：需要参数。 "

#: src/SleepJob.cc:262
#, c-format
msgid "%s: date-time specification missed\n"
msgstr "%s：date-time 规格缺失\n"

#: src/SleepJob.cc:269
#, c-format
msgid "%s: date-time parse error\n"
msgstr "%s：date-time 解析错误\n"

#: src/SleepJob.cc:303
msgid ""
"Usage: sleep <time>[unit]\n"
"Sleep for given amount of time. The time argument can be optionally\n"
"followed by unit specifier: d - days, h - hours, m - minutes, s - seconds.\n"
"By default time is assumed to be seconds.\n"
msgstr ""
"用法：sleep <time>[unit]\n"
"睡眠给定的时间。时间参数可加上指定的单位：\n"
"d - 天，h - 小时，m - 分钟，s - 秒。\n"
"默认的时间单位为秒。\n"

#: src/SleepJob.cc:310
msgid ""
"Repeat specified command with a delay between iterations.\n"
"Default delay is one second, default command is empty.\n"
" -c <count>  maximum number of iterations\n"
" -d <delay>  delay between iterations\n"
" --while-ok  stop when command exits with non-zero code\n"
" --until-ok  stop when command exits with zero code\n"
" --weak      stop when lftp moves to background.\n"
msgstr ""
"指定迭代之间延迟，重复指定的命令。\n"
"默认延迟是一秒钟，默认命令是空的。\n"
" -c <count>  最大迭代次数\n"
" -d <delay>  迭代之间的延时\n"
" --while-ok  当命令以非零代码退出时停止\n"
" --until-ok  当命令以零代码退出时停止\n"
" --weak      当 lftp 移动到后台时停止。\n"

#: src/Speedometer.cc:90
#, c-format
msgid "%.0fb/s"
msgstr ""

#: src/Speedometer.cc:93
#, c-format
msgid "%.1fK/s"
msgstr ""

#: src/Speedometer.cc:96
#, c-format
msgid "%.2fM/s"
msgstr ""

#: src/Speedometer.cc:103
#, c-format
msgid "%.0f B/s"
msgstr ""

#: src/Speedometer.cc:105
#, c-format
msgid "%.1f KiB/s"
msgstr ""

#: src/Speedometer.cc:107
#, c-format
msgid "%.2f MiB/s"
msgstr ""

#: src/Speedometer.cc:129
msgid "eta:"
msgstr ""

#: src/SSH_Access.cc:97
msgid "Password required"
msgstr "要求输入密码"

#: src/SSH_Access.cc:102
msgid "Login incorrect"
msgstr "登入错误"

#: src/SSH_Access.cc:196
#, c-format
msgid "Disconnecting"
msgstr "断开"

#: src/SysCmdJob.cc:73
#, c-format
msgid "execlp(%s) failed: %s\n"
msgstr "execlp(%s) 失败：%s\n"

#: src/TimeDate.cc:155
msgid "day"
msgstr "天"

#: src/TimeDate.cc:156
msgid "hour"
msgstr "小时"

#: src/TimeDate.cc:157
msgid "minute"
msgstr "分钟"

#: src/TimeDate.cc:158
msgid "second"
msgstr "秒"

#: src/Torrent.cc:585
msgid "announced via "
msgstr "已发布通过 "

#: src/Torrent.cc:599
#, c-format
msgid "next announce in %s"
msgstr "接下来的发布在 %s"

#: src/Torrent.cc:1142
#, c-format
msgid "%d file$|s$ found, now scanning %s"
msgstr "找到 %d 个文件，现在扫描 %s"

#: src/Torrent.cc:1143
#, c-format
msgid "%d file$|s$ found"
msgstr "找到 %d 个文件"

#: src/Torrent.cc:2075 src/Torrent.cc:2085
#, c-format
msgid "Getting meta-data: %s"
msgstr "获取 meta-data：%s"

#: src/Torrent.cc:2077
#, c-format
msgid "Validation: %u/%u (%u%%) %s%s"
msgstr "验证：%u/%u (%u%%) %s%s"

#: src/Torrent.cc:2088
msgid "Waiting for meta-data..."
msgstr "等待 meta-data..."

#: src/Torrent.cc:2096
msgid "Shutting down: "
msgstr "关闭： "

#: src/Torrent.cc:3207
#, c-format
msgid "Connecting to peer %s port %u"
msgstr "连接到对端 %s 端口 %u"

#: src/Torrent.cc:3258 src/Torrent.cc:3375
#, c-format
msgid "peer unexpectedly closed connection after %s"
msgstr "%s 后对端意外关闭连接"

#: src/Torrent.cc:3259 src/Torrent.cc:3376
msgid "peer unexpectedly closed connection"
msgstr "对端意外关闭连接"

#: src/Torrent.cc:3261 src/Torrent.cc:3262
#, c-format
msgid "peer closed connection (before handshake)"
msgstr "对端关闭连接(握手之前)"

#: src/Torrent.cc:3265 src/Torrent.cc:3378 src/Torrent.cc:3379
#, c-format
msgid "invalid peer response format"
msgstr "无效的对端响应格式"

#: src/Torrent.cc:3360 src/Torrent.cc:3361
#, c-format
msgid "peer closed connection"
msgstr "对端关闭连接"

#: src/Torrent.cc:3538
msgid "Handshaking..."
msgstr "握手..."

#: src/Torrent.cc:3798
msgid "Cannot bind a socket for torrent:port-range"
msgstr "无法为 Torrent 绑定套接字：port-range"

#: src/Torrent.cc:3858
#, c-format
msgid "Accepted connection from [%s]:%d"
msgstr "已接受的连接从 [%s]:%d"

#: src/Torrent.cc:3924
#, c-format
msgid "peer sent unknown info_hash=%s in handshake"
msgstr "对端握手时发送未知的 info_hash=%s"

#: src/Torrent.cc:3948
#, c-format
msgid "peer handshake timeout"
msgstr "对端握手超时"

#: src/Torrent.cc:3960
#, c-format
msgid "peer short handshake"
msgstr "对端短握手"

#: src/Torrent.cc:3962
#, c-format
msgid "peer closed just accepted connection"
msgstr "对端只是关闭已接受的连接"

#: src/Torrent.cc:4013
#, c-format
msgid "Seeding in background...\n"
msgstr "在后台做种...\n"

#: src/Torrent.cc:4176
#, c-format
msgid "%s: --share conflicts with --output-directory.\n"
msgstr "%s：--share 与 --output-directory 冲突。\n"

#: src/Torrent.cc:4180
#, c-format
msgid "%s: --share conflicts with --only-new.\n"
msgstr "%s：--share 与 --only-new 冲突。\n"

#: src/Torrent.cc:4184
#, c-format
msgid "%s: --share conflicts with --only-incomplete.\n"
msgstr "%s：--share 与 --only-incomplete 冲突。\n"

#: src/Torrent.cc:4226
#, c-format
msgid "%s: Please specify a file or directory to share.\n"
msgstr "%s：请指定要共享的文件或目录。\n"

#: src/Torrent.cc:4228
#, c-format
msgid "%s: Please specify meta-info file or URL.\n"
msgstr "%s：请指定 meta-info 文件或 URL。\n"

#: src/Torrent.cc:4258
msgid ""
"Start BitTorrent job for the given torrent-files, which can be a local "
"file,\n"
"URL, magnet link or plain info_hash written in hex or base32. Local "
"wildcards\n"
"are expanded. Options:\n"
" -O <base>      specifies base directory where files should be placed\n"
" --force-valid  skip file validation\n"
" --dht-bootstrap=<node>  bootstrap DHT by sending a query to the node\n"
" --share        share specified file or directory\n"
msgstr ""
"为给定的 torrent-files 启动 BitTorrent 任务，该文件可以是本地文件，\n"
"URL，磁链或 info_hash 16进制或基底32 的纯文本。本地的通配符\n"
"扩展。选项：\n"
" -O <base>      指定要放置文件的基础目录。\n"
" --force-valid  跳过文件验证\n"
" --dht-bootstrap=<node>  通过向节点发送查询来进行 DHT 引导\n"
" --share        共享指定的文件或目录\n"

#: src/TorrentTracker.cc:178
msgid "not started"
msgstr "未启动"

#: src/TorrentTracker.cc:181
#, c-format
msgid "next request in %s"
msgstr "下一个请求在 %s"

#: src/TorrentTracker.cc:284 src/TorrentTracker.cc:501
#, c-format
msgid "Received valid info about %d peer$|s$"
msgstr "收到 %d 个对端有关的有效信息"

#: src/TorrentTracker.cc:298
#, c-format
msgid "Received valid info about %d IPv6 peer$|s$"
msgstr "收到 %d 个 IPv6 对端有关的有效信息"
