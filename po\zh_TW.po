# Chinese (Taiwan) translation of lftp.
# Copyright (C) 2001, 02, 05, 07 <PERSON> <<EMAIL>>
#
# <PERSON> <abel<PERSON><EMAIL>>, 2001, 02, 05, 07.
# <PERSON> <<EMAIL>>, 2015, 2016, 2017, 2020.
msgid ""
msgstr ""
"Project-Id-Version: lftp 4.6.4\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2024-11-08 13:05+0300\n"
"PO-Revision-Date: 2020-01-16 11:56+0800\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Chinese <<EMAIL>>\n"
"Language: zh_TW\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Lokalize 19.08.3\n"

#: lib/argmatch.c:145
#, c-format
msgid "invalid argument %s for %s"
msgstr "%2$s 的參數 %1$s 無效"

#: lib/argmatch.c:146
#, c-format
msgid "ambiguous argument %s for %s"
msgstr "%2$s 的參數 %1$s 不明確"

#: lib/argmatch.c:165
msgid "Valid arguments are:"
msgstr "有效的參數為："

#: lib/error.c:208
msgid "Unknown system error"
msgstr "不明的系統錯誤"

#: lib/getopt.c:282
#, c-format
msgid "%s: option '%s%s' is ambiguous\n"
msgstr "%s：選項 '%s%s' 不明確\n"

#: lib/getopt.c:288
#, c-format
msgid "%s: option '%s%s' is ambiguous; possibilities:"
msgstr "%s：選項 '%s%s' 不明確，可能是："

#: lib/getopt.c:322
#, c-format
msgid "%s: unrecognized option '%s%s'\n"
msgstr "%s：'%s%s' 選項無法辨識\n"

#: lib/getopt.c:348
#, c-format
msgid "%s: option '%s%s' doesn't allow an argument\n"
msgstr "%s：選項 '%s%s' 不可配合參數使用\n"

#: lib/getopt.c:363
#, c-format
msgid "%s: option '%s%s' requires an argument\n"
msgstr "%s：選項 '%s%s' 需要參數\n"

#: lib/getopt.c:624
#, c-format
msgid "%s: invalid option -- '%c'\n"
msgstr "%s：無效的選項 ─ ‘%c’\n"

#: lib/getopt.c:639 lib/getopt.c:685
#, c-format
msgid "%s: option requires an argument -- '%c'\n"
msgstr "%s：選項需要參數 ─ ‘%c’\n"

#. TRANSLATORS:
#. Get translations for open and closing quotation marks.
#. The message catalog should translate "`" to a left
#. quotation mark suitable for the locale, and similarly for
#. "'".  For example, a French Unicode local should translate
#. these to U+00AB (LEFT-POINTING DOUBLE ANGLE
#. QUOTATION MARK), and U+00BB (RIGHT-POINTING DOUBLE ANGLE
#. QUOTATION MARK), respectively.
#.
#. If the catalog has no translation, we will try to
#. use Unicode U+2018 (LEFT SINGLE QUOTATION MARK) and
#. Unicode U+2019 (RIGHT SINGLE QUOTATION MARK).  If the
#. current locale is not Unicode, locale_quoting_style
#. will quote 'like this', and clocale_quoting_style will
#. quote "like this".  You should always include translations
#. for "`" and "'" even if U+2018 and U+2019 are appropriate
#. for your locale.
#.
#. If you don't know what to put here, please see
#. <https://en.wikipedia.org/wiki/Quotation_marks_in_other_languages>
#. and use glyphs suitable for your language.
#: lib/quotearg.c:354
msgid "`"
msgstr "‘"

#: lib/quotearg.c:355
msgid "'"
msgstr "’"

#: lib/xalloc-die.c:34
msgid "memory exhausted"
msgstr "記憶體耗盡"

#: src/ArgV.cc:107
msgid "option requires an argument"
msgstr "選項需要一個參數"

#: src/ArgV.cc:109 src/ArgV.cc:118
msgid "invalid option"
msgstr "選項無效"

#: src/ArgV.cc:114
#, c-format
msgid "option `%s' requires an argument"
msgstr "選項 ‘%s’ 需要一個參數"

#: src/ArgV.cc:116
#, c-format
msgid "unrecognized option `%s'"
msgstr "‘%s’ 選項無法辨識"

#: src/attach.h:138
#, c-format
msgid "[%u] Attached to terminal %s. %s\n"
msgstr "[%u] 已附著到終端機 %s。%s\n"

#: src/attach.h:150
#, c-format
msgid "[%u] Attached to terminal.\n"
msgstr "[%u] 已附著到終端機。\n"

#: src/buffer.cc:253 src/FileAccess.cc:866
msgid " [cached]"
msgstr "[已快取]"

#: src/ChmodJob.cc:80
#, c-format
msgid "Failed to change mode of `%s' to %04o (%s).\n"
msgstr "無法將‘%s’的存取權限變更為 %04o (%s)。\n"

#: src/ChmodJob.cc:83
#, c-format
msgid "Mode of `%s' changed to %04o (%s).\n"
msgstr "‘%s’的存取權限已變更為 %04o (%s)。\n"

#: src/ChmodJob.cc:88
#, c-format
msgid "Failed to change mode of `%s' because no old mode is available.\n"
msgstr "無法變更‘%s’的存取權限，因為無法獲取它本來的權限。\n"

#: src/CmdExec.cc:105
#, c-format
msgid "Warning: chdir(%s) failed: %s\n"
msgstr "警告：chdir(%s) 失敗：%s\n"

#: src/CmdExec.cc:207
#, c-format
msgid "Unknown command `%s'.\n"
msgstr "不明的指令 ‘%s’。\n"

#: src/CmdExec.cc:209
#, c-format
msgid "Ambiguous command `%s'.\n"
msgstr "指令 ‘%s’ 不明確。\n"

#: src/CmdExec.cc:228
#, c-format
msgid "Module for command `%s' did not register the command.\n"
msgstr "應該和指令 ‘%s’ 有關的模組根本沒有包含該指令在內。\n"

#: src/CmdExec.cc:384
#, c-format
msgid "cd ok, cwd=%s\n"
msgstr "cd 成功，目前的目錄為 %s\n"

#: src/CmdExec.cc:405 src/MirrorJob.cc:736
#, c-format
msgid "%s: received redirection to `%s'\n"
msgstr "%s：將輸出結果重新導向至 ‘%s’\n"

#: src/CmdExec.cc:408 src/FileCopy.cc:1230
msgid "Too many redirections"
msgstr "重新導向次數太多"

#: src/CmdExec.cc:517 src/CmdExec.cc:574
msgid "Interrupt"
msgstr "中斷"

#: src/CmdExec.cc:639
#, c-format
msgid "Warning: discarding incomplete command\n"
msgstr "警告：忽略不完整的指令\n"

#: src/CmdExec.cc:737
#, c-format
msgid "\tExecuting builtin `%s' [%s]\n"
msgstr "\t執行內建的 ‘%s’ [%s]\n"

#: src/CmdExec.cc:742
msgid "Queue is stopped."
msgstr "佇列已停止。"

#: src/CmdExec.cc:747
msgid "Now executing:"
msgstr "現在執行："

#: src/CmdExec.cc:758
#, c-format
msgid "\tWaiting for job [%d] to terminate\n"
msgstr "\t等待工作 [%d] 終止\n"

#: src/CmdExec.cc:761
#, c-format
msgid "\tWaiting for termination of jobs: "
msgstr "\t等待工作終止："

#: src/CmdExec.cc:770
msgid "\tRunning\n"
msgstr "\t正在執行\n"

#: src/CmdExec.cc:772
msgid "\tWaiting for command\n"
msgstr "\t等待輸入指令\n"

#: src/CmdExec.cc:1261
#, c-format
msgid "%s: command `%s' is not compiled in.\n"
msgstr "%s：編譯程式時沒有包括指令 ‘%s’ 的支援。\n"

#: src/CmdExec.cc:1278
#, c-format
msgid "Usage: %s cmd [args...]\n"
msgstr "用法：%s 指令 [參數……]\n"

#: src/CmdExec.cc:1284
#, c-format
msgid "%s: cannot create local session\n"
msgstr "%s：無法建立本機工作階段\n"

#: src/commands.cc:114
msgid "!<shell-command>"
msgstr "!<shell 指令>"

#: src/commands.cc:115
msgid "Launch shell or shell command\n"
msgstr "開啟 shell 或執行 shell 指令\n"

#: src/commands.cc:116
msgid "(commands)"
msgstr "(指令)"

#: src/commands.cc:117
msgid ""
"Group commands together to be executed as one command\n"
"You can launch such a group in background\n"
msgstr ""
"將多個指令組合成一個指令來執行\n"
"此組指令可在背景執行\n"

#: src/commands.cc:120
msgid "alias [<name> [<value>]]"
msgstr "alias [<名稱> [<變數值>]]"

#: src/commands.cc:121
msgid ""
"Define or undefine alias <name>. If <value> omitted,\n"
"the alias is undefined, else is takes the value <value>.\n"
"If no argument is given the current aliases are listed.\n"
msgstr ""
"建立或刪除 <名稱> 的定義。如果不填入 <變數值>，<名稱> 會沒有定義，否則\n"
"<名稱> 的值為 <變數值>。如果沒有任何參數則會列出所有別名。\n"

#: src/commands.cc:125
msgid "anon - login anonymously (by default)\n"
msgstr "anon - 以無名氏身份登入(預設)\n"

#: src/commands.cc:127
msgid "bookmark [SUBCMD]"
msgstr "bookmark [副指令]"

#: src/commands.cc:128
msgid ""
"bookmark command controls bookmarks\n"
"\n"
"The following subcommands are recognized:\n"
"  add <name> [<loc>] - add current place or given location to bookmarks\n"
"                       and bind to given name\n"
"  del <name>         - remove bookmark with the name\n"
"  edit               - start editor on bookmarks file\n"
"  import <type>      - import foreign bookmarks\n"
"  list               - list bookmarks (default)\n"
msgstr ""
"bookmark 指令可處理書籤\n"
"\n"
"可使用以下的副指令：\n"
"  add <名稱> [<位置>] - 在書籤加入目前的位置或指定的位置，\n"
"                        並將該位置命名為指定的名稱\n"
"  del <名稱>          - 刪除指定名稱的書籤\n"
"  edit                - 開啟編輯器編輯書籤檔\n"
"  import <類型>       - 匯入其它格式的書籤\n"
"  list                - 列出所有書籤 (預設操作)\n"

#: src/commands.cc:137
msgid "cache [SUBCMD]"
msgstr "cache [副指令]"

#: src/commands.cc:138
msgid ""
"cache command controls local memory cache\n"
"\n"
"The following subcommands are recognized:\n"
"  stat        - print cache status (default)\n"
"  on|off      - turn on/off caching\n"
"  flush       - flush cache\n"
"  size <lim>  - set memory limit\n"
"  expire <Nx> - set cache expiration time to N seconds (x=s)\n"
"                minutes (x=m) hours (x=h) or days (x=d)\n"
msgstr ""
"cache 指令可控制本機的快取記憶\n"
"\n"
"可使用以下的副指令：\n"
"  stat        - 顯示快取記憶的狀況 (預設操作)\n"
"  on|off      - 開啟/關閉快取記憶功能\n"
"  flush       - 清除快取記憶\n"
"  size <極限> - 設定快取記憶上限\n"
"  expire <Nx> - 設定快取記憶的有效期限為 N 秒 (x=s)、\n"
"                N 分鐘 (x=m)、N 小時 (x=h) 或 N 日 (x=d)\n"

#: src/commands.cc:146
msgid "cat [-b] <files>"
msgstr "cat [-b] <檔案>"

#: src/commands.cc:147
msgid ""
"cat - output remote files to stdout (can be redirected)\n"
" -b  use binary mode (ascii is the default)\n"
msgstr ""
"cat - 在標準輸出中顯示遠端檔案的內容 (可以重新導向至其它地方)\n"
" -b  使用 binary 模式 (預設為 ascii 模式)\n"

#: src/commands.cc:149
msgid "cd <rdir>"
msgstr "cd <遠端目錄>"

#: src/commands.cc:150
msgid ""
"Change current remote directory to <rdir>. The previous remote directory\n"
"is stored as `-'. You can do `cd -' to change the directory back.\n"
"The previous directory for each site is also stored on disk, so you can\n"
"do `open site; cd -' even after lftp restart.\n"
msgstr ""
"將目前的遠端目錄變更為 <rdir>。上一個遠端目錄會記錄為‘-’。可以輸入\n"
"‘cd -’返回上次的遠端目錄。離開 lftp 前每一個站台最後進入的目錄都會被\n"
"儲存，所以即使 lftp 重新啟動後仍然可以輸入‘open site; cd -’。\n"

#: src/commands.cc:154
msgid "chmod [OPTS] mode file..."
msgstr "chmod [選項] 模式 檔案……"

#: src/commands.cc:155
msgid ""
"Change the mode of each FILE to MODE.\n"
"\n"
" -c, --changes        - like verbose but report only when a change is made\n"
" -f, --quiet          - suppress most error messages\n"
" -v, --verbose        - output a diagnostic for every file processed\n"
" -R, --recursive      - change files and directories recursively\n"
"\n"
"MODE can be an octal number or symbolic mode (see chmod(1))\n"
msgstr ""
"變更每個 <檔案> 的存取權限 <模式>。\n"
"\n"
" -c, --changes        -  類似 --verbose，但只在變更資料時才顯示訊息\n"
" -f, --quiet          -  不顯示絕大部份的錯誤訊息\n"
" -v, --verbose        -  顯示每個已處理的檔案\n"
" -R, --recursive      -  同時變更目錄下的所有目錄層\n"
"\n"

#: src/commands.cc:164
msgid ""
"Close idle connections. By default only with current server.\n"
" -a  close idle connections with all servers\n"
msgstr ""
"關閉閒置的連線。預設只會關閉最後使用的伺服器連線。\n"
" -a  關閉所有閒置的伺服器連線\n"

#: src/commands.cc:166
msgid "[re]cls [opts] [path/][pattern]"
msgstr "[re]cle [選項] [路徑/][樣式]"

#: src/commands.cc:167
msgid ""
"List remote files. You can redirect output of this command to file\n"
"or via pipe to external command.\n"
"\n"
" -1                   - single-column output\n"
" -a, --all            - show dot files\n"
" -B, --basename       - show basename of files only\n"
"     --block-size=SIZ - use SIZ-byte blocks\n"
" -d, --directory      - list directory entries instead of contents\n"
" -F, --classify       - append indicator (one of /@) to entries\n"
" -h, --human-readable - print sizes in human readable format (e.g., 1K)\n"
"     --si             - likewise, but use powers of 1000 not 1024\n"
" -k, --kilobytes      - like --block-size=1024\n"
" -l, --long           - use a long listing format\n"
" -q, --quiet          - don't show status\n"
" -s, --size           - print size of each file\n"
"     --filesize       - if printing size, only print size for files\n"
" -i, --nocase         - case-insensitive pattern matching\n"
" -I, --sortnocase     - sort names case-insensitively\n"
" -D, --dirsfirst      - list directories first\n"
"     --sort=OPT       - \"name\", \"size\", \"date\"\n"
" -S                   - sort by file size\n"
" --user, --group, --perms, --date, --linkcount, --links\n"
"                      - show individual fields\n"
" --time-style=STYLE   - use specified time format\n"
"\n"
"By default, cls output is cached, to see new listing use `recls' or\n"
"`cache flush'.\n"
"\n"
"The variables cls-default and cls-completion-default can be used to\n"
"specify defaults for cls listings and completion listings, respectively.\n"
"For example, to make completion listings show file sizes, set\n"
"cls-completion-default to \"-s\".\n"
"\n"
"Tips: Use --filesize with -D to pack the listing better.  If you don't\n"
"always want to see file sizes, --filesize in cls-default will affect the\n"
"-s flag on the commandline as well.  Add `-i' to cls-completion-default\n"
"to make filename completion case-insensitive.\n"
msgstr ""
"列出遠端站台的檔案。本指令的輸出結果可以\n"
"重新導向至其它檔案或是經 pipe 導向至其它外部指令。\n"
"\n"
" -1                    -  每行只顯示一個名稱\n"
" -a, --all            - 顯示開頭為點的檔案\n"
" -B, --basename        -  只顯示檔案本身的名稱\n"
"     --block-size=SIZ  -  以 SIZ 位元組為單位\n"
" -d, --directory       -  顯示目錄本身的資料，而非列出目錄下的檔案\n"
" -F, --classify        -  在每個項目後加上符號 ( / 或 @ ) 表示檔案種類\n"
" -h, --human-readable  -  以容易理解的方式顯示檔案大小 (例如 1K)\n"
"     --si              -  類似 -h，但以 1000 的次方為單位，而非 1024\n"
" -k, --kilobytes       -  即 --block-size=1024\n"
" -l, --long            -  顯示檔案的詳細資料\n"
" -q, --quiet           -  不顯示狀態\n"
" -s, --size            -  顯示每個檔案的大小\n"
"     --filesize        -  顯示檔案大小時，不會顯示目錄及符號鏈結的大小\n"
" -i, --nocase          -  搜尋符合的樣式時不分辨大小寫\n"
" -I, --sortnocase      -  排序時不分辨大小寫\n"
" -D, --dirsfirst       -  先列出目錄\n"
"     --sort=OPT        -  OPT 可以是 “name”, “size” 或 “date”\n"
" -S                    -  依檔案大小排列\n"
" --user, --group, --perms, --date, --linkcount, --links\n"
"                       -  顯示個別的欄位\n"
" --time-style=STYLE    -  顯示時間時使用指定格式\n"
"\n"
"cls 的輸出預設會放在快取記憶中，如果要取得最新的檔案清單，請使用 ‘recls’ 或\n"
"‘cache flush’。\n"
"\n"
"變數 cls-default 和 cls-completion-default 分別用來指定 cls 在列出檔案和自動"
"補齊檔案名稱時的輸出格式。\n"
"例如想在自動補齊檔案名稱時顯示檔案大小，可將 cls-completion-default 設定為 “-"
"s”。\n"
"\n"
"提示：\n"
"將 --filesize 和 -D 選項配合使用會令清單的編排較緊密。\n"
"如果不需要留意目錄或符號鏈結的大小，請注意 cls-default 變數中加入\n"
"--filesize 選項會對指令列中的 -s 選項有影響。如果在 cls-completion-default\n"
"中加上 ‘-i’ 選項可在進行自動補齊時不分辨大小寫。\n"

#: src/commands.cc:217
msgid "debug [OPTS] [<level>|off]"
msgstr "debug [選項] [<level>|off]"

#: src/commands.cc:218
msgid ""
"Set debug level to given value or turn debug off completely.\n"
" -o <file>  redirect debug output to the file\n"
" -c  show message context\n"
" -p  show PID\n"
" -t  show timestamps\n"
msgstr ""
"設定 debug level 為指定的數值或完全關閉偵錯功能。\n"
" -o <檔案>  將偵錯輸出的結果重新導向至該檔案。\n"
" -c  顯示訊息內容\n"
" -p  顯示 PID\n"
" -t  顯示時間戳\n"

#: src/commands.cc:223
msgid "du [options] <dirs>"
msgstr "du [選項] <目錄>"

#: src/commands.cc:224
msgid ""
"Summarize disk usage.\n"
" -a, --all             write counts for all files, not just directories\n"
"     --block-size=SIZ  use SIZ-byte blocks\n"
" -b, --bytes           print size in bytes\n"
" -c, --total           produce a grand total\n"
" -d, --max-depth=N     print the total for a directory (or file, with --"
"all)\n"
"                       only if it is N or fewer levels below the command\n"
"                       line argument;  --max-depth=0 is the same as\n"
"                       --summarize\n"
" -F, --files           print number of files instead of sizes\n"
" -h, --human-readable  print sizes in human readable format (e.g., 1K 234M "
"2G)\n"
" -H, --si              likewise, but use powers of 1000 not 1024\n"
" -k, --kilobytes       like --block-size=1024\n"
" -m, --megabytes       like --block-size=1048576\n"
" -S, --separate-dirs   do not include size of subdirectories\n"
" -s, --summarize       display only a total for each argument\n"
"     --exclude=PAT     exclude files that match PAT\n"
msgstr ""
"列出磁碟的使用量。\n"
" -a, --all             除了所有目錄外，同時列出所有檔案的大小\n"
"     --block-size=SIZ  以 SIZ 位元組為單位\n"
" -b, --bytes           以位元組為單位顯示\n"
" -c, --total           顯示所有項目大小的總和\n"
" -d, --max-depth=N     只列出目前目錄下 N 層或少於 N 層的子目錄的磁碟使用量\n"
"                       （如果配合 --all 選項，也顯示檔案的磁碟使用量）；\n"
"                       --max-depth=0 的效果和 --summarize 一樣\n"
" -F, --files           印出檔案數目，而不是檔案的大小\n"
" -h, --human-readable  以容易明白的方式顯示大小（例如 1K、234M、2G）\n"
" -H, --si              和 -h 一樣，但以 1000 的次方為單位，而非 1024\n"
" -k, --kilobytes       等於 --block-size=1024\n"
" -m, --megabytes       等於 --block-size=1048576\n"
" -S, --separate-dirs   不包括子目錄的大小\n"
" -s, --summarize       只分別顯示指令列每個項目的總大小\n"
"     --exclude=PAT     不計算符合 PAT 樣式的檔案在內\n"

#: src/commands.cc:242
msgid "edit [OPTS] <file>"
msgstr "edit [選項] <檔案>"

#: src/commands.cc:243
msgid ""
"Retrieve remote file to a temporary location, run a local editor on it\n"
"and upload the file back if changed.\n"
" -k  keep the temporary file\n"
" -o <temp>  explicit temporary file location\n"
msgstr ""
"將遠端檔案擷取到暫時位置，在其上執行本機編輯器\n"
"並在它變更時上傳檔案回去。\n"
" -k  保持暫存檔案\n"
" -o <暫存位置>  明確指定暫存檔案位置\n"

#: src/commands.cc:248
msgid "exit [<code>|bg]"
msgstr "exit [<回傳碼>|bg]"

#: src/commands.cc:249
msgid ""
"exit - exit from lftp or move to background if jobs are active\n"
"\n"
"If no jobs active, the code is passed to operating system as lftp\n"
"termination status. If omitted, exit code of last command is used.\n"
"`bg' forces moving to background if cmd:move-background is false.\n"
msgstr ""
"exit - 離開 lftp，或在仍然有工作進行的情形下進入背景作業\n"
"\n"
"如果沒有正在進行的工作，<回傳碼> 會成為 lftp 在作業系統中的結束狀態。\n"
"如果不指定回傳碼，就會使用最後一個指令的回傳碼。\n"
"‘bg’在 cmd:move-background 設定為 false 時會強迫進入背景作業。\n"

#: src/commands.cc:255
msgid ""
"Usage: find [OPTS] [directory]\n"
"Print contents of specified directory or current directory recursively.\n"
"Directories in the list are marked with trailing slash.\n"
"You can redirect output of this command.\n"
" -d, --maxdepth=LEVELS  Descend at most LEVELS of directories.\n"
msgstr ""
"用法：find [選項] [目錄]\n"
"顯示某個指定目錄 (或者目前的目錄) 及所有子目錄的內容。在輸出結果中，\n"
"所有目錄後都會加上一個 ‘/’ 標記。\n"
"這個指令的輸出結果可以重新導向至其它地方。\n"
" -d, --maxdepth=層數  最多只進入指定層數的目錄來搜尋檔案。\n"

#: src/commands.cc:260
msgid "get [OPTS] <rfile> [-o <lfile>]"
msgstr "get [選項] <遠端檔案> [-o <本機檔案>]"

#: src/commands.cc:261
msgid ""
"Retrieve remote file <rfile> and store it to local file <lfile>.\n"
" -o <lfile> specifies local file name (default - basename of rfile)\n"
" -c  continue, resume transfer\n"
" -E  delete remote files after successful transfer\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"下載遠端檔案 <rfile> 並儲存為本機檔案 <lfile>。\n"
" -o <檔案> 指定本機檔案的名稱（預設為 rfile 中的檔案名稱）\n"
" -c  繼續、恢復下載\n"
" -E  下載成功後刪除遠端檔案\n"
" -a  使用 ascii 模式（預設為 binary 模式）\n"
" -O <base> 指定下載後的檔案放置的目錄或 URL\n"

#: src/commands.cc:268
msgid "glob [OPTS] <cmd> <args>"
msgstr "glob [選項] <指令> <參數>"

#: src/commands.cc:270
msgid ""
"Expand wildcards and run specified command.\n"
"Options can be used to expand wildcards to list of files, directories,\n"
"or both types. Type selection is not very reliable and depends on server.\n"
"If entry type cannot be determined, it will be included in the list.\n"
" -f  plain files (default)\n"
" -d  directories\n"
" -a  all types\n"
" --exist      return zero exit code when the patterns expand to non-empty "
"list\n"
" --not-exist  return zero exit code when the patterns expand to an empty "
"list\n"
msgstr ""
"展開萬用字元並執行指定的指令。\n"
"可使用選項來指明萬用字元會展開成檔案清單、目錄清單還是兩者皆包括在內。\n"
"這種展開萬用字元的方式，會依不同伺服器而有所不同。\n"
"如果無法決定某個名稱屬於何種類型，它就會自動被加入至清單中。\n"
" -f  普通檔案 (預設)\n"
" -d  目錄\n"
" -a  所有類型\n"
" --exist      當樣式被展開為非空清單時，返回狀態碼 0\n"
" --not-exist  當樣式被展開為空清單時，返回狀態碼 0\n"

#: src/commands.cc:279
msgid "help [<cmd>]"
msgstr "help [<指令>]"

#: src/commands.cc:280
msgid "Print help for command <cmd>, or list of available commands\n"
msgstr "印出 <指令> 的說明文字，或列出所有可用的指令\n"

#: src/commands.cc:282
msgid ""
"List running jobs. -v means verbose, several -v can be specified.\n"
"If <job_no> is specified, only list a job with that number.\n"
msgstr ""
"列出正在執行的工作。-v 表示輸出結果會更詳細，可使用多於一個 -v 來累積效果。\n"
"若指定了 <工作號碼>，則只有該號碼的工作會被列出。\n"

#: src/commands.cc:284
msgid "kill all|<job_no>"
msgstr "kill all|<工作編號>"

#: src/commands.cc:285
msgid "Delete specified job with <job_no> or all jobs\n"
msgstr "終止由 <工作編號> 指定的工作或所有工作\n"

#: src/commands.cc:286
msgid "lcd <ldir>"
msgstr "lcd <目錄>"

#: src/commands.cc:287
msgid ""
"Change current local directory to <ldir>. The previous local directory\n"
"is stored as `-'. You can do `lcd -' to change the directory back.\n"
msgstr ""
"將目前的本機目錄變更為 <目錄>。上一個本機目錄會記錄為‘-’。\n"
"可以輸入‘lcd -’返回上次的本機目錄。\n"

#: src/commands.cc:289
msgid "lftp [OPTS] <site>"
msgstr "lftp [選項] <站台>"

#: src/commands.cc:290
msgid ""
"`lftp' is the first command executed by lftp after rc files\n"
" -f <file>           execute commands from the file and exit\n"
" -c <cmd>            execute the commands and exit\n"
" --norc              don't execute rc files from the home directory\n"
" --help              print this help and exit\n"
" --version           print lftp version and exit\n"
"Other options are the same as in `open' command:\n"
" -e <cmd>            execute the command just after selecting\n"
" -u <user>[,<pass>]  use the user/password for authentication\n"
" -p <port>           use the port for connection\n"
" -s <slot>           assign the connection to this slot\n"
" -d                  switch on debugging mode\n"
" <site>              host name, URL or bookmark name\n"
msgstr ""
"‘lftp’是讀入設定檔後第一個 lftp 會執行的指令\n"
" -f <檔案>               執行在 <檔案> 裡指定的指令並離開\n"
" -c <指令>               執行指令並離開\n"
" --norc              不要從家目錄執行設定檔\n"
" --help                  印出此項說明文字並離開\n"
" --version               印出 lftp 版本並離開\n"
"其它選項和‘open’指令的一樣：\n"
" -e <指令>               選擇檔案執行所指定的指令\n"
" -u <用戶名稱>[,<密碼>]  使用用戶名稱和密碼登入\n"
" -p <連接埠>             使用該連接埠連線\n"
" -s <插槽>           將連線指派給此插槽\n"
" -d                  開啟除錯模式\n"
" <站台>                  主機名稱、URL 或書籤名稱\n"

#: src/commands.cc:303
msgid "ln [-s] <file1> <file2>"
msgstr "ln [-s] <檔案1> <檔案2>"

#: src/commands.cc:304
msgid "Link <file1> to <file2>\n"
msgstr "將 <檔案1> 連結至 <檔案2>\n"

#: src/commands.cc:308
msgid "ls [<args>]"
msgstr "ls [<參數>]"

#: src/commands.cc:309
msgid ""
"List remote files. You can redirect output of this command to file\n"
"or via pipe to external command.\n"
"By default, ls output is cached, to see new listing use `rels' or\n"
"`cache flush'.\n"
"See also `help cls'.\n"
msgstr ""
"列出遠端的檔案。可以將此指令的輸出重新導向至檔案或經 pipe 導向至外部指令。\n"
"預設 ls 的輸出是放進快取記憶中的，若想看最新的清單內容請使用‘rels’或\n"
"‘cache flush’。\n"
"請參考‘help cls’。\n"

#: src/commands.cc:314
msgid "mget [OPTS] <files>"
msgstr "mget [選項] <檔案>"

#: src/commands.cc:315
msgid ""
"Gets selected files with expanded wildcards\n"
" -c  continue, resume transfer\n"
" -d  create directories the same as in file names and get the\n"
"     files into them instead of current directory\n"
" -E  delete remote files after successful transfer\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"展開萬用字元後，下載所選擇的檔案\n"
" -c        繼續、恢復下載\n"
" -d        建立與遠端結構相同的目錄層，並將原本應該下載\n"
"           至目前本機目錄的檔案改為下載至該目錄層中\n"
" -E        下載成功後刪除遠端的檔案\n"
" -a        使用 ascii 模式（預設為 binary 模式）\n"
" -O <base> 指定下載後的檔案所儲存的目錄或 URL\n"

#: src/commands.cc:322
msgid "mirror [OPTS] [remote [local]]"
msgstr "mirror [選項] [遠端目錄 [本機目錄]]"

#: src/commands.cc:323
msgid "mkdir [OPTS] <dirs>"
msgstr "mkdir [選項] <目錄>"

#: src/commands.cc:324
msgid ""
"Make remote directories\n"
" -p  make all levels of path\n"
" -f  be quiet, suppress messages\n"
msgstr ""
"建立遠端目錄\n"
" -p  建立路徑中的每一層目錄\n"
" -f 安靜，抑制訊息\n"

#: src/commands.cc:327
msgid "module name [args]"
msgstr "module 名稱 [參數]"

#: src/commands.cc:328
msgid ""
"Load module (shared object). The module should contain function\n"
"   void module_init(int argc,const char *const *argv)\n"
"If name contains a slash, then the module is searched in current\n"
"directory, otherwise in directories specified by setting module:path.\n"
msgstr ""
"載入模組 (共用目的檔)。此模組應有以下的函式︰\n"
"   void module_init(int argc,const char *const *argv)\n"
"如果名稱中含有‘/’，則會在目前的目錄中搜尋模組，\n"
"否則會在 module:path 設定值所指定的目錄中搜尋。\n"

#: src/commands.cc:332
msgid "more <files>"
msgstr "more <檔案>"

#: src/commands.cc:333
msgid "Same as `cat <files> | more'. if PAGER is set, it is used as filter\n"
msgstr "即‘cat <檔案> | more’。如果定義了 PAGER 變數，會使用它來過濾內容\n"

#: src/commands.cc:334
msgid "mput [OPTS] <files>"
msgstr "mput [選項] <檔案>"

#: src/commands.cc:335
msgid ""
"Upload files with wildcard expansion\n"
" -c  continue, reput\n"
" -d  create directories the same as in file names and put the\n"
"     files into them instead of current directory\n"
" -E  delete local files after successful transfer (dangerous)\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"展開萬用字元後，上傳所選擇的檔案\n"
" -c         繼續上傳 (即 reput)\n"
" -d         在遠端建立與本機結構相同的目錄層，並將原本應該\n"
"            上傳至目前目錄的檔案改為上傳至該目錄層中\n"
" -E         成功上傳後刪除本機的檔案 (危險)\n"
" -a         使用 ascii 模式 (預設為 binary 模式)\n"
" -O <base>  設定上傳後的檔案放置的目錄或 URL\n"

#: src/commands.cc:342
msgid "mrm <files>"
msgstr "mrm <檔案>"

#: src/commands.cc:343
msgid "Removes specified files with wildcard expansion\n"
msgstr "展開萬用字元後，刪除指定的檔案\n"

#: src/commands.cc:344
msgid "mv <file1> <file2>"
msgstr "mv <檔案1> <檔案2>"

#: src/commands.cc:345
msgid "Rename <file1> to <file2>\n"
msgstr "將 <檔案1> 重新命名為 <檔案2>\n"

#: src/commands.cc:346
msgid "mmv [OPTS] <files> <target-dir>"
msgstr "mmv [選項] <檔案> <目標目錄>"

#: src/commands.cc:347
msgid ""
"Move <files> to <target-directory> with wildcard expansion\n"
" -O <dir>  specifies the target directory (alternative way)\n"
msgstr ""
"以萬用字元擴充來移動 <檔案> 到 <目標目錄>\n"
"-O <目錄> 指定目標目錄（替代方式）\n"

#: src/commands.cc:349
msgid "[re]nlist [<args>]"
msgstr "[re]nlist [<參數>]"

#: src/commands.cc:350
msgid ""
"List remote file names.\n"
"By default, nlist output is cached, to see new listing use `renlist' or\n"
"`cache flush'.\n"
msgstr ""
"列出遠端的檔案。\n"
"預設 nlist 的輸出結果是放進快取記憶中的，若要獲取最新的檔案清單請用\n"
"‘renlist’ 或 ‘cache flush’。\n"

#: src/commands.cc:353
msgid "open [OPTS] <site>"
msgstr "open [選項] <站台>"

#: src/commands.cc:354
msgid ""
"Select a server, URL or bookmark\n"
" -e <cmd>            execute the command just after selecting\n"
" -u <user>[,<pass>]  use the user/password for authentication\n"
" -p <port>           use the port for connection\n"
" -s <slot>           assign the connection to this slot\n"
" -d                  switch on debugging mode\n"
" <site>              host name, URL or bookmark name\n"
msgstr ""
"選擇伺服器、URL 或書籤\n"
" -e <指令>               選擇後立刻執行 <指令>\n"
" -u <用戶名稱>[,<密碼>]  使用指定的用戶名稱及密碼登入\n"
" -p <連接埠>             使用指定連接埠來連線\n"
" -s <位置>           將連線分配給該位置\n"
" -d                  開啟除錯模式e\n"
" <站台>                  主機名稱、URL 或書籤名稱\n"

#: src/commands.cc:361
msgid "pget [OPTS] <rfile> [-o <lfile>]"
msgstr "pget [選項] <遠端檔> [-o <本機檔>]"

#: src/commands.cc:362
msgid ""
"Gets the specified file using several connections. This can speed up "
"transfer,\n"
"but loads the net heavily impacting other users. Use only if you really\n"
"have to transfer the file ASAP.\n"
"\n"
"Options:\n"
" -c  continue transfer. Requires <lfile>.lftp-pget-status file.\n"
" -n <maxconn>  set maximum number of connections (default is is taken from\n"
"     pget:default-n setting)\n"
" -O <base> specifies base directory where files should be placed\n"
msgstr ""
"使用數個連線同時下載指定的檔案。這樣能加快傳送速度，但因過度佔用頻寬，會影響"
"別的用戶。除非有必要盡最快速度下載檔案，否則可能會遭人投訴呢。\n"
"\n"
"選項：\n"
" -c  續傳，<lfile>.lftp-pget-status 檔案必須已經存在\n"
" -n <maxconn>  設定最大的連線數目 (預設為 pget:default-n 中的數目)\n"
" -O <base> 指定下載的檔案應存取在哪個目錄\n"

#: src/commands.cc:370
msgid "put [OPTS] <lfile> [-o <rfile>]"
msgstr "put [選項] <本機檔案> [-o <遠端檔案>]"

#: src/commands.cc:371
msgid ""
"Upload <lfile> with remote name <rfile>.\n"
" -o <rfile> specifies remote file name (default - basename of lfile)\n"
" -c  continue, reput\n"
"     it requires permission to overwrite remote files\n"
" -E  delete local files after successful transfer (dangerous)\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"上傳 <本機檔> 並將上傳後的檔案命名為 <遠端檔>。\n"
" -o <遠端檔>  指定遠端檔案名稱 (預設為本機檔案名稱)\n"
" -c           繼續 (即 reput)\n"
"              此選項需要有覆蓋檔案的權限\n"
" -E           上傳成功後刪除本機檔案 (危險)\n"
" -a           使用 ascii 模式 (預設為 binary 模式)\n"
" -O <base>    指定上傳後的檔案放置的目錄或 URL\n"

#: src/commands.cc:379
msgid ""
"Print current remote URL.\n"
" -p  show password\n"
msgstr ""
"顯示目前的遠端 URL。\n"
" -p  顯示密碼\n"

#: src/commands.cc:381
msgid "queue [OPTS] [<cmd>]"
msgstr "queue [選項] [<指令>]"

#: src/commands.cc:382
msgid ""
"\n"
"       queue [-n num] <command>\n"
"\n"
"Add the command to queue for current site. Each site has its own command\n"
"queue. `-n' adds the command before the given item in the queue. It is\n"
"possible to queue up a running job by using command `queue wait <jobno>'.\n"
"\n"
"       queue --delete|-d [index or wildcard expression]\n"
"\n"
"Delete one or more items from the queue. If no argument is given, the last\n"
"entry in the queue is deleted.\n"
"\n"
"       queue --move|-m <index or wildcard expression> [index]\n"
"\n"
"Move the given items before the given queue index, or to the end if no\n"
"destination is given.\n"
"\n"
"Options:\n"
" -q                  Be quiet.\n"
" -v                  Be verbose.\n"
" -Q                  Output in a format that can be used to re-queue.\n"
"                     Useful with --delete.\n"
msgstr ""
"\n"
"       queue [-n 數目] <指令>\n"
"\n"
"將指令加入目前站台的指令佇列中。每一個站台都可以有它自己的指令佇列。\n"
"‘-n’ 會將指令插入佇列中的指定位置。也可以使用指令 ‘queue wait <工作編號>’\n"
"將執行中的工作加入佇列。\n"
"\n"
"       queue --delete|-d [索引或萬用字元表示式]\n"
"\n"
"由佇列中刪除一個或多個項目。如果沒有指定參數，則會刪除佇列的最後一個項目。\n"
"\n"
"       queue --move|-m <索引或萬用字元表示式> [索引]\n"
"\n"
"將項目移至指定的佇列位置。沒有指定 [索引] 位置的話，會移至佇列的末端。\n"
"\n"
"選項：\n"
" -q                  不輸出任何訊息。\n"
" -v                  顯示詳細資訊。\n"
" -Q                  變更輸出的格式，方便將指令重新加入佇列中。\n"
"                     和 --delete 配合時比較有用。\n"

#: src/commands.cc:403
msgid "quote <cmd>"
msgstr "quote <指令>"

#: src/commands.cc:404
msgid ""
"Send the command uninterpreted. Use with caution - it can lead to\n"
"unknown remote state and thus will cause reconnect. You cannot\n"
"be sure that any change of remote state because of quoted command\n"
"is solid - it can be reset by reconnect at any time.\n"
msgstr ""
"不加解譯地送出指令。請小心使用 — 它有可能令遠端站台進入無法預計的狀態並使\n"
"之重新連線。因該種指令而導致的連線狀態改變不一定可靠 — 連線狀態隨時可能因\n"
"為重新連線而重設。\n"

#: src/commands.cc:409
msgid ""
"recls [<args>]\n"
"Same as `cls', but don't look in cache\n"
msgstr ""
"recls [<參數>]\n"
"即 ‘cls’，但不讀入快取記憶的資料\n"

#: src/commands.cc:412
msgid ""
"Usage: reget [OPTS] <rfile> [-o <lfile>]\n"
"Same as `get -c'\n"
msgstr ""
"用法：reget [選項] <遠端檔> [-o <本機檔>]\n"
"等於 ‘get -c’\n"

#: src/commands.cc:415
msgid ""
"Usage: rels [<args>]\n"
"Same as `ls', but don't look in cache\n"
msgstr ""
"用法：rels [<參數>]\n"
"即 ‘ls’，但不讀入快取記憶的資料\n"

#: src/commands.cc:418
msgid ""
"Usage: renlist [<args>]\n"
"Same as `nlist', but don't look in cache\n"
msgstr ""
"用法：renlist [<參數>]\n"
"即 ‘nlist’，但不讀入快取記憶的資料\n"

#: src/commands.cc:420
msgid "repeat [OPTS] [delay] [command]"
msgstr "repeat [選項] [延遲秒數] [指令]"

#: src/commands.cc:422
msgid ""
"Usage: reput <lfile> [-o <rfile>]\n"
"Same as `put -c'\n"
msgstr ""
"reput <本機檔案> [-o <遠端檔案>]\n"
"即 ‘put -c’\n"

#: src/commands.cc:424
msgid "rm [-r] [-f] <files>"
msgstr "rm [-r] [-f] <檔案>"

#: src/commands.cc:425
msgid ""
"Remove remote files\n"
" -r  recursive directory removal, be careful\n"
" -f  work quietly\n"
msgstr ""
"刪除遠端檔案\n"
" -r  同時刪除所有子目錄，請小心使用\n"
" -f  不輸出任何結果\n"

#: src/commands.cc:428
msgid "rmdir [-f] <dirs>"
msgstr "rmdir [-f] <目錄>"

#: src/commands.cc:429
msgid "Remove remote directories\n"
msgstr "刪除遠端目錄\n"

#: src/commands.cc:430
msgid "scache [<session_no>]"
msgstr "scache [<作業階段代號>]"

#: src/commands.cc:431
msgid "List cached sessions or switch to specified session number\n"
msgstr "列出所有快取記憶中的作業階段或切換至指定的作業階段代號\n"

#: src/commands.cc:432
msgid "set [OPT] [<var> [<val>]]"
msgstr "set [選項] [<變數名稱> [<變數值>]]"

#: src/commands.cc:433
msgid ""
"Set variable to given value. If the value is omitted, unset the variable.\n"
"Variable name has format ``name/closure'', where closure can specify\n"
"exact application of the setting. See lftp(1) for details.\n"
"If set is called with no variable then only altered settings are listed.\n"
"It can be changed by options:\n"
" -a  list all settings, including default values\n"
" -d  list only default values, not necessary current ones\n"
msgstr ""
"設定變數為指定的變數值。如果沒有變數值，則會刪除該變數。\n"
"變數名稱的格式是 ‘名稱/closure’，這裡 closure 是指該變數適用的站台。\n"
"詳細資料請參考 lftp(1)。\n"
"如果 set 後面沒有指明任何變數則只會列出所有曾被修改的設定值。\n"
"可使用以下的選項：\n"
" -a  列出所有設定值，包括預設的在內\n"
" -d  只列出預設的設定值，未必符合目前的設定\n"

#: src/commands.cc:441
msgid "site <site-cmd>"
msgstr "site <站台指令>"

#: src/commands.cc:442
msgid ""
"Execute site command <site_cmd> and output the result\n"
"You can redirect its output\n"
msgstr ""
"執行 site 指令 <站台指令> 並輸出結果\n"
"可以將輸出結果重新導向至其它地方\n"

#: src/commands.cc:446
msgid ""
"Usage: slot [<label>]\n"
"List assigned slots.\n"
"If <label> is specified, switch to the slot named <label>.\n"
msgstr ""
"用法：slot [<標籤>]\n"
"列出已分配的位置。\n"
"若 <標籤> 已被指定，切換到名為 <標籤> 的位置。\n"

#: src/commands.cc:449
msgid "source <file>"
msgstr "source <檔案>"

#: src/commands.cc:450
msgid "Execute commands recorded in file <file>\n"
msgstr "執行記錄在 <檔案> 內的指令\n"

#: src/commands.cc:452
msgid "torrent [OPTS] <file|URL>..."
msgstr "torrent [選項] <檔案|URL>"

#: src/commands.cc:453
msgid "user <user|URL> [<pass>]"
msgstr "user <用戶名稱|URL> [<密碼>]"

#: src/commands.cc:454
msgid ""
"Use specified info for remote login. If you specify URL, the password\n"
"will be cached for future usage.\n"
msgstr "使用指定的資料登入。如果指定了 URL，密碼將會被儲存以便將來使用。\n"

#: src/commands.cc:457
msgid "Shows lftp version\n"
msgstr "顯示 lftp 版本\n"

#: src/commands.cc:458
msgid "wait [<jobno>]"
msgstr "wait [<工作編號>]"

#: src/commands.cc:459
msgid ""
"Wait for specified job to terminate. If jobno is omitted, wait\n"
"for last backgrounded job.\n"
msgstr ""
"等待指定的工作終止。如果沒有指定 <工作編號>，則等待最後一個背景工作完成。\n"

#: src/commands.cc:461
msgid "zcat <files>"
msgstr "zcat <檔案>"

#: src/commands.cc:462
msgid "Same as cat, but filter each file through zcat\n"
msgstr "用途和 cat 一樣，但每個檔案會經 zcat 過濾\n"

#: src/commands.cc:463
msgid "zmore <files>"
msgstr "zmore <檔案>"

#: src/commands.cc:464
msgid "Same as more, but filter each file through zcat\n"
msgstr "用途和 more 一樣，但每個檔案會經 zcat 過濾\n"

#: src/commands.cc:466
msgid "Same as cat, but filter each file through bzcat\n"
msgstr "用途和 cat 一樣，但每個檔案會經 bzcat 過濾\n"

#: src/commands.cc:468
msgid "Same as more, but filter each file through bzcat\n"
msgstr "用途和 more 一樣，但每個檔案會經 bzcat 過濾\n"

#: src/commands.cc:524
#, c-format
msgid "Usage: %s local-dir\n"
msgstr "用法：%s 本機目錄\n"

#: src/commands.cc:560
#, c-format
msgid "lcd ok, local cwd=%s\n"
msgstr "lcd 成功，本機目錄為 %s\n"

#: src/commands.cc:576
#, c-format
msgid "Usage: cd remote-dir\n"
msgstr "用法：cd 遠端目錄\n"

#: src/commands.cc:588
#, c-format
msgid "%s: no old directory for this site\n"
msgstr "%s：沒有關於此站台以往進入的目錄的紀錄\n"

#: src/commands.cc:677
#, c-format
msgid "Usage: %s [<exit_code>]\n"
msgstr "用法：%s [<回傳碼>]\n"

#: src/commands.cc:686
msgid ""
"There are running jobs and `cmd:move-background' is not set.\n"
"Use `exit bg' to force moving to background or `kill all' to terminate "
"jobs.\n"
msgstr ""
"目前仍然有未完成的工作，而且沒有設定 ‘cmd:move-background’ 變數。\n"
"用 ‘exit bg’ 將所有工作轉移至背景繼續執行，或 ‘kill all’ 停止所有工作。\n"

#: src/commands.cc:703
msgid ""
"\n"
"lftp now tricks the shell to move it to background process group.\n"
"lftp continues to run in the background despite the `Stopped' message.\n"
"lftp will automatically terminate when all jobs are finished.\n"
"Use `fg' shell command to return to lftp if it is still running.\n"
msgstr ""
"\n"
"lftp 現在做了一些事情讓 shell 將它移動到背景行程群組。\n"
"lftp 即便接收到「已停止」的訊息也會繼續在背景執行。\n"
"lftp 將會自動在所有工作都結束時關閉自己。\n"
"使用「fg」 shell 指令以將 lftp 拉回前景，如果它還在執行中的話。\n"

#: src/commands.cc:837 src/commands.cc:3616
#, c-format
msgid "Try `%s --help' for more information\n"
msgstr "請輸入 ‘%s --help’ 顯示額外資訊\n"

#: src/commands.cc:856
#, c-format
msgid "%s: -c, -f, -v, -h conflict with other `open' options and arguments\n"
msgstr "%s: -c, -f, -v, -h 與其他「開啟」選項與參數衝突\n"

#: src/commands.cc:956
#, c-format
msgid "Usage: %s [-e cmd] [-p port] [-u user[,pass]] <host|url>\n"
msgstr "用法：%s [-e 指令] [-p 連接埠] [-u 用戶名稱[,密碼]] <主機名稱|URL>\n"

#: src/commands.cc:1046 src/commands.cc:2276 src/DummyProto.cc:65
#: src/MirrorJob.cc:2172 src/MirrorJob.cc:2194
msgid " - not supported protocol"
msgstr " - 不支援該種協定"

#: src/commands.cc:1078 src/commands.cc:2260
msgid "Password: "
msgstr "密碼："

#: src/commands.cc:1080
#, c-format
msgid "%s: GetPass() failed -- assume anonymous login\n"
msgstr "%s：GetPass() 錯誤 -- 假設是無名氏身份登入\n"

#: src/commands.cc:1191 src/commands.cc:1284 src/commands.cc:1660
#: src/commands.cc:1691 src/commands.cc:1829 src/commands.cc:1914
#: src/commands.cc:2187 src/commands.cc:2381 src/commands.cc:2543
#: src/commands.cc:2588 src/commands.cc:2616 src/commands.cc:2623
#: src/commands.cc:2930 src/commands.cc:2957 src/commands.cc:2964
#: src/commands.cc:3293 src/lftp.cc:267 src/MirrorJob.cc:2136
#: src/SleepJob.cc:144 src/SleepJob.cc:200 src/Torrent.cc:4169
#, c-format
msgid "Try `help %s' for more information.\n"
msgstr "請輸入 ‘help %s’ 顯示額外資訊。\n"

#: src/commands.cc:1201
#, c-format
msgid "Usage: %s [OPTS] command args...\n"
msgstr "用法：%s [選項] 指令 參數……\n"

#: src/commands.cc:1254
#, c-format
msgid "%s: -n: positive number expected. "
msgstr "%s：-n：應該使用正整數。 "

# (Abel) 這句譯起來可能有問題
#: src/commands.cc:1306
#, c-format
msgid "Created a stopped queue.\n"
msgstr "產生了一個停止狀態的佇列。\n"

#: src/commands.cc:1349 src/commands.cc:1377
#, c-format
msgid "%s: No queue is active.\n"
msgstr "%s：沒有任何佇列正在運作。\n"

#: src/commands.cc:1369
#, c-format
msgid "%s: -m: Number expected as second argument. "
msgstr "%s：-m：第二個參數應該是數字。 "

#: src/commands.cc:1421
#, c-format
msgid "Usage: %s <cmd>\n"
msgstr "用法：%s <指令>\n"

#: src/commands.cc:1527
msgid "invalid argument for `--sort'"
msgstr "‘--sort’的參數無效"

#: src/commands.cc:1557
msgid "invalid block size"
msgstr "無效的區段大小"

#: src/commands.cc:1700
#, c-format
msgid "Usage: %s [OPTS] files...\n"
msgstr "用法：%s [選項] 檔案……\n"

#: src/commands.cc:1785 src/commands.cc:1814
#, c-format
msgid "%s: %s: Number expected. "
msgstr "%s：%s：應該使用數字。 "

#: src/commands.cc:1834
#, c-format
msgid "%s: --continue conflicts with --remove-target.\n"
msgstr "%s：--continue 與 --remove-target 衝突。\n"

#: src/commands.cc:1844 src/commands.cc:1920
#, c-format
msgid "File name missed. "
msgstr "缺少了檔案名稱。 "

#: src/commands.cc:1989
#, c-format
msgid "Usage: %s %s[-f] files...\n"
msgstr "用法：%s %s[-f] 檔案……\n"

#: src/commands.cc:2032
#, c-format
msgid "Usage: %s [-e] <file|command>\n"
msgstr "用法：%s [-e] <檔案|指令>\n"

#: src/commands.cc:2079
#, c-format
msgid "Usage: %s [-v] [-v] ...\n"
msgstr "用法：%s [-v] [-v] ……\n"

#: src/commands.cc:2093 src/commands.cc:2347 src/commands.cc:2469
#: src/commands.cc:2685 src/commands.cc:3101 src/commands.cc:3182
#: src/lftp.cc:279
#, c-format
msgid "%s: %s - not a number\n"
msgstr "%s：%s - 不是數字\n"

#: src/commands.cc:2100 src/commands.cc:2326 src/commands.cc:2356
#: src/commands.cc:2487
#, c-format
msgid "%s: %d - no such job\n"
msgstr "%s：%d - 指定的工作編號不存在\n"

#: src/commands.cc:2135
#, c-format
msgid "Usage: %s [-p]\n"
msgstr "用法：%s [-p]\n"

#: src/commands.cc:2227
#, c-format
msgid "debug level is %d, output goes to %s\n"
msgstr "偵錯輸出詳細程度為 %d，輸出至 %s\n"

#: src/commands.cc:2230
#, c-format
msgid "debug is off\n"
msgstr "關閉偵錯輸出\n"

#: src/commands.cc:2241
#, c-format
msgid "Usage: %s <user|URL> [<pass>]\n"
msgstr "用法：%s <用戶名稱|URL> [<密碼>]\n"

#: src/commands.cc:2316 src/commands.cc:2479
#, c-format
msgid "%s: no current job\n"
msgstr "%s：目前沒有任何工作\n"

#: src/commands.cc:2328
#, c-format
msgid "Usage: %s <jobno> ... | all\n"
msgstr "用法：%s <工作編號> …… | all\n"

#: src/commands.cc:2409
#, c-format
msgid "%s: %s. Use `set -a' to look at all variables.\n"
msgstr "%s：%s。請輸入‘set -a’列出所有變數。\n"

#: src/commands.cc:2453
#, c-format
msgid "Usage: %s [<jobno>]\n"
msgstr "用法：%s [<工作編號>]\n"

#: src/commands.cc:2492
#, c-format
msgid "%s: some other job waits for job %d\n"
msgstr "%s：有其它工作正在等待工作 %d 完成\n"

#: src/commands.cc:2497
#, c-format
msgid "%s: wait loop detected\n"
msgstr "%s：某項工作正在等待自己完成 (迴圈)\n"

#: src/commands.cc:2553
#, c-format
msgid "Usage: %s [OPTS] <files> <target-dir>\n"
msgstr "用法：%s [選項] <檔案> <目標目錄>\n"

#: src/commands.cc:2615 src/commands.cc:2956
#, c-format
msgid "Invalid command. "
msgstr "指令無效。 "

#: src/commands.cc:2622 src/commands.cc:2963
#, c-format
msgid "Ambiguous command. "
msgstr "指令不明確。 "

#: src/commands.cc:2641
#, c-format
msgid "%s: Operand missed for size\n"
msgstr "%s：size 副指令缺少了參數\n"

#: src/commands.cc:2658
#, c-format
msgid "%s: Operand missed for `expire'\n"
msgstr "%s：‘expire’副指令缺少了參數\n"

#: src/commands.cc:2691
#, c-format
msgid ""
"%s: %s - no such cached session. Use `scache' to look at session list.\n"
msgstr "%s：%s - 指定的作業階段不存在。請使用‘scache’列出作業階段的清單。\n"

#: src/commands.cc:2715
#, c-format
msgid "Sorry, no help for %s\n"
msgstr "對不起，沒有有關 %s 的說明文字\n"

#: src/commands.cc:2720
#, c-format
msgid "%s is a built-in alias for %s\n"
msgstr "%s 是 %s 的內建別名\n"

#: src/commands.cc:2725
#, c-format
msgid "Usage: %s\n"
msgstr "用法：%s\n"

#: src/commands.cc:2733
#, c-format
msgid "%s is an alias to `%s'\n"
msgstr "%s 是 ‘%s’ 的別名\n"

#: src/commands.cc:2737
#, c-format
msgid "No such command `%s'. Use `help' to see available commands.\n"
msgstr "沒有 ‘%s’ 這個指令。請輸入 ‘help’ 列出可使用的指令。\n"

#: src/commands.cc:2739
#, c-format
msgid "Ambiguous command `%s'. Use `help' to see available commands.\n"
msgstr "指令 ‘%s’ 不明確。請輸入 ‘help’ 列出可使用的指令。\n"

#: src/commands.cc:2805
#, c-format
msgid "LFTP | Version %s | Copyright (c) 1996-%d Alexander V. Lukyanov\n"
msgstr "LFTP | %s 版本 | 版權所有 (c) 1996-%d Alexander V. Lukyanov\n"

#: src/commands.cc:2808
#, c-format
msgid ""
"LFTP is free software: you can redistribute it and/or modify\n"
"it under the terms of the GNU General Public License as published by\n"
"the Free Software Foundation, either version 3 of the License, or\n"
"(at your option) any later version.\n"
"\n"
"This program is distributed in the hope that it will be useful,\n"
"but WITHOUT ANY WARRANTY; without even the implied warranty of\n"
"MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n"
"GNU General Public License for more details.\n"
"\n"
"You should have received a copy of the GNU General Public License\n"
"along with LFTP.  If not, see <http://www.gnu.org/licenses/>.\n"
msgstr ""
"LFTP 是自由軟體：您可以在 GNU 通用公共授權條款下\n"
"轉散發及／或修改它，GNU 通用公共授權條款是由\n"
"自由軟體基金會所發佈，您可以選擇使用第三版，\n"
"或是任何更新版本。\n"
"\n"
"發佈這個程式的目的是希望它有用，\n"
"但並無任何擔保；無適用性或\n"
"對特定用途的可用性擔保。\n"
"見 GNU 通用公共授權條款以取得更多資訊。\n"
"\n"
"您應該已經隨 LFTP 一起收到一份 GNU 通用公共授權條款\n"
"的拷貝。若沒有，見 <http://www.gnu.org/licenses/>。\n"

#: src/commands.cc:2822
#, c-format
msgid "Send bug reports and questions to the mailing list <%s>.\n"
msgstr "請將錯誤報告及問題寄至 <%s> 郵件清單。\n"

#: src/commands.cc:2828
msgid "Libraries used: "
msgstr "本程式使用的函式庫："

#: src/commands.cc:2979 src/commands.cc:3007
#, c-format
msgid "%s: bookmark name required\n"
msgstr "%s：需要書籤名稱\n"

#: src/commands.cc:2996
#, c-format
msgid "%s: spaces in bookmark name are not allowed\n"
msgstr "%s：書籤名稱不可有空格\n"

#: src/commands.cc:3009
#, c-format
msgid "%s: no such bookmark `%s'\n"
msgstr "%s：沒有‘%s’這個書籤\n"

#: src/commands.cc:3032
#, c-format
msgid "%s: import type required (netscape,ncftp)\n"
msgstr "%s：需要指定匯入的方式 (netscape,ncftp)\n"

#: src/commands.cc:3110
#, c-format
msgid "Usage: %s [-d #] dir\n"
msgstr "用法：%s [-d 數字] 目錄\n"

#: src/commands.cc:3215
#, c-format
msgid "%s: invalid block size `%s'\n"
msgstr "%s：區段大小‘%s’無效\n"

#: src/commands.cc:3226
#, c-format
msgid "Usage: %s [options] <dirs>\n"
msgstr "用法：%s [選項] <目錄>\n"

#: src/commands.cc:3232
#, c-format
msgid "%s: warning: summarizing is the same as using --max-depth=0\n"
msgstr "%s：警告：summarizing 和 --max-depth=0 是一樣的\n"

#: src/commands.cc:3236
#, c-format
msgid "%s: summarizing conflicts with --max-depth=%i\n"
msgstr "%s：summarizing 和 --max-depth=%i 選項互相矛盾\n"

#: src/commands.cc:3280
#, c-format
msgid "Usage: %s command args...\n"
msgstr "用法：%s 指令 參數……\n"

#: src/commands.cc:3292
#, c-format
msgid "Usage: %s module [args...]\n"
msgstr "用法：%s 模組 [參數……]\n"

#: src/commands.cc:3310 src/LocalAccess.cc:642
msgid "cannot get current directory"
msgstr "無法決定目前的目錄"

#: src/commands.cc:3374
#, c-format
msgid "Usage: %s [OPTS] mode file...\n"
msgstr "用法：%s [選項] 模式 檔案……\n"

#: src/commands.cc:3394
#, c-format
msgid "invalid mode string: %s\n"
msgstr "無效的檔案模式字串：%s\n"

#: src/commands.cc:3457 src/commands.cc:3466
msgid "Invalid range format. Format is min-max, e.g. 10-20."
msgstr "範圍格式無效。格式是 min-max，例如 10-20。"

#: src/commands.cc:3478
#, c-format
msgid "Usage: %s [OPTS] file\n"
msgstr "用法：%s [選項] 檔案\n"

#: src/CopyJob.cc:82
#, c-format
msgid "`%s' at %lld %s%s%s%s"
msgstr "‘%s’ 位置 %lld %s%s%s%s"

#: src/CopyJob.cc:159
#, c-format
msgid "%lld $#ll#byte|bytes$ transferred in %ld $#l#second|seconds$"
msgstr "於 %2$ld 秒內傳送了 %1$lld 位元組"

#: src/CopyJob.cc:167
#, c-format
msgid "%lld $#ll#byte|bytes$ transferred"
msgstr "傳送了 %lld 位元組"

#: src/CopyJob.cc:283
#, c-format
msgid "Transfer of %d of %d $file|files$ failed\n"
msgstr "傳送 %2$d 個檔案時有 %1$d 個失敗了\n"

#: src/CopyJob.cc:289
#, c-format
msgid "Total %d $file|files$ transferred\n"
msgstr "共傳送了 %d 個檔案\n"

#: src/FileAccess.cc:160
msgid "Access failed: "
msgstr "存取失敗： "

#: src/FileAccess.cc:161
msgid "File cannot be accessed"
msgstr "無法存取檔案"

#: src/FileAccess.cc:163 src/Fish.cc:982 src/ftpclass.cc:4599
#: src/ftpclass.cc:4608 src/SFtp.cc:1339 src/Torrent.cc:3533
msgid "Not connected"
msgstr "並未連線"

#: src/FileAccess.cc:166 src/FileAccess.cc:167
msgid "Fatal error"
msgstr "嚴重錯誤"

#: src/FileAccess.cc:169
msgid "Store failed - you have to reput"
msgstr "儲存失敗 - 需要重新上傳 (reput)"

#: src/FileAccess.cc:172 src/FileAccess.cc:173
msgid "Login failed"
msgstr "登入錯誤"

#: src/FileAccess.cc:176 src/FileAccess.cc:177
msgid "Operation not supported"
msgstr "不支援此項操作"

#: src/FileAccess.cc:180
msgid "File moved"
msgstr "檔案已移動"

#: src/FileAccess.cc:182
msgid "File moved to `"
msgstr "檔案被移動至 ‘"

#: src/FileCopy.cc:141
msgid "copy: destination file is already complete\n"
msgstr "複製：目的地檔案早已完成下載\n"

#: src/FileCopy.cc:182
msgid "copy: put is broken\n"
msgstr "複製：put 無法正常運作\n"

#: src/FileCopy.cc:201
msgid "seek failed"
msgstr "搜尋失敗"

#: src/FileCopy.cc:207
msgid "no progress timeout"
msgstr "無進度逾時"

#: src/FileCopy.cc:235
msgid "cannot seek on data source"
msgstr "無法搜尋資料來源"

#: src/FileCopy.cc:238
#, c-format
msgid "copy: put rolled back to %lld, seeking get accordingly\n"
msgstr "複製：寫出的位置回捲至 %lld，調整讀入的位置\n"

#: src/FileCopy.cc:251
msgid "copy: all data received, but get rolled back\n"
msgstr "複製：已收到所有資料，但回捲至檔案末端的位置\n"

#: src/FileCopy.cc:267
#, c-format
msgid "copy: get rolled back to %lld, seeking put accordingly\n"
msgstr "複製：讀入的位置回捲至 %lld, 調整寫出的位置\n"

#: src/FileCopy.cc:364
msgid "file size decreased during transfer"
msgstr "在傳輸時，檔案大小減少了"

#: src/FileCopy.cc:1227
#, c-format
msgid "copy: received redirection to `%s'\n"
msgstr "複製：重新導向至‘%s’\n"

#: src/FileCopy.cc:1290
#, fuzzy
#| msgid "file size decreased during transfer"
msgid "file size increased during transfer"
msgstr "在傳輸時，檔案大小減少了"

#: src/FileCopy.cc:1390
msgid "file name missed in URL"
msgstr "URL 裡缺少了檔案名稱"

#: src/FileCopy.cc:2086
msgid "Verify command failed without a message"
msgstr "確認指令失敗，但沒有錯誤訊息"

#: src/FileCopyFtp.cc:95
msgid "**** FXP: trying to reverse ftp:fxp-passive-source\n"
msgstr "**** FXP：嘗試切換 ftp:fxp-passive-source 設定值\n"

#: src/FileCopyFtp.cc:102
msgid "**** FXP: trying to reverse ftp:fxp-passive-sscn\n"
msgstr "**** FXP：嘗試切換 ftp:fxp-passive-sscn 設定值\n"

#: src/FileCopyFtp.cc:110
msgid "**** FXP: trying to reverse ftp:ssl-protect-fxp\n"
msgstr "**** FXP：嘗試切換 ftp:ssl-protect-fxp 設定值\n"

#: src/FileCopyFtp.cc:116
msgid "**** FXP: giving up, reverting to plain copy\n"
msgstr "**** FXP：放棄，使用普通方式複製\n"

#: src/FileCopyFtp.cc:125
msgid "ftp:fxp-force is set but FXP is not available"
msgstr "設定了 ftp:fxp-force 變數，但無法進行 FXP 連線"

#: src/FileCopy.h:285
msgid "Verifying..."
msgstr "確認中……"

#: src/FileSetOutput.cc:230
msgid "non-option arguments found"
msgstr "出現並非選項的參數"

#: src/Filter.cc:166
msgid "pipe() failed: "
msgstr "pipe() 失敗："

#: src/Filter.cc:200 src/PtyShell.cc:135
#, c-format
msgid "chdir(%s) failed: %s\n"
msgstr "chdir(%s) 失敗：%s\n"

#: src/Filter.cc:208
#, c-format
msgid "execvp(%s) failed: %s\n"
msgstr "execvp(%s) 失敗：%s\n"

#: src/Filter.cc:213 src/PtyShell.cc:147
#, c-format
msgid "execl(/bin/sh) failed: %s\n"
msgstr "execl(/bin/sh) 失敗：%s\n"

#: src/Filter.cc:415
msgid "file already exists and xfer:clobber is unset"
msgstr "檔案早已存在，而且沒有設定 xfer:clobber 變數"

#: src/FindJobDu.cc:101
msgid "total"
msgstr "總數"

#: src/Fish.cc:75 src/ftpclass.cc:1292 src/Http.cc:1232 src/SFtp.cc:77
#, c-format
msgid "Closing idle connection"
msgstr "關閉閒置連線"

#: src/Fish.cc:162 src/SFtp.cc:177
msgid "Running connect program"
msgstr "正在執行進行連線的程式"

#: src/Fish.cc:588 src/ftpclass.cc:2887 src/ftpclass.cc:3107 src/Http.cc:1510
#: src/SFtp.cc:742 src/SFtp.cc:1083 src/SFtp.cc:1084 src/SSH_Access.cc:167
#, c-format
msgid "Peer closed connection"
msgstr "對方關閉了連線"

#: src/Fish.cc:634 src/ftpclass.cc:3037 src/ftpclass.cc:4219 src/SFtp.cc:1108
#, c-format
msgid "extra server response"
msgstr "額外的伺服器回應"

#: src/Fish.cc:987 src/ftpclass.cc:4611 src/Http.cc:2329 src/Http.cc:2336
#: src/SFtp.cc:1345 src/Torrent.cc:3536 src/TorrentTracker.cc:624
msgid "Connecting..."
msgstr "嘗試連線……"

#: src/Fish.cc:989 src/ftpclass.cc:4617 src/SFtp.cc:1347
msgid "Connected"
msgstr "成功連線"

#: src/Fish.cc:991 src/ftpclass.cc:4594 src/ftpclass.cc:4622
#: src/ftpclass.cc:4636 src/Http.cc:2338 src/SFtp.cc:1349
#: src/TorrentTracker.cc:626
msgid "Waiting for response..."
msgstr "正在等待回應……"

#: src/Fish.cc:993 src/ftpclass.cc:4656 src/Http.cc:2341 src/SFtp.cc:1351
msgid "Receiving data"
msgstr "下載中"

#: src/Fish.cc:995 src/ftpclass.cc:4654 src/Http.cc:2334 src/SFtp.cc:1353
msgid "Sending data"
msgstr "上傳中"

#: src/Fish.cc:997 src/SFtp.cc:1355
msgid "Done"
msgstr "完成"

#: src/Fish.cc:1136 src/FtpDirList.cc:123 src/HttpDir.cc:1384 src/SFtp.cc:2200
#: src/SFtp.cc:2320
#, c-format
msgid "Getting file list (%lld) [%s]"
msgstr "正在擷取檔案清單 (%lld) [%s]"

#: src/ftpclass.cc:197
#, c-format
msgid "Data connection peer has wrong port number"
msgstr "對方使用了錯誤的連接埠號碼"

#: src/ftpclass.cc:203
#, c-format
msgid "Data connection peer has mismatching address"
msgstr "對方使用了不匹配的地址"

#: src/ftpclass.cc:225 src/ftpclass.cc:250
#, c-format
msgid "Switching to NOREST mode"
msgstr "切換至 NOREST 模式"

#: src/ftpclass.cc:425
#, c-format
msgid "Server reply matched ftp:retry-530, retrying"
msgstr "伺服器回應為 ftp:retry-530，會再嘗試"

#: src/ftpclass.cc:433
#, c-format
msgid "Server reply matched ftp:retry-530-anonymous, retrying"
msgstr "伺服器回應為 ftp:retry-530-anonymous，會再嘗試"

#: src/ftpclass.cc:466
msgid "Account is required, set ftp:acct variable"
msgstr "需要用戶戶口，請設定 ftp:acct 變數"

#: src/ftpclass.cc:483
msgid "ftp:skey-force is set and server does not support OPIE nor S/KEY"
msgstr "設定了 ftp:skey-force 但伺服器不支援 OPIE 或 S/KEY"

#: src/ftpclass.cc:501
#, c-format
msgid "assuming failed host name lookup"
msgstr "假設查看主機名稱已失敗"

#: src/ftpclass.cc:809 src/ftpclass.cc:810
#, c-format
msgid "cannot parse EPSV response"
msgstr "無法分析 EPSV 回應內容"

#: src/ftpclass.cc:837 src/ftpclass.cc:838
#, c-format
msgid "cannot parse custom EPSV response"
msgstr "無法解析自訂的 EPSV 回應內容"

#: src/ftpclass.cc:1122
#, c-format
msgid "Closing control socket"
msgstr "正在關閉控制 socket"

#: src/ftpclass.cc:1341
msgid "MLSD is disabled by ftp:use-mlsd"
msgstr "ftp:use-mlsd 設定關閉了 MLSD 功能"

#: src/ftpclass.cc:1411 src/Http.cc:1431 src/Torrent.cc:3204
#: src/Torrent.cc:3743 src/TorrentTracker.cc:395
#, c-format
msgid "cannot create socket of address family %d"
msgstr "無法建立地址族群 %d 的 socket"

#: src/ftpclass.cc:1442 src/Http.cc:1457
#, c-format
msgid "Socket error (%s) - reconnecting"
msgstr "Socket 錯誤 (%s) - 準備重新連線"

#: src/ftpclass.cc:1715
msgid "MFF and SITE CHMOD are not supported by this site"
msgstr "該站台不支援 MFF 與 SITE CHMOD"

#: src/ftpclass.cc:1720
msgid "MLST and MLSD are not supported by this site"
msgstr "該站台不支援 MLST 和 MLSD"

#: src/ftpclass.cc:2031
msgid "SITE SYMLINK is not supported by the server"
msgstr "伺服器不支援 SITE SYMLINK"

#: src/ftpclass.cc:2204
msgid "unsupported network protocol"
msgstr "不支援這種網路協定"

#: src/ftpclass.cc:2253 src/ftpclass.cc:2355
#, c-format
msgid "Data socket error (%s) - reconnecting"
msgstr "資料 socket 錯誤 (%s) - 正在重新連線"

#: src/ftpclass.cc:2281
#, c-format
msgid "Accepted data connection from (%s) port %u"
msgstr "已接受自 (%s) 而來，埠號 %u 的資料連線"

#: src/ftpclass.cc:2330
#, c-format
msgid "Connecting data socket to (%s) port %u"
msgstr "連接資料 socket 到 (%s)，埠號為 %u"

#: src/ftpclass.cc:2336
#, c-format
msgid "Connecting data socket to proxy %s (%s) port %u"
msgstr "連接資料 socket 到 %s (%s)，埠號為 %u"

#: src/ftpclass.cc:2358 src/ftpclass.cc:4414
#, c-format
msgid "Switching passive mode off"
msgstr "關閉 passive 模式"

#: src/ftpclass.cc:2366
#, c-format
msgid "Data connection established"
msgstr "成功建立數據連線"

#: src/ftpclass.cc:2688 src/ftpclass.cc:4548
msgid "ftp:ssl-force is set and server does not support or allow SSL"
msgstr "已設定 ftp:ssl-force 但伺服器不支援/不使用 SSL"

#: src/ftpclass.cc:3053
#, c-format
msgid "Persist and retry"
msgstr "繼續重試"

#: src/ftpclass.cc:3321
#, c-format
msgid "Closing data socket"
msgstr "正在關閉資料 socket"

#: src/ftpclass.cc:3343
#, c-format
msgid "Closing aborted data socket"
msgstr "關閉被中止的數據連線"

#: src/ftpclass.cc:4193
#, c-format
msgid "saw file size in response"
msgstr "回應中包括檔案的大小"

#: src/ftpclass.cc:4233 src/ftpclass.cc:4260
#, c-format
msgid "Turning on sync-mode"
msgstr "開啟 sync-mode"

#: src/ftpclass.cc:4434
#, c-format
msgid "Switching passive mode on"
msgstr "開啟 passive 模式"

#: src/ftpclass.cc:4585
msgid "FEAT negotiation..."
msgstr "支援功能 (FEAT) 協商……"

#: src/ftpclass.cc:4592
msgid "Sending commands..."
msgstr "正在送出指令……"

#: src/ftpclass.cc:4596
msgid "Delaying before retry"
msgstr "重新嘗試之前稍為延遲"

#: src/ftpclass.cc:4597 src/Http.cc:2331
msgid "Connection idle"
msgstr "連線閒置"

#: src/ftpclass.cc:4604 src/Http.cc:2323 src/Resolver.cc:172
#: src/Resolver.cc:217 src/TorrentTracker.cc:622
#, c-format
msgid "Resolving host address..."
msgstr "正在解析主機地址……"

#: src/ftpclass.cc:4615
msgid "TLS negotiation..."
msgstr "使用 TLS 協定通訊……"

#: src/ftpclass.cc:4619
msgid "Logging in..."
msgstr "正在登入……"

#: src/ftpclass.cc:4623
msgid "Making data connection..."
msgstr "正在建立資料傳送連線……"

#: src/ftpclass.cc:4626
msgid "Changing remote directory..."
msgstr "變更遠端目錄……"

#: src/ftpclass.cc:4632
msgid "Waiting for other copy peer..."
msgstr "等待複製程序的遠端回應……"

#: src/ftpclass.cc:4634 src/ftpclass.cc:4658
msgid "Waiting for transfer to complete"
msgstr "等待傳送完成"

#: src/ftpclass.cc:4638
msgid "Waiting for TLS shutdown..."
msgstr "正在等待 TLS 關閉……"

#: src/ftpclass.cc:4640
msgid "Waiting for data connection..."
msgstr "等待建立資料傳送連線……"

#: src/ftpclass.cc:4646
msgid "Sending data/TLS"
msgstr "上傳中/TLS"

#: src/ftpclass.cc:4648
msgid "Receiving data/TLS"
msgstr "下載中/TLS"

#: src/Http.cc:230
#, c-format
msgid "Closing HTTP connection"
msgstr "正在關閉 HTTP 連線"

#: src/Http.cc:240
msgid "POST method failed"
msgstr "使用 POST 方法失敗"

#: src/Http.cc:1381
msgid "ftp over http cannot work without proxy, set hftp:proxy."
msgstr ""
"若沒有代理伺服器，是不可能在 http 連線上使用 ftp 的，請設定 hftp:proxy。"

#: src/Http.cc:1537
#, c-format
msgid "Sending request..."
msgstr "正在送出要求……"

#: src/Http.cc:1575
#, c-format
msgid "Hit EOF while fetching headers"
msgstr "讀取標頭時檔案已終結了"

#: src/Http.cc:1695
#, c-format
msgid "Could not parse HTTP status line"
msgstr "無法分析 HTTP 狀態"

#: src/Http.cc:1726
msgid "Object is not cached and http:cache-control has only-if-cached"
msgstr "快取記憶中沒有該物件，但 http:cache-control 的設定為 only-if-cached"

#: src/Http.cc:1891
#, c-format
msgid "Receiving body..."
msgstr "正在下載內容……"

#: src/Http.cc:2092
#, c-format
msgid "Hit EOF"
msgstr "檔案終結"

#: src/Http.cc:2095
#, c-format
msgid "Received not enough data, retrying"
msgstr "接收的資料不足，繼續嘗試"

#: src/Http.cc:2106
#, c-format
msgid "Received all"
msgstr "已收到所有資料"

#: src/Http.cc:2111
#, c-format
msgid "Received all (total)"
msgstr "已收到所有資料"

#: src/Http.cc:2135 src/Http.cc:2159
msgid "chunked format violated"
msgstr "資料格式錯誤"

#: src/Http.cc:2145
#, c-format
msgid "Received last chunk"
msgstr "已收到最後的片段"

#: src/Http.cc:2339
msgid "Fetching headers..."
msgstr "正在讀取標頭資料……"

#: src/Job.cc:293
#, c-format
msgid "[%d] Done (%s)"
msgstr "[%d] 完成 (%s)"

#: src/lftp.cc:370
#, c-format
msgid "[%u] Terminated by signal %d. %s\n"
msgstr "[%u] 被以訊號 %d 終止。%s\n"

#: src/lftp.cc:445
#, c-format
msgid "[%u] Started.  %s\n"
msgstr "[%u] 已開始。%s\n"

#: src/lftp.cc:463
#, c-format
msgid "[%u] Detaching from the terminal to complete transfers...\n"
msgstr "[%u] 正在從終端機脫離以完成傳輸...\n"

#: src/lftp.cc:465
#, c-format
msgid "[%u] Exiting and detaching from the terminal.\n"
msgstr "[%u] 正在結束並從終端機脫離。\n"

#: src/lftp.cc:470
#, c-format
msgid "[%u] Detached from terminal. %s\n"
msgstr "[%u] 已從終端機脫離。%s\n"

#: src/lftp.cc:480
#, c-format
msgid "[%u] Finished. %s\n"
msgstr "[%u] 已結束。%s\n"

#: src/lftp.cc:484
#, c-format
msgid "[%u] Moving to background to complete transfers...\n"
msgstr "[%u] 正在轉移至背景以完成傳輸...\n"

#: src/lftp.cc:553
msgid "history -w file|-r file|-c|-l [cnt]"
msgstr "history -w 檔案|-r 檔案|-c|-l [數目]"

#: src/lftp.cc:554
msgid ""
" -w <file> Write history to file.\n"
" -r <file> Read history from file; appends to current history.\n"
" -c  Clear the history.\n"
" -l  List the history (default).\n"
"Optional argument cnt specifies the number of history lines to list,\n"
"or \"all\" to list all entries.\n"
msgstr ""
" -w <檔案>   將指令紀錄寫入至指定檔案。\n"
" -r <檔案>   由指定檔案讀入指令紀錄；會將結果附加至目前的指令紀錄。\n"
" -c          清除指令紀錄。\n"
" -l          列出指令紀錄 (預設操作)。\n"
"選擇性的選項 <數目> 可指定準備列出的指令紀錄行數，\n"
"或者用 “all” 來指定所有條目。\n"

#: src/lftp.cc:561
msgid "Attach the terminal to specified backgrounded lftp process.\n"
msgstr "將終端機附著在指定的背景 lftp 行程上。\n"

#: src/lftp_ssl.cc:1291
#, c-format
msgid "No certificate presented by %s.\n"
msgstr "沒有由 %s 提供的憑證。\n"

#: src/LocalAccess.cc:604 src/NetAccess.cc:686
msgid "Getting directory contents"
msgstr "正在擷取目錄內容"

#: src/LocalAccess.cc:606 src/NetAccess.cc:690
msgid "Getting files information"
msgstr "正在擷取檔案資訊"

#: src/LsCache.cc:190
#, c-format
msgid "%ld $#l#byte|bytes$ cached"
msgstr "快取記憶內有 %ld 位元組"

#: src/LsCache.cc:194
msgid ", no size limit"
msgstr "，沒有大小限制"

#: src/LsCache.cc:196
#, c-format
msgid ", maximum size %ld\n"
msgstr "，最大為 %ld\n"

#: src/mgetJob.cc:78
#, c-format
msgid "%s: %s: no files found\n"
msgstr "%s：%s：找不到檔案\n"

#: src/MirrorJob.cc:100
#, c-format
msgid "%sTotal: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
msgstr "%s總數︰%d 個目錄，%d 個檔案，%d 個符號鏈結\n"

#: src/MirrorJob.cc:104
#, c-format
msgid "%sNew: %d file$|s$, %d symlink$|s$\n"
msgstr "%s建立︰%d 個檔案，%d 個符號鏈結\n"

#: src/MirrorJob.cc:108
#, c-format
msgid "%sModified: %d file$|s$, %d symlink$|s$\n"
msgstr "%s修改︰%d 個檔案，%d 個符號鏈結\n"

#: src/MirrorJob.cc:115
#, c-format
msgid "%sRemoved: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
msgstr "%s刪除︰%d 個目錄，%d 個檔案，%d 個符號鏈結\n"

#: src/MirrorJob.cc:120
#, c-format
msgid "%s%d error$|s$ detected\n"
msgstr "%s發現了 %d 個錯誤\n"

#: src/MirrorJob.cc:231
#, c-format
msgid "Finished %s"
msgstr "%s 已完成"

#: src/MirrorJob.cc:344 src/MirrorJob.cc:519 src/MirrorJob.cc:1218
#, c-format
msgid "Removing old file `%s'"
msgstr "刪除舊的檔案 ‘%s’"

#: src/MirrorJob.cc:346
#, c-format
msgid "Overwriting old file `%s'"
msgstr "覆寫舊的檔案 ‘%s’"

#: src/MirrorJob.cc:355
#, c-format
msgid "Skipping file `%s' (only-existing)"
msgstr "正在跳過檔案‘%s’（唯一存在）"

#: src/MirrorJob.cc:361
#, c-format
msgid "Transferring file `%s'"
msgstr "正在上傳檔案 ‘%s’"

#: src/MirrorJob.cc:439
#, c-format
msgid "Skipping directory `%s' (only-existing)"
msgstr "正在跳過 ‘%s’ 目錄（唯一存在）"

#: src/MirrorJob.cc:461 src/MirrorJob.cc:550 src/MirrorJob.cc:902
#, c-format
msgid "Removing old local file `%s'"
msgstr "刪除舊的本機檔案 ‘%s’"

#: src/MirrorJob.cc:487
#, c-format
msgid "Scanning directory `%s'"
msgstr "正在掃描 ‘%s’ 目錄"

#: src/MirrorJob.cc:489
#, c-format
msgid "Mirroring directory `%s'"
msgstr "正在 mirror ‘%s’ 目錄"

#: src/MirrorJob.cc:525 src/MirrorJob.cc:567
#, c-format
msgid "Making symbolic link `%s' to `%s'"
msgstr "建立符號鏈結 ‘%s’ 到 ‘%s’"

#: src/MirrorJob.cc:562
#, c-format
msgid "Skipping symlink `%s' (only-existing)"
msgstr "正在跳過 ‘%s’ 符號連結（唯一存在）"

#: src/MirrorJob.cc:807
#, c-format
msgid "mirror: protocol `%s' is not suitable for mirror\n"
msgstr "mirror：協定 ‘%s’ 不適合作為 mirror 用途\n"

#: src/MirrorJob.cc:923
#, c-format
msgid "Making directory `%s'"
msgstr "正在建立 ‘%s’ 目錄"

#: src/MirrorJob.cc:1181
#, c-format
msgid "Old directory `%s' is not removed"
msgstr "沒有刪除 ‘%s’ 舊目錄"

#: src/MirrorJob.cc:1183
#, c-format
msgid "Old file `%s' is not removed"
msgstr "沒有刪除舊檔案 ‘%s’"

#: src/MirrorJob.cc:1216
#, c-format
msgid "Removing old directory `%s'"
msgstr "刪除 ‘%s’ 舊目錄"

#: src/MirrorJob.cc:1339
#, c-format
msgid "Removing source directory `%s'"
msgstr "正在移除 ‘%s’ 來源目錄"

#: src/MirrorJob.cc:1405
#, c-format
msgid "Removing source file `%s'"
msgstr "刪除來源檔案 ‘%s’"

#: src/MirrorJob.cc:1425
#, c-format
msgid "Retrying mirror...\n"
msgstr "重新嘗試 mirror……\n"

#: src/MirrorJob.cc:1694
msgid "pattern is empty"
msgstr ""

#: src/MirrorJob.cc:1779
#, c-format
msgid "%s must be one of: %s"
msgstr "%s 必須為以下之一：%s"

#: src/MirrorJob.cc:2019
#, c-format
msgid ""
"%s: multiple --file or --directory options must have the same base "
"directory\n"
msgstr "%s：多個 --file 或 --directory 選項必須有相同的基礎資料夾\n"

#: src/MirrorJob.cc:2160
#, c-format
msgid "%s: ambiguous source directory (`%s' or `%s'?)\n"
msgstr "%s：不明確的來源目錄（`%s' 或 `%s'？）\n"

#: src/MirrorJob.cc:2182
#, c-format
msgid "%s: ambiguous target directory (`%s' or `%s'?)\n"
msgstr "%s：不明確的目標目錄（`%s' 或 `%s'？）\n"

#: src/MirrorJob.cc:2223
#, c-format
msgid "%s: source directory is required (mirror:require-source is set)\n"
msgstr "%s：需要來源目錄（鏡像：已設定 require-source）\n"

#: src/MirrorJob.cc:2343
msgid ""
"\n"
"Mirror specified remote directory to local directory\n"
"\n"
" -R, --reverse          reverse mirror (put files)\n"
"Lots of other options are documented in the man page lftp(1).\n"
"\n"
"When using -R, the first directory is local and the second is remote.\n"
"If the second directory is omitted, basename of the first directory is "
"used.\n"
"If both directories are omitted, current local and remote directories are "
"used.\n"
"\n"
"See the man page lftp(1) for a complete documentation.\n"
msgstr ""
"\n"
"將指定的遠端目錄鏡像至本機目錄\n"
"\n"
" -R, --reverse          將鏡像順序反過來（放置檔案）\n"
"許多其他選項都在 lftp(1) 手冊頁中有詳細的文件。\n"
"\n"
"當使用 -R 時，第一個目錄是本機目錄，而第二個目錄為遠端目錄。\n"
"若第二個目錄被忽略，將會使用第一個目錄的基礎名稱。\n"
"若兩個目錄都被忽略，將會使用目前的本機與遠端目錄。\n"
"\n"
"檢視 lftp(1) 手冊頁來取得完整的文件。\n"

#: src/mkdirJob.cc:128
#, c-format
msgid "%s ok, `%s' created\n"
msgstr "%s 成功，建立了‘%s’\n"

#: src/mkdirJob.cc:130 src/rmJob.cc:51
#, c-format
msgid "%s failed for %d of %d director$y|ies$\n"
msgstr "在 %3$d 個目錄中的其中 %2$d 個無法進行 %1$s\n"

#: src/mkdirJob.cc:133
#, c-format
msgid "%s ok, %d director$y|ies$ created\n"
msgstr "%s 成功，建立了 %d 個目錄\n"

#: src/module.cc:190
#, c-format
msgid "depend module `%s': %s\n"
msgstr "需要模組‘%s’：%s\n"

#: src/module.cc:210
msgid "modules are not supported on this system"
msgstr "此系統不支援模組"

#: src/mvJob.cc:92
#, c-format
msgid "rename successful\n"
msgstr "成功變更名稱\n"

#: src/NetAccess.cc:168
#, c-format
msgid "Connecting to %s%s (%s) port %u"
msgstr "連接至 %s%s (%s) 連接埠 %u"

#: src/NetAccess.cc:224 src/Torrent.cc:3326
#, c-format
msgid "Timeout - reconnecting"
msgstr "逾時 - 重新連線"

#: src/NetAccess.cc:323
msgid "Connection limit reached"
msgstr "到達連線數目的上限"

#: src/NetAccess.cc:330
msgid "Delaying before reconnect"
msgstr "重新連接前延遲(秒)"

#: src/NetAccess.cc:354 src/NetAccess.cc:356
msgid "max-retries exceeded"
msgstr "超過 max-retries 上限"

#: src/OutputJob.cc:145
#, c-format
msgid "%s (filter)"
msgstr "%s (filter)"

#: src/parsecmd.cc:290
msgid "parse: missing filter command\n"
msgstr "分析：缺少了過濾器的指令\n"

#: src/parsecmd.cc:292
msgid "parse: missing redirection filename\n"
msgstr "分析：缺少了重新導向的檔案名稱\n"

#: src/PatternSet.cc:110
#, c-format
msgid "regular expression `%s': %s"
msgstr "正規表示式 ‘%s’：%s"

#: src/pgetJob.cc:127
msgid "pget: falling back to plain get"
msgstr "pget：回退至普通下載"

#: src/pgetJob.cc:131
msgid "the target file is remote"
msgstr "目標檔案在遠端"

#: src/pgetJob.cc:136
msgid "the source file size is unknown"
msgstr "來源檔案大小未知"

#: src/pgetJob.cc:173
#, c-format
msgid "pget: warning: space allocation for %s (%lld bytes) failed: %s\n"
msgstr "pget：警告：%s（%lld 位元組）空間分配失敗：%s\n"

#: src/pgetJob.cc:234
#, c-format
msgid "`%s', got %lld of %lld (%d%%) %s%s"
msgstr "‘%1$s’，下載了 %3$lld 中的 %2$lld (%4$d%%) %5$s%6$s"

#: src/plural.c:96
msgid "=1 =0|>1"
msgstr "=1"

#: src/PollVec.cc:69
#, c-format
msgid "%s: BUG - deadlock detected\n"
msgstr "%s：錯誤 - 偵測到資源鎖定\n"

#: src/PtyShell.cc:68
msgid "pseudo-tty allocation failed: "
msgstr "無法分配虛擬 tty：%s"

#: src/QueueFeeder.cc:68
msgid "Added job$|s$"
msgstr "已加入工作"

#: src/QueueFeeder.cc:164 src/QueueFeeder.cc:185
#, c-format
msgid "No queued jobs.\n"
msgstr "沒有任何背景工作存在。\n"

#: src/QueueFeeder.cc:166
#, c-format
msgid "No queued job #%i.\n"
msgstr "背景工作編號 %i 不存在。\n"

#: src/QueueFeeder.cc:171 src/QueueFeeder.cc:192
msgid "Deleted job$|s$"
msgstr "已刪除工作"

#: src/QueueFeeder.cc:187
#, c-format
msgid "No queued jobs match \"%s\".\n"
msgstr "沒有任何背景工作符合“%s”。\n"

#: src/QueueFeeder.cc:212 src/QueueFeeder.cc:230
msgid "Moved job$|s$"
msgstr "已變更工作次序"

#: src/QueueFeeder.cc:354
msgid "Commands queued:"
msgstr "已放入佇列中的指令:"

#: src/ResMgr.cc:123
msgid "no such variable"
msgstr "這個變數不存在"

#: src/ResMgr.cc:127
msgid "ambiguous variable name"
msgstr "變數名稱不明確"

#: src/ResMgr.cc:335
msgid "invalid boolean value"
msgstr "布林值無效"

#: src/ResMgr.cc:357
msgid "invalid boolean/auto value"
msgstr "布林值或自動變動值無效"

#: src/ResMgr.cc:402 src/ResMgr.cc:713
msgid "invalid number"
msgstr "數字無效"

#: src/ResMgr.cc:415
msgid "invalid floating point number"
msgstr "浮點數無效"

#: src/ResMgr.cc:429
msgid "invalid unsigned number"
msgstr "無效的無符號數字"

#: src/ResMgr.cc:678
msgid "Invalid time unit letter, only [smhd] are allowed."
msgstr "時間單位無效，只允許使用 [smhd] 其中一個。"

#: src/ResMgr.cc:686
msgid "Invalid time format. Format is <time><unit>, e.g. 2h30m."
msgstr "時間格式無效。格式是 <time><unit>，例如：2h30m。"

#: src/ResMgr.cc:718
msgid "integer overflow"
msgstr "整數溢位"

#: src/ResMgr.cc:804
msgid "Invalid IPv4 numeric address"
msgstr "IPv4 地址無效"

#: src/ResMgr.cc:814
msgid "Invalid IPv6 numeric address"
msgstr "IPv6 地址無效"

#: src/ResMgr.cc:884 src/ResMgr.cc:888
msgid "this encoding is not supported"
msgstr "不支援這種編碼"

#: src/ResMgr.cc:894
msgid "no closure defined for this setting"
msgstr "本設定值並沒有定義 closure"

#: src/ResMgr.cc:900
msgid "a closure is required for this setting"
msgstr "本設定值需要 closure"

#: src/Resolver.cc:236
msgid "host name resolve timeout"
msgstr "逾時仍找不到主機地址"

#: src/Resolver.cc:282
#, c-format
msgid "%d address$|es$ found"
msgstr "找到 %d 個地址"

#: src/Resolver.cc:327
msgid "Link-local IPv6 address should have a scope"
msgstr "連結本機 IPv6 地址需要有 scope"

#: src/Resolver.cc:765
msgid "DNS resolution not trusted."
msgstr "不信任的 DNS 解析。"

#: src/Resolver.cc:871
msgid "Host name lookup failure"
msgstr "檢查主機名稱失敗"

#: src/Resolver.cc:903
#, c-format
msgid "no such %s service"
msgstr "%s 服務不存在"

#: src/Resolver.cc:930
msgid "No address found"
msgstr "沒有地址"

#: src/resource.cc:50 src/resource.cc:108
msgid "Proxy protocol unsupported"
msgstr "不支援代理協定"

#: src/resource.cc:54
msgid "ftp:proxy password: "
msgstr "ftp:proxy 密碼："

#: src/resource.cc:70
#, c-format
msgid "%s must be one of: "
msgstr "%s 必須為以下之一："

#: src/resource.cc:72
msgid "must be one of: "
msgstr "必須為以下之一："

#: src/resource.cc:84
msgid ", or empty"
msgstr "，或是空白"

#: src/resource.cc:116
msgid "only PUT and POST values allowed"
msgstr "只允許使用 PUT 或 POST"

#: src/resource.cc:148
#, c-format
msgid "unknown address family `%s'"
msgstr "不明的地址族群‘%s’"

#: src/rmJob.cc:47
#, c-format
msgid "%s ok, `%s' removed\n"
msgstr "%s 成功，刪除了‘%s’\n"

#: src/rmJob.cc:54
#, c-format
msgid "%s failed for %d of %d file$|s$\n"
msgstr "%3$d 個檔案中的其中 %2$d 個無法進行 %1$s\n"

#: src/rmJob.cc:60
#, c-format
msgid "%s ok, %d director$y|ies$ removed\n"
msgstr "%s 成功，刪除了 %d 個目錄\n"

#: src/rmJob.cc:63
#, c-format
msgid "%s ok, %d file$|s$ removed\n"
msgstr "%s 成功，刪除了 %d 個檔案\n"

#: src/SFtp.cc:1099 src/SFtp.cc:1100
#, c-format
msgid "invalid server response format"
msgstr "伺服器回應格式無效"

#: src/SleepJob.cc:99
msgid "Sleeping forever"
msgstr "永遠睡眠"

#: src/SleepJob.cc:100
msgid "Sleep time left: "
msgstr "睡眠時間剩餘："

#: src/SleepJob.cc:108
#, c-format
msgid "\tRepeat count: %d\n"
msgstr "\t重複次數：%d\n"

#: src/SleepJob.cc:142
#, c-format
msgid "%s: argument required. "
msgstr "%s：需要參數。"

#: src/SleepJob.cc:262
#, c-format
msgid "%s: date-time specification missed\n"
msgstr "%s：日期-時間指標遺失\n"

#: src/SleepJob.cc:269
#, c-format
msgid "%s: date-time parse error\n"
msgstr "%s：日期-時間解析錯誤\n"

#: src/SleepJob.cc:303
msgid ""
"Usage: sleep <time>[unit]\n"
"Sleep for given amount of time. The time argument can be optionally\n"
"followed by unit specifier: d - days, h - hours, m - minutes, s - seconds.\n"
"By default time is assumed to be seconds.\n"
msgstr ""
"用法：sleep <時間>[單位]\n"
"在指定時間之內不進行任何操作。指定時間時可選擇加入單位：\n"
"d = 日，h = 小時，m = 分鐘，s = 秒。預設以秒為單位。\n"

#: src/SleepJob.cc:310
msgid ""
"Repeat specified command with a delay between iterations.\n"
"Default delay is one second, default command is empty.\n"
" -c <count>  maximum number of iterations\n"
" -d <delay>  delay between iterations\n"
" --while-ok  stop when command exits with non-zero code\n"
" --until-ok  stop when command exits with zero code\n"
" --weak      stop when lftp moves to background.\n"
msgstr ""
"重複執行特定指令並在每次之後稍微停頓。\n"
"預設每次相隔一秒執行指令，但指令本身沒有預設值。\n"
"-c <次數>  最大迭代次數\n"
" -d <延遲時間>  每次迭代後的延遲時間\n"
" --while-ok  當指令以非零的狀態碼離開時停止\n"
" --until-ok  當指令以狀態碼零離開時停止\n"
" --weak      當 lftp 移動到背景時停止。\n"

#: src/Speedometer.cc:90
#, c-format
msgid "%.0fb/s"
msgstr "每秒 %.0f 位元"

#: src/Speedometer.cc:93
#, c-format
msgid "%.1fK/s"
msgstr "每秒 %.1fK"

#: src/Speedometer.cc:96
#, c-format
msgid "%.2fM/s"
msgstr "每秒 %.2fM"

#: src/Speedometer.cc:103
#, c-format
msgid "%.0f B/s"
msgstr "%.0f B/s"

#: src/Speedometer.cc:105
#, c-format
msgid "%.1f KiB/s"
msgstr "%.1f KiB/s"

#: src/Speedometer.cc:107
#, c-format
msgid "%.2f MiB/s"
msgstr "%.2f MiB/s"

#: src/Speedometer.cc:129
msgid "eta:"
msgstr "距離完成約:"

#: src/SSH_Access.cc:97
msgid "Password required"
msgstr "需要密碼"

#: src/SSH_Access.cc:102
msgid "Login incorrect"
msgstr "登入錯誤"

#: src/SSH_Access.cc:196
#, c-format
msgid "Disconnecting"
msgstr "正在中斷連線"

#: src/SysCmdJob.cc:73
#, c-format
msgid "execlp(%s) failed: %s\n"
msgstr "execlp(%s) 失敗：%s\n"

#: src/TimeDate.cc:155
msgid "day"
msgstr "d"

#: src/TimeDate.cc:156
msgid "hour"
msgstr "h"

#: src/TimeDate.cc:157
msgid "minute"
msgstr "m"

#: src/TimeDate.cc:158
msgid "second"
msgstr "s"

#: src/Torrent.cc:585
msgid "announced via "
msgstr "發佈經由 "

#: src/Torrent.cc:599
#, c-format
msgid "next announce in %s"
msgstr "下一個發佈在 %s"

#: src/Torrent.cc:1142
#, c-format
msgid "%d file$|s$ found, now scanning %s"
msgstr "找到 %d 個檔案，現在正在掃描 %s"

#: src/Torrent.cc:1143
#, c-format
msgid "%d file$|s$ found"
msgstr "找到 %d 個檔案"

#: src/Torrent.cc:2075 src/Torrent.cc:2085
#, c-format
msgid "Getting meta-data: %s"
msgstr "正在取得後設資料：%s"

#: src/Torrent.cc:2077
#, c-format
msgid "Validation: %u/%u (%u%%) %s%s"
msgstr "驗證：%u/%u (%u%%) %s%s"

#: src/Torrent.cc:2088
msgid "Waiting for meta-data..."
msgstr "正在等待後設資料..."

#: src/Torrent.cc:2096
msgid "Shutting down: "
msgstr "正在關閉： "

#: src/Torrent.cc:3207
#, c-format
msgid "Connecting to peer %s port %u"
msgstr "正在連接至 %s 連接埠 %u"

#: src/Torrent.cc:3258 src/Torrent.cc:3375
#, c-format
msgid "peer unexpectedly closed connection after %s"
msgstr "對方無預警的在 %s 之後關閉了連線"

#: src/Torrent.cc:3259 src/Torrent.cc:3376
msgid "peer unexpectedly closed connection"
msgstr "對方無預警的關閉了連線"

#: src/Torrent.cc:3261 src/Torrent.cc:3262
#, c-format
msgid "peer closed connection (before handshake)"
msgstr "對方關閉了連線（在交握前）"

#: src/Torrent.cc:3265 src/Torrent.cc:3378 src/Torrent.cc:3379
#, c-format
msgid "invalid peer response format"
msgstr "對方回應格式無效"

#: src/Torrent.cc:3360 src/Torrent.cc:3361
#, c-format
msgid "peer closed connection"
msgstr "對方關閉了連線"

#: src/Torrent.cc:3538
msgid "Handshaking..."
msgstr "正在交握..."

#: src/Torrent.cc:3798
msgid "Cannot bind a socket for torrent:port-range"
msgstr "無法綁定一個為 torrent:port-range 的 socket"

#: src/Torrent.cc:3858
#, c-format
msgid "Accepted connection from [%s]:%d"
msgstr "已接受自 [%s] 而來的連線：%d"

#: src/Torrent.cc:3924
#, c-format
msgid "peer sent unknown info_hash=%s in handshake"
msgstr "對方在交握時送出了未知的 info_hash=%s"

#: src/Torrent.cc:3948
#, c-format
msgid "peer handshake timeout"
msgstr "對方交握逾時"

#: src/Torrent.cc:3960
#, c-format
msgid "peer short handshake"
msgstr "對方短交握"

#: src/Torrent.cc:3962
#, c-format
msgid "peer closed just accepted connection"
msgstr "對方關閉了剛接受的連線"

#: src/Torrent.cc:4013
#, c-format
msgid "Seeding in background...\n"
msgstr "正在背景做種...\n"

#: src/Torrent.cc:4176
#, c-format
msgid "%s: --share conflicts with --output-directory.\n"
msgstr "%s：--share 與 --output-directory 衝突。\n"

#: src/Torrent.cc:4180
#, c-format
msgid "%s: --share conflicts with --only-new.\n"
msgstr "%s：--share 與 --only-new 衝突。\n"

#: src/Torrent.cc:4184
#, c-format
msgid "%s: --share conflicts with --only-incomplete.\n"
msgstr "%s：--share 與 --only-incomplete 衝突。\n"

#: src/Torrent.cc:4226
#, c-format
msgid "%s: Please specify a file or directory to share.\n"
msgstr "%s：請指定要分享的檔案或目錄。\n"

#: src/Torrent.cc:4228
#, c-format
msgid "%s: Please specify meta-info file or URL.\n"
msgstr "%s：請指定後設資訊的檔案或 URL。\n"

#: src/Torrent.cc:4258
msgid ""
"Start BitTorrent job for the given torrent-files, which can be a local "
"file,\n"
"URL, magnet link or plain info_hash written in hex or base32. Local "
"wildcards\n"
"are expanded. Options:\n"
" -O <base>      specifies base directory where files should be placed\n"
" --force-valid  skip file validation\n"
" --dht-bootstrap=<node>  bootstrap DHT by sending a query to the node\n"
" --share        share specified file or directory\n"
msgstr ""
"為給定的啟動 BitTorrent 工作，其為本機 檔案、\n"
"URL、磁力連結或是普通的以 hex 或 base32 編寫的 info_hash。本機 萬用字元\n"
"會被展開。選項：\n"
" -O <base>      指定檔案應該要被放置的目錄層\n"
" --force-valid  跳過檔案驗證\n"
" --dht-bootstrap=<節點>  透過送出查詢到節點來引導 DHT\n"
" --share        分享指定的檔案或目錄\n"

#: src/TorrentTracker.cc:178
msgid "not started"
msgstr "未啟動"

#: src/TorrentTracker.cc:181
#, c-format
msgid "next request in %s"
msgstr "在 %s 的下一個要求"

#: src/TorrentTracker.cc:284 src/TorrentTracker.cc:501
#, c-format
msgid "Received valid info about %d peer$|s$"
msgstr "已收到關於 %d 個對方的驗證資訊"

#: src/TorrentTracker.cc:298
#, c-format
msgid "Received valid info about %d IPv6 peer$|s$"
msgstr "已收到關於 %d 個 IPv6 對方的驗證資訊"

#~ msgid "%s: option '--%s' doesn't allow an argument\n"
#~ msgstr "%s：選項 ‘--%s’ 不可配合參數使用\n"

#~ msgid "%s: unrecognized option '--%s'\n"
#~ msgstr "%s：‘--%s’ 選項無法辨識\n"

#~ msgid "%s: option '-W %s' is ambiguous\n"
#~ msgstr "%s：選項 ‘-W %s’ 不明確\n"

#~ msgid "%s: option '-W %s' doesn't allow an argument\n"
#~ msgstr "%s：選項 ‘-W %s’ 不可配合參數使用\n"

#~ msgid "%s: option '-W %s' requires an argument\n"
#~ msgstr "%s：選項 ‘%s’ 需要參數\n"

#~ msgid "Usage: mv <file1> <file2>\n"
#~ msgstr "用法：mv <檔案1> <檔案2>\n"

#, fuzzy
#~ msgid "number"
#~ msgstr "數字無效"

#, fuzzy
#~ msgid "error: %s:%d\n"
#~ msgstr "嚴重錯誤：%s"

#, fuzzy
#~ msgid ""
#~ "\n"
#~ "Mirror specified remote directory to local directory\n"
#~ "\n"
#~ " -c, --continue         continue a mirror job if possible\n"
#~ " -e, --delete           delete files not present at remote site\n"
#~ "     --delete-first     delete old files before transferring new ones\n"
#~ " -s, --allow-suid       set suid/sgid bits according to remote site\n"
#~ "     --allow-chown      try to set owner and group on files\n"
#~ "     --ignore-time      ignore time when deciding whether to download\n"
#~ " -n, --only-newer       download only newer files (-c won't work)\n"
#~ " -r, --no-recursion     don't go to subdirectories\n"
#~ " -p, --no-perms         don't set file permissions\n"
#~ "     --no-umask         don't apply umask to file modes\n"
#~ " -R, --reverse          reverse mirror (put files)\n"
#~ " -L, --dereference      download symbolic links as files\n"
#~ " -N, --newer-than=SPEC  download only files newer than specified time\n"
#~ " -P, --parallel[=N]     download N files in parallel\n"
#~ " -i RX, --include RX    include matching files\n"
#~ " -x RX, --exclude RX    exclude matching files\n"
#~ "                        RX is extended regular expression\n"
#~ " -v, --verbose[=N]      verbose operation\n"
#~ "     --log=FILE         write lftp commands being executed to FILE\n"
#~ "     --script=FILE      write lftp commands to FILE, but don't execute "
#~ "them\n"
#~ "     --just-print, --dry-run    same as --script=-\n"
#~ "\n"
#~ "When using -R, the first directory is local and the second is remote.\n"
#~ "If the second directory is omitted, basename of first directory is used.\n"
#~ "If both directories are omitted, current local and remote directories are "
#~ "used.\n"
#~ "See lftp(1) for a complete list of options.\n"
#~ msgstr ""
#~ "\n"
#~ "將指定的遠端目錄完整複製至本機目錄\n"
#~ "\n"
#~ " -c, --continue         若有可能，繼續未完成的 mirror 動作\n"
#~ " -e, --delete           刪除所有在遠端站台中不存在的檔案\n"
#~ "     --delete-first     傳送新檔案之前先刪除舊檔案\n"
#~ " -s, --allow-suid       依遠端站台的資料，為本機檔案加上 suid/sgid 權限\n"
#~ "     --allow-chown      依遠端站台的資料，設定檔案的擁有者及所屬群組\n"
#~ "     --ignore-time      決定下載甚麼檔案時，不將檔案時間列入考慮條件之中\n"
#~ " -n, --only-newer       只下載較新的檔案 (-c 不能作此用途)\n"
#~ " -r, --no-recursion     不下載子目錄\n"
#~ " -p, --no-perms         不設定檔案權限\n"
#~ "     --no-umask         不依 umask 設定檔案權限\n"
#~ " -R, --reverse          反向 mirror (即上傳檔案)\n"
#~ " -L, --dereference      下載符號鏈結指定的目標檔案\n"
#~ " -N, --newer-than=SPEC  只下載指定時間之後的檔案\n"
#~ " -P, --parallel[=N]     同時下載 N 個檔案\n"
#~ " -i RX, --include RX    包括符合指定樣式的檔案\n"
#~ " -x RX, --exclude RX    排除符合指定樣式的檔案\n"
#~ "                        其中 RX 是擴展正規表示式\n"
#~ " -v, --verbose[=N]      運作時顯示更詳細的訊息\n"
#~ "     --log=檔案         將 lftp 執行過的指令寫入 <檔案>\n"
#~ "     --script=檔案      將 lftp 指令寫入 <檔案> 但不執行\n"
#~ "     --just-print, --dry-run    意義相等於 --script=-\n"
#~ "\n"
#~ "當使用 -R 時，第一個目錄是本機目錄，而第二個是遠端目錄。\n"
#~ "如果沒有填第二個，會採用第一個目錄的名稱。\n"
#~ "如果兩個都沒有填，則會使用目前的本機及遠端目錄。\n"

#~ msgid "SITE CHMOD is disabled by ftp:use-site-chmod"
#~ msgstr "ftp:use-site-chmod 設定關閉了 SITE CHMOD 功能"

#~ msgid "ftp:ssl-auth must be one of: SSL, TLS, TLS-P, TLS-C"
#~ msgstr "ftp:ssl-auth 必須為 SSL、TLS、TLS-P、TLS-C 其中一個"

#~ msgid "Invalid suffix. Valid suffixes are: k, M, G, T, P, E, Z, Y"
#~ msgstr "單位無效。有效的單位為 k、M、G、T、P、E、Z 或 Y"

#~ msgid "invalid pair of numbers"
#~ msgstr "該對數字無效"

#~ msgid "Saw `unknown', assume failed login"
#~ msgstr "伺服器回應中含有‘unknown’，假設登入已失敗"

#~ msgid "Can only edit plain queues.\n"
#~ msgstr "只可以修改普通的佇列。\n"

#~ msgid "Couldn't create temporary file `%s': %s.\n"
#~ msgstr "無法建立暫存檔 ‘%s’：%s。\n"

#~ msgid "%s: error writing %s: %s\n"
#~ msgstr "%s：寫入 %s 時出現錯誤：%s\n"

#~ msgid "%s: illegal option -- %c\n"
#~ msgstr "%s：不合法的選項 ─ %c\n"

#~ msgid ""
#~ "LFTP is free software, covered by the GNU General Public License, and you "
#~ "are\n"
#~ "welcome to change it and/or distribute copies of it under certain "
#~ "conditions.\n"
#~ "There is absolutely no warranty for LFTP.  See COPYING for details.\n"
#~ msgstr ""
#~ "LFTP 為自由軟體，依據 GNU 通用公共授權條款 (GPL) 發行。您可以在該條款的規"
#~ "定\n"
#~ "下修改及 / 或散布該程式。\n"
#~ "LFTP 不負任何擔保責任。請參閱 COPYING 檔案。\n"

#~ msgid "block size"
#~ msgstr "區塊大小"
