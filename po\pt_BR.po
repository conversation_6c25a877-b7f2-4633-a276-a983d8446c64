# mensagens em português (Brasil)
# Copyright (C) 1998 Free Software Foundation, Inc.
#
msgid ""
msgstr ""
"Project-Id-Version: lftp 1.0\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2024-11-08 13:05+0300\n"
"PO-Revision-Date: 1999-07-12 00:00+0400\n"
"Last-Translator: Arnaldo <PERSON> <<EMAIL>>\n"
"Language-Team: pt_BR <<EMAIL>>\n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8-bit\n"

#: lib/argmatch.c:145
#, fuzzy, c-format
msgid "invalid argument %s for %s"
msgstr "número inválido"

#: lib/argmatch.c:146
#, c-format
msgid "ambiguous argument %s for %s"
msgstr ""

#: lib/argmatch.c:165
msgid "Valid arguments are:"
msgstr ""

#: lib/error.c:208
msgid "Unknown system error"
msgstr ""

#: lib/getopt.c:282
#, c-format
msgid "%s: option '%s%s' is ambiguous\n"
msgstr ""

#: lib/getopt.c:288
#, c-format
msgid "%s: option '%s%s' is ambiguous; possibilities:"
msgstr ""

#: lib/getopt.c:322
#, fuzzy, c-format
msgid "%s: unrecognized option '%s%s'\n"
msgstr "número inválido"

#: lib/getopt.c:348
#, c-format
msgid "%s: option '%s%s' doesn't allow an argument\n"
msgstr ""

#: lib/getopt.c:363
#, c-format
msgid "%s: option '%s%s' requires an argument\n"
msgstr ""

#: lib/getopt.c:624
#, fuzzy, c-format
msgid "%s: invalid option -- '%c'\n"
msgstr "número inválido"

#: lib/getopt.c:639 lib/getopt.c:685
#, c-format
msgid "%s: option requires an argument -- '%c'\n"
msgstr ""

#. TRANSLATORS:
#. Get translations for open and closing quotation marks.
#. The message catalog should translate "`" to a left
#. quotation mark suitable for the locale, and similarly for
#. "'".  For example, a French Unicode local should translate
#. these to U+00AB (LEFT-POINTING DOUBLE ANGLE
#. QUOTATION MARK), and U+00BB (RIGHT-POINTING DOUBLE ANGLE
#. QUOTATION MARK), respectively.
#.
#. If the catalog has no translation, we will try to
#. use Unicode U+2018 (LEFT SINGLE QUOTATION MARK) and
#. Unicode U+2019 (RIGHT SINGLE QUOTATION MARK).  If the
#. current locale is not Unicode, locale_quoting_style
#. will quote 'like this', and clocale_quoting_style will
#. quote "like this".  You should always include translations
#. for "`" and "'" even if U+2018 and U+2019 are appropriate
#. for your locale.
#.
#. If you don't know what to put here, please see
#. <https://en.wikipedia.org/wiki/Quotation_marks_in_other_languages>
#. and use glyphs suitable for your language.
#: lib/quotearg.c:354
msgid "`"
msgstr ""

#: lib/quotearg.c:355
msgid "'"
msgstr ""

#: lib/xalloc-die.c:34
msgid "memory exhausted"
msgstr ""

#: src/ArgV.cc:107
msgid "option requires an argument"
msgstr ""

#: src/ArgV.cc:109 src/ArgV.cc:118
#, fuzzy
msgid "invalid option"
msgstr "número inválido"

#: src/ArgV.cc:114
#, c-format
msgid "option `%s' requires an argument"
msgstr ""

#: src/ArgV.cc:116
#, fuzzy, c-format
msgid "unrecognized option `%s'"
msgstr "número inválido"

#: src/attach.h:138
#, c-format
msgid "[%u] Attached to terminal %s. %s\n"
msgstr ""

#: src/attach.h:150
#, c-format
msgid "[%u] Attached to terminal.\n"
msgstr ""

#: src/buffer.cc:253 src/FileAccess.cc:866
#, fuzzy
msgid " [cached]"
msgstr "1 byte no cache"

#: src/ChmodJob.cc:80
#, c-format
msgid "Failed to change mode of `%s' to %04o (%s).\n"
msgstr ""

#: src/ChmodJob.cc:83
#, c-format
msgid "Mode of `%s' changed to %04o (%s).\n"
msgstr ""

#: src/ChmodJob.cc:88
#, c-format
msgid "Failed to change mode of `%s' because no old mode is available.\n"
msgstr ""

#: src/CmdExec.cc:105
#, c-format
msgid "Warning: chdir(%s) failed: %s\n"
msgstr "Atenção: chdir(%s) falhou: %s\n"

#: src/CmdExec.cc:207
#, c-format
msgid "Unknown command `%s'.\n"
msgstr "Comando `%s' desconhecido.\n"

#: src/CmdExec.cc:209
#, c-format
msgid "Ambiguous command `%s'.\n"
msgstr "Comando `%s' ambíguo.\n"

#: src/CmdExec.cc:228
#, c-format
msgid "Module for command `%s' did not register the command.\n"
msgstr ""

#: src/CmdExec.cc:384
#, c-format
msgid "cd ok, cwd=%s\n"
msgstr ""

#: src/CmdExec.cc:405 src/MirrorJob.cc:736
#, c-format
msgid "%s: received redirection to `%s'\n"
msgstr ""

#: src/CmdExec.cc:408 src/FileCopy.cc:1230
msgid "Too many redirections"
msgstr ""

#: src/CmdExec.cc:517 src/CmdExec.cc:574
msgid "Interrupt"
msgstr "Interrupção"

#: src/CmdExec.cc:639
#, c-format
msgid "Warning: discarding incomplete command\n"
msgstr "Atenção: descartando comando incompleto\n"

#: src/CmdExec.cc:737
#, c-format
msgid "\tExecuting builtin `%s' [%s]\n"
msgstr "\tExecutando comando embutido `%s' [%s]\n"

#: src/CmdExec.cc:742
msgid "Queue is stopped."
msgstr ""

#: src/CmdExec.cc:747
msgid "Now executing:"
msgstr ""

#: src/CmdExec.cc:758
#, c-format
msgid "\tWaiting for job [%d] to terminate\n"
msgstr "\tEsperanto término do serviço [%d]\n"

#: src/CmdExec.cc:761
#, fuzzy, c-format
msgid "\tWaiting for termination of jobs: "
msgstr "Esperando término da filtragem"

#: src/CmdExec.cc:770
msgid "\tRunning\n"
msgstr "\tExecutando\n"

#: src/CmdExec.cc:772
#, fuzzy
msgid "\tWaiting for command\n"
msgstr "Atenção: descartando comando incompleto\n"

#: src/CmdExec.cc:1261
#, c-format
msgid "%s: command `%s' is not compiled in.\n"
msgstr ""

#: src/CmdExec.cc:1278
#, fuzzy, c-format
msgid "Usage: %s cmd [args...]\n"
msgstr "mget [-c] [-d] [-e] <arquivos>"

#: src/CmdExec.cc:1284
#, c-format
msgid "%s: cannot create local session\n"
msgstr ""

#: src/commands.cc:114
#, fuzzy
msgid "!<shell-command>"
msgstr "!<comando_shell>"

#: src/commands.cc:115
msgid "Launch shell or shell command\n"
msgstr "Disponibilize shell ou comando shell\n"

#: src/commands.cc:116
msgid "(commands)"
msgstr "(comandos)"

#: src/commands.cc:117
msgid ""
"Group commands together to be executed as one command\n"
"You can launch such a group in background\n"
msgstr ""
"Agrupe comandos para serem executados com um comando\n"
"Você pode executar um grupo em segundo plano\n"

#: src/commands.cc:120
msgid "alias [<name> [<value>]]"
msgstr "alias [<nome> [<valor>]]"

#: src/commands.cc:121
msgid ""
"Define or undefine alias <name>. If <value> omitted,\n"
"the alias is undefined, else is takes the value <value>.\n"
"If no argument is given the current aliases are listed.\n"
msgstr ""
"Define ou elimine alias <nome>. Se <valor> for omitido,\n"
"o alias é eliminado, de outra forma ele assume o valor <valor>.\n"
"Se nenhum argumento for dado os aliases correntemente definidos serão "
"listados.\n"

#: src/commands.cc:125
msgid "anon - login anonymously (by default)\n"
msgstr "anom - efetue a conexão de forma anônima (como padrão)\n"

#: src/commands.cc:127
msgid "bookmark [SUBCMD]"
msgstr "bookmark [SUBCOMANDO]"

#: src/commands.cc:128
#, fuzzy
msgid ""
"bookmark command controls bookmarks\n"
"\n"
"The following subcommands are recognized:\n"
"  add <name> [<loc>] - add current place or given location to bookmarks\n"
"                       and bind to given name\n"
"  del <name>         - remove bookmark with the name\n"
"  edit               - start editor on bookmarks file\n"
"  import <type>      - import foreign bookmarks\n"
"  list               - list bookmarks (default)\n"
msgstr ""
"\n"
"Os seguintes subcomandos são reconhecidos:\n"
"  add <nome> [<local>] - adicione local corrente ou local informado\n"
"                         no bookmark e o associe ao nome informado\n"
"  del <nome>           - remova o bookmark com este nome\n"
"  list                 - lista os bookmarks (padrão)\n"

#: src/commands.cc:137
msgid "cache [SUBCMD]"
msgstr "cache [SUBCOMANDO]"

#: src/commands.cc:138
#, fuzzy
msgid ""
"cache command controls local memory cache\n"
"\n"
"The following subcommands are recognized:\n"
"  stat        - print cache status (default)\n"
"  on|off      - turn on/off caching\n"
"  flush       - flush cache\n"
"  size <lim>  - set memory limit\n"
"  expire <Nx> - set cache expiration time to N seconds (x=s)\n"
"                minutes (x=m) hours (x=h) or days (x=d)\n"
msgstr ""
"O comando cache controla o cache local de memória\n"
"\n"
"Os subcomandos seguintes são possíveis:\n"
"  stat          - mostre o status do cache (padrão)\n"
"  on|off        - liga/desliga cache\n"
"  flush         - limpa o cache\n"
"  size <limite> - configura limite de memória, -1 significa ilimitado\n"
"  expire <Nx>   - configure tempo de expiração do cache para N segundos "
"(x=s),\n"
"                  minutos (x=m), horas (x=h) ou dias (x=d)\n"

#: src/commands.cc:146
#, fuzzy
msgid "cat [-b] <files>"
msgstr "cat [-u] <arquivos>"

#: src/commands.cc:147
#, fuzzy
msgid ""
"cat - output remote files to stdout (can be redirected)\n"
" -b  use binary mode (ascii is the default)\n"
msgstr ""
"cat - mostra conteúdo de arquivos remotos\n"
" -u  tente reconhecer URLs\n"

#: src/commands.cc:149
msgid "cd <rdir>"
msgstr "cd <diretório_remoto>"

#: src/commands.cc:150
msgid ""
"Change current remote directory to <rdir>. The previous remote directory\n"
"is stored as `-'. You can do `cd -' to change the directory back.\n"
"The previous directory for each site is also stored on disk, so you can\n"
"do `open site; cd -' even after lftp restart.\n"
msgstr ""
"muda o diretório remoto corrente para <diretório_remoto>. O diretório "
"remoto\n"
"anterior fica guardado como `-'. Você pode simplesmente executar `cd -' "
"para\n"
"voltar ao diretório anterior. O diretório anterior para cada servidor "
"acessado\n"
"também é armazenado em disco, desta forma você pode executar `open "
"servidor;\n"
"cd -' mesmo após ter saido e voltado ao lftp.\n"

#: src/commands.cc:154
msgid "chmod [OPTS] mode file..."
msgstr ""

#: src/commands.cc:155
msgid ""
"Change the mode of each FILE to MODE.\n"
"\n"
" -c, --changes        - like verbose but report only when a change is made\n"
" -f, --quiet          - suppress most error messages\n"
" -v, --verbose        - output a diagnostic for every file processed\n"
" -R, --recursive      - change files and directories recursively\n"
"\n"
"MODE can be an octal number or symbolic mode (see chmod(1))\n"
msgstr ""

#: src/commands.cc:164
msgid ""
"Close idle connections. By default only with current server.\n"
" -a  close idle connections with all servers\n"
msgstr ""
"Feche conexões sem atividade. Como padrão somente no servidor sendo acessado "
"no\n"
"momento.\n"
" -a  feche conexões sem atividade em todos os servidores\n"

#: src/commands.cc:166
msgid "[re]cls [opts] [path/][pattern]"
msgstr ""

#: src/commands.cc:167
msgid ""
"List remote files. You can redirect output of this command to file\n"
"or via pipe to external command.\n"
"\n"
" -1                   - single-column output\n"
" -a, --all            - show dot files\n"
" -B, --basename       - show basename of files only\n"
"     --block-size=SIZ - use SIZ-byte blocks\n"
" -d, --directory      - list directory entries instead of contents\n"
" -F, --classify       - append indicator (one of /@) to entries\n"
" -h, --human-readable - print sizes in human readable format (e.g., 1K)\n"
"     --si             - likewise, but use powers of 1000 not 1024\n"
" -k, --kilobytes      - like --block-size=1024\n"
" -l, --long           - use a long listing format\n"
" -q, --quiet          - don't show status\n"
" -s, --size           - print size of each file\n"
"     --filesize       - if printing size, only print size for files\n"
" -i, --nocase         - case-insensitive pattern matching\n"
" -I, --sortnocase     - sort names case-insensitively\n"
" -D, --dirsfirst      - list directories first\n"
"     --sort=OPT       - \"name\", \"size\", \"date\"\n"
" -S                   - sort by file size\n"
" --user, --group, --perms, --date, --linkcount, --links\n"
"                      - show individual fields\n"
" --time-style=STYLE   - use specified time format\n"
"\n"
"By default, cls output is cached, to see new listing use `recls' or\n"
"`cache flush'.\n"
"\n"
"The variables cls-default and cls-completion-default can be used to\n"
"specify defaults for cls listings and completion listings, respectively.\n"
"For example, to make completion listings show file sizes, set\n"
"cls-completion-default to \"-s\".\n"
"\n"
"Tips: Use --filesize with -D to pack the listing better.  If you don't\n"
"always want to see file sizes, --filesize in cls-default will affect the\n"
"-s flag on the commandline as well.  Add `-i' to cls-completion-default\n"
"to make filename completion case-insensitive.\n"
msgstr ""

#: src/commands.cc:217
#, fuzzy
msgid "debug [OPTS] [<level>|off]"
msgstr "debug [<nível>|off] [-o <arquivo>]"

#: src/commands.cc:218
#, fuzzy
msgid ""
"Set debug level to given value or turn debug off completely.\n"
" -o <file>  redirect debug output to the file\n"
" -c  show message context\n"
" -p  show PID\n"
" -t  show timestamps\n"
msgstr ""
"Configura o nível de debug para o valor informado ou desliga\n"
"completamente o debug\n"
" -o <arquivo>  redireciona a saída do debug para o arquivo.\n"

#: src/commands.cc:223
#, fuzzy
msgid "du [options] <dirs>"
msgstr "rm [-r] <arquivos>"

#: src/commands.cc:224
msgid ""
"Summarize disk usage.\n"
" -a, --all             write counts for all files, not just directories\n"
"     --block-size=SIZ  use SIZ-byte blocks\n"
" -b, --bytes           print size in bytes\n"
" -c, --total           produce a grand total\n"
" -d, --max-depth=N     print the total for a directory (or file, with --"
"all)\n"
"                       only if it is N or fewer levels below the command\n"
"                       line argument;  --max-depth=0 is the same as\n"
"                       --summarize\n"
" -F, --files           print number of files instead of sizes\n"
" -h, --human-readable  print sizes in human readable format (e.g., 1K 234M "
"2G)\n"
" -H, --si              likewise, but use powers of 1000 not 1024\n"
" -k, --kilobytes       like --block-size=1024\n"
" -m, --megabytes       like --block-size=1048576\n"
" -S, --separate-dirs   do not include size of subdirectories\n"
" -s, --summarize       display only a total for each argument\n"
"     --exclude=PAT     exclude files that match PAT\n"
msgstr ""

#: src/commands.cc:242
#, fuzzy
msgid "edit [OPTS] <file>"
msgstr "rm [-r] <arquivos>"

#: src/commands.cc:243
msgid ""
"Retrieve remote file to a temporary location, run a local editor on it\n"
"and upload the file back if changed.\n"
" -k  keep the temporary file\n"
" -o <temp>  explicit temporary file location\n"
msgstr ""

#: src/commands.cc:248
#, fuzzy
msgid "exit [<code>|bg]"
msgstr "exit [<código>]"

#: src/commands.cc:249
#, fuzzy
msgid ""
"exit - exit from lftp or move to background if jobs are active\n"
"\n"
"If no jobs active, the code is passed to operating system as lftp\n"
"termination status. If omitted, exit code of last command is used.\n"
"`bg' forces moving to background if cmd:move-background is false.\n"
msgstr ""
"exit - sai do lftp ou o coloca em segundo plano se serviços estiverem\n"
"       ativos\n"
"Se nenhum serviço estiver ativo o código é passado para o sistema\n"
"operacional como o status de término. Se omitido o código de saida do\n"
"último comando é usado.\n"

#: src/commands.cc:255
msgid ""
"Usage: find [OPTS] [directory]\n"
"Print contents of specified directory or current directory recursively.\n"
"Directories in the list are marked with trailing slash.\n"
"You can redirect output of this command.\n"
" -d, --maxdepth=LEVELS  Descend at most LEVELS of directories.\n"
msgstr ""

#: src/commands.cc:260
msgid "get [OPTS] <rfile> [-o <lfile>]"
msgstr "get [OPÇÕES] <arquivo_remoto> [-o <arquivo_local>]"

#: src/commands.cc:261
#, fuzzy
msgid ""
"Retrieve remote file <rfile> and store it to local file <lfile>.\n"
" -o <lfile> specifies local file name (default - basename of rfile)\n"
" -c  continue, resume transfer\n"
" -E  delete remote files after successful transfer\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Busca arquivo remoto <arquivo_remoto> e o salva no arquivo local\n"
"<arquivo_local>.\n"
" -o  <arquivo_local> especifica o nome do arquivo local (padrão: \n"
"                     último componente do arquivo_remoto)\n"
" -c  continua, reget\n"
" -e  apaga arquivos remotos após recepção com sucesso\n"
" -u  tenta reconhecer URLs\n"

#: src/commands.cc:268
msgid "glob [OPTS] <cmd> <args>"
msgstr ""

#: src/commands.cc:270
msgid ""
"Expand wildcards and run specified command.\n"
"Options can be used to expand wildcards to list of files, directories,\n"
"or both types. Type selection is not very reliable and depends on server.\n"
"If entry type cannot be determined, it will be included in the list.\n"
" -f  plain files (default)\n"
" -d  directories\n"
" -a  all types\n"
" --exist      return zero exit code when the patterns expand to non-empty "
"list\n"
" --not-exist  return zero exit code when the patterns expand to an empty "
"list\n"
msgstr ""

#: src/commands.cc:279
msgid "help [<cmd>]"
msgstr "help [<comando>]"

#: src/commands.cc:280
msgid "Print help for command <cmd>, or list of available commands\n"
msgstr "Mostra ajuda para o comando <comando> ou lista comandos disponíveis\n"

#: src/commands.cc:282
#, fuzzy
msgid ""
"List running jobs. -v means verbose, several -v can be specified.\n"
"If <job_no> is specified, only list a job with that number.\n"
msgstr ""
"Lista serviços sendo executados. -v significa detalhado, vários -v podem ser "
"especificados.\n"

#: src/commands.cc:284
msgid "kill all|<job_no>"
msgstr "kill all|<número_do_serviço>"

#: src/commands.cc:285
msgid "Delete specified job with <job_no> or all jobs\n"
msgstr ""
"Termina serviço especificado em <número_do_serviço> ou todos os serviços\n"

#: src/commands.cc:286
msgid "lcd <ldir>"
msgstr "lcd <diretório_local>"

#: src/commands.cc:287
msgid ""
"Change current local directory to <ldir>. The previous local directory\n"
"is stored as `-'. You can do `lcd -' to change the directory back.\n"
msgstr ""
"Muda o diretório corrente local para <diretório_local>. O diretório local\n"
"anterior é armazenado como `-'. Você pode executar `lcd -' para voltar ao\n"
"diretório anterior.\n"

#: src/commands.cc:289
msgid "lftp [OPTS] <site>"
msgstr ""

#: src/commands.cc:290
msgid ""
"`lftp' is the first command executed by lftp after rc files\n"
" -f <file>           execute commands from the file and exit\n"
" -c <cmd>            execute the commands and exit\n"
" --norc              don't execute rc files from the home directory\n"
" --help              print this help and exit\n"
" --version           print lftp version and exit\n"
"Other options are the same as in `open' command:\n"
" -e <cmd>            execute the command just after selecting\n"
" -u <user>[,<pass>]  use the user/password for authentication\n"
" -p <port>           use the port for connection\n"
" -s <slot>           assign the connection to this slot\n"
" -d                  switch on debugging mode\n"
" <site>              host name, URL or bookmark name\n"
msgstr ""

#: src/commands.cc:303
#, fuzzy
msgid "ln [-s] <file1> <file2>"
msgstr "get [OPÇÕES] <arquivo_remoto> [-o <arquivo_local>]"

#: src/commands.cc:304
msgid "Link <file1> to <file2>\n"
msgstr ""

#: src/commands.cc:308
msgid "ls [<args>]"
msgstr "ls [<argumentos>]"

#: src/commands.cc:309
#, fuzzy
msgid ""
"List remote files. You can redirect output of this command to file\n"
"or via pipe to external command.\n"
"By default, ls output is cached, to see new listing use `rels' or\n"
"`cache flush'.\n"
"See also `help cls'.\n"
msgstr ""
"Lista arquivos remotos. Você pode redirecionar a saída deste comando\n"
"para um arquivo ou via pipe para um comando externo.\n"
"Como padrão a saída do ls é armazenada no cache, para ver uma nova\n"
"listagem execute `rels' ou `cache flush'.\n"

#: src/commands.cc:314
#, fuzzy
msgid "mget [OPTS] <files>"
msgstr "rm [-r] <arquivos>"

#: src/commands.cc:315
#, fuzzy
msgid ""
"Gets selected files with expanded wildcards\n"
" -c  continue, resume transfer\n"
" -d  create directories the same as in file names and get the\n"
"     files into them instead of current directory\n"
" -E  delete remote files after successful transfer\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Busca os arquivos selecionados com coringas (wildcards) expandidos\n"
" -c  continue, reget\n"
" -d  crie diretórios com o mesmo nome dos arquivos e os coloque os\n"
"     os arquivos neles ao invés de no diretório corrente\n"
" -e  remova os arquivos remotos após recepção bem-sucedida\n"

#: src/commands.cc:322
msgid "mirror [OPTS] [remote [local]]"
msgstr "mirror [OPÇÕES] [remoto [local]]"

#: src/commands.cc:323
#, fuzzy
msgid "mkdir [OPTS] <dirs>"
msgstr "rm [-r] <arquivos>"

#: src/commands.cc:324
msgid ""
"Make remote directories\n"
" -p  make all levels of path\n"
" -f  be quiet, suppress messages\n"
msgstr ""

#: src/commands.cc:327
msgid "module name [args]"
msgstr ""

#: src/commands.cc:328
msgid ""
"Load module (shared object). The module should contain function\n"
"   void module_init(int argc,const char *const *argv)\n"
"If name contains a slash, then the module is searched in current\n"
"directory, otherwise in directories specified by setting module:path.\n"
msgstr ""

#: src/commands.cc:332
#, fuzzy
msgid "more <files>"
msgstr "rm [-r] <arquivos>"

#: src/commands.cc:333
msgid "Same as `cat <files> | more'. if PAGER is set, it is used as filter\n"
msgstr ""

#: src/commands.cc:334
#, fuzzy
msgid "mput [OPTS] <files>"
msgstr "rm [-r] <arquivos>"

#: src/commands.cc:335
#, fuzzy
msgid ""
"Upload files with wildcard expansion\n"
" -c  continue, reput\n"
" -d  create directories the same as in file names and put the\n"
"     files into them instead of current directory\n"
" -E  delete local files after successful transfer (dangerous)\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Busca os arquivos selecionados com coringas (wildcards) expandidos\n"
" -c  continue, reget\n"
" -d  crie diretórios com o mesmo nome dos arquivos e os coloque os\n"
"     os arquivos neles ao invés de no diretório corrente\n"
" -e  remova os arquivos remotos após recepção bem-sucedida\n"

#: src/commands.cc:342
msgid "mrm <files>"
msgstr ""

#: src/commands.cc:343
msgid "Removes specified files with wildcard expansion\n"
msgstr ""

#: src/commands.cc:344
msgid "mv <file1> <file2>"
msgstr ""

#: src/commands.cc:345
msgid "Rename <file1> to <file2>\n"
msgstr ""

#: src/commands.cc:346
#, fuzzy
msgid "mmv [OPTS] <files> <target-dir>"
msgstr "rm [-r] <arquivos>"

#: src/commands.cc:347
msgid ""
"Move <files> to <target-directory> with wildcard expansion\n"
" -O <dir>  specifies the target directory (alternative way)\n"
msgstr ""

#: src/commands.cc:349
#, fuzzy
msgid "[re]nlist [<args>]"
msgstr "ls [<argumentos>]"

#: src/commands.cc:350
#, fuzzy
msgid ""
"List remote file names.\n"
"By default, nlist output is cached, to see new listing use `renlist' or\n"
"`cache flush'.\n"
msgstr ""
"Lista arquivos remotos. Você pode redirecionar a saída deste comando\n"
"para um arquivo ou via pipe para um comando externo.\n"
"Como padrão a saída do ls é armazenada no cache, para ver uma nova\n"
"listagem execute `rels' ou `cache flush'.\n"

#: src/commands.cc:353
msgid "open [OPTS] <site>"
msgstr ""

#: src/commands.cc:354
msgid ""
"Select a server, URL or bookmark\n"
" -e <cmd>            execute the command just after selecting\n"
" -u <user>[,<pass>]  use the user/password for authentication\n"
" -p <port>           use the port for connection\n"
" -s <slot>           assign the connection to this slot\n"
" -d                  switch on debugging mode\n"
" <site>              host name, URL or bookmark name\n"
msgstr ""

#: src/commands.cc:361
msgid "pget [OPTS] <rfile> [-o <lfile>]"
msgstr ""

#: src/commands.cc:362
msgid ""
"Gets the specified file using several connections. This can speed up "
"transfer,\n"
"but loads the net heavily impacting other users. Use only if you really\n"
"have to transfer the file ASAP.\n"
"\n"
"Options:\n"
" -c  continue transfer. Requires <lfile>.lftp-pget-status file.\n"
" -n <maxconn>  set maximum number of connections (default is is taken from\n"
"     pget:default-n setting)\n"
" -O <base> specifies base directory where files should be placed\n"
msgstr ""

#: src/commands.cc:370
#, fuzzy
msgid "put [OPTS] <lfile> [-o <rfile>]"
msgstr "get [OPÇÕES] <arquivo_remoto> [-o <arquivo_local>]"

#: src/commands.cc:371
#, fuzzy
msgid ""
"Upload <lfile> with remote name <rfile>.\n"
" -o <rfile> specifies remote file name (default - basename of lfile)\n"
" -c  continue, reput\n"
"     it requires permission to overwrite remote files\n"
" -E  delete local files after successful transfer (dangerous)\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Busca arquivo remoto <arquivo_remoto> e o salva no arquivo local\n"
"<arquivo_local>.\n"
" -o  <arquivo_local> especifica o nome do arquivo local (padrão: \n"
"                     último componente do arquivo_remoto)\n"
" -c  continua, reget\n"
" -e  apaga arquivos remotos após recepção com sucesso\n"
" -u  tenta reconhecer URLs\n"

#: src/commands.cc:379
msgid ""
"Print current remote URL.\n"
" -p  show password\n"
msgstr ""

#: src/commands.cc:381
msgid "queue [OPTS] [<cmd>]"
msgstr ""

#: src/commands.cc:382
msgid ""
"\n"
"       queue [-n num] <command>\n"
"\n"
"Add the command to queue for current site. Each site has its own command\n"
"queue. `-n' adds the command before the given item in the queue. It is\n"
"possible to queue up a running job by using command `queue wait <jobno>'.\n"
"\n"
"       queue --delete|-d [index or wildcard expression]\n"
"\n"
"Delete one or more items from the queue. If no argument is given, the last\n"
"entry in the queue is deleted.\n"
"\n"
"       queue --move|-m <index or wildcard expression> [index]\n"
"\n"
"Move the given items before the given queue index, or to the end if no\n"
"destination is given.\n"
"\n"
"Options:\n"
" -q                  Be quiet.\n"
" -v                  Be verbose.\n"
" -Q                  Output in a format that can be used to re-queue.\n"
"                     Useful with --delete.\n"
msgstr ""

#: src/commands.cc:403
msgid "quote <cmd>"
msgstr ""

#: src/commands.cc:404
msgid ""
"Send the command uninterpreted. Use with caution - it can lead to\n"
"unknown remote state and thus will cause reconnect. You cannot\n"
"be sure that any change of remote state because of quoted command\n"
"is solid - it can be reset by reconnect at any time.\n"
msgstr ""

#: src/commands.cc:409
msgid ""
"recls [<args>]\n"
"Same as `cls', but don't look in cache\n"
msgstr ""

#: src/commands.cc:412
#, fuzzy
msgid ""
"Usage: reget [OPTS] <rfile> [-o <lfile>]\n"
"Same as `get -c'\n"
msgstr "get [OPÇÕES] <arquivo_remoto> [-o <arquivo_local>]"

#: src/commands.cc:415
msgid ""
"Usage: rels [<args>]\n"
"Same as `ls', but don't look in cache\n"
msgstr ""

#: src/commands.cc:418
msgid ""
"Usage: renlist [<args>]\n"
"Same as `nlist', but don't look in cache\n"
msgstr ""

#: src/commands.cc:420
msgid "repeat [OPTS] [delay] [command]"
msgstr ""

#: src/commands.cc:422
#, fuzzy
msgid ""
"Usage: reput <lfile> [-o <rfile>]\n"
"Same as `put -c'\n"
msgstr "get [OPÇÕES] <arquivo_remoto> [-o <arquivo_local>]"

#: src/commands.cc:424
#, fuzzy
msgid "rm [-r] [-f] <files>"
msgstr "rm [-r] <arquivos>"

#: src/commands.cc:425
msgid ""
"Remove remote files\n"
" -r  recursive directory removal, be careful\n"
" -f  work quietly\n"
msgstr ""

#: src/commands.cc:428
#, fuzzy
msgid "rmdir [-f] <dirs>"
msgstr "rm [-r] <arquivos>"

#: src/commands.cc:429
msgid "Remove remote directories\n"
msgstr ""

#: src/commands.cc:430
msgid "scache [<session_no>]"
msgstr ""

#: src/commands.cc:431
msgid "List cached sessions or switch to specified session number\n"
msgstr ""

#: src/commands.cc:432
#, fuzzy
msgid "set [OPT] [<var> [<val>]]"
msgstr "get [OPÇÕES] <arquivo_remoto> [-o <arquivo_local>]"

#: src/commands.cc:433
msgid ""
"Set variable to given value. If the value is omitted, unset the variable.\n"
"Variable name has format ``name/closure'', where closure can specify\n"
"exact application of the setting. See lftp(1) for details.\n"
"If set is called with no variable then only altered settings are listed.\n"
"It can be changed by options:\n"
" -a  list all settings, including default values\n"
" -d  list only default values, not necessary current ones\n"
msgstr ""

#: src/commands.cc:441
msgid "site <site-cmd>"
msgstr ""

#: src/commands.cc:442
msgid ""
"Execute site command <site_cmd> and output the result\n"
"You can redirect its output\n"
msgstr ""

#: src/commands.cc:446
msgid ""
"Usage: slot [<label>]\n"
"List assigned slots.\n"
"If <label> is specified, switch to the slot named <label>.\n"
msgstr ""

#: src/commands.cc:449
msgid "source <file>"
msgstr ""

#: src/commands.cc:450
msgid "Execute commands recorded in file <file>\n"
msgstr ""

#: src/commands.cc:452
#, fuzzy
msgid "torrent [OPTS] <file|URL>..."
msgstr "rm [-r] <arquivos>"

#: src/commands.cc:453
msgid "user <user|URL> [<pass>]"
msgstr ""

#: src/commands.cc:454
msgid ""
"Use specified info for remote login. If you specify URL, the password\n"
"will be cached for future usage.\n"
msgstr ""

#: src/commands.cc:457
msgid "Shows lftp version\n"
msgstr ""

#: src/commands.cc:458
msgid "wait [<jobno>]"
msgstr ""

#: src/commands.cc:459
msgid ""
"Wait for specified job to terminate. If jobno is omitted, wait\n"
"for last backgrounded job.\n"
msgstr ""

#: src/commands.cc:461
#, fuzzy
msgid "zcat <files>"
msgstr "cat [-u] <arquivos>"

#: src/commands.cc:462
msgid "Same as cat, but filter each file through zcat\n"
msgstr ""

#: src/commands.cc:463
#, fuzzy
msgid "zmore <files>"
msgstr "rm [-r] <arquivos>"

#: src/commands.cc:464
msgid "Same as more, but filter each file through zcat\n"
msgstr ""

#: src/commands.cc:466
msgid "Same as cat, but filter each file through bzcat\n"
msgstr ""

#: src/commands.cc:468
msgid "Same as more, but filter each file through bzcat\n"
msgstr ""

#: src/commands.cc:524
#, c-format
msgid "Usage: %s local-dir\n"
msgstr ""

#: src/commands.cc:560
#, c-format
msgid "lcd ok, local cwd=%s\n"
msgstr ""

#: src/commands.cc:576
#, c-format
msgid "Usage: cd remote-dir\n"
msgstr ""

#: src/commands.cc:588
#, c-format
msgid "%s: no old directory for this site\n"
msgstr ""

#: src/commands.cc:677
#, c-format
msgid "Usage: %s [<exit_code>]\n"
msgstr ""

#: src/commands.cc:686
msgid ""
"There are running jobs and `cmd:move-background' is not set.\n"
"Use `exit bg' to force moving to background or `kill all' to terminate "
"jobs.\n"
msgstr ""

#: src/commands.cc:703
msgid ""
"\n"
"lftp now tricks the shell to move it to background process group.\n"
"lftp continues to run in the background despite the `Stopped' message.\n"
"lftp will automatically terminate when all jobs are finished.\n"
"Use `fg' shell command to return to lftp if it is still running.\n"
msgstr ""

#: src/commands.cc:837 src/commands.cc:3616
#, c-format
msgid "Try `%s --help' for more information\n"
msgstr ""

#: src/commands.cc:856
#, c-format
msgid "%s: -c, -f, -v, -h conflict with other `open' options and arguments\n"
msgstr ""

#: src/commands.cc:956
#, c-format
msgid "Usage: %s [-e cmd] [-p port] [-u user[,pass]] <host|url>\n"
msgstr ""

#: src/commands.cc:1046 src/commands.cc:2276 src/DummyProto.cc:65
#: src/MirrorJob.cc:2172 src/MirrorJob.cc:2194
#, fuzzy
msgid " - not supported protocol"
msgstr "%s: %s - protocolo não suportado\n"

#: src/commands.cc:1078 src/commands.cc:2260
msgid "Password: "
msgstr ""

#: src/commands.cc:1080
#, c-format
msgid "%s: GetPass() failed -- assume anonymous login\n"
msgstr ""

#: src/commands.cc:1191 src/commands.cc:1284 src/commands.cc:1660
#: src/commands.cc:1691 src/commands.cc:1829 src/commands.cc:1914
#: src/commands.cc:2187 src/commands.cc:2381 src/commands.cc:2543
#: src/commands.cc:2588 src/commands.cc:2616 src/commands.cc:2623
#: src/commands.cc:2930 src/commands.cc:2957 src/commands.cc:2964
#: src/commands.cc:3293 src/lftp.cc:267 src/MirrorJob.cc:2136
#: src/SleepJob.cc:144 src/SleepJob.cc:200 src/Torrent.cc:4169
#, c-format
msgid "Try `help %s' for more information.\n"
msgstr ""

#: src/commands.cc:1201
#, c-format
msgid "Usage: %s [OPTS] command args...\n"
msgstr ""

#: src/commands.cc:1254
#, c-format
msgid "%s: -n: positive number expected. "
msgstr ""

#: src/commands.cc:1306
#, c-format
msgid "Created a stopped queue.\n"
msgstr ""

#: src/commands.cc:1349 src/commands.cc:1377
#, c-format
msgid "%s: No queue is active.\n"
msgstr ""

#: src/commands.cc:1369
#, c-format
msgid "%s: -m: Number expected as second argument. "
msgstr ""

#: src/commands.cc:1421
#, c-format
msgid "Usage: %s <cmd>\n"
msgstr ""

#: src/commands.cc:1527
msgid "invalid argument for `--sort'"
msgstr ""

#: src/commands.cc:1557
#, fuzzy
msgid "invalid block size"
msgstr "número inválido"

#: src/commands.cc:1700
#, c-format
msgid "Usage: %s [OPTS] files...\n"
msgstr ""

#: src/commands.cc:1785 src/commands.cc:1814
#, c-format
msgid "%s: %s: Number expected. "
msgstr ""

#: src/commands.cc:1834
#, c-format
msgid "%s: --continue conflicts with --remove-target.\n"
msgstr ""

#: src/commands.cc:1844 src/commands.cc:1920
#, c-format
msgid "File name missed. "
msgstr ""

#: src/commands.cc:1989
#, fuzzy, c-format
msgid "Usage: %s %s[-f] files...\n"
msgstr "mget [-c] [-d] [-e] <arquivos>"

#: src/commands.cc:2032
#, fuzzy, c-format
msgid "Usage: %s [-e] <file|command>\n"
msgstr "mget [-c] [-d] [-e] <arquivos>"

#: src/commands.cc:2079
#, c-format
msgid "Usage: %s [-v] [-v] ...\n"
msgstr ""

#: src/commands.cc:2093 src/commands.cc:2347 src/commands.cc:2469
#: src/commands.cc:2685 src/commands.cc:3101 src/commands.cc:3182
#: src/lftp.cc:279
#, c-format
msgid "%s: %s - not a number\n"
msgstr ""

#: src/commands.cc:2100 src/commands.cc:2326 src/commands.cc:2356
#: src/commands.cc:2487
#, c-format
msgid "%s: %d - no such job\n"
msgstr ""

#: src/commands.cc:2135
#, c-format
msgid "Usage: %s [-p]\n"
msgstr ""

#: src/commands.cc:2227
#, c-format
msgid "debug level is %d, output goes to %s\n"
msgstr ""

#: src/commands.cc:2230
#, c-format
msgid "debug is off\n"
msgstr ""

#: src/commands.cc:2241
#, c-format
msgid "Usage: %s <user|URL> [<pass>]\n"
msgstr ""

#: src/commands.cc:2316 src/commands.cc:2479
#, c-format
msgid "%s: no current job\n"
msgstr ""

#: src/commands.cc:2328
#, c-format
msgid "Usage: %s <jobno> ... | all\n"
msgstr ""

#: src/commands.cc:2409
#, c-format
msgid "%s: %s. Use `set -a' to look at all variables.\n"
msgstr ""

#: src/commands.cc:2453
#, c-format
msgid "Usage: %s [<jobno>]\n"
msgstr ""

#: src/commands.cc:2492
#, c-format
msgid "%s: some other job waits for job %d\n"
msgstr ""

#: src/commands.cc:2497
#, c-format
msgid "%s: wait loop detected\n"
msgstr ""

#: src/commands.cc:2553
#, fuzzy, c-format
msgid "Usage: %s [OPTS] <files> <target-dir>\n"
msgstr "mget [-c] [-d] [-e] <arquivos>"

#: src/commands.cc:2615 src/commands.cc:2956
#, c-format
msgid "Invalid command. "
msgstr ""

#: src/commands.cc:2622 src/commands.cc:2963
#, c-format
msgid "Ambiguous command. "
msgstr ""

#: src/commands.cc:2641
#, c-format
msgid "%s: Operand missed for size\n"
msgstr ""

#: src/commands.cc:2658
#, c-format
msgid "%s: Operand missed for `expire'\n"
msgstr ""

#: src/commands.cc:2691
#, c-format
msgid ""
"%s: %s - no such cached session. Use `scache' to look at session list.\n"
msgstr ""

#: src/commands.cc:2715
#, c-format
msgid "Sorry, no help for %s\n"
msgstr ""

#: src/commands.cc:2720
#, c-format
msgid "%s is a built-in alias for %s\n"
msgstr ""

#: src/commands.cc:2725
#, c-format
msgid "Usage: %s\n"
msgstr ""

#: src/commands.cc:2733
#, c-format
msgid "%s is an alias to `%s'\n"
msgstr ""

#: src/commands.cc:2737
#, c-format
msgid "No such command `%s'. Use `help' to see available commands.\n"
msgstr ""

#: src/commands.cc:2739
#, c-format
msgid "Ambiguous command `%s'. Use `help' to see available commands.\n"
msgstr ""

#: src/commands.cc:2805
#, c-format
msgid "LFTP | Version %s | Copyright (c) 1996-%d Alexander V. Lukyanov\n"
msgstr ""

#: src/commands.cc:2808
#, c-format
msgid ""
"LFTP is free software: you can redistribute it and/or modify\n"
"it under the terms of the GNU General Public License as published by\n"
"the Free Software Foundation, either version 3 of the License, or\n"
"(at your option) any later version.\n"
"\n"
"This program is distributed in the hope that it will be useful,\n"
"but WITHOUT ANY WARRANTY; without even the implied warranty of\n"
"MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n"
"GNU General Public License for more details.\n"
"\n"
"You should have received a copy of the GNU General Public License\n"
"along with LFTP.  If not, see <http://www.gnu.org/licenses/>.\n"
msgstr ""

#: src/commands.cc:2822
#, c-format
msgid "Send bug reports and questions to the mailing list <%s>.\n"
msgstr ""

#: src/commands.cc:2828
msgid "Libraries used: "
msgstr ""

#: src/commands.cc:2979 src/commands.cc:3007
#, c-format
msgid "%s: bookmark name required\n"
msgstr ""

#: src/commands.cc:2996
#, c-format
msgid "%s: spaces in bookmark name are not allowed\n"
msgstr ""

#: src/commands.cc:3009
#, c-format
msgid "%s: no such bookmark `%s'\n"
msgstr ""

#: src/commands.cc:3032
#, c-format
msgid "%s: import type required (netscape,ncftp)\n"
msgstr ""

#: src/commands.cc:3110
#, fuzzy, c-format
msgid "Usage: %s [-d #] dir\n"
msgstr "mget [-c] [-d] [-e] <arquivos>"

#: src/commands.cc:3215
#, fuzzy, c-format
msgid "%s: invalid block size `%s'\n"
msgstr "número inválido"

#: src/commands.cc:3226
#, fuzzy, c-format
msgid "Usage: %s [options] <dirs>\n"
msgstr "mget [-c] [-d] [-e] <arquivos>"

#: src/commands.cc:3232
#, c-format
msgid "%s: warning: summarizing is the same as using --max-depth=0\n"
msgstr ""

#: src/commands.cc:3236
#, c-format
msgid "%s: summarizing conflicts with --max-depth=%i\n"
msgstr ""

#: src/commands.cc:3280
#, c-format
msgid "Usage: %s command args...\n"
msgstr ""

#: src/commands.cc:3292
#, c-format
msgid "Usage: %s module [args...]\n"
msgstr ""

#: src/commands.cc:3310 src/LocalAccess.cc:642
#, fuzzy
msgid "cannot get current directory"
msgstr "Criando diretório remoto `%s'"

#: src/commands.cc:3374
#, fuzzy, c-format
msgid "Usage: %s [OPTS] mode file...\n"
msgstr "mget [-c] [-d] [-e] <arquivos>"

#: src/commands.cc:3394
#, fuzzy, c-format
msgid "invalid mode string: %s\n"
msgstr "número inválido"

#: src/commands.cc:3457 src/commands.cc:3466
msgid "Invalid range format. Format is min-max, e.g. 10-20."
msgstr ""

#: src/commands.cc:3478
#, c-format
msgid "Usage: %s [OPTS] file\n"
msgstr ""

#: src/CopyJob.cc:82
#, fuzzy, c-format
msgid "`%s' at %lld %s%s%s%s"
msgstr "`%s' em %lu %s%s%s[%s]"

#: src/CopyJob.cc:159
#, fuzzy, c-format
msgid "%lld $#ll#byte|bytes$ transferred in %ld $#l#second|seconds$"
msgstr "%ld bytes transmitidos em %ld segundos (%g bytes/s)\n"

#: src/CopyJob.cc:167
#, fuzzy, c-format
msgid "%lld $#ll#byte|bytes$ transferred"
msgstr "%ld bytes transmitidos\n"

#: src/CopyJob.cc:283
#, c-format
msgid "Transfer of %d of %d $file|files$ failed\n"
msgstr "Transferência de %d de %d arquivos falhou\n"

#: src/CopyJob.cc:289
#, fuzzy, c-format
msgid "Total %d $file|files$ transferred\n"
msgstr "%d arquivos transmitidos"

#: src/FileAccess.cc:160
#, fuzzy
msgid "Access failed: "
msgstr "Falha no acesso: %s"

#: src/FileAccess.cc:161
msgid "File cannot be accessed"
msgstr "Arquivo não pode ser acessado"

#: src/FileAccess.cc:163 src/Fish.cc:982 src/ftpclass.cc:4599
#: src/ftpclass.cc:4608 src/SFtp.cc:1339 src/Torrent.cc:3533
msgid "Not connected"
msgstr "Não conectado"

#: src/FileAccess.cc:166 src/FileAccess.cc:167
msgid "Fatal error"
msgstr "Erro fatal"

#: src/FileAccess.cc:169
msgid "Store failed - you have to reput"
msgstr "Falha no armazenamento - você terá que reenviar"

#: src/FileAccess.cc:172 src/FileAccess.cc:173
msgid "Login failed"
msgstr "Falha na conexão"

#: src/FileAccess.cc:176 src/FileAccess.cc:177
msgid "Operation not supported"
msgstr "Operação não suportada"

#: src/FileAccess.cc:180
msgid "File moved"
msgstr ""

#: src/FileAccess.cc:182
msgid "File moved to `"
msgstr ""

#: src/FileCopy.cc:141
msgid "copy: destination file is already complete\n"
msgstr ""

#: src/FileCopy.cc:182
msgid "copy: put is broken\n"
msgstr ""

#: src/FileCopy.cc:201
#, fuzzy
msgid "seek failed"
msgstr "Falha na conexão"

#: src/FileCopy.cc:207
msgid "no progress timeout"
msgstr ""

#: src/FileCopy.cc:235
msgid "cannot seek on data source"
msgstr ""

#: src/FileCopy.cc:238
#, c-format
msgid "copy: put rolled back to %lld, seeking get accordingly\n"
msgstr ""

#: src/FileCopy.cc:251
msgid "copy: all data received, but get rolled back\n"
msgstr ""

#: src/FileCopy.cc:267
#, c-format
msgid "copy: get rolled back to %lld, seeking put accordingly\n"
msgstr ""

#: src/FileCopy.cc:364
msgid "file size decreased during transfer"
msgstr ""

#: src/FileCopy.cc:1227
#, c-format
msgid "copy: received redirection to `%s'\n"
msgstr ""

#: src/FileCopy.cc:1290
msgid "file size increased during transfer"
msgstr ""

#: src/FileCopy.cc:1390
msgid "file name missed in URL"
msgstr ""

#: src/FileCopy.cc:2086
msgid "Verify command failed without a message"
msgstr ""

#: src/FileCopyFtp.cc:95
msgid "**** FXP: trying to reverse ftp:fxp-passive-source\n"
msgstr ""

#: src/FileCopyFtp.cc:102
msgid "**** FXP: trying to reverse ftp:fxp-passive-sscn\n"
msgstr ""

#: src/FileCopyFtp.cc:110
msgid "**** FXP: trying to reverse ftp:ssl-protect-fxp\n"
msgstr ""

#: src/FileCopyFtp.cc:116
msgid "**** FXP: giving up, reverting to plain copy\n"
msgstr ""

#: src/FileCopyFtp.cc:125
msgid "ftp:fxp-force is set but FXP is not available"
msgstr ""

#: src/FileCopy.h:285
msgid "Verifying..."
msgstr ""

#: src/FileSetOutput.cc:230
msgid "non-option arguments found"
msgstr ""

#: src/Filter.cc:166
#, fuzzy
msgid "pipe() failed: "
msgstr "falha em execlp(%s): %s\n"

#: src/Filter.cc:200 src/PtyShell.cc:135
#, fuzzy, c-format
msgid "chdir(%s) failed: %s\n"
msgstr "Atenção: chdir() falhou: %s\n"

#: src/Filter.cc:208
#, fuzzy, c-format
msgid "execvp(%s) failed: %s\n"
msgstr "falha em execlp(%s): %s\n"

#: src/Filter.cc:213 src/PtyShell.cc:147
#, fuzzy, c-format
msgid "execl(/bin/sh) failed: %s\n"
msgstr "falha em execlp(%s): %s\n"

#: src/Filter.cc:415
msgid "file already exists and xfer:clobber is unset"
msgstr ""

#: src/FindJobDu.cc:101
msgid "total"
msgstr ""

#: src/Fish.cc:75 src/ftpclass.cc:1292 src/Http.cc:1232 src/SFtp.cc:77
#, c-format
msgid "Closing idle connection"
msgstr ""

#: src/Fish.cc:162 src/SFtp.cc:177
msgid "Running connect program"
msgstr ""

#: src/Fish.cc:588 src/ftpclass.cc:2887 src/ftpclass.cc:3107 src/Http.cc:1510
#: src/SFtp.cc:742 src/SFtp.cc:1083 src/SFtp.cc:1084 src/SSH_Access.cc:167
#, c-format
msgid "Peer closed connection"
msgstr ""

#: src/Fish.cc:634 src/ftpclass.cc:3037 src/ftpclass.cc:4219 src/SFtp.cc:1108
#, c-format
msgid "extra server response"
msgstr ""

#: src/Fish.cc:987 src/ftpclass.cc:4611 src/Http.cc:2329 src/Http.cc:2336
#: src/SFtp.cc:1345 src/Torrent.cc:3536 src/TorrentTracker.cc:624
msgid "Connecting..."
msgstr ""

#: src/Fish.cc:989 src/ftpclass.cc:4617 src/SFtp.cc:1347
#, fuzzy
msgid "Connected"
msgstr "Não conectado"

#: src/Fish.cc:991 src/ftpclass.cc:4594 src/ftpclass.cc:4622
#: src/ftpclass.cc:4636 src/Http.cc:2338 src/SFtp.cc:1349
#: src/TorrentTracker.cc:626
msgid "Waiting for response..."
msgstr ""

#: src/Fish.cc:993 src/ftpclass.cc:4656 src/Http.cc:2341 src/SFtp.cc:1351
msgid "Receiving data"
msgstr ""

#: src/Fish.cc:995 src/ftpclass.cc:4654 src/Http.cc:2334 src/SFtp.cc:1353
msgid "Sending data"
msgstr ""

#: src/Fish.cc:997 src/SFtp.cc:1355
msgid "Done"
msgstr ""

#: src/Fish.cc:1136 src/FtpDirList.cc:123 src/HttpDir.cc:1384 src/SFtp.cc:2200
#: src/SFtp.cc:2320
#, fuzzy, c-format
msgid "Getting file list (%lld) [%s]"
msgstr "\tExecutando comando embutido `%s' [%s]\n"

#: src/ftpclass.cc:197
#, c-format
msgid "Data connection peer has wrong port number"
msgstr ""

#: src/ftpclass.cc:203
#, c-format
msgid "Data connection peer has mismatching address"
msgstr ""

#: src/ftpclass.cc:225 src/ftpclass.cc:250
#, c-format
msgid "Switching to NOREST mode"
msgstr ""

#: src/ftpclass.cc:425
#, c-format
msgid "Server reply matched ftp:retry-530, retrying"
msgstr ""

#: src/ftpclass.cc:433
#, c-format
msgid "Server reply matched ftp:retry-530-anonymous, retrying"
msgstr ""

#: src/ftpclass.cc:466
msgid "Account is required, set ftp:acct variable"
msgstr ""

#: src/ftpclass.cc:483
msgid "ftp:skey-force is set and server does not support OPIE nor S/KEY"
msgstr ""

#: src/ftpclass.cc:501
#, c-format
msgid "assuming failed host name lookup"
msgstr ""

#: src/ftpclass.cc:809 src/ftpclass.cc:810
#, c-format
msgid "cannot parse EPSV response"
msgstr ""

#: src/ftpclass.cc:837 src/ftpclass.cc:838
#, c-format
msgid "cannot parse custom EPSV response"
msgstr ""

#: src/ftpclass.cc:1122
#, c-format
msgid "Closing control socket"
msgstr ""

#: src/ftpclass.cc:1341
msgid "MLSD is disabled by ftp:use-mlsd"
msgstr ""

#: src/ftpclass.cc:1411 src/Http.cc:1431 src/Torrent.cc:3204
#: src/Torrent.cc:3743 src/TorrentTracker.cc:395
#, c-format
msgid "cannot create socket of address family %d"
msgstr ""

#: src/ftpclass.cc:1442 src/Http.cc:1457
#, c-format
msgid "Socket error (%s) - reconnecting"
msgstr "Erro no socket (%s) - reconectando"

#: src/ftpclass.cc:1715
msgid "MFF and SITE CHMOD are not supported by this site"
msgstr ""

#: src/ftpclass.cc:1720
msgid "MLST and MLSD are not supported by this site"
msgstr ""

#: src/ftpclass.cc:2031
msgid "SITE SYMLINK is not supported by the server"
msgstr ""

#: src/ftpclass.cc:2204
#, fuzzy
msgid "unsupported network protocol"
msgstr "%s: %s - protocolo não suportado\n"

#: src/ftpclass.cc:2253 src/ftpclass.cc:2355
#, fuzzy, c-format
msgid "Data socket error (%s) - reconnecting"
msgstr "Erro no socket (%s) - reconectando"

#: src/ftpclass.cc:2281
#, c-format
msgid "Accepted data connection from (%s) port %u"
msgstr ""

#: src/ftpclass.cc:2330
#, c-format
msgid "Connecting data socket to (%s) port %u"
msgstr ""

#: src/ftpclass.cc:2336
#, c-format
msgid "Connecting data socket to proxy %s (%s) port %u"
msgstr ""

#: src/ftpclass.cc:2358 src/ftpclass.cc:4414
#, c-format
msgid "Switching passive mode off"
msgstr ""

#: src/ftpclass.cc:2366
#, c-format
msgid "Data connection established"
msgstr ""

#: src/ftpclass.cc:2688 src/ftpclass.cc:4548
msgid "ftp:ssl-force is set and server does not support or allow SSL"
msgstr ""

#: src/ftpclass.cc:3053
#, c-format
msgid "Persist and retry"
msgstr ""

#: src/ftpclass.cc:3321
#, c-format
msgid "Closing data socket"
msgstr ""

#: src/ftpclass.cc:3343
#, c-format
msgid "Closing aborted data socket"
msgstr ""

#: src/ftpclass.cc:4193
#, c-format
msgid "saw file size in response"
msgstr ""

#: src/ftpclass.cc:4233 src/ftpclass.cc:4260
#, c-format
msgid "Turning on sync-mode"
msgstr ""

#: src/ftpclass.cc:4434
#, c-format
msgid "Switching passive mode on"
msgstr ""

#: src/ftpclass.cc:4585
msgid "FEAT negotiation..."
msgstr ""

#: src/ftpclass.cc:4592
msgid "Sending commands..."
msgstr ""

#: src/ftpclass.cc:4596
msgid "Delaying before retry"
msgstr ""

#: src/ftpclass.cc:4597 src/Http.cc:2331
msgid "Connection idle"
msgstr ""

#: src/ftpclass.cc:4604 src/Http.cc:2323 src/Resolver.cc:172
#: src/Resolver.cc:217 src/TorrentTracker.cc:622
#, c-format
msgid "Resolving host address..."
msgstr ""

#: src/ftpclass.cc:4615
msgid "TLS negotiation..."
msgstr ""

#: src/ftpclass.cc:4619
msgid "Logging in..."
msgstr ""

#: src/ftpclass.cc:4623
msgid "Making data connection..."
msgstr ""

#: src/ftpclass.cc:4626
msgid "Changing remote directory..."
msgstr ""

#: src/ftpclass.cc:4632
msgid "Waiting for other copy peer..."
msgstr ""

#: src/ftpclass.cc:4634 src/ftpclass.cc:4658
msgid "Waiting for transfer to complete"
msgstr ""

#: src/ftpclass.cc:4638
msgid "Waiting for TLS shutdown..."
msgstr ""

#: src/ftpclass.cc:4640
msgid "Waiting for data connection..."
msgstr ""

#: src/ftpclass.cc:4646
msgid "Sending data/TLS"
msgstr ""

#: src/ftpclass.cc:4648
msgid "Receiving data/TLS"
msgstr ""

#: src/Http.cc:230
#, c-format
msgid "Closing HTTP connection"
msgstr ""

#: src/Http.cc:240
msgid "POST method failed"
msgstr ""

#: src/Http.cc:1381
msgid "ftp over http cannot work without proxy, set hftp:proxy."
msgstr ""

#: src/Http.cc:1537
#, c-format
msgid "Sending request..."
msgstr ""

#: src/Http.cc:1575
#, c-format
msgid "Hit EOF while fetching headers"
msgstr ""

#: src/Http.cc:1695
#, c-format
msgid "Could not parse HTTP status line"
msgstr ""

#: src/Http.cc:1726
msgid "Object is not cached and http:cache-control has only-if-cached"
msgstr ""

#: src/Http.cc:1891
#, c-format
msgid "Receiving body..."
msgstr ""

#: src/Http.cc:2092
#, c-format
msgid "Hit EOF"
msgstr ""

#: src/Http.cc:2095
#, c-format
msgid "Received not enough data, retrying"
msgstr ""

#: src/Http.cc:2106
#, c-format
msgid "Received all"
msgstr ""

#: src/Http.cc:2111
#, c-format
msgid "Received all (total)"
msgstr ""

#: src/Http.cc:2135 src/Http.cc:2159
msgid "chunked format violated"
msgstr ""

#: src/Http.cc:2145
#, c-format
msgid "Received last chunk"
msgstr ""

#: src/Http.cc:2339
msgid "Fetching headers..."
msgstr ""

#: src/Job.cc:293
#, c-format
msgid "[%d] Done (%s)"
msgstr "[%d] Feito (%s)"

#: src/lftp.cc:370
#, c-format
msgid "[%u] Terminated by signal %d. %s\n"
msgstr ""

#: src/lftp.cc:445
#, c-format
msgid "[%u] Started.  %s\n"
msgstr ""

#: src/lftp.cc:463
#, c-format
msgid "[%u] Detaching from the terminal to complete transfers...\n"
msgstr ""

#: src/lftp.cc:465
#, c-format
msgid "[%u] Exiting and detaching from the terminal.\n"
msgstr ""

#: src/lftp.cc:470
#, c-format
msgid "[%u] Detached from terminal. %s\n"
msgstr ""

#: src/lftp.cc:480
#, c-format
msgid "[%u] Finished. %s\n"
msgstr ""

#: src/lftp.cc:484
#, c-format
msgid "[%u] Moving to background to complete transfers...\n"
msgstr ""

#: src/lftp.cc:553
msgid "history -w file|-r file|-c|-l [cnt]"
msgstr ""

#: src/lftp.cc:554
msgid ""
" -w <file> Write history to file.\n"
" -r <file> Read history from file; appends to current history.\n"
" -c  Clear the history.\n"
" -l  List the history (default).\n"
"Optional argument cnt specifies the number of history lines to list,\n"
"or \"all\" to list all entries.\n"
msgstr ""

#: src/lftp.cc:561
msgid "Attach the terminal to specified backgrounded lftp process.\n"
msgstr ""

#: src/lftp_ssl.cc:1291
#, c-format
msgid "No certificate presented by %s.\n"
msgstr ""

#: src/LocalAccess.cc:604 src/NetAccess.cc:686
#, fuzzy
msgid "Getting directory contents"
msgstr "Buscando conteúdo do diretório (%ld)"

#: src/LocalAccess.cc:606 src/NetAccess.cc:690
#, fuzzy
msgid "Getting files information"
msgstr "Buscando informações dos arquivos (%d%%)"

#: src/LsCache.cc:190
#, c-format
msgid "%ld $#l#byte|bytes$ cached"
msgstr "%ld bytes no cache"

#: src/LsCache.cc:194
msgid ", no size limit"
msgstr ", sem limite de tamanho"

#: src/LsCache.cc:196
#, c-format
msgid ", maximum size %ld\n"
msgstr ", tamanho máximo %ld\n"

#: src/mgetJob.cc:78
#, fuzzy, c-format
msgid "%s: %s: no files found\n"
msgstr "%s: nenhum arquivo encontrado\n"

#: src/MirrorJob.cc:100
#, c-format
msgid "%sTotal: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
msgstr ""

#: src/MirrorJob.cc:104
#, c-format
msgid "%sNew: %d file$|s$, %d symlink$|s$\n"
msgstr ""

#: src/MirrorJob.cc:108
#, c-format
msgid "%sModified: %d file$|s$, %d symlink$|s$\n"
msgstr ""

#: src/MirrorJob.cc:115
#, c-format
msgid "%sRemoved: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
msgstr ""

#: src/MirrorJob.cc:120
#, fuzzy, c-format
msgid "%s%d error$|s$ detected\n"
msgstr "%s: nenhum arquivo encontrado\n"

#: src/MirrorJob.cc:231
#, c-format
msgid "Finished %s"
msgstr ""

#: src/MirrorJob.cc:344 src/MirrorJob.cc:519 src/MirrorJob.cc:1218
#, fuzzy, c-format
msgid "Removing old file `%s'"
msgstr "Removendo arquivo local antigo `%s'"

#: src/MirrorJob.cc:346
#, fuzzy, c-format
msgid "Overwriting old file `%s'"
msgstr "Removendo arquivo local antigo `%s'"

#: src/MirrorJob.cc:355
#, c-format
msgid "Skipping file `%s' (only-existing)"
msgstr ""

#: src/MirrorJob.cc:361
#, fuzzy, c-format
msgid "Transferring file `%s'"
msgstr "Enviando arquivo local `%s'"

#: src/MirrorJob.cc:439
#, fuzzy, c-format
msgid "Skipping directory `%s' (only-existing)"
msgstr "Criando diretório remoto `%s'"

#: src/MirrorJob.cc:461 src/MirrorJob.cc:550 src/MirrorJob.cc:902
#, c-format
msgid "Removing old local file `%s'"
msgstr "Removendo arquivo local antigo `%s'"

#: src/MirrorJob.cc:487
#, fuzzy, c-format
msgid "Scanning directory `%s'"
msgstr "Criando diretório remoto `%s'"

#: src/MirrorJob.cc:489
#, fuzzy, c-format
msgid "Mirroring directory `%s'"
msgstr "Criando diretório remoto `%s'"

#: src/MirrorJob.cc:525 src/MirrorJob.cc:567
#, c-format
msgid "Making symbolic link `%s' to `%s'"
msgstr "Criando link simbólico de `%s' para `%s'"

#: src/MirrorJob.cc:562
#, c-format
msgid "Skipping symlink `%s' (only-existing)"
msgstr ""

#: src/MirrorJob.cc:807
#, c-format
msgid "mirror: protocol `%s' is not suitable for mirror\n"
msgstr "mirror: o protocolo `%s' não pode ser espelhado\n"

#: src/MirrorJob.cc:923
#, fuzzy, c-format
msgid "Making directory `%s'"
msgstr "Criando diretório remoto `%s'"

#: src/MirrorJob.cc:1181
#, fuzzy, c-format
msgid "Old directory `%s' is not removed"
msgstr "%s: nenhum arquivo encontrado\n"

#: src/MirrorJob.cc:1183
#, c-format
msgid "Old file `%s' is not removed"
msgstr ""

#: src/MirrorJob.cc:1216
#, fuzzy, c-format
msgid "Removing old directory `%s'"
msgstr "Removendo arquivo remoto antigo `%s'"

#: src/MirrorJob.cc:1339
#, fuzzy, c-format
msgid "Removing source directory `%s'"
msgstr "Removendo arquivo remoto antigo `%s'"

#: src/MirrorJob.cc:1405
#, fuzzy, c-format
msgid "Removing source file `%s'"
msgstr "Removendo arquivo local antigo `%s'"

#: src/MirrorJob.cc:1425
#, c-format
msgid "Retrying mirror...\n"
msgstr ""

#: src/MirrorJob.cc:1694
msgid "pattern is empty"
msgstr ""

#: src/MirrorJob.cc:1779
#, c-format
msgid "%s must be one of: %s"
msgstr ""

#: src/MirrorJob.cc:2019
#, c-format
msgid ""
"%s: multiple --file or --directory options must have the same base "
"directory\n"
msgstr ""

#: src/MirrorJob.cc:2160
#, c-format
msgid "%s: ambiguous source directory (`%s' or `%s'?)\n"
msgstr ""

#: src/MirrorJob.cc:2182
#, c-format
msgid "%s: ambiguous target directory (`%s' or `%s'?)\n"
msgstr ""

#: src/MirrorJob.cc:2223
#, c-format
msgid "%s: source directory is required (mirror:require-source is set)\n"
msgstr ""

#: src/MirrorJob.cc:2343
msgid ""
"\n"
"Mirror specified remote directory to local directory\n"
"\n"
" -R, --reverse          reverse mirror (put files)\n"
"Lots of other options are documented in the man page lftp(1).\n"
"\n"
"When using -R, the first directory is local and the second is remote.\n"
"If the second directory is omitted, basename of the first directory is "
"used.\n"
"If both directories are omitted, current local and remote directories are "
"used.\n"
"\n"
"See the man page lftp(1) for a complete documentation.\n"
msgstr ""

#: src/mkdirJob.cc:128
#, c-format
msgid "%s ok, `%s' created\n"
msgstr ""

#: src/mkdirJob.cc:130 src/rmJob.cc:51
#, fuzzy, c-format
msgid "%s failed for %d of %d director$y|ies$\n"
msgstr "Transferência de %d de %d arquivos falhou\n"

#: src/mkdirJob.cc:133
#, fuzzy, c-format
msgid "%s ok, %d director$y|ies$ created\n"
msgstr "%s: nenhum arquivo encontrado\n"

#: src/module.cc:190
#, c-format
msgid "depend module `%s': %s\n"
msgstr ""

#: src/module.cc:210
msgid "modules are not supported on this system"
msgstr ""

#: src/mvJob.cc:92
#, c-format
msgid "rename successful\n"
msgstr ""

#: src/NetAccess.cc:168
#, c-format
msgid "Connecting to %s%s (%s) port %u"
msgstr ""

#: src/NetAccess.cc:224 src/Torrent.cc:3326
#, c-format
msgid "Timeout - reconnecting"
msgstr ""

#: src/NetAccess.cc:323
msgid "Connection limit reached"
msgstr ""

#: src/NetAccess.cc:330
msgid "Delaying before reconnect"
msgstr ""

#: src/NetAccess.cc:354 src/NetAccess.cc:356
msgid "max-retries exceeded"
msgstr ""

#: src/OutputJob.cc:145
#, c-format
msgid "%s (filter)"
msgstr ""

#: src/parsecmd.cc:290
#, fuzzy
msgid "parse: missing filter command\n"
msgstr "Atenção: descartando comando incompleto\n"

#: src/parsecmd.cc:292
msgid "parse: missing redirection filename\n"
msgstr ""

#: src/PatternSet.cc:110
#, c-format
msgid "regular expression `%s': %s"
msgstr ""

#: src/pgetJob.cc:127
msgid "pget: falling back to plain get"
msgstr ""

#: src/pgetJob.cc:131
msgid "the target file is remote"
msgstr ""

#: src/pgetJob.cc:136
msgid "the source file size is unknown"
msgstr ""

#: src/pgetJob.cc:173
#, c-format
msgid "pget: warning: space allocation for %s (%lld bytes) failed: %s\n"
msgstr ""

#: src/pgetJob.cc:234
#, fuzzy, c-format
msgid "`%s', got %lld of %lld (%d%%) %s%s"
msgstr "`%s' em %lu %s%s[%s]"

#: src/plural.c:96
msgid "=1 =0|>1"
msgstr ""

#: src/PollVec.cc:69
#, c-format
msgid "%s: BUG - deadlock detected\n"
msgstr ""

#: src/PtyShell.cc:68
msgid "pseudo-tty allocation failed: "
msgstr ""

#: src/QueueFeeder.cc:68
msgid "Added job$|s$"
msgstr ""

#: src/QueueFeeder.cc:164 src/QueueFeeder.cc:185
#, c-format
msgid "No queued jobs.\n"
msgstr ""

#: src/QueueFeeder.cc:166
#, c-format
msgid "No queued job #%i.\n"
msgstr ""

#: src/QueueFeeder.cc:171 src/QueueFeeder.cc:192
msgid "Deleted job$|s$"
msgstr ""

#: src/QueueFeeder.cc:187
#, c-format
msgid "No queued jobs match \"%s\".\n"
msgstr ""

#: src/QueueFeeder.cc:212 src/QueueFeeder.cc:230
msgid "Moved job$|s$"
msgstr ""

#: src/QueueFeeder.cc:354
msgid "Commands queued:"
msgstr ""

#: src/ResMgr.cc:123
msgid "no such variable"
msgstr "variável desconhecida"

#: src/ResMgr.cc:127
msgid "ambiguous variable name"
msgstr "nome ambíguo de variável"

#: src/ResMgr.cc:335
msgid "invalid boolean value"
msgstr "valor booleano inválido"

#: src/ResMgr.cc:357
#, fuzzy
msgid "invalid boolean/auto value"
msgstr "valor booleano inválido"

#: src/ResMgr.cc:402 src/ResMgr.cc:713
msgid "invalid number"
msgstr "número inválido"

#: src/ResMgr.cc:415
#, fuzzy
msgid "invalid floating point number"
msgstr "número inválido"

#: src/ResMgr.cc:429
#, fuzzy
msgid "invalid unsigned number"
msgstr "número inválido"

#: src/ResMgr.cc:678
msgid "Invalid time unit letter, only [smhd] are allowed."
msgstr ""

#: src/ResMgr.cc:686
msgid "Invalid time format. Format is <time><unit>, e.g. 2h30m."
msgstr ""

#: src/ResMgr.cc:718
msgid "integer overflow"
msgstr ""

#: src/ResMgr.cc:804
msgid "Invalid IPv4 numeric address"
msgstr ""

#: src/ResMgr.cc:814
msgid "Invalid IPv6 numeric address"
msgstr ""

#: src/ResMgr.cc:884 src/ResMgr.cc:888
#, fuzzy
msgid "this encoding is not supported"
msgstr "Operação não suportada"

#: src/ResMgr.cc:894
msgid "no closure defined for this setting"
msgstr ""

#: src/ResMgr.cc:900
msgid "a closure is required for this setting"
msgstr ""

#: src/Resolver.cc:236
msgid "host name resolve timeout"
msgstr ""

#: src/Resolver.cc:282
#, c-format
msgid "%d address$|es$ found"
msgstr ""

#: src/Resolver.cc:327
msgid "Link-local IPv6 address should have a scope"
msgstr ""

#: src/Resolver.cc:765
msgid "DNS resolution not trusted."
msgstr ""

#: src/Resolver.cc:871
msgid "Host name lookup failure"
msgstr ""

#: src/Resolver.cc:903
#, fuzzy, c-format
msgid "no such %s service"
msgstr "variável desconhecida"

#: src/Resolver.cc:930
msgid "No address found"
msgstr ""

#: src/resource.cc:50 src/resource.cc:108
msgid "Proxy protocol unsupported"
msgstr ""

#: src/resource.cc:54
msgid "ftp:proxy password: "
msgstr ""

#: src/resource.cc:70
#, c-format
msgid "%s must be one of: "
msgstr ""

#: src/resource.cc:72
msgid "must be one of: "
msgstr ""

#: src/resource.cc:84
msgid ", or empty"
msgstr ""

#: src/resource.cc:116
msgid "only PUT and POST values allowed"
msgstr ""

#: src/resource.cc:148
#, c-format
msgid "unknown address family `%s'"
msgstr ""

#: src/rmJob.cc:47
#, c-format
msgid "%s ok, `%s' removed\n"
msgstr ""

#: src/rmJob.cc:54
#, fuzzy, c-format
msgid "%s failed for %d of %d file$|s$\n"
msgstr "Transferência de %d de %d arquivos falhou\n"

#: src/rmJob.cc:60
#, fuzzy, c-format
msgid "%s ok, %d director$y|ies$ removed\n"
msgstr "%s: nenhum arquivo encontrado\n"

#: src/rmJob.cc:63
#, fuzzy, c-format
msgid "%s ok, %d file$|s$ removed\n"
msgstr "%s: nenhum arquivo encontrado\n"

#: src/SFtp.cc:1099 src/SFtp.cc:1100
#, c-format
msgid "invalid server response format"
msgstr ""

#: src/SleepJob.cc:99
msgid "Sleeping forever"
msgstr ""

#: src/SleepJob.cc:100
msgid "Sleep time left: "
msgstr ""

#: src/SleepJob.cc:108
#, c-format
msgid "\tRepeat count: %d\n"
msgstr ""

#: src/SleepJob.cc:142
#, c-format
msgid "%s: argument required. "
msgstr ""

#: src/SleepJob.cc:262
#, c-format
msgid "%s: date-time specification missed\n"
msgstr ""

#: src/SleepJob.cc:269
#, c-format
msgid "%s: date-time parse error\n"
msgstr ""

#: src/SleepJob.cc:303
msgid ""
"Usage: sleep <time>[unit]\n"
"Sleep for given amount of time. The time argument can be optionally\n"
"followed by unit specifier: d - days, h - hours, m - minutes, s - seconds.\n"
"By default time is assumed to be seconds.\n"
msgstr ""

#: src/SleepJob.cc:310
msgid ""
"Repeat specified command with a delay between iterations.\n"
"Default delay is one second, default command is empty.\n"
" -c <count>  maximum number of iterations\n"
" -d <delay>  delay between iterations\n"
" --while-ok  stop when command exits with non-zero code\n"
" --until-ok  stop when command exits with zero code\n"
" --weak      stop when lftp moves to background.\n"
msgstr ""

#: src/Speedometer.cc:90
#, c-format
msgid "%.0fb/s"
msgstr ""

#: src/Speedometer.cc:93
#, c-format
msgid "%.1fK/s"
msgstr ""

#: src/Speedometer.cc:96
#, c-format
msgid "%.2fM/s"
msgstr ""

#: src/Speedometer.cc:103
#, c-format
msgid "%.0f B/s"
msgstr ""

#: src/Speedometer.cc:105
#, c-format
msgid "%.1f KiB/s"
msgstr ""

#: src/Speedometer.cc:107
#, c-format
msgid "%.2f MiB/s"
msgstr ""

#: src/Speedometer.cc:129
msgid "eta:"
msgstr ""

#: src/SSH_Access.cc:97
msgid "Password required"
msgstr ""

#: src/SSH_Access.cc:102
msgid "Login incorrect"
msgstr ""

#: src/SSH_Access.cc:196
#, c-format
msgid "Disconnecting"
msgstr ""

#: src/SysCmdJob.cc:73
#, c-format
msgid "execlp(%s) failed: %s\n"
msgstr "falha em execlp(%s): %s\n"

#: src/TimeDate.cc:155
msgid "day"
msgstr ""

#: src/TimeDate.cc:156
msgid "hour"
msgstr ""

#: src/TimeDate.cc:157
msgid "minute"
msgstr ""

#: src/TimeDate.cc:158
msgid "second"
msgstr ""

#: src/Torrent.cc:585
msgid "announced via "
msgstr ""

#: src/Torrent.cc:599
#, c-format
msgid "next announce in %s"
msgstr ""

#: src/Torrent.cc:1142
#, c-format
msgid "%d file$|s$ found, now scanning %s"
msgstr ""

#: src/Torrent.cc:1143
#, fuzzy, c-format
msgid "%d file$|s$ found"
msgstr "%s: nenhum arquivo encontrado\n"

#: src/Torrent.cc:2075 src/Torrent.cc:2085
#, c-format
msgid "Getting meta-data: %s"
msgstr ""

#: src/Torrent.cc:2077
#, c-format
msgid "Validation: %u/%u (%u%%) %s%s"
msgstr ""

#: src/Torrent.cc:2088
#, fuzzy
msgid "Waiting for meta-data..."
msgstr "Atenção: descartando comando incompleto\n"

#: src/Torrent.cc:2096
msgid "Shutting down: "
msgstr ""

#: src/Torrent.cc:3207
#, c-format
msgid "Connecting to peer %s port %u"
msgstr ""

#: src/Torrent.cc:3258 src/Torrent.cc:3375
#, c-format
msgid "peer unexpectedly closed connection after %s"
msgstr ""

#: src/Torrent.cc:3259 src/Torrent.cc:3376
msgid "peer unexpectedly closed connection"
msgstr ""

#: src/Torrent.cc:3261 src/Torrent.cc:3262
#, c-format
msgid "peer closed connection (before handshake)"
msgstr ""

#: src/Torrent.cc:3265 src/Torrent.cc:3378 src/Torrent.cc:3379
#, c-format
msgid "invalid peer response format"
msgstr ""

#: src/Torrent.cc:3360 src/Torrent.cc:3361
#, c-format
msgid "peer closed connection"
msgstr ""

#: src/Torrent.cc:3538
msgid "Handshaking..."
msgstr ""

#: src/Torrent.cc:3798
msgid "Cannot bind a socket for torrent:port-range"
msgstr ""

#: src/Torrent.cc:3858
#, c-format
msgid "Accepted connection from [%s]:%d"
msgstr ""

#: src/Torrent.cc:3924
#, c-format
msgid "peer sent unknown info_hash=%s in handshake"
msgstr ""

#: src/Torrent.cc:3948
#, c-format
msgid "peer handshake timeout"
msgstr ""

#: src/Torrent.cc:3960
#, c-format
msgid "peer short handshake"
msgstr ""

#: src/Torrent.cc:3962
#, c-format
msgid "peer closed just accepted connection"
msgstr ""

#: src/Torrent.cc:4013
#, c-format
msgid "Seeding in background...\n"
msgstr ""

#: src/Torrent.cc:4176
#, c-format
msgid "%s: --share conflicts with --output-directory.\n"
msgstr ""

#: src/Torrent.cc:4180
#, c-format
msgid "%s: --share conflicts with --only-new.\n"
msgstr ""

#: src/Torrent.cc:4184
#, c-format
msgid "%s: --share conflicts with --only-incomplete.\n"
msgstr ""

#: src/Torrent.cc:4226
#, c-format
msgid "%s: Please specify a file or directory to share.\n"
msgstr ""

#: src/Torrent.cc:4228
#, c-format
msgid "%s: Please specify meta-info file or URL.\n"
msgstr ""

#: src/Torrent.cc:4258
msgid ""
"Start BitTorrent job for the given torrent-files, which can be a local "
"file,\n"
"URL, magnet link or plain info_hash written in hex or base32. Local "
"wildcards\n"
"are expanded. Options:\n"
" -O <base>      specifies base directory where files should be placed\n"
" --force-valid  skip file validation\n"
" --dht-bootstrap=<node>  bootstrap DHT by sending a query to the node\n"
" --share        share specified file or directory\n"
msgstr ""

#: src/TorrentTracker.cc:178
msgid "not started"
msgstr ""

#: src/TorrentTracker.cc:181
#, c-format
msgid "next request in %s"
msgstr ""

#: src/TorrentTracker.cc:284 src/TorrentTracker.cc:501
#, c-format
msgid "Received valid info about %d peer$|s$"
msgstr ""

#: src/TorrentTracker.cc:298
#, c-format
msgid "Received valid info about %d IPv6 peer$|s$"
msgstr ""

#, fuzzy
#~ msgid "%s: unrecognized option '--%s'\n"
#~ msgstr "número inválido"

#, fuzzy
#~ msgid "number"
#~ msgstr "número inválido"

#, fuzzy
#~ msgid "error: %s:%d\n"
#~ msgstr "Erro fatal: %s"

#, fuzzy
#~ msgid "invalid pair of numbers"
#~ msgstr "número inválido"

#, fuzzy
#~ msgid "block size"
#~ msgstr "número inválido"

#~ msgid "Cache is on"
#~ msgstr "Cache ligado"

#~ msgid "Cache is off"
#~ msgstr "Cache desligado"

#~ msgid "Cache entries do not expire"
#~ msgstr "As entradas do cache não expiram"

#~ msgid "Cache entries expire in %ld $#l#second|seconds$\n"
#~ msgstr "As entradas do cache expirarão em %ld segundos\n"

#~ msgid "Cache entries expire in %ld $#l#minute|minutes$\n"
#~ msgstr "As entradas do cache expirarão em %ld minuto\n"

#~ msgid "Warning: getcwd() failed: %s\n"
#~ msgstr "Atenção: getcwd() falhou: %s\n"

#~ msgid "Removing old remote file `%s'"
#~ msgstr "Removendo arquivo remoto antigo `%s'"

#~ msgid "Retrieving remote file `%s'"
#~ msgstr "Buscando arquivo remoto `%s'"

#, fuzzy
#~ msgid "bzcat <files>"
#~ msgstr "cat [-u] <arquivos>"

#, fuzzy
#~ msgid "bzmore <files>"
#~ msgstr "rm [-r] <arquivos>"

#, fuzzy
#~ msgid "Getting size of `%s' [%s]"
#~ msgstr "\tExecutando comando embutido `%s' [%s]\n"

#, fuzzy
#~ msgid "Copying of `%s' in progress (%c)"
#~ msgstr "Operação em progresso"

#, fuzzy
#~ msgid "%s: no such files\n"
#~ msgstr "%s: nenhum arquivo encontrado\n"

#, fuzzy
#~ msgid " - not supported protocol\n"
#~ msgstr "%s: %s - protocolo não suportado\n"

#~ msgid "%s: %s - not supported protocol\n"
#~ msgstr "%s: %s - protocolo não suportado\n"

#~ msgid "remote rm(%s) - %s\n"
#~ msgstr "rm(%s) remoto - %s\n"

#~ msgid "\tNo files transferred successfully :(\n"
#~ msgstr "\tNenhum arquivo foi transmitido com sucesso :(\n"

#~ msgid "Average transfer rate %g bytes/s\n"
#~ msgstr "Taxa média de tranmissão %g bytes/s\n"

#~ msgid "%s: cannot write -- disk full?\n"
#~ msgstr "%s: não foi possível escrever -- disco cheio?\n"

#~ msgid "Operation is in progress"
#~ msgstr "Operação em progresso"

#~ msgid "Error 0"
#~ msgstr "Erro 0"

#~ msgid "Class is not Open()ed"
#~ msgstr "a classe não está aberta - Open()"

#~ msgid "%s: %s - no such tcp service, using default\n"
#~ msgstr "%s: %s - serviço tcp desconhecido, usando padrão\n"

#~ msgid "Cache entries expire in 1 second"
#~ msgstr "As entradas no cache expirarão em 1 segundo"

#~ msgid "Cache entries expire in 1 minute"
#~ msgstr "As entradas do cache expirarão em 1 minuto"

#, fuzzy
#~ msgid "Total %d $file|files$ transferred"
#~ msgstr "%d arquivos transmitidos"

#, fuzzy
#~ msgid "%d files total\n"
#~ msgstr "tota de arquivos %d\n"
