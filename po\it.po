# Italian messages for lftp
# Copyright (C) 1998-2000 Free Software Foundation, Inc.
# <PERSON> <<EMAIL>>, 1998-2000.
#
msgid ""
msgstr ""
"Project-Id-Version: lftp 2.2.3\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2024-11-08 13:05+0300\n"
"PO-Revision-Date: 2000-06-18 10:46+02:00\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Italian <<EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8-bit\n"

#: lib/argmatch.c:145
#, fuzzy, c-format
msgid "invalid argument %s for %s"
msgstr "numero non valido"

#: lib/argmatch.c:146
#, c-format
msgid "ambiguous argument %s for %s"
msgstr ""

#: lib/argmatch.c:165
msgid "Valid arguments are:"
msgstr ""

#: lib/error.c:208
msgid "Unknown system error"
msgstr ""

#: lib/getopt.c:282
#, c-format
msgid "%s: option '%s%s' is ambiguous\n"
msgstr ""

#: lib/getopt.c:288
#, c-format
msgid "%s: option '%s%s' is ambiguous; possibilities:"
msgstr ""

#: lib/getopt.c:322
#, fuzzy, c-format
msgid "%s: unrecognized option '%s%s'\n"
msgstr "numero non valido"

#: lib/getopt.c:348
#, c-format
msgid "%s: option '%s%s' doesn't allow an argument\n"
msgstr ""

#: lib/getopt.c:363
#, c-format
msgid "%s: option '%s%s' requires an argument\n"
msgstr ""

#: lib/getopt.c:624
#, fuzzy, c-format
msgid "%s: invalid option -- '%c'\n"
msgstr "numero non valido"

#: lib/getopt.c:639 lib/getopt.c:685
#, c-format
msgid "%s: option requires an argument -- '%c'\n"
msgstr ""

#. TRANSLATORS:
#. Get translations for open and closing quotation marks.
#. The message catalog should translate "`" to a left
#. quotation mark suitable for the locale, and similarly for
#. "'".  For example, a French Unicode local should translate
#. these to U+00AB (LEFT-POINTING DOUBLE ANGLE
#. QUOTATION MARK), and U+00BB (RIGHT-POINTING DOUBLE ANGLE
#. QUOTATION MARK), respectively.
#.
#. If the catalog has no translation, we will try to
#. use Unicode U+2018 (LEFT SINGLE QUOTATION MARK) and
#. Unicode U+2019 (RIGHT SINGLE QUOTATION MARK).  If the
#. current locale is not Unicode, locale_quoting_style
#. will quote 'like this', and clocale_quoting_style will
#. quote "like this".  You should always include translations
#. for "`" and "'" even if U+2018 and U+2019 are appropriate
#. for your locale.
#.
#. If you don't know what to put here, please see
#. <https://en.wikipedia.org/wiki/Quotation_marks_in_other_languages>
#. and use glyphs suitable for your language.
#: lib/quotearg.c:354
msgid "`"
msgstr ""

#: lib/quotearg.c:355
msgid "'"
msgstr ""

#: lib/xalloc-die.c:34
msgid "memory exhausted"
msgstr ""

#: src/ArgV.cc:107
msgid "option requires an argument"
msgstr ""

#: src/ArgV.cc:109 src/ArgV.cc:118
#, fuzzy
msgid "invalid option"
msgstr "numero non valido"

#: src/ArgV.cc:114
#, c-format
msgid "option `%s' requires an argument"
msgstr ""

#: src/ArgV.cc:116
#, fuzzy, c-format
msgid "unrecognized option `%s'"
msgstr "numero non valido"

#: src/attach.h:138
#, fuzzy, c-format
msgid "[%u] Attached to terminal %s. %s\n"
msgstr "[%lu] Terminato dal signal %d. %s"

#: src/attach.h:150
#, c-format
msgid "[%u] Attached to terminal.\n"
msgstr ""

#: src/buffer.cc:253 src/FileAccess.cc:866
msgid " [cached]"
msgstr ""

#: src/ChmodJob.cc:80
#, c-format
msgid "Failed to change mode of `%s' to %04o (%s).\n"
msgstr ""

#: src/ChmodJob.cc:83
#, c-format
msgid "Mode of `%s' changed to %04o (%s).\n"
msgstr ""

#: src/ChmodJob.cc:88
#, c-format
msgid "Failed to change mode of `%s' because no old mode is available.\n"
msgstr ""

#: src/CmdExec.cc:105
#, c-format
msgid "Warning: chdir(%s) failed: %s\n"
msgstr "Attenzione: chdir(%s) fallita: %s\n"

#: src/CmdExec.cc:207
#, c-format
msgid "Unknown command `%s'.\n"
msgstr "`%s' comando sconosciuto.\n"

#: src/CmdExec.cc:209
#, c-format
msgid "Ambiguous command `%s'.\n"
msgstr "`%s' comando ambiguo.\n"

#: src/CmdExec.cc:228
#, c-format
msgid "Module for command `%s' did not register the command.\n"
msgstr ""

#: src/CmdExec.cc:384
#, c-format
msgid "cd ok, cwd=%s\n"
msgstr "cd ok, cwd=%s\n"

#: src/CmdExec.cc:405 src/MirrorJob.cc:736
#, c-format
msgid "%s: received redirection to `%s'\n"
msgstr ""

#: src/CmdExec.cc:408 src/FileCopy.cc:1230
msgid "Too many redirections"
msgstr ""

#: src/CmdExec.cc:517 src/CmdExec.cc:574
msgid "Interrupt"
msgstr "Interruzione"

#: src/CmdExec.cc:639
#, c-format
msgid "Warning: discarding incomplete command\n"
msgstr "Attenzione: ignoro il comando incompleto\n"

#: src/CmdExec.cc:737
#, c-format
msgid "\tExecuting builtin `%s' [%s]\n"
msgstr "\tEsecuzione del comando interno `%s' [%s]\n"

#: src/CmdExec.cc:742
msgid "Queue is stopped."
msgstr ""

#: src/CmdExec.cc:747
msgid "Now executing:"
msgstr "Ora sto eseguendo:"

#: src/CmdExec.cc:758
#, c-format
msgid "\tWaiting for job [%d] to terminate\n"
msgstr "\tAttendo la fine del job [%d] per terminare\n"

#: src/CmdExec.cc:761
#, fuzzy, c-format
msgid "\tWaiting for termination of jobs: "
msgstr "\tAttendo la fine del job [%d] per terminare\n"

#: src/CmdExec.cc:770
msgid "\tRunning\n"
msgstr "\tIn esecuzione\n"

#: src/CmdExec.cc:772
#, fuzzy
msgid "\tWaiting for command\n"
msgstr "parse: manca il comando filtro\n"

#: src/CmdExec.cc:1261
#, c-format
msgid "%s: command `%s' is not compiled in.\n"
msgstr ""

#: src/CmdExec.cc:1278
#, fuzzy, c-format
msgid "Usage: %s cmd [args...]\n"
msgstr "Uso: %s modulo [arg...]\n"

#: src/CmdExec.cc:1284
#, c-format
msgid "%s: cannot create local session\n"
msgstr ""

#: src/commands.cc:114
#, fuzzy
msgid "!<shell-command>"
msgstr "!<comando_shell>"

#: src/commands.cc:115
msgid "Launch shell or shell command\n"
msgstr "Lancia una shell o un comando shell\n"

#: src/commands.cc:116
msgid "(commands)"
msgstr "(comandi)"

#: src/commands.cc:117
msgid ""
"Group commands together to be executed as one command\n"
"You can launch such a group in background\n"
msgstr ""
"Raggruppa assieme più comandi per eseguirli come fossero uno solo\n"
"È possibile lanciare tale gruppo in background\n"

#: src/commands.cc:120
msgid "alias [<name> [<value>]]"
msgstr "alias [<nome> [<valore>]]"

#: src/commands.cc:121
msgid ""
"Define or undefine alias <name>. If <value> omitted,\n"
"the alias is undefined, else is takes the value <value>.\n"
"If no argument is given the current aliases are listed.\n"
msgstr ""
"Definisce o elimina un alias <nome>. Se <valore> è omesso, l'alias è\n"
"eliminato, diversamente è preso <valore> come valore.\n"
"Se non è dato alcun argomento vengono elencati gli alias correnti.\n"

#: src/commands.cc:125
msgid "anon - login anonymously (by default)\n"
msgstr "anon - login anonimo (predefinito)\n"

#: src/commands.cc:127
msgid "bookmark [SUBCMD]"
msgstr "bookmark [SUBCMD]"

#: src/commands.cc:128
msgid ""
"bookmark command controls bookmarks\n"
"\n"
"The following subcommands are recognized:\n"
"  add <name> [<loc>] - add current place or given location to bookmarks\n"
"                       and bind to given name\n"
"  del <name>         - remove bookmark with the name\n"
"  edit               - start editor on bookmarks file\n"
"  import <type>      - import foreign bookmarks\n"
"  list               - list bookmarks (default)\n"
msgstr ""
"Il comando bookmark controlla i bookmark\n"
"\n"
"Sono riconosciuti i seguenti comandi secondari:\n"
"  add <nome> [<loc>] - aggiunge il sito corrente o la posizione specificata\n"
"                       ai bookmark, assegnandogli il nome specificato\n"
"  del <nome>         - rimuove il bookmark specificato dal nome\n"
"  edit               - avvia l'editor sul file dei bookmark\n"
"  import <tipo>      - importa bookmark esterni\n"
"  list               - elenca i bookmark (default)\n"

#: src/commands.cc:137
msgid "cache [SUBCMD]"
msgstr "cache [SUBCMD]"

#: src/commands.cc:138
#, fuzzy
msgid ""
"cache command controls local memory cache\n"
"\n"
"The following subcommands are recognized:\n"
"  stat        - print cache status (default)\n"
"  on|off      - turn on/off caching\n"
"  flush       - flush cache\n"
"  size <lim>  - set memory limit\n"
"  expire <Nx> - set cache expiration time to N seconds (x=s)\n"
"                minutes (x=m) hours (x=h) or days (x=d)\n"
msgstr ""
"Il comando cache controlla la cache di memoria locale\n"
"\n"
"Sono riconosciuti i seguenti comandi secondari:\n"
"  stat        - mostra lo stato della cache (predefinito)\n"
"  on|off      - attiva o disattiva la cache\n"
"  flush       - scarica la cache\n"
"  size <lim>  - imposta un limite di memoria, -1 significa illimitata\n"
"  expire <Nx> - imposta il tempo di scadenza della cache a N secondi (x=s),\n"
"                minuti (x=m), ore (x=h) o giorni (x=d)\n"

#: src/commands.cc:146
msgid "cat [-b] <files>"
msgstr "cat [-b] <file>"

#: src/commands.cc:147
msgid ""
"cat - output remote files to stdout (can be redirected)\n"
" -b  use binary mode (ascii is the default)\n"
msgstr ""
"cat - visualizza i file remoti su stdout (può essere rediretto)\n"
" -b  usa la modalità binaria (quella ascii è la predefinita)\n"

#: src/commands.cc:149
msgid "cd <rdir>"
msgstr "cd <rdir>"

#: src/commands.cc:150
msgid ""
"Change current remote directory to <rdir>. The previous remote directory\n"
"is stored as `-'. You can do `cd -' to change the directory back.\n"
"The previous directory for each site is also stored on disk, so you can\n"
"do `open site; cd -' even after lftp restart.\n"
msgstr ""
"Cambia la directory remota corrente a <rdir>. La directory remota "
"precedente\n"
"è salvata come `-'. Si può fare `cd -' per tornare indietro. Su disco è\n"
"salvata la directory precedente di ogni sito, quindi si può fare \n"
"`open sito; cd -' anche dopo il riavvio di lftp.\n"

#: src/commands.cc:154
#, fuzzy
msgid "chmod [OPTS] mode file..."
msgstr "chmod permessi file..."

#: src/commands.cc:155
msgid ""
"Change the mode of each FILE to MODE.\n"
"\n"
" -c, --changes        - like verbose but report only when a change is made\n"
" -f, --quiet          - suppress most error messages\n"
" -v, --verbose        - output a diagnostic for every file processed\n"
" -R, --recursive      - change files and directories recursively\n"
"\n"
"MODE can be an octal number or symbolic mode (see chmod(1))\n"
msgstr ""

#: src/commands.cc:164
msgid ""
"Close idle connections. By default only with current server.\n"
" -a  close idle connections with all servers\n"
msgstr ""
"Chiude le connessioni inattive. Di default solo quelle con il server "
"corrente.\n"
" -a  chiude le connessioni inattive con tutti i server.\n"

#: src/commands.cc:166
msgid "[re]cls [opts] [path/][pattern]"
msgstr ""

#: src/commands.cc:167
msgid ""
"List remote files. You can redirect output of this command to file\n"
"or via pipe to external command.\n"
"\n"
" -1                   - single-column output\n"
" -a, --all            - show dot files\n"
" -B, --basename       - show basename of files only\n"
"     --block-size=SIZ - use SIZ-byte blocks\n"
" -d, --directory      - list directory entries instead of contents\n"
" -F, --classify       - append indicator (one of /@) to entries\n"
" -h, --human-readable - print sizes in human readable format (e.g., 1K)\n"
"     --si             - likewise, but use powers of 1000 not 1024\n"
" -k, --kilobytes      - like --block-size=1024\n"
" -l, --long           - use a long listing format\n"
" -q, --quiet          - don't show status\n"
" -s, --size           - print size of each file\n"
"     --filesize       - if printing size, only print size for files\n"
" -i, --nocase         - case-insensitive pattern matching\n"
" -I, --sortnocase     - sort names case-insensitively\n"
" -D, --dirsfirst      - list directories first\n"
"     --sort=OPT       - \"name\", \"size\", \"date\"\n"
" -S                   - sort by file size\n"
" --user, --group, --perms, --date, --linkcount, --links\n"
"                      - show individual fields\n"
" --time-style=STYLE   - use specified time format\n"
"\n"
"By default, cls output is cached, to see new listing use `recls' or\n"
"`cache flush'.\n"
"\n"
"The variables cls-default and cls-completion-default can be used to\n"
"specify defaults for cls listings and completion listings, respectively.\n"
"For example, to make completion listings show file sizes, set\n"
"cls-completion-default to \"-s\".\n"
"\n"
"Tips: Use --filesize with -D to pack the listing better.  If you don't\n"
"always want to see file sizes, --filesize in cls-default will affect the\n"
"-s flag on the commandline as well.  Add `-i' to cls-completion-default\n"
"to make filename completion case-insensitive.\n"
msgstr ""

#: src/commands.cc:217
#, fuzzy
msgid "debug [OPTS] [<level>|off]"
msgstr "debug [<livello>|off] [-o <file>]"

#: src/commands.cc:218
#, fuzzy
msgid ""
"Set debug level to given value or turn debug off completely.\n"
" -o <file>  redirect debug output to the file\n"
" -c  show message context\n"
" -p  show PID\n"
" -t  show timestamps\n"
msgstr ""
"Imposta il livello di debug al valore dato oppure lo disabilita "
"completamente.\n"
" -o <file>  redirige l'output di debug sul file.\n"

#: src/commands.cc:223
#, fuzzy
msgid "du [options] <dirs>"
msgstr "mkdir [-p] <dir>"

#: src/commands.cc:224
msgid ""
"Summarize disk usage.\n"
" -a, --all             write counts for all files, not just directories\n"
"     --block-size=SIZ  use SIZ-byte blocks\n"
" -b, --bytes           print size in bytes\n"
" -c, --total           produce a grand total\n"
" -d, --max-depth=N     print the total for a directory (or file, with --"
"all)\n"
"                       only if it is N or fewer levels below the command\n"
"                       line argument;  --max-depth=0 is the same as\n"
"                       --summarize\n"
" -F, --files           print number of files instead of sizes\n"
" -h, --human-readable  print sizes in human readable format (e.g., 1K 234M "
"2G)\n"
" -H, --si              likewise, but use powers of 1000 not 1024\n"
" -k, --kilobytes       like --block-size=1024\n"
" -m, --megabytes       like --block-size=1048576\n"
" -S, --separate-dirs   do not include size of subdirectories\n"
" -s, --summarize       display only a total for each argument\n"
"     --exclude=PAT     exclude files that match PAT\n"
msgstr ""

#: src/commands.cc:242
#, fuzzy
msgid "edit [OPTS] <file>"
msgstr "mget [OPZIONI] <file>"

#: src/commands.cc:243
msgid ""
"Retrieve remote file to a temporary location, run a local editor on it\n"
"and upload the file back if changed.\n"
" -k  keep the temporary file\n"
" -o <temp>  explicit temporary file location\n"
msgstr ""

#: src/commands.cc:248
#, fuzzy
msgid "exit [<code>|bg]"
msgstr "exit [<codice>]"

#: src/commands.cc:249
#, fuzzy
msgid ""
"exit - exit from lftp or move to background if jobs are active\n"
"\n"
"If no jobs active, the code is passed to operating system as lftp\n"
"termination status. If omitted, exit code of last command is used.\n"
"`bg' forces moving to background if cmd:move-background is false.\n"
msgstr ""
"exit - esce da lftp o passa in background se ci sono job attivi\n"
"\n"
"Se non ci sono job attivi, il codice è passato al sistema operativo come\n"
"stato di terminazione di lftp. Se omesso, è usato il codice d'uscita\n"
"dell'ultimo comando.\n"

#: src/commands.cc:255
#, fuzzy
msgid ""
"Usage: find [OPTS] [directory]\n"
"Print contents of specified directory or current directory recursively.\n"
"Directories in the list are marked with trailing slash.\n"
"You can redirect output of this command.\n"
" -d, --maxdepth=LEVELS  Descend at most LEVELS of directories.\n"
msgstr ""
"Uso: find [directory]\n"
"Mostra ricorsivamente il contenuto della directory specificata della \n"
"directory corrente.\n"
"Nell'elenco le directory sono marcate con uno slash finale.\n"
"È possibile redigire l'output di questo comando.\n"

#: src/commands.cc:260
msgid "get [OPTS] <rfile> [-o <lfile>]"
msgstr "get [OPZIONI] <rfile> [-o <lfile>]"

#: src/commands.cc:261
#, fuzzy
msgid ""
"Retrieve remote file <rfile> and store it to local file <lfile>.\n"
" -o <lfile> specifies local file name (default - basename of rfile)\n"
" -c  continue, resume transfer\n"
" -E  delete remote files after successful transfer\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Scarica il file remoto <rfile> e lo salva nel file locale <lfile>.\n"
" -o <lfile> specifica il nome del file locale (di default il nome di rfile)\n"
" -c  continua, reget\n"
" -E  cancella i file remoti dopo averli trasferiti con successo\n"
" -a  usa la modalità ascii (quella predefinita è la binaria)\n"
" -O <base> specifica la directory o l'URL base dove dovrebbero essere\n"
"     messi i file\n"

#: src/commands.cc:268
msgid "glob [OPTS] <cmd> <args>"
msgstr ""

#: src/commands.cc:270
#, fuzzy
msgid ""
"Expand wildcards and run specified command.\n"
"Options can be used to expand wildcards to list of files, directories,\n"
"or both types. Type selection is not very reliable and depends on server.\n"
"If entry type cannot be determined, it will be included in the list.\n"
" -f  plain files (default)\n"
" -d  directories\n"
" -a  all types\n"
" --exist      return zero exit code when the patterns expand to non-empty "
"list\n"
" --not-exist  return zero exit code when the patterns expand to an empty "
"list\n"
msgstr ""
"Uso: glob [OPZ] comando arg...\\n\"\n"
"Espande i caratteri jolly e esegue il comando specificato.\n"
"Le opzioni possono essere usate per espandere i caratteri jolly in una "
"lista\n"
"di file, di directory o di entrambi. La selezione del tipo non è molto\n"
"affidabile e dipende dal server. Se non può essere determinato il tipo di\n"
"una voce, sarà comunque inclusa nella lista.\n"
" -f  file regolari (default)\n"
" -d  directory\n"
" -a  tutti i tipi\n"

#: src/commands.cc:279
msgid "help [<cmd>]"
msgstr "help [<cmd>]"

#: src/commands.cc:280
msgid "Print help for command <cmd>, or list of available commands\n"
msgstr "Mostra un aiuto per il comando <cmd>, o elenca i comandi disponibili\n"

#: src/commands.cc:282
#, fuzzy
msgid ""
"List running jobs. -v means verbose, several -v can be specified.\n"
"If <job_no> is specified, only list a job with that number.\n"
msgstr ""
"Mostra i job in esecuzione, -v indica prolisso e possono essere specificati\n"
"più -v.\n"

#: src/commands.cc:284
msgid "kill all|<job_no>"
msgstr "kill all|<num_job>"

#: src/commands.cc:285
msgid "Delete specified job with <job_no> or all jobs\n"
msgstr "Cancella il job specificato con <num_job> oppure tutti i job\n"

#: src/commands.cc:286
msgid "lcd <ldir>"
msgstr "lcd <ldir>"

#: src/commands.cc:287
msgid ""
"Change current local directory to <ldir>. The previous local directory\n"
"is stored as `-'. You can do `lcd -' to change the directory back.\n"
msgstr ""
"Cambia la directory locale a <ldir>. La directory locale precedente è "
"salvata\n"
"come `-'. Si può quindi fare `lcd -' per tornare indietro.\n"

#: src/commands.cc:289
msgid "lftp [OPTS] <site>"
msgstr "lftp [OPZIONI] <sito>"

#: src/commands.cc:290
#, fuzzy
msgid ""
"`lftp' is the first command executed by lftp after rc files\n"
" -f <file>           execute commands from the file and exit\n"
" -c <cmd>            execute the commands and exit\n"
" --norc              don't execute rc files from the home directory\n"
" --help              print this help and exit\n"
" --version           print lftp version and exit\n"
"Other options are the same as in `open' command:\n"
" -e <cmd>            execute the command just after selecting\n"
" -u <user>[,<pass>]  use the user/password for authentication\n"
" -p <port>           use the port for connection\n"
" -s <slot>           assign the connection to this slot\n"
" -d                  switch on debugging mode\n"
" <site>              host name, URL or bookmark name\n"
msgstr ""
"`lftp' è il primo comando eseguito da lftp dopo i file rc\n"
" -f <file>             esegue i comandi presenti nel file ed esce\n"
" -c <cmd>              esegue i comandi ed esce\n"
" --help                mostra questo messaggio ed esce\n"
" --version             mostra la versione di lftp ed esce\n"
"Le altre opzioni sono analoghe a quelle del comando `open'\n"
" -e <cmd>              esegue il comando subito dopo la selezione\n"
" -u <utente>[,<pass>]  usa utente/password per l'autentificazione\n"
" -p <porta>            usa la porta per la connessione\n"
" <sito>                nome host, URL o nome di un bookmark\n"

#: src/commands.cc:303
#, fuzzy
msgid "ln [-s] <file1> <file2>"
msgstr "mv <file1> <file2>"

#: src/commands.cc:304
#, fuzzy
msgid "Link <file1> to <file2>\n"
msgstr "Rinomina <file1> in <file2>\n"

#: src/commands.cc:308
msgid "ls [<args>]"
msgstr "ls [<arg>]"

#: src/commands.cc:309
#, fuzzy
msgid ""
"List remote files. You can redirect output of this command to file\n"
"or via pipe to external command.\n"
"By default, ls output is cached, to see new listing use `rels' or\n"
"`cache flush'.\n"
"See also `help cls'.\n"
msgstr ""
"Elenca i file remoti. È possibile redirigere l'output di questo comando\n"
"su un file o tramite una pipe ad un comando esterno.\n"
"Di default, l'output di ls viene messo in cache, per vedere un nuovo elenco\n"
"usare `rels' o `cache flush'.\n"

#: src/commands.cc:314
msgid "mget [OPTS] <files>"
msgstr "mget [OPZIONI] <file>"

#: src/commands.cc:315
#, fuzzy
msgid ""
"Gets selected files with expanded wildcards\n"
" -c  continue, resume transfer\n"
" -d  create directories the same as in file names and get the\n"
"     files into them instead of current directory\n"
" -E  delete remote files after successful transfer\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Scarica i file selezionati espandendo i caratteri jolly\n"
" -c  continua, reget\n"
" -d  crea le directory presenti nei nomi dei file e mette i file lì dentro\n"
"     invece che nella directory corrente\n"
" -E  cancella i file remoti dopo averli trasferiti con successo\n"
" -a  usa la modalità ascii (quella binaria è la predefinita)\n"
" -O <base> specifica la directory o l'URL base dove dovrebbero essere\n"
"     messi i file\n"

#: src/commands.cc:322
msgid "mirror [OPTS] [remote [local]]"
msgstr "mirror [OPZIONI] [remota [locale]]"

#: src/commands.cc:323
#, fuzzy
msgid "mkdir [OPTS] <dirs>"
msgstr "mkdir [-p] <dir>"

#: src/commands.cc:324
#, fuzzy
msgid ""
"Make remote directories\n"
" -p  make all levels of path\n"
" -f  be quiet, suppress messages\n"
msgstr ""
"Crea directory remote\n"
" -p  crea tutti i livelli del percorso\n"

#: src/commands.cc:327
msgid "module name [args]"
msgstr "module nome [arg...]"

#: src/commands.cc:328
#, fuzzy
msgid ""
"Load module (shared object). The module should contain function\n"
"   void module_init(int argc,const char *const *argv)\n"
"If name contains a slash, then the module is searched in current\n"
"directory, otherwise in directories specified by setting module:path.\n"
msgstr ""
"Carica un modulo (oggetto condiviso).  Il modulo non può contenere la "
"funzione\n"
"   void module_init(int argc,const char *const *argv)\n"
"Se il nome contiene uno slash, allora il modulo è cercato nella directory\n"
"corrente, diversamente in PKGLIBDIR.\n"

#: src/commands.cc:332
msgid "more <files>"
msgstr "more <file>"

#: src/commands.cc:333
msgid "Same as `cat <files> | more'. if PAGER is set, it is used as filter\n"
msgstr ""
"Uguale a `cat <file> | more'. Se PAGER è impostato, è usato come filtro\n"

#: src/commands.cc:334
msgid "mput [OPTS] <files>"
msgstr "mput [OPZ] <file>"

#: src/commands.cc:335
msgid ""
"Upload files with wildcard expansion\n"
" -c  continue, reput\n"
" -d  create directories the same as in file names and put the\n"
"     files into them instead of current directory\n"
" -E  delete local files after successful transfer (dangerous)\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Copia i file sul sito remoto espandendo i caratteri jolly\n"
" -c  continua, reput\n"
" -d  crea le directory presenti nei nome dei file e mette i file lì dentro\n"
"     invece che nella directory corrente\n"
" -E  cancella i file locali dopo averli trasferiti con successo "
"(pericolosa)\n"
" -a  usa la modalità ascii (quella predefinita è la binaria)\n"
" -O <base> specifica la directory o l'URL base dove dovrebbero essere messi\n"
"     i file\n"

#: src/commands.cc:342
msgid "mrm <files>"
msgstr "mrm <file>"

#: src/commands.cc:343
msgid "Removes specified files with wildcard expansion\n"
msgstr "Cancella i file specificati espandendo i caratteri jolly\n"

#: src/commands.cc:344
msgid "mv <file1> <file2>"
msgstr "mv <file1> <file2>"

#: src/commands.cc:345
msgid "Rename <file1> to <file2>\n"
msgstr "Rinomina <file1> in <file2>\n"

#: src/commands.cc:346
#, fuzzy
msgid "mmv [OPTS] <files> <target-dir>"
msgstr "mget [OPZIONI] <file>"

#: src/commands.cc:347
msgid ""
"Move <files> to <target-directory> with wildcard expansion\n"
" -O <dir>  specifies the target directory (alternative way)\n"
msgstr ""

#: src/commands.cc:349
#, fuzzy
msgid "[re]nlist [<args>]"
msgstr "renlist [<arg>]"

#: src/commands.cc:350
#, fuzzy
msgid ""
"List remote file names.\n"
"By default, nlist output is cached, to see new listing use `renlist' or\n"
"`cache flush'.\n"
msgstr ""
"Elenca i file remoti. È possibile redirigere l'output di questo comando\n"
"su un file o tramite una pipe ad un comando esterno.\n"
"Di default, l'output di ls viene messo in cache, per vedere un nuovo elenco\n"
"usare `rels' o `cache flush'.\n"

#: src/commands.cc:353
msgid "open [OPTS] <site>"
msgstr "open [OPZIONI] <host>"

#: src/commands.cc:354
#, fuzzy
msgid ""
"Select a server, URL or bookmark\n"
" -e <cmd>            execute the command just after selecting\n"
" -u <user>[,<pass>]  use the user/password for authentication\n"
" -p <port>           use the port for connection\n"
" -s <slot>           assign the connection to this slot\n"
" -d                  switch on debugging mode\n"
" <site>              host name, URL or bookmark name\n"
msgstr ""
"Seleziona un server, URL o bookmark\n"
" -e <comando>          esegue il comando subito dopo la selezione\n"
" -u <utente>[,<pass>]  usa utente/password per l'autentificazione\n"
" -p <porta>            usa la porta per la connessione\n"
" <sito>                nome host, URL o nome di un bookmark\n"

#: src/commands.cc:361
msgid "pget [OPTS] <rfile> [-o <lfile>]"
msgstr "pget [OPZIONI] <rfile> [-o <lfile>]"

#: src/commands.cc:362
#, fuzzy
msgid ""
"Gets the specified file using several connections. This can speed up "
"transfer,\n"
"but loads the net heavily impacting other users. Use only if you really\n"
"have to transfer the file ASAP.\n"
"\n"
"Options:\n"
" -c  continue transfer. Requires <lfile>.lftp-pget-status file.\n"
" -n <maxconn>  set maximum number of connections (default is is taken from\n"
"     pget:default-n setting)\n"
" -O <base> specifies base directory where files should be placed\n"
msgstr ""
"Scarica il file specificato usando diverse connessioni. Ciò può velocizzare\n"
"il trasferimento, ma carica pesantemente la rete con un impatto notevole "
"sugli\n"
"altri utenti. Usare solo se si deve realmente trasferire il file il più "
"presto\n"
"possibile, altrimenti gli altri utenti potrebbero arrabbiarsi :)\n"
"\n"
"Opzioni:\n"
" -n <maxconn>  imposta il numero massimo di connessioni (default 5)\n"

#: src/commands.cc:370
msgid "put [OPTS] <lfile> [-o <rfile>]"
msgstr "put [OPZ] <lfile> [-o <rfile>]"

#: src/commands.cc:371
msgid ""
"Upload <lfile> with remote name <rfile>.\n"
" -o <rfile> specifies remote file name (default - basename of lfile)\n"
" -c  continue, reput\n"
"     it requires permission to overwrite remote files\n"
" -E  delete local files after successful transfer (dangerous)\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Copia <lfile> con nome remoto <rfile>.\n"
" -o <rfile> specifica il nome remoto per il file (default - il nome di "
"lfile)\n"
" -c  continua, reput\n"
"     richiede il permesso per sovrascrivere i file remoti\n"
" -E  cancella i file locali dopo averli trasferiti con successo "
"(pericoloso)\n"
" -a  usa la modalità ascii (quella predefinita è la binaria)\n"
" -O <base> specifica la directory o l'URL base dove dovrebbero essere messi\n"
"     i file\n"

#: src/commands.cc:379
#, fuzzy
msgid ""
"Print current remote URL.\n"
" -p  show password\n"
msgstr ""
"Mostra l'URL remoto corrente.\n"
" -u  mostra la password\n"

#: src/commands.cc:381
msgid "queue [OPTS] [<cmd>]"
msgstr ""

#: src/commands.cc:382
msgid ""
"\n"
"       queue [-n num] <command>\n"
"\n"
"Add the command to queue for current site. Each site has its own command\n"
"queue. `-n' adds the command before the given item in the queue. It is\n"
"possible to queue up a running job by using command `queue wait <jobno>'.\n"
"\n"
"       queue --delete|-d [index or wildcard expression]\n"
"\n"
"Delete one or more items from the queue. If no argument is given, the last\n"
"entry in the queue is deleted.\n"
"\n"
"       queue --move|-m <index or wildcard expression> [index]\n"
"\n"
"Move the given items before the given queue index, or to the end if no\n"
"destination is given.\n"
"\n"
"Options:\n"
" -q                  Be quiet.\n"
" -v                  Be verbose.\n"
" -Q                  Output in a format that can be used to re-queue.\n"
"                     Useful with --delete.\n"
msgstr ""

#: src/commands.cc:403
msgid "quote <cmd>"
msgstr "quote <cmd>"

#: src/commands.cc:404
msgid ""
"Send the command uninterpreted. Use with caution - it can lead to\n"
"unknown remote state and thus will cause reconnect. You cannot\n"
"be sure that any change of remote state because of quoted command\n"
"is solid - it can be reset by reconnect at any time.\n"
msgstr ""
"Invia il comando senza interpretarlo. Usare con cautela - può portare ad\n"
"uno stato remoto sconosciuto e quindi provocare la riconnessione. Non è\n"
"possibile essere sicuri che qualsiasi modifica dello stato remoto a causa\n"
"di un comando \"quoted\" sia stabile - può essere reinizializzato da una\n"
"riconnessione in qualsiasi istante.\n"

#: src/commands.cc:409
#, fuzzy
msgid ""
"recls [<args>]\n"
"Same as `cls', but don't look in cache\n"
msgstr "Simile a `ls', ma non guarda in cache\n"

#: src/commands.cc:412
#, fuzzy
msgid ""
"Usage: reget [OPTS] <rfile> [-o <lfile>]\n"
"Same as `get -c'\n"
msgstr "reget [OPZIONI] <rfile> [-o <lfile>]"

#: src/commands.cc:415
#, fuzzy
msgid ""
"Usage: rels [<args>]\n"
"Same as `ls', but don't look in cache\n"
msgstr "Simile a `ls', ma non guarda in cache\n"

#: src/commands.cc:418
#, fuzzy
msgid ""
"Usage: renlist [<args>]\n"
"Same as `nlist', but don't look in cache\n"
msgstr "Simile a `nlist', ma non guarda in cache\n"

#: src/commands.cc:420
msgid "repeat [OPTS] [delay] [command]"
msgstr ""

#: src/commands.cc:422
#, fuzzy
msgid ""
"Usage: reput <lfile> [-o <rfile>]\n"
"Same as `put -c'\n"
msgstr "reput <lfile> [-o <rfile>]"

#: src/commands.cc:424
msgid "rm [-r] [-f] <files>"
msgstr "rm [-r] [-f] <file>"

#: src/commands.cc:425
msgid ""
"Remove remote files\n"
" -r  recursive directory removal, be careful\n"
" -f  work quietly\n"
msgstr ""
"Cancella file remoti\n"
" -r  cancellazione ricorsiva di directory, usare con cautela\n"
" -f  lavora silenziosamente\n"

#: src/commands.cc:428
msgid "rmdir [-f] <dirs>"
msgstr "rmdir [-f] <dir>"

#: src/commands.cc:429
msgid "Remove remote directories\n"
msgstr "Cancella directory remote\n"

#: src/commands.cc:430
msgid "scache [<session_no>]"
msgstr "scache [<num_sessione>]"

#: src/commands.cc:431
msgid "List cached sessions or switch to specified session number\n"
msgstr ""
"Elenca le sessioni in cache o passa al numero di sessione specificato\n"

#: src/commands.cc:432
msgid "set [OPT] [<var> [<val>]]"
msgstr "set [OPT] [<var> [<val>]]"

#: src/commands.cc:433
msgid ""
"Set variable to given value. If the value is omitted, unset the variable.\n"
"Variable name has format ``name/closure'', where closure can specify\n"
"exact application of the setting. See lftp(1) for details.\n"
"If set is called with no variable then only altered settings are listed.\n"
"It can be changed by options:\n"
" -a  list all settings, including default values\n"
" -d  list only default values, not necessary current ones\n"
msgstr ""
"Imposta la variabile al valore specificato. Se è omesso il valore, allora\n"
"la cancella. Il nome della variabile è nel formato ``nome/chiusura'' dove\n"
"chiusura può specificare l'esatta applicazione dell'impostazione. Si veda\n"
"lftp(1) per i dettagli.\n"
"Se set è chiamato senza specificare una variabile allora sono mostrate solo\n"
"le impostazioni modificate.\n"
"Ciò può essere modificato con le opzioni:\n"
" -a  elenca tutte le impostazioni, compresi i valori predefiniti\n"
" -d  elenca solo i valori predefiniti, che non sono necessariamente quelli\n"
"     correnti\n"

#: src/commands.cc:441
#, fuzzy
msgid "site <site-cmd>"
msgstr "site <site_cmd>"

#: src/commands.cc:442
msgid ""
"Execute site command <site_cmd> and output the result\n"
"You can redirect its output\n"
msgstr ""
"Esegue il comando site <site_cmd> e mostra il risultato\n"
"È possibile redirigere il suo output\n"

#: src/commands.cc:446
msgid ""
"Usage: slot [<label>]\n"
"List assigned slots.\n"
"If <label> is specified, switch to the slot named <label>.\n"
msgstr ""

#: src/commands.cc:449
msgid "source <file>"
msgstr "source <file>"

#: src/commands.cc:450
msgid "Execute commands recorded in file <file>\n"
msgstr "Esegue i comandi specificati nel file <file>\n"

#: src/commands.cc:452
#, fuzzy
msgid "torrent [OPTS] <file|URL>..."
msgstr "mget [OPZIONI] <file>"

#: src/commands.cc:453
msgid "user <user|URL> [<pass>]"
msgstr "user <utente|URL> [<pass>]"

#: src/commands.cc:454
#, fuzzy
msgid ""
"Use specified info for remote login. If you specify URL, the password\n"
"will be cached for future usage.\n"
msgstr ""
"Usa le informazioni specificate per il login remoto. Se si specifica l'URL,\n"
"la password sarà messa in cache per l'uso futuro."

#: src/commands.cc:457
msgid "Shows lftp version\n"
msgstr "Mostra la versione di lftp\n"

#: src/commands.cc:458
msgid "wait [<jobno>]"
msgstr "wait [<num_job>]"

#: src/commands.cc:459
msgid ""
"Wait for specified job to terminate. If jobno is omitted, wait\n"
"for last backgrounded job.\n"
msgstr ""
"Attende la conclusione del job specificato.  Se è omesso il numjob,\n"
"attende l'ultimo job messo in background.\n"

#: src/commands.cc:461
msgid "zcat <files>"
msgstr "zcat <file>"

#: src/commands.cc:462
msgid "Same as cat, but filter each file through zcat\n"
msgstr "Uguale a cat, ma filtra ognuno dei file attraverso zcat\n"

#: src/commands.cc:463
msgid "zmore <files>"
msgstr "zmore <file>"

#: src/commands.cc:464
msgid "Same as more, but filter each file through zcat\n"
msgstr "Uguale a more, ma filtra ognuno dei file attraverso zmore\n"

#: src/commands.cc:466
#, fuzzy
msgid "Same as cat, but filter each file through bzcat\n"
msgstr "Uguale a cat, ma filtra ognuno dei file attraverso zcat\n"

#: src/commands.cc:468
#, fuzzy
msgid "Same as more, but filter each file through bzcat\n"
msgstr "Uguale a more, ma filtra ognuno dei file attraverso zmore\n"

#: src/commands.cc:524
#, c-format
msgid "Usage: %s local-dir\n"
msgstr "Uso: %s dir-locale\n"

#: src/commands.cc:560
#, c-format
msgid "lcd ok, local cwd=%s\n"
msgstr "lcd ok, cwd locale=%s\n"

#: src/commands.cc:576
#, c-format
msgid "Usage: cd remote-dir\n"
msgstr "Uso: cd dir-remota\n"

#: src/commands.cc:588
#, c-format
msgid "%s: no old directory for this site\n"
msgstr "%s: nessuna vecchia directory per questo sito\n"

#: src/commands.cc:677
#, c-format
msgid "Usage: %s [<exit_code>]\n"
msgstr "Uso: %s [<codice_d'uscita>]\n"

#: src/commands.cc:686
msgid ""
"There are running jobs and `cmd:move-background' is not set.\n"
"Use `exit bg' to force moving to background or `kill all' to terminate "
"jobs.\n"
msgstr ""

#: src/commands.cc:703
msgid ""
"\n"
"lftp now tricks the shell to move it to background process group.\n"
"lftp continues to run in the background despite the `Stopped' message.\n"
"lftp will automatically terminate when all jobs are finished.\n"
"Use `fg' shell command to return to lftp if it is still running.\n"
msgstr ""

#: src/commands.cc:837 src/commands.cc:3616
#, c-format
msgid "Try `%s --help' for more information\n"
msgstr "Usare `%s --help' per maggiori informazioni\n"

#: src/commands.cc:856
#, c-format
msgid "%s: -c, -f, -v, -h conflict with other `open' options and arguments\n"
msgstr ""

#: src/commands.cc:956
#, c-format
msgid "Usage: %s [-e cmd] [-p port] [-u user[,pass]] <host|url>\n"
msgstr "Uso: %s [-e cmd] [-p porta] [-u utente,[password]] <host|url>\n"

#: src/commands.cc:1046 src/commands.cc:2276 src/DummyProto.cc:65
#: src/MirrorJob.cc:2172 src/MirrorJob.cc:2194
msgid " - not supported protocol"
msgstr "- protocollo non supportato"

#: src/commands.cc:1078 src/commands.cc:2260
msgid "Password: "
msgstr "Password: "

#: src/commands.cc:1080
#, c-format
msgid "%s: GetPass() failed -- assume anonymous login\n"
msgstr "%s: GetPass() fallita -- assumo login anonimo\n"

#: src/commands.cc:1191 src/commands.cc:1284 src/commands.cc:1660
#: src/commands.cc:1691 src/commands.cc:1829 src/commands.cc:1914
#: src/commands.cc:2187 src/commands.cc:2381 src/commands.cc:2543
#: src/commands.cc:2588 src/commands.cc:2616 src/commands.cc:2623
#: src/commands.cc:2930 src/commands.cc:2957 src/commands.cc:2964
#: src/commands.cc:3293 src/lftp.cc:267 src/MirrorJob.cc:2136
#: src/SleepJob.cc:144 src/SleepJob.cc:200 src/Torrent.cc:4169
#, c-format
msgid "Try `help %s' for more information.\n"
msgstr "Usare `help %s' per maggiori informazioni.\n"

#: src/commands.cc:1201
#, c-format
msgid "Usage: %s [OPTS] command args...\n"
msgstr "Uso: %s [OPZ] comando arg...\n"

#: src/commands.cc:1254
#, fuzzy, c-format
msgid "%s: -n: positive number expected. "
msgstr "%s: -n: Ci vuole un numero. "

#: src/commands.cc:1306
#, c-format
msgid "Created a stopped queue.\n"
msgstr ""

#: src/commands.cc:1349 src/commands.cc:1377
#, c-format
msgid "%s: No queue is active.\n"
msgstr ""

#: src/commands.cc:1369
#, fuzzy, c-format
msgid "%s: -m: Number expected as second argument. "
msgstr "%s: -n: Ci vuole un numero. "

#: src/commands.cc:1421
#, c-format
msgid "Usage: %s <cmd>\n"
msgstr "Uso: %s <cmd>\n"

#: src/commands.cc:1527
msgid "invalid argument for `--sort'"
msgstr ""

#: src/commands.cc:1557
#, fuzzy
msgid "invalid block size"
msgstr "numero non valido"

#: src/commands.cc:1700
#, c-format
msgid "Usage: %s [OPTS] files...\n"
msgstr "Uso: %s [OPZ] file...\n"

#: src/commands.cc:1785 src/commands.cc:1814
#, fuzzy, c-format
msgid "%s: %s: Number expected. "
msgstr "%s: -n: Ci vuole un numero. "

#: src/commands.cc:1834
#, c-format
msgid "%s: --continue conflicts with --remove-target.\n"
msgstr ""

#: src/commands.cc:1844 src/commands.cc:1920
#, c-format
msgid "File name missed. "
msgstr "Manca il nome del file. "

#: src/commands.cc:1989
#, fuzzy, c-format
msgid "Usage: %s %s[-f] files...\n"
msgstr "Uso: %s [-r] [-f] file...\n"

#: src/commands.cc:2032
#, fuzzy, c-format
msgid "Usage: %s [-e] <file|command>\n"
msgstr "Uso: %s <file>\n"

#: src/commands.cc:2079
#, c-format
msgid "Usage: %s [-v] [-v] ...\n"
msgstr "Uso: %s [-v] [-v] ...\n"

#: src/commands.cc:2093 src/commands.cc:2347 src/commands.cc:2469
#: src/commands.cc:2685 src/commands.cc:3101 src/commands.cc:3182
#: src/lftp.cc:279
#, c-format
msgid "%s: %s - not a number\n"
msgstr "%s: %s - non è un numero\n"

#: src/commands.cc:2100 src/commands.cc:2326 src/commands.cc:2356
#: src/commands.cc:2487
#, c-format
msgid "%s: %d - no such job\n"
msgstr "%s: %d - job non esistente\n"

#: src/commands.cc:2135
#, c-format
msgid "Usage: %s [-p]\n"
msgstr "Uso: %s [-p]\n"

#: src/commands.cc:2227
#, c-format
msgid "debug level is %d, output goes to %s\n"
msgstr "il livello di debug è %d, l'output va su %s\n"

#: src/commands.cc:2230
#, c-format
msgid "debug is off\n"
msgstr "il debug è off\n"

#: src/commands.cc:2241
#, fuzzy, c-format
msgid "Usage: %s <user|URL> [<pass>]\n"
msgstr "user <utente|URL> [<pass>]"

#: src/commands.cc:2316 src/commands.cc:2479
#, c-format
msgid "%s: no current job\n"
msgstr "%s: nessun job corrente\n"

#: src/commands.cc:2328
#, c-format
msgid "Usage: %s <jobno> ... | all\n"
msgstr "Uso: %s <numjob> ...| all\n"

#: src/commands.cc:2409
#, c-format
msgid "%s: %s. Use `set -a' to look at all variables.\n"
msgstr "%s: %s. Usare `set -a' per vedere tutte le variabili.\n"

#: src/commands.cc:2453
#, c-format
msgid "Usage: %s [<jobno>]\n"
msgstr "Uso: %s [<numjob>]\n"

#: src/commands.cc:2492
#, c-format
msgid "%s: some other job waits for job %d\n"
msgstr "%s: qualche altro job è in attesa del job %d\n"

#: src/commands.cc:2497
#, c-format
msgid "%s: wait loop detected\n"
msgstr ""

#: src/commands.cc:2553
#, fuzzy, c-format
msgid "Usage: %s [OPTS] <files> <target-dir>\n"
msgstr "Uso: %s [OPZ] <file>\n"

#: src/commands.cc:2615 src/commands.cc:2956
#, c-format
msgid "Invalid command. "
msgstr "Comando non valido. "

#: src/commands.cc:2622 src/commands.cc:2963
#, c-format
msgid "Ambiguous command. "
msgstr "Comando ambiguo."

#: src/commands.cc:2641
#, c-format
msgid "%s: Operand missed for size\n"
msgstr "%s: Manca l'operando per la dimensione\n"

#: src/commands.cc:2658
#, c-format
msgid "%s: Operand missed for `expire'\n"
msgstr "%s: Manca l'operando per `expire'\n"

#: src/commands.cc:2691
#, c-format
msgid ""
"%s: %s - no such cached session. Use `scache' to look at session list.\n"
msgstr ""
"%s: %s - non c'è questa sessione in cache. Usare `scache' per vedere\n"
"un elenco delle sessioni.\n"

#: src/commands.cc:2715
#, c-format
msgid "Sorry, no help for %s\n"
msgstr "Spiacente, nessun aiuto per %s\n"

#: src/commands.cc:2720
#, c-format
msgid "%s is a built-in alias for %s\n"
msgstr "%s è un alias interno per %s\n"

#: src/commands.cc:2725
#, c-format
msgid "Usage: %s\n"
msgstr "Uso: %s\n"

#: src/commands.cc:2733
#, c-format
msgid "%s is an alias to `%s'\n"
msgstr "%s è un alias per `%s'\n"

#: src/commands.cc:2737
#, c-format
msgid "No such command `%s'. Use `help' to see available commands.\n"
msgstr ""
"`%s' comando inesistente. Usare `help' per vedere i comandi disponibili.\n"

#: src/commands.cc:2739
#, c-format
msgid "Ambiguous command `%s'. Use `help' to see available commands.\n"
msgstr "`%s' comando ambiguo. Usare `help' per vedere i comandi disponibili.\n"

#: src/commands.cc:2805
#, fuzzy, c-format
msgid "LFTP | Version %s | Copyright (c) 1996-%d Alexander V. Lukyanov\n"
msgstr "Lftp | Versione %s | Copyright (c) 1996-2001 Alexander V. Lukyanov\n"

#: src/commands.cc:2808
#, c-format
msgid ""
"LFTP is free software: you can redistribute it and/or modify\n"
"it under the terms of the GNU General Public License as published by\n"
"the Free Software Foundation, either version 3 of the License, or\n"
"(at your option) any later version.\n"
"\n"
"This program is distributed in the hope that it will be useful,\n"
"but WITHOUT ANY WARRANTY; without even the implied warranty of\n"
"MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n"
"GNU General Public License for more details.\n"
"\n"
"You should have received a copy of the GNU General Public License\n"
"along with LFTP.  If not, see <http://www.gnu.org/licenses/>.\n"
msgstr ""

#: src/commands.cc:2822
#, fuzzy, c-format
msgid "Send bug reports and questions to the mailing list <%s>.\n"
msgstr "Inviare segnalazioni di bug e domande a <%s>.\n"

#: src/commands.cc:2828
msgid "Libraries used: "
msgstr ""

#: src/commands.cc:2979 src/commands.cc:3007
#, c-format
msgid "%s: bookmark name required\n"
msgstr "%s: serve un nome di bookmark\n"

#: src/commands.cc:2996
#, c-format
msgid "%s: spaces in bookmark name are not allowed\n"
msgstr "%s: non sono permessi gli spazi bianchi nei nomi dei bookmark\n"

#: src/commands.cc:3009
#, c-format
msgid "%s: no such bookmark `%s'\n"
msgstr "%s: bookmark `%s' inesistente\n"

#: src/commands.cc:3032
#, c-format
msgid "%s: import type required (netscape,ncftp)\n"
msgstr "%s: importa il tipo richiesto (netscape, ncftp)\n"

#: src/commands.cc:3110
#, fuzzy, c-format
msgid "Usage: %s [-d #] dir\n"
msgstr "Uso: %s [-p]\n"

#: src/commands.cc:3215
#, fuzzy, c-format
msgid "%s: invalid block size `%s'\n"
msgstr "numero non valido"

#: src/commands.cc:3226
#, fuzzy, c-format
msgid "Usage: %s [options] <dirs>\n"
msgstr "Uso: %s [-p]\n"

#: src/commands.cc:3232
#, c-format
msgid "%s: warning: summarizing is the same as using --max-depth=0\n"
msgstr ""

#: src/commands.cc:3236
#, c-format
msgid "%s: summarizing conflicts with --max-depth=%i\n"
msgstr ""

#: src/commands.cc:3280
#, c-format
msgid "Usage: %s command args...\n"
msgstr "Uso: %s comando arg...\n"

#: src/commands.cc:3292
#, c-format
msgid "Usage: %s module [args...]\n"
msgstr "Uso: %s modulo [arg...]\n"

#: src/commands.cc:3310 src/LocalAccess.cc:642
#, fuzzy
msgid "cannot get current directory"
msgstr "Cambio la directory remota..."

#: src/commands.cc:3374
#, fuzzy, c-format
msgid "Usage: %s [OPTS] mode file...\n"
msgstr "Uso: %s [OPZ] file...\n"

#: src/commands.cc:3394
#, fuzzy, c-format
msgid "invalid mode string: %s\n"
msgstr "numero non valido"

#: src/commands.cc:3457 src/commands.cc:3466
msgid "Invalid range format. Format is min-max, e.g. 10-20."
msgstr "Formato dell'intervallo non valido. Il formato è min-max, es. 10-20."

#: src/commands.cc:3478
#, c-format
msgid "Usage: %s [OPTS] file\n"
msgstr "Uso: %s [OPZ] <file>\n"

#: src/CopyJob.cc:82
#, c-format
msgid "`%s' at %lld %s%s%s%s"
msgstr ""

#: src/CopyJob.cc:159
#, fuzzy, c-format
msgid "%lld $#ll#byte|bytes$ transferred in %ld $#l#second|seconds$"
msgstr "%ld byte $#l#trasferito|trasferiti$ in %ld $#l#secondo|secondi$"

#: src/CopyJob.cc:167
#, fuzzy, c-format
msgid "%lld $#ll#byte|bytes$ transferred"
msgstr "Trasferit$#l#o|i$ %ld byte\n"

#: src/CopyJob.cc:283
#, c-format
msgid "Transfer of %d of %d $file|files$ failed\n"
msgstr "Fallito il trasferimento di %d su %d file$|$\n"

#: src/CopyJob.cc:289
#, c-format
msgid "Total %d $file|files$ transferred\n"
msgstr "Trasferit$o|i$ %d file in totale\n"

#: src/FileAccess.cc:160
#, fuzzy
msgid "Access failed: "
msgstr "Accesso fallito: %s"

#: src/FileAccess.cc:161
msgid "File cannot be accessed"
msgstr "Impossibile accedere al file"

#: src/FileAccess.cc:163 src/Fish.cc:982 src/ftpclass.cc:4599
#: src/ftpclass.cc:4608 src/SFtp.cc:1339 src/Torrent.cc:3533
msgid "Not connected"
msgstr "Non connesso"

#: src/FileAccess.cc:166 src/FileAccess.cc:167
msgid "Fatal error"
msgstr "Errore fatale"

#: src/FileAccess.cc:169
msgid "Store failed - you have to reput"
msgstr "Salvataggio fallito - si deve fare reput"

#: src/FileAccess.cc:172 src/FileAccess.cc:173
msgid "Login failed"
msgstr "Login fallito"

#: src/FileAccess.cc:176 src/FileAccess.cc:177
msgid "Operation not supported"
msgstr "Operazione non supportata"

#: src/FileAccess.cc:180
msgid "File moved"
msgstr ""

#: src/FileAccess.cc:182
msgid "File moved to `"
msgstr ""

#: src/FileCopy.cc:141
msgid "copy: destination file is already complete\n"
msgstr ""

#: src/FileCopy.cc:182
msgid "copy: put is broken\n"
msgstr ""

#: src/FileCopy.cc:201
#, fuzzy
msgid "seek failed"
msgstr "Login fallito"

#: src/FileCopy.cc:207
msgid "no progress timeout"
msgstr ""

#: src/FileCopy.cc:235
msgid "cannot seek on data source"
msgstr ""

#: src/FileCopy.cc:238
#, c-format
msgid "copy: put rolled back to %lld, seeking get accordingly\n"
msgstr ""

#: src/FileCopy.cc:251
msgid "copy: all data received, but get rolled back\n"
msgstr ""

#: src/FileCopy.cc:267
#, c-format
msgid "copy: get rolled back to %lld, seeking put accordingly\n"
msgstr ""

#: src/FileCopy.cc:364
msgid "file size decreased during transfer"
msgstr ""

#: src/FileCopy.cc:1227
#, c-format
msgid "copy: received redirection to `%s'\n"
msgstr ""

#: src/FileCopy.cc:1290
#, fuzzy
#| msgid "saw file size in response"
msgid "file size increased during transfer"
msgstr "ottenuta in risposta la dimensione del file"

#: src/FileCopy.cc:1390
msgid "file name missed in URL"
msgstr "nell'URL manca il nome del file"

#: src/FileCopy.cc:2086
msgid "Verify command failed without a message"
msgstr ""

#: src/FileCopyFtp.cc:95
msgid "**** FXP: trying to reverse ftp:fxp-passive-source\n"
msgstr ""

#: src/FileCopyFtp.cc:102
msgid "**** FXP: trying to reverse ftp:fxp-passive-sscn\n"
msgstr ""

#: src/FileCopyFtp.cc:110
msgid "**** FXP: trying to reverse ftp:ssl-protect-fxp\n"
msgstr ""

#: src/FileCopyFtp.cc:116
msgid "**** FXP: giving up, reverting to plain copy\n"
msgstr ""

#: src/FileCopyFtp.cc:125
msgid "ftp:fxp-force is set but FXP is not available"
msgstr ""

#: src/FileCopy.h:285
msgid "Verifying..."
msgstr ""

#: src/FileSetOutput.cc:230
msgid "non-option arguments found"
msgstr ""

#: src/Filter.cc:166
#, fuzzy
msgid "pipe() failed: "
msgstr "execlp(%s) fallita: %s\n"

#: src/Filter.cc:200 src/PtyShell.cc:135
#, fuzzy, c-format
msgid "chdir(%s) failed: %s\n"
msgstr "Attenzione: chdir(%s) fallita: %s\n"

#: src/Filter.cc:208
#, fuzzy, c-format
msgid "execvp(%s) failed: %s\n"
msgstr "execlp(%s) fallita: %s\n"

#: src/Filter.cc:213 src/PtyShell.cc:147
#, fuzzy, c-format
msgid "execl(/bin/sh) failed: %s\n"
msgstr "execlp(%s) fallita: %s\n"

#: src/Filter.cc:415
#, fuzzy
msgid "file already exists and xfer:clobber is unset"
msgstr "%s: %s: il file esiste già e xfer:clobber non è impostato\n"

#: src/FindJobDu.cc:101
msgid "total"
msgstr ""

#: src/Fish.cc:75 src/ftpclass.cc:1292 src/Http.cc:1232 src/SFtp.cc:77
#, c-format
msgid "Closing idle connection"
msgstr "Chiusura della connessione inattiva"

#: src/Fish.cc:162 src/SFtp.cc:177
msgid "Running connect program"
msgstr ""

#: src/Fish.cc:588 src/ftpclass.cc:2887 src/ftpclass.cc:3107 src/Http.cc:1510
#: src/SFtp.cc:742 src/SFtp.cc:1083 src/SFtp.cc:1084 src/SSH_Access.cc:167
#, c-format
msgid "Peer closed connection"
msgstr "Connessione chiusa dal peer"

#: src/Fish.cc:634 src/ftpclass.cc:3037 src/ftpclass.cc:4219 src/SFtp.cc:1108
#, c-format
msgid "extra server response"
msgstr "risposta extra del server"

#: src/Fish.cc:987 src/ftpclass.cc:4611 src/Http.cc:2329 src/Http.cc:2336
#: src/SFtp.cc:1345 src/Torrent.cc:3536 src/TorrentTracker.cc:624
msgid "Connecting..."
msgstr "Connessione in corso..."

#: src/Fish.cc:989 src/ftpclass.cc:4617 src/SFtp.cc:1347
#, fuzzy
msgid "Connected"
msgstr "Non connesso"

#: src/Fish.cc:991 src/ftpclass.cc:4594 src/ftpclass.cc:4622
#: src/ftpclass.cc:4636 src/Http.cc:2338 src/SFtp.cc:1349
#: src/TorrentTracker.cc:626
msgid "Waiting for response..."
msgstr "In attesa della risposta..."

#: src/Fish.cc:993 src/ftpclass.cc:4656 src/Http.cc:2341 src/SFtp.cc:1351
msgid "Receiving data"
msgstr "Sto ricevendo i dati"

#: src/Fish.cc:995 src/ftpclass.cc:4654 src/Http.cc:2334 src/SFtp.cc:1353
msgid "Sending data"
msgstr "Sto inviando i dati"

#: src/Fish.cc:997 src/SFtp.cc:1355
msgid "Done"
msgstr ""

#: src/Fish.cc:1136 src/FtpDirList.cc:123 src/HttpDir.cc:1384 src/SFtp.cc:2200
#: src/SFtp.cc:2320
#, fuzzy, c-format
msgid "Getting file list (%lld) [%s]"
msgstr "Scarico la lista dei file (%ld) [%s]"

#: src/ftpclass.cc:197
#, c-format
msgid "Data connection peer has wrong port number"
msgstr "L'altro estremo della connessione dati ha un numero di porta errato"

#: src/ftpclass.cc:203
#, c-format
msgid "Data connection peer has mismatching address"
msgstr ""
"L'altro estremo della connesione dati ha un indirizzo che non corrisponde"

#: src/ftpclass.cc:225 src/ftpclass.cc:250
#, fuzzy, c-format
msgid "Switching to NOREST mode"
msgstr "Abilito la modalità passiva"

#: src/ftpclass.cc:425
#, c-format
msgid "Server reply matched ftp:retry-530, retrying"
msgstr ""

#: src/ftpclass.cc:433
#, c-format
msgid "Server reply matched ftp:retry-530-anonymous, retrying"
msgstr ""

#: src/ftpclass.cc:466
msgid "Account is required, set ftp:acct variable"
msgstr ""

#: src/ftpclass.cc:483
msgid "ftp:skey-force is set and server does not support OPIE nor S/KEY"
msgstr ""

#: src/ftpclass.cc:501
#, c-format
msgid "assuming failed host name lookup"
msgstr "dò per fallita la ricerca del nome host"

#: src/ftpclass.cc:809 src/ftpclass.cc:810
#, c-format
msgid "cannot parse EPSV response"
msgstr ""

#: src/ftpclass.cc:837 src/ftpclass.cc:838
#, c-format
msgid "cannot parse custom EPSV response"
msgstr ""

#: src/ftpclass.cc:1122
#, c-format
msgid "Closing control socket"
msgstr "Chiusura del socket di controllo"

#: src/ftpclass.cc:1341
#, fuzzy
msgid "MLSD is disabled by ftp:use-mlsd"
msgstr "SITE CHMOD non è supportato da questo sito"

#: src/ftpclass.cc:1411 src/Http.cc:1431 src/Torrent.cc:3204
#: src/Torrent.cc:3743 src/TorrentTracker.cc:395
#, c-format
msgid "cannot create socket of address family %d"
msgstr ""

#: src/ftpclass.cc:1442 src/Http.cc:1457
#, c-format
msgid "Socket error (%s) - reconnecting"
msgstr "Errore di socket (%s) - riconnessione in corso"

#: src/ftpclass.cc:1715
#, fuzzy
msgid "MFF and SITE CHMOD are not supported by this site"
msgstr "SITE CHMOD non è supportato da questo sito"

#: src/ftpclass.cc:1720
#, fuzzy
msgid "MLST and MLSD are not supported by this site"
msgstr "SITE CHMOD non è supportato da questo sito"

#: src/ftpclass.cc:2031
#, fuzzy
msgid "SITE SYMLINK is not supported by the server"
msgstr "SITE CHMOD non è supportato da questo sito"

#: src/ftpclass.cc:2204
#, fuzzy
msgid "unsupported network protocol"
msgstr "- protocollo non supportato"

#: src/ftpclass.cc:2253 src/ftpclass.cc:2355
#, fuzzy, c-format
msgid "Data socket error (%s) - reconnecting"
msgstr "Errore di socket (%s) - riconnessione in corso"

#: src/ftpclass.cc:2281
#, fuzzy, c-format
msgid "Accepted data connection from (%s) port %u"
msgstr "Connessione del socket dei dati a (%s) porta %u"

#: src/ftpclass.cc:2330
#, c-format
msgid "Connecting data socket to (%s) port %u"
msgstr "Connessione del socket dei dati a (%s) porta %u"

#: src/ftpclass.cc:2336
#, fuzzy, c-format
msgid "Connecting data socket to proxy %s (%s) port %u"
msgstr "Connessione del socket dei dati a (%s) porta %u"

#: src/ftpclass.cc:2358 src/ftpclass.cc:4414
#, c-format
msgid "Switching passive mode off"
msgstr "Metto ad off (disabilito) la modalità passiva"

#: src/ftpclass.cc:2366
#, fuzzy, c-format
msgid "Data connection established"
msgstr ""
"L'altro estremo della connesione dati ha un indirizzo che non corrisponde"

#: src/ftpclass.cc:2688 src/ftpclass.cc:4548
msgid "ftp:ssl-force is set and server does not support or allow SSL"
msgstr ""

#: src/ftpclass.cc:3053
#, c-format
msgid "Persist and retry"
msgstr ""

#: src/ftpclass.cc:3321
#, c-format
msgid "Closing data socket"
msgstr "Chiusura del socket dati"

#: src/ftpclass.cc:3343
#, fuzzy, c-format
msgid "Closing aborted data socket"
msgstr "Chiusura del socket dati"

#: src/ftpclass.cc:4193
#, c-format
msgid "saw file size in response"
msgstr "ottenuta in risposta la dimensione del file"

#: src/ftpclass.cc:4233 src/ftpclass.cc:4260
#, c-format
msgid "Turning on sync-mode"
msgstr ""

#: src/ftpclass.cc:4434
#, c-format
msgid "Switching passive mode on"
msgstr "Abilito la modalità passiva"

#: src/ftpclass.cc:4585
msgid "FEAT negotiation..."
msgstr ""

#: src/ftpclass.cc:4592
msgid "Sending commands..."
msgstr "Invio comandi..."

#: src/ftpclass.cc:4596
msgid "Delaying before retry"
msgstr ""

#: src/ftpclass.cc:4597 src/Http.cc:2331
msgid "Connection idle"
msgstr "Connessione inattiva"

#: src/ftpclass.cc:4604 src/Http.cc:2323 src/Resolver.cc:172
#: src/Resolver.cc:217 src/TorrentTracker.cc:622
#, c-format
msgid "Resolving host address..."
msgstr "Risoluzione dell'indirizzo dell'host..."

#: src/ftpclass.cc:4615
msgid "TLS negotiation..."
msgstr ""

#: src/ftpclass.cc:4619
msgid "Logging in..."
msgstr "Accesso al server..."

#: src/ftpclass.cc:4623
msgid "Making data connection..."
msgstr "Creo la connessione dati..."

#: src/ftpclass.cc:4626
msgid "Changing remote directory..."
msgstr "Cambio la directory remota..."

#: src/ftpclass.cc:4632
#, fuzzy
msgid "Waiting for other copy peer..."
msgstr "In attesa della risposta..."

#: src/ftpclass.cc:4634 src/ftpclass.cc:4658
msgid "Waiting for transfer to complete"
msgstr "Attendo la fine del trasferimento"

#: src/ftpclass.cc:4638
#, fuzzy
msgid "Waiting for TLS shutdown..."
msgstr "In attesa della risposta..."

#: src/ftpclass.cc:4640
msgid "Waiting for data connection..."
msgstr "In attesa della connessione dati..."

#: src/ftpclass.cc:4646
#, fuzzy
msgid "Sending data/TLS"
msgstr "Sto inviando i dati"

#: src/ftpclass.cc:4648
#, fuzzy
msgid "Receiving data/TLS"
msgstr "Sto ricevendo i dati"

#: src/Http.cc:230
#, fuzzy, c-format
msgid "Closing HTTP connection"
msgstr "Chiusura della connessione inattiva"

#: src/Http.cc:240
msgid "POST method failed"
msgstr ""

#: src/Http.cc:1381
msgid "ftp over http cannot work without proxy, set hftp:proxy."
msgstr ""

#: src/Http.cc:1537
#, fuzzy, c-format
msgid "Sending request..."
msgstr "Invio comandi..."

#: src/Http.cc:1575
#, fuzzy, c-format
msgid "Hit EOF while fetching headers"
msgstr "Scarico gli header..."

#: src/Http.cc:1695
#, c-format
msgid "Could not parse HTTP status line"
msgstr ""

#: src/Http.cc:1726
msgid "Object is not cached and http:cache-control has only-if-cached"
msgstr ""

#: src/Http.cc:1891
#, fuzzy, c-format
msgid "Receiving body..."
msgstr "Sto ricevendo i dati"

#: src/Http.cc:2092
#, c-format
msgid "Hit EOF"
msgstr ""

#: src/Http.cc:2095
#, c-format
msgid "Received not enough data, retrying"
msgstr ""

#: src/Http.cc:2106
#, fuzzy, c-format
msgid "Received all"
msgstr "Sto ricevendo i dati"

#: src/Http.cc:2111
#, c-format
msgid "Received all (total)"
msgstr ""

#: src/Http.cc:2135 src/Http.cc:2159
msgid "chunked format violated"
msgstr "violato il formato chunked"

#: src/Http.cc:2145
#, fuzzy, c-format
msgid "Received last chunk"
msgstr "Sto ricevendo i dati"

#: src/Http.cc:2339
msgid "Fetching headers..."
msgstr "Scarico gli header..."

#: src/Job.cc:293
#, c-format
msgid "[%d] Done (%s)"
msgstr "[%d] Fatto (%s)"

#: src/lftp.cc:370
#, fuzzy, c-format
msgid "[%u] Terminated by signal %d. %s\n"
msgstr "[%lu] Terminato dal signal %d. %s"

#: src/lftp.cc:445
#, fuzzy, c-format
msgid "[%u] Started.  %s\n"
msgstr "[%lu] Avviato.  %s"

#: src/lftp.cc:463
#, fuzzy, c-format
msgid "[%u] Detaching from the terminal to complete transfers...\n"
msgstr "[%lu] Passo in background per completare il trasferimento...\n"

#: src/lftp.cc:465
#, c-format
msgid "[%u] Exiting and detaching from the terminal.\n"
msgstr ""

#: src/lftp.cc:470
#, c-format
msgid "[%u] Detached from terminal. %s\n"
msgstr ""

#: src/lftp.cc:480
#, fuzzy, c-format
msgid "[%u] Finished. %s\n"
msgstr "[%lu] Concluso. %s"

#: src/lftp.cc:484
#, fuzzy, c-format
msgid "[%u] Moving to background to complete transfers...\n"
msgstr "[%lu] Passo in background per completare il trasferimento...\n"

#: src/lftp.cc:553
msgid "history -w file|-r file|-c|-l [cnt]"
msgstr ""

#: src/lftp.cc:554
msgid ""
" -w <file> Write history to file.\n"
" -r <file> Read history from file; appends to current history.\n"
" -c  Clear the history.\n"
" -l  List the history (default).\n"
"Optional argument cnt specifies the number of history lines to list,\n"
"or \"all\" to list all entries.\n"
msgstr ""

#: src/lftp.cc:561
msgid "Attach the terminal to specified backgrounded lftp process.\n"
msgstr ""

#: src/lftp_ssl.cc:1291
#, c-format
msgid "No certificate presented by %s.\n"
msgstr ""

#: src/LocalAccess.cc:604 src/NetAccess.cc:686
#, fuzzy
msgid "Getting directory contents"
msgstr "Scarico il contenuto della directory (%ld)"

#: src/LocalAccess.cc:606 src/NetAccess.cc:690
#, fuzzy
msgid "Getting files information"
msgstr "Scarico le informazioni sui file (%d%%)"

#: src/LsCache.cc:190
#, c-format
msgid "%ld $#l#byte|bytes$ cached"
msgstr "%ld byte salvat$#l#o|i$ in cache"

#: src/LsCache.cc:194
msgid ", no size limit"
msgstr ", dimensione illimitata"

#: src/LsCache.cc:196
#, c-format
msgid ", maximum size %ld\n"
msgstr ", dimensione massima %ld\n"

#: src/mgetJob.cc:78
#, fuzzy, c-format
msgid "%s: %s: no files found\n"
msgstr "%s: nessun file trovato\n"

#: src/MirrorJob.cc:100
#, c-format
msgid "%sTotal: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
msgstr "%sTotale: %d directory$|$, %d file$|$, %d link simbolic$o|i$\n"

#: src/MirrorJob.cc:104
#, c-format
msgid "%sNew: %d file$|s$, %d symlink$|s$\n"
msgstr "%sNuovi: %d file$|$, %d link simbolic$o|i$\n"

#: src/MirrorJob.cc:108
#, c-format
msgid "%sModified: %d file$|s$, %d symlink$|s$\n"
msgstr "%sModificati: %d file$|$, %d link simbolic$o|i$\n"

#: src/MirrorJob.cc:115
#, c-format
msgid "%sRemoved: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
msgstr "%sCancellati: %d directory$|$, %d file$|$, %d link simbolic$o|i$\n"

#: src/MirrorJob.cc:120
#, fuzzy, c-format
msgid "%s%d error$|s$ detected\n"
msgstr "%s ok, create %d directory$|$\n"

#: src/MirrorJob.cc:231
#, fuzzy, c-format
msgid "Finished %s"
msgstr "[%lu] Concluso. %s"

#: src/MirrorJob.cc:344 src/MirrorJob.cc:519 src/MirrorJob.cc:1218
#, fuzzy, c-format
msgid "Removing old file `%s'"
msgstr "Cancellazione del vecchio file locale `%s'"

#: src/MirrorJob.cc:346
#, fuzzy, c-format
msgid "Overwriting old file `%s'"
msgstr "Cancellazione del vecchio file locale `%s'"

#: src/MirrorJob.cc:355
#, c-format
msgid "Skipping file `%s' (only-existing)"
msgstr ""

#: src/MirrorJob.cc:361
#, fuzzy, c-format
msgid "Transferring file `%s'"
msgstr "Invio il file locale `%s'"

#: src/MirrorJob.cc:439
#, fuzzy, c-format
msgid "Skipping directory `%s' (only-existing)"
msgstr "Creo la directory remota `%s'"

#: src/MirrorJob.cc:461 src/MirrorJob.cc:550 src/MirrorJob.cc:902
#, c-format
msgid "Removing old local file `%s'"
msgstr "Cancellazione del vecchio file locale `%s'"

#: src/MirrorJob.cc:487
#, fuzzy, c-format
msgid "Scanning directory `%s'"
msgstr "Creo la directory remota `%s'"

#: src/MirrorJob.cc:489
#, c-format
msgid "Mirroring directory `%s'"
msgstr "Mirror della directory `%s'"

#: src/MirrorJob.cc:525 src/MirrorJob.cc:567
#, c-format
msgid "Making symbolic link `%s' to `%s'"
msgstr "Creo il link simbolico da `%s' a `%s'"

#: src/MirrorJob.cc:562
#, c-format
msgid "Skipping symlink `%s' (only-existing)"
msgstr ""

#: src/MirrorJob.cc:807
#, c-format
msgid "mirror: protocol `%s' is not suitable for mirror\n"
msgstr "mirror: il protocollo `%s' non va bene per fare il mirror\n"

#: src/MirrorJob.cc:923
#, fuzzy, c-format
msgid "Making directory `%s'"
msgstr "Creo la directory remota `%s'"

#: src/MirrorJob.cc:1181
#, fuzzy, c-format
msgid "Old directory `%s' is not removed"
msgstr "Il vecchio file locale `%s' non è stato rimosso"

#: src/MirrorJob.cc:1183
#, fuzzy, c-format
msgid "Old file `%s' is not removed"
msgstr "Il vecchio file locale `%s' non è stato rimosso"

#: src/MirrorJob.cc:1216
#, fuzzy, c-format
msgid "Removing old directory `%s'"
msgstr "Cancellazione del vecchio file remoto `%s'"

#: src/MirrorJob.cc:1339
#, fuzzy, c-format
msgid "Removing source directory `%s'"
msgstr "Cancellazione del vecchio file remoto `%s'"

#: src/MirrorJob.cc:1405
#, fuzzy, c-format
msgid "Removing source file `%s'"
msgstr "Cancellazione del vecchio file locale `%s'"

#: src/MirrorJob.cc:1425
#, c-format
msgid "Retrying mirror...\n"
msgstr ""

#: src/MirrorJob.cc:1694
msgid "pattern is empty"
msgstr ""

#: src/MirrorJob.cc:1779
#, c-format
msgid "%s must be one of: %s"
msgstr ""

#: src/MirrorJob.cc:2019
#, c-format
msgid ""
"%s: multiple --file or --directory options must have the same base "
"directory\n"
msgstr ""

#: src/MirrorJob.cc:2160
#, c-format
msgid "%s: ambiguous source directory (`%s' or `%s'?)\n"
msgstr ""

#: src/MirrorJob.cc:2182
#, c-format
msgid "%s: ambiguous target directory (`%s' or `%s'?)\n"
msgstr ""

#: src/MirrorJob.cc:2223
#, c-format
msgid "%s: source directory is required (mirror:require-source is set)\n"
msgstr ""

#: src/MirrorJob.cc:2343
msgid ""
"\n"
"Mirror specified remote directory to local directory\n"
"\n"
" -R, --reverse          reverse mirror (put files)\n"
"Lots of other options are documented in the man page lftp(1).\n"
"\n"
"When using -R, the first directory is local and the second is remote.\n"
"If the second directory is omitted, basename of the first directory is "
"used.\n"
"If both directories are omitted, current local and remote directories are "
"used.\n"
"\n"
"See the man page lftp(1) for a complete documentation.\n"
msgstr ""

#: src/mkdirJob.cc:128
#, c-format
msgid "%s ok, `%s' created\n"
msgstr "%s ok, creato `%s'\n"

#: src/mkdirJob.cc:130 src/rmJob.cc:51
#, fuzzy, c-format
msgid "%s failed for %d of %d director$y|ies$\n"
msgstr "%s fallito per %d su %d directory\n"

#: src/mkdirJob.cc:133
#, c-format
msgid "%s ok, %d director$y|ies$ created\n"
msgstr "%s ok, create %d directory$|$\n"

#: src/module.cc:190
#, c-format
msgid "depend module `%s': %s\n"
msgstr ""

#: src/module.cc:210
#, fuzzy
msgid "modules are not supported on this system"
msgstr "SITE CHMOD non è supportato da questo sito"

#: src/mvJob.cc:92
#, c-format
msgid "rename successful\n"
msgstr "rinominazione riuscita\n"

#: src/NetAccess.cc:168
#, fuzzy, c-format
msgid "Connecting to %s%s (%s) port %u"
msgstr "Connessione del socket dei dati a (%s) porta %u"

#: src/NetAccess.cc:224 src/Torrent.cc:3326
#, c-format
msgid "Timeout - reconnecting"
msgstr "Timeout - riconnessione in corso"

#: src/NetAccess.cc:323
#, fuzzy
msgid "Connection limit reached"
msgstr "Connessione inattiva"

#: src/NetAccess.cc:330
msgid "Delaying before reconnect"
msgstr ""

#: src/NetAccess.cc:354 src/NetAccess.cc:356
msgid "max-retries exceeded"
msgstr ""

#: src/OutputJob.cc:145
#, c-format
msgid "%s (filter)"
msgstr ""

#: src/parsecmd.cc:290
msgid "parse: missing filter command\n"
msgstr "parse: manca il comando filtro\n"

#: src/parsecmd.cc:292
msgid "parse: missing redirection filename\n"
msgstr "parse: manca il nome del file per la redirezione\n"

#: src/PatternSet.cc:110
#, c-format
msgid "regular expression `%s': %s"
msgstr ""

#: src/pgetJob.cc:127
msgid "pget: falling back to plain get"
msgstr ""

#: src/pgetJob.cc:131
#, fuzzy
msgid "the target file is remote"
msgstr "Il vecchio file remoto `%s' non è stato rimosso"

#: src/pgetJob.cc:136
msgid "the source file size is unknown"
msgstr ""

#: src/pgetJob.cc:173
#, c-format
msgid "pget: warning: space allocation for %s (%lld bytes) failed: %s\n"
msgstr ""

#: src/pgetJob.cc:234
#, c-format
msgid "`%s', got %lld of %lld (%d%%) %s%s"
msgstr ""

#: src/plural.c:96
msgid "=1 =0|>1"
msgstr "=1 =0|>1"

#: src/PollVec.cc:69
#, c-format
msgid "%s: BUG - deadlock detected\n"
msgstr ""

#: src/PtyShell.cc:68
msgid "pseudo-tty allocation failed: "
msgstr ""

#: src/QueueFeeder.cc:68
msgid "Added job$|s$"
msgstr ""

#: src/QueueFeeder.cc:164 src/QueueFeeder.cc:185
#, c-format
msgid "No queued jobs.\n"
msgstr ""

#: src/QueueFeeder.cc:166
#, c-format
msgid "No queued job #%i.\n"
msgstr ""

#: src/QueueFeeder.cc:171 src/QueueFeeder.cc:192
msgid "Deleted job$|s$"
msgstr ""

#: src/QueueFeeder.cc:187
#, c-format
msgid "No queued jobs match \"%s\".\n"
msgstr ""

#: src/QueueFeeder.cc:212 src/QueueFeeder.cc:230
msgid "Moved job$|s$"
msgstr ""

#: src/QueueFeeder.cc:354
#, fuzzy
msgid "Commands queued:"
msgstr "\tComandi in coda:\n"

#: src/ResMgr.cc:123
msgid "no such variable"
msgstr "variabile inesistente"

#: src/ResMgr.cc:127
msgid "ambiguous variable name"
msgstr "nome della variabile ambiguo"

#: src/ResMgr.cc:335
msgid "invalid boolean value"
msgstr "valore booleano non valido"

#: src/ResMgr.cc:357
#, fuzzy
msgid "invalid boolean/auto value"
msgstr "valore booleano non valido"

#: src/ResMgr.cc:402 src/ResMgr.cc:713
msgid "invalid number"
msgstr "numero non valido"

#: src/ResMgr.cc:415
msgid "invalid floating point number"
msgstr "numero in virgola mobile non valido"

#: src/ResMgr.cc:429
#, fuzzy
msgid "invalid unsigned number"
msgstr "numero non valido"

#: src/ResMgr.cc:678
msgid "Invalid time unit letter, only [smhd] are allowed."
msgstr "Lettera per l'unità di tempo non valida. Sono permesse solo [smhd]"

#: src/ResMgr.cc:686
msgid "Invalid time format. Format is <time><unit>, e.g. 2h30m."
msgstr "Formato temporale non valido. Il formato è <tempo><unità>, es. 2h30m."

#: src/ResMgr.cc:718
msgid "integer overflow"
msgstr ""

#: src/ResMgr.cc:804
msgid "Invalid IPv4 numeric address"
msgstr ""

#: src/ResMgr.cc:814
msgid "Invalid IPv6 numeric address"
msgstr ""

#: src/ResMgr.cc:884 src/ResMgr.cc:888
#, fuzzy
msgid "this encoding is not supported"
msgstr "Operazione non supportata"

#: src/ResMgr.cc:894
msgid "no closure defined for this setting"
msgstr ""

#: src/ResMgr.cc:900
msgid "a closure is required for this setting"
msgstr ""

#: src/Resolver.cc:236
msgid "host name resolve timeout"
msgstr "timeout nella risoluzione del nome dell'host"

#: src/Resolver.cc:282
#, c-format
msgid "%d address$|es$ found"
msgstr ""

#: src/Resolver.cc:327
msgid "Link-local IPv6 address should have a scope"
msgstr ""

#: src/Resolver.cc:765
msgid "DNS resolution not trusted."
msgstr ""

#: src/Resolver.cc:871
msgid "Host name lookup failure"
msgstr ""

#: src/Resolver.cc:903
#, fuzzy, c-format
msgid "no such %s service"
msgstr "variabile inesistente"

#: src/Resolver.cc:930
msgid "No address found"
msgstr ""

#: src/resource.cc:50 src/resource.cc:108
msgid "Proxy protocol unsupported"
msgstr "Protocollo di proxy non supportato"

#: src/resource.cc:54
msgid "ftp:proxy password: "
msgstr ""

#: src/resource.cc:70
#, c-format
msgid "%s must be one of: "
msgstr ""

#: src/resource.cc:72
msgid "must be one of: "
msgstr ""

#: src/resource.cc:84
msgid ", or empty"
msgstr ""

#: src/resource.cc:116
msgid "only PUT and POST values allowed"
msgstr ""

#: src/resource.cc:148
#, c-format
msgid "unknown address family `%s'"
msgstr "famiglia di indirizzi `%s' sconosciuta"

#: src/rmJob.cc:47
#, c-format
msgid "%s ok, `%s' removed\n"
msgstr "%s ok, rimosso `%s'\n"

#: src/rmJob.cc:54
#, fuzzy, c-format
msgid "%s failed for %d of %d file$|s$\n"
msgstr "%s fallito per %d file su %d\n"

#: src/rmJob.cc:60
#, c-format
msgid "%s ok, %d director$y|ies$ removed\n"
msgstr "%s ok, rimosse %d directory$|$\n"

#: src/rmJob.cc:63
#, c-format
msgid "%s ok, %d file$|s$ removed\n"
msgstr "%s ok, rimossi %d file$|$\n"

#: src/SFtp.cc:1099 src/SFtp.cc:1100
#, fuzzy, c-format
msgid "invalid server response format"
msgstr "risposta extra del server"

#: src/SleepJob.cc:99
msgid "Sleeping forever"
msgstr ""

#: src/SleepJob.cc:100
msgid "Sleep time left: "
msgstr ""

#: src/SleepJob.cc:108
#, c-format
msgid "\tRepeat count: %d\n"
msgstr ""

#: src/SleepJob.cc:142
#, fuzzy, c-format
msgid "%s: argument required. "
msgstr "%s: serve un nome di bookmark\n"

#: src/SleepJob.cc:262
#, c-format
msgid "%s: date-time specification missed\n"
msgstr ""

#: src/SleepJob.cc:269
#, c-format
msgid "%s: date-time parse error\n"
msgstr ""

#: src/SleepJob.cc:303
msgid ""
"Usage: sleep <time>[unit]\n"
"Sleep for given amount of time. The time argument can be optionally\n"
"followed by unit specifier: d - days, h - hours, m - minutes, s - seconds.\n"
"By default time is assumed to be seconds.\n"
msgstr ""
"Uso: sleep <tempo>[unità]\\n\"\n"
"Dorme per il tempo specificato.  L'argomento tempo opzionalmente può essere\n"
"seguito da un'unità: d - giorni, h - ore, m - minuti, s - secondi.\n"
"Per default il tempo è assunto essere in secondi.\n"

#: src/SleepJob.cc:310
#, fuzzy
msgid ""
"Repeat specified command with a delay between iterations.\n"
"Default delay is one second, default command is empty.\n"
" -c <count>  maximum number of iterations\n"
" -d <delay>  delay between iterations\n"
" --while-ok  stop when command exits with non-zero code\n"
" --until-ok  stop when command exits with zero code\n"
" --weak      stop when lftp moves to background.\n"
msgstr ""
"Uso: repeat [ritardo] [comando]\n"
"Ripete il comando specificato con un ritardo tra le iterazioni.\n"
"Il ritardo predefinito è di un secondo, mentre il comando predefinito è\n"
"vuoto.\n"

#: src/Speedometer.cc:90
#, c-format
msgid "%.0fb/s"
msgstr ""

#: src/Speedometer.cc:93
#, c-format
msgid "%.1fK/s"
msgstr ""

#: src/Speedometer.cc:96
#, c-format
msgid "%.2fM/s"
msgstr ""

#: src/Speedometer.cc:103
#, c-format
msgid "%.0f B/s"
msgstr ""

#: src/Speedometer.cc:105
#, c-format
msgid "%.1f KiB/s"
msgstr ""

#: src/Speedometer.cc:107
#, c-format
msgid "%.2f MiB/s"
msgstr ""

#: src/Speedometer.cc:129
msgid "eta:"
msgstr ""

#: src/SSH_Access.cc:97
#, fuzzy
msgid "Password required"
msgstr "Password: "

#: src/SSH_Access.cc:102
msgid "Login incorrect"
msgstr ""

#: src/SSH_Access.cc:196
#, fuzzy, c-format
msgid "Disconnecting"
msgstr "Connessione in corso..."

#: src/SysCmdJob.cc:73
#, c-format
msgid "execlp(%s) failed: %s\n"
msgstr "execlp(%s) fallita: %s\n"

#: src/TimeDate.cc:155
msgid "day"
msgstr ""

#: src/TimeDate.cc:156
msgid "hour"
msgstr ""

#: src/TimeDate.cc:157
msgid "minute"
msgstr ""

#: src/TimeDate.cc:158
msgid "second"
msgstr ""

#: src/Torrent.cc:585
msgid "announced via "
msgstr ""

#: src/Torrent.cc:599
#, c-format
msgid "next announce in %s"
msgstr ""

#: src/Torrent.cc:1142
#, c-format
msgid "%d file$|s$ found, now scanning %s"
msgstr ""

#: src/Torrent.cc:1143
#, fuzzy, c-format
msgid "%d file$|s$ found"
msgstr "%s: nessun file trovato\n"

#: src/Torrent.cc:2075 src/Torrent.cc:2085
#, c-format
msgid "Getting meta-data: %s"
msgstr ""

#: src/Torrent.cc:2077
#, c-format
msgid "Validation: %u/%u (%u%%) %s%s"
msgstr ""

#: src/Torrent.cc:2088
#, fuzzy
msgid "Waiting for meta-data..."
msgstr "parse: manca il comando filtro\n"

#: src/Torrent.cc:2096
msgid "Shutting down: "
msgstr ""

#: src/Torrent.cc:3207
#, fuzzy, c-format
msgid "Connecting to peer %s port %u"
msgstr "Connessione del socket dei dati a (%s) porta %u"

#: src/Torrent.cc:3258 src/Torrent.cc:3375
#, fuzzy, c-format
msgid "peer unexpectedly closed connection after %s"
msgstr "connessione chiusa dal sito remoto"

#: src/Torrent.cc:3259 src/Torrent.cc:3376
#, fuzzy
msgid "peer unexpectedly closed connection"
msgstr "connessione chiusa dal sito remoto"

#: src/Torrent.cc:3261 src/Torrent.cc:3262
#, fuzzy, c-format
msgid "peer closed connection (before handshake)"
msgstr "Connessione chiusa dal peer"

#: src/Torrent.cc:3265 src/Torrent.cc:3378 src/Torrent.cc:3379
#, fuzzy, c-format
msgid "invalid peer response format"
msgstr "risposta extra del server"

#: src/Torrent.cc:3360 src/Torrent.cc:3361
#, fuzzy, c-format
msgid "peer closed connection"
msgstr "Connessione chiusa dal peer"

#: src/Torrent.cc:3538
msgid "Handshaking..."
msgstr ""

#: src/Torrent.cc:3798
msgid "Cannot bind a socket for torrent:port-range"
msgstr ""

#: src/Torrent.cc:3858
#, fuzzy, c-format
msgid "Accepted connection from [%s]:%d"
msgstr "Connessione del socket dei dati a (%s) porta %u"

#: src/Torrent.cc:3924
#, c-format
msgid "peer sent unknown info_hash=%s in handshake"
msgstr ""

#: src/Torrent.cc:3948
#, c-format
msgid "peer handshake timeout"
msgstr ""

#: src/Torrent.cc:3960
#, c-format
msgid "peer short handshake"
msgstr ""

#: src/Torrent.cc:3962
#, fuzzy, c-format
msgid "peer closed just accepted connection"
msgstr "Connessione chiusa dal peer"

#: src/Torrent.cc:4013
#, fuzzy, c-format
msgid "Seeding in background...\n"
msgstr "Invio comandi..."

#: src/Torrent.cc:4176
#, c-format
msgid "%s: --share conflicts with --output-directory.\n"
msgstr ""

#: src/Torrent.cc:4180
#, c-format
msgid "%s: --share conflicts with --only-new.\n"
msgstr ""

#: src/Torrent.cc:4184
#, c-format
msgid "%s: --share conflicts with --only-incomplete.\n"
msgstr ""

#: src/Torrent.cc:4226
#, c-format
msgid "%s: Please specify a file or directory to share.\n"
msgstr ""

#: src/Torrent.cc:4228
#, c-format
msgid "%s: Please specify meta-info file or URL.\n"
msgstr ""

#: src/Torrent.cc:4258
msgid ""
"Start BitTorrent job for the given torrent-files, which can be a local "
"file,\n"
"URL, magnet link or plain info_hash written in hex or base32. Local "
"wildcards\n"
"are expanded. Options:\n"
" -O <base>      specifies base directory where files should be placed\n"
" --force-valid  skip file validation\n"
" --dht-bootstrap=<node>  bootstrap DHT by sending a query to the node\n"
" --share        share specified file or directory\n"
msgstr ""

#: src/TorrentTracker.cc:178
msgid "not started"
msgstr ""

#: src/TorrentTracker.cc:181
#, c-format
msgid "next request in %s"
msgstr ""

#: src/TorrentTracker.cc:284 src/TorrentTracker.cc:501
#, c-format
msgid "Received valid info about %d peer$|s$"
msgstr ""

#: src/TorrentTracker.cc:298
#, c-format
msgid "Received valid info about %d IPv6 peer$|s$"
msgstr ""

#, fuzzy
#~ msgid "%s: unrecognized option '--%s'\n"
#~ msgstr "numero non valido"

#~ msgid "Usage: mv <file1> <file2>\n"
#~ msgstr "Uso: mv <file1> <file2>\n"

#, fuzzy
#~ msgid "number"
#~ msgstr "numero non valido"

#, fuzzy
#~ msgid "error: %s:%d\n"
#~ msgstr "Errore fatale: %s"

#, fuzzy
#~ msgid ""
#~ "\n"
#~ "Mirror specified remote directory to local directory\n"
#~ "\n"
#~ " -c, --continue         continue a mirror job if possible\n"
#~ " -e, --delete           delete files not present at remote site\n"
#~ "     --delete-first     delete old files before transferring new ones\n"
#~ " -s, --allow-suid       set suid/sgid bits according to remote site\n"
#~ "     --allow-chown      try to set owner and group on files\n"
#~ "     --ignore-time      ignore time when deciding whether to download\n"
#~ " -n, --only-newer       download only newer files (-c won't work)\n"
#~ " -r, --no-recursion     don't go to subdirectories\n"
#~ " -p, --no-perms         don't set file permissions\n"
#~ "     --no-umask         don't apply umask to file modes\n"
#~ " -R, --reverse          reverse mirror (put files)\n"
#~ " -L, --dereference      download symbolic links as files\n"
#~ " -N, --newer-than=SPEC  download only files newer than specified time\n"
#~ " -P, --parallel[=N]     download N files in parallel\n"
#~ " -i RX, --include RX    include matching files\n"
#~ " -x RX, --exclude RX    exclude matching files\n"
#~ "                        RX is extended regular expression\n"
#~ " -v, --verbose[=N]      verbose operation\n"
#~ "     --log=FILE         write lftp commands being executed to FILE\n"
#~ "     --script=FILE      write lftp commands to FILE, but don't execute "
#~ "them\n"
#~ "     --just-print, --dry-run    same as --script=-\n"
#~ "\n"
#~ "When using -R, the first directory is local and the second is remote.\n"
#~ "If the second directory is omitted, basename of first directory is used.\n"
#~ "If both directories are omitted, current local and remote directories are "
#~ "used.\n"
#~ "See lftp(1) for a complete list of options.\n"
#~ msgstr ""
#~ "\n"
#~ "Fa il mirror della directory remota specificata nella directory locale\n"
#~ "\n"
#~ " -c, --continue         se possibile riprende a fare il mirror\n"
#~ " -e, --delete           cancella i file non presenti sul sito remoto\n"
#~ " -s, --allow-suid       imposta i bit suid/sgid come nel sito remoto\n"
#~ " -n, --only-newer       scarica solo i file più recenti (-c non "
#~ "funziona)\n"
#~ " -r, --no-recursion     non va nelle sottodirectory\n"
#~ " -p, --no-perms         non imposta i permessi sui file\n"
#~ "     --no-umask         non applica l'umask ai permessi dei file\n"
#~ " -R, --reverse          mirror inverso (fa il put dei file)\n"
#~ " -L, --dereference      scarica i link simbolici come file\n"
#~ " -N, --newer-than FILE  scarica solo i file più recenti di FILE\n"
#~ " -i RX, --include RX    include i file corrispondenti (usabile una sola\n"
#~ "                        volta)\n"
#~ " -x RX, --exclude RX    esclude i file corrispondenti (usabile una sola\n"
#~ "                        volta)  RX è un espressione regolare estesa\n"
#~ " -t Nx, --time-prec Nx  imposta la precisione temporale a N secondi "
#~ "(x=s),\n"
#~ "                        minuti (x=m), ore (x=h) o giorni (x=d)\n"
#~ "                        default - impostazione mirror:time-precision\n"
#~ "-T Nx, --loose-time-prec  imposta la precisione temporale per i tempi "
#~ "imprecisi\n"
#~ "                        default - mirror:loose-time-precision\n"
#~ " -v, --verbose          funzionamento prolisso\n"
#~ "     --use-cache        usa gli elenchi di directory in cache\n"
#~ "\n"
#~ "Quando si usa -R, la prima directory è quella locale mentre la seconda è "
#~ "la\n"
#~ "remota. Se è omessa la seconda directory, è usato il nome della prima\n"
#~ "directory. Se sono omesse entrambe le directory, sono usate le directory\n"
#~ "locale e remota correnti.\n"

#, fuzzy
#~ msgid "SITE CHMOD is disabled by ftp:use-site-chmod"
#~ msgstr "SITE CHMOD non è supportato da questo sito"

#, fuzzy
#~ msgid "invalid pair of numbers"
#~ msgstr "numero non valido"

#~ msgid "Saw `unknown', assume failed login"
#~ msgstr "Ottenuto `unknown', assumo login fallita"

#, fuzzy
#~ msgid "block size"
#~ msgstr "numero non valido"

#~ msgid "%sTo be removed: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
#~ msgstr ""
#~ "%sDa cancellare: %d directory$|$, %d file$|$, %d link simbolic$o|i$\n"

#~ msgid "Usage: %s userid [pass]\n"
#~ msgstr "Uso: %s userid [pass]\n"

#~ msgid "Cache is on"
#~ msgstr "La cache è on"

#~ msgid "Cache is off"
#~ msgstr "La cache è off"

#~ msgid "Cache entries do not expire"
#~ msgstr "Le voci della cache non scadono"

#~ msgid "Cache entries expire in %ld $#l#second|seconds$\n"
#~ msgstr "Le voci della cache scadono in %ld $#l#secondo|secondi$\n"

#~ msgid "Cache entries expire in %ld $#l#minute|minutes$\n"
#~ msgstr "Le voci della cache scadono in %ld $#l#minuto|minuti$\n"

#~ msgid ""
#~ "This is free software with ABSOLUTELY NO WARRANTY. See COPYING for "
#~ "details.\n"
#~ msgstr ""
#~ "Questo è un software libero ASSOLUTAMENTE NON GARANTITO. Si veda il file\n"
#~ "COPYNG per i dettagli.\n"

#, fuzzy
#~ msgid "%s: %s: %s\n"
#~ msgstr "Uso: %s\n"

#~ msgid "%s: Invalid number for size\n"
#~ msgstr "%s: Numero per la dimensione non valido\n"

#~ msgid "Warning: getcwd() failed: %s\n"
#~ msgstr "Attenzione: getcwd() fallita: %s\n"

#~ msgid "Usage: %s mode file...\n"
#~ msgstr "Uso: %s permessi file...\n"

#~ msgid "%s: %s - not an octal number\n"
#~ msgstr "%s: %s - non è un numero ottale\n"

#~ msgid "Removing old remote file `%s'"
#~ msgstr "Cancellazione del vecchio file remoto `%s'"

#~ msgid "Retrieving remote file `%s'"
#~ msgstr "Scarico il file remoto `%s'"

#~ msgid "nlist [<args>]"
#~ msgstr "nlist [<arg>]"

#~ msgid "List remote file names\n"
#~ msgstr "Elenca i nomi dei file remoti\n"

#~ msgid "Same as `get -c'\n"
#~ msgstr "Uguale a `get -c'\n"

#~ msgid "rels [<args>]"
#~ msgstr "rels [<arg>]"

#~ msgid "Same as `put -c'\n"
#~ msgstr "Uguale a `put -c'\n"

#, fuzzy
#~ msgid "bzcat <files>"
#~ msgstr "zcat <file>"

#, fuzzy
#~ msgid "bzmore <files>"
#~ msgstr "zmore <file>"

#~ msgid "child returned invalid data"
#~ msgstr "il processo figlio ha restituito dati non validi"

#, fuzzy
#~ msgid ""
#~ "Usage: queue -e|-s <file>|<command>\n"
#~ " -e|--edit    Edit the queue\n"
#~ " -s|--source <file> adds the contents of a file to the end of the queue\n"
#~ "<command>     Add the command to queue for current site. Each site has "
#~ "its\n"
#~ "own command queue. It is possible to queue up a running job by using\n"
#~ "command `queue wait <jobno>'.\n"
#~ msgstr ""
#~ "Uso: queue <comando>\n"
#~ "Aggiunge il comando alla coda per il sito corrente. Ogni sito ha la "
#~ "propria\n"
#~ "coda di comandi. È possibile accodare dopo il job in esecuzione usando\n"

#, fuzzy
#~ msgid "Usage: %s -e|-s|command args...\n"
#~ msgstr "Uso: %s comando arg...\n"

#~ msgid "Saw `Login incorrect', assume failed login"
#~ msgstr "Ottenuto `Login incorrect', assumo login fallita"

#~ msgid "%s: cannot add empty bookmark\n"
#~ msgstr "%s: impossibile aggiungere un bookmark vuoto\n"

#, fuzzy
#~ msgid "%s: cannot get current directory\n"
#~ msgstr "%s: nessun job corrente\n"

#~ msgid "Fatal protocol error occured"
#~ msgstr "Errore fatale di protocollo"

#~ msgid "rmdir <dirs>"
#~ msgstr "rmdir <dir>"

#~ msgid "Usage: %s files...\n"
#~ msgstr "Uso: %s file...\n"
