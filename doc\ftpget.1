.\" ftpget.1
.\"
.\" This file is part of lftp.
.\"
.\" This program is free software; you can redistribute it and/or modify
.\" it under the terms of the GNU General Public License as published by
.\" the Free Software Foundation; either version 2 of the License , or
.\" (at your option) any later version.
.\"
.\" This program is distributed in the hope that it will be useful,
.\" but WITHOUT ANY WARRANTY; without even the implied warranty of
.\" MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
.\" GNU General Public License for more details.
.\"
.\" You should have received a copy of the GNU General Public License
.\" along with this program.  If not, see <http://www.gnu.org/licenses/>.
.\"
.TH ftpget 1 "02 Jun 1999"
.SH NAME
ftpget \- Get files fast from the commandline
.SH SYNTAX
.B ftpget
.RI [ options ] " host filename
.RB [ "\-o"
.IR local "] [" filename "] ..."
.SH VERSION
This man page documents ftpget version 2.0. Note that ftpget is deprecated
and it is recommended to use scripting capabilities of \fBlftp\fP.
.SH "DESCRIPTION"
\fBftpget\fR is a program that gets the given \fIfilename\fR from the
indicated \fIhost\fR.  Multiple filenames can be specified. If a
filename needs to be redirected to another local file then use the
\fB-o\fR option.
.SH OPTIONS
.TP
.I \-p, \-\-port
Set port number
.TP
.I \-u, \-\-user
login as user using pass as password
.TP
.I \-l, \-\-list
Get listing of specified directories
.TP
.I \-c, \-\-continue
Reget specified file(s)
.TP
.I \-q, \-\-quiet
Run quietly (no output).
.TP
.I \-v, \-\-verbose
Run verbosely (lots of output).
.TP
.I \-\-async-mode
Use asynchronous mode (faster)
.TP
.I \-\-sync-mode
Use synchronous mode (compatible with bugs)
.TP
.I \-o file
output to local file 'local' (default \- base name of filename)

.SH SEE ALSO
.BR lftp (1)

.SH AUTHOR
.nf
Alexander V. Lukyanov
<EMAIL>
.fi

.SH ACKNOWLEDGMENTS
This manual page was originally written by Christoph Lameter
<<EMAIL>>, for the Debian GNU/Linux system.
