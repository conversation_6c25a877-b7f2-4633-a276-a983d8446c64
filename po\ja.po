# Japanese Messages for lftp.
# Copyright (C) 2000 Free Software Foundation, Inc.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2000.
#
msgid ""
msgstr ""
"Project-Id-Version: lftp 2.3.0\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2024-11-08 13:05+0300\n"
"PO-Revision-Date: 2002-04-12 00:06+0900\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Japanese <<EMAIL>>\n"
"Language: ja\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8-bit\n"

#: lib/argmatch.c:145
#, fuzzy, c-format
msgid "invalid argument %s for %s"
msgstr "不正な数"

#: lib/argmatch.c:146
#, c-format
msgid "ambiguous argument %s for %s"
msgstr ""

#: lib/argmatch.c:165
msgid "Valid arguments are:"
msgstr ""

#: lib/error.c:208
msgid "Unknown system error"
msgstr ""

#: lib/getopt.c:282
#, c-format
msgid "%s: option '%s%s' is ambiguous\n"
msgstr ""

#: lib/getopt.c:288
#, c-format
msgid "%s: option '%s%s' is ambiguous; possibilities:"
msgstr ""

#: lib/getopt.c:322
#, fuzzy, c-format
msgid "%s: unrecognized option '%s%s'\n"
msgstr "%s: `%s'へのリダイレクションを受け取りました\n"

#: lib/getopt.c:348
#, c-format
msgid "%s: option '%s%s' doesn't allow an argument\n"
msgstr ""

#: lib/getopt.c:363
#, c-format
msgid "%s: option '%s%s' requires an argument\n"
msgstr ""

#: lib/getopt.c:624
#, fuzzy, c-format
msgid "%s: invalid option -- '%c'\n"
msgstr "不正な数"

#: lib/getopt.c:639 lib/getopt.c:685
#, c-format
msgid "%s: option requires an argument -- '%c'\n"
msgstr ""

#. TRANSLATORS:
#. Get translations for open and closing quotation marks.
#. The message catalog should translate "`" to a left
#. quotation mark suitable for the locale, and similarly for
#. "'".  For example, a French Unicode local should translate
#. these to U+00AB (LEFT-POINTING DOUBLE ANGLE
#. QUOTATION MARK), and U+00BB (RIGHT-POINTING DOUBLE ANGLE
#. QUOTATION MARK), respectively.
#.
#. If the catalog has no translation, we will try to
#. use Unicode U+2018 (LEFT SINGLE QUOTATION MARK) and
#. Unicode U+2019 (RIGHT SINGLE QUOTATION MARK).  If the
#. current locale is not Unicode, locale_quoting_style
#. will quote 'like this', and clocale_quoting_style will
#. quote "like this".  You should always include translations
#. for "`" and "'" even if U+2018 and U+2019 are appropriate
#. for your locale.
#.
#. If you don't know what to put here, please see
#. <https://en.wikipedia.org/wiki/Quotation_marks_in_other_languages>
#. and use glyphs suitable for your language.
#: lib/quotearg.c:354
msgid "`"
msgstr ""

#: lib/quotearg.c:355
msgid "'"
msgstr ""

#: lib/xalloc-die.c:34
msgid "memory exhausted"
msgstr ""

#: src/ArgV.cc:107
msgid "option requires an argument"
msgstr ""

#: src/ArgV.cc:109 src/ArgV.cc:118
#, fuzzy
msgid "invalid option"
msgstr "不正な数"

#: src/ArgV.cc:114
#, c-format
msgid "option `%s' requires an argument"
msgstr ""

#: src/ArgV.cc:116
#, fuzzy, c-format
msgid "unrecognized option `%s'"
msgstr "%s: `%s'へのリダイレクションを受け取りました\n"

#: src/attach.h:138
#, fuzzy, c-format
msgid "[%u] Attached to terminal %s. %s\n"
msgstr "[%lu] シグナル %d で中断しました。 %s"

#: src/attach.h:150
#, c-format
msgid "[%u] Attached to terminal.\n"
msgstr ""

#: src/buffer.cc:253 src/FileAccess.cc:866
msgid " [cached]"
msgstr ""

#: src/ChmodJob.cc:80
#, c-format
msgid "Failed to change mode of `%s' to %04o (%s).\n"
msgstr ""

#: src/ChmodJob.cc:83
#, c-format
msgid "Mode of `%s' changed to %04o (%s).\n"
msgstr ""

#: src/ChmodJob.cc:88
#, c-format
msgid "Failed to change mode of `%s' because no old mode is available.\n"
msgstr ""

#: src/CmdExec.cc:105
#, c-format
msgid "Warning: chdir(%s) failed: %s\n"
msgstr "警告: chdir(%s) が失敗しました: %s\n"

#: src/CmdExec.cc:207
#, c-format
msgid "Unknown command `%s'.\n"
msgstr "`%s' は不明なコマンドです。\n"

#: src/CmdExec.cc:209
#, c-format
msgid "Ambiguous command `%s'.\n"
msgstr "`%s' ではコマンドを特定できません。\n"

#: src/CmdExec.cc:228
#, c-format
msgid "Module for command `%s' did not register the command.\n"
msgstr "コマンド `%s' 用のモジュールがコマンドを登録しませんでした。\n"

#: src/CmdExec.cc:384
#, c-format
msgid "cd ok, cwd=%s\n"
msgstr "cd 成功、cwd=%s\n"

#: src/CmdExec.cc:405 src/MirrorJob.cc:736
#, c-format
msgid "%s: received redirection to `%s'\n"
msgstr "%s: `%s'へのリダイレクションを受け取りました\n"

#: src/CmdExec.cc:408 src/FileCopy.cc:1230
msgid "Too many redirections"
msgstr "リダイレクションが多すぎます"

#: src/CmdExec.cc:517 src/CmdExec.cc:574
msgid "Interrupt"
msgstr "中断します"

#: src/CmdExec.cc:639
#, c-format
msgid "Warning: discarding incomplete command\n"
msgstr "警告: 不完全なコマンドを放棄します\n"

#: src/CmdExec.cc:737
#, c-format
msgid "\tExecuting builtin `%s' [%s]\n"
msgstr "\t内蔵された `%s' [%s] を実行します\n"

#: src/CmdExec.cc:742
msgid "Queue is stopped."
msgstr ""

#: src/CmdExec.cc:747
msgid "Now executing:"
msgstr "現在実行中:"

#: src/CmdExec.cc:758
#, c-format
msgid "\tWaiting for job [%d] to terminate\n"
msgstr "\tジョブ [%d] が終了するのを待っています\n"

#: src/CmdExec.cc:761
#, c-format
msgid "\tWaiting for termination of jobs: "
msgstr "\tジョブの終了を待っています: "

#: src/CmdExec.cc:770
msgid "\tRunning\n"
msgstr "\t実行中\n"

#: src/CmdExec.cc:772
msgid "\tWaiting for command\n"
msgstr "\tコマンドを待っています\n"

#: src/CmdExec.cc:1261
#, c-format
msgid "%s: command `%s' is not compiled in.\n"
msgstr "%s: コマンド `%s'はコンパイル時に組み込まれていません。\n"

#: src/CmdExec.cc:1278
#, fuzzy, c-format
msgid "Usage: %s cmd [args...]\n"
msgstr "使い方: %s module [args...]\n"

#: src/CmdExec.cc:1284
#, c-format
msgid "%s: cannot create local session\n"
msgstr ""

#: src/commands.cc:114
#, fuzzy
msgid "!<shell-command>"
msgstr "!<shell_command>"

#: src/commands.cc:115
msgid "Launch shell or shell command\n"
msgstr "シェルあるいはシェルコマンドを実行します\n"

#: src/commands.cc:116
msgid "(commands)"
msgstr "(commands)"

#: src/commands.cc:117
msgid ""
"Group commands together to be executed as one command\n"
"You can launch such a group in background\n"
msgstr ""
"複数のコマンドをまとめて、一つのコマンドとして実行できるようにします\n"
"グループ化されたコマンドはバックグラウンドで実行できます\n"

#: src/commands.cc:120
msgid "alias [<name> [<value>]]"
msgstr "alias [<name> [<value>]]"

#: src/commands.cc:121
msgid ""
"Define or undefine alias <name>. If <value> omitted,\n"
"the alias is undefined, else is takes the value <value>.\n"
"If no argument is given the current aliases are listed.\n"
msgstr ""
"エイリアス <name> を定義または解除します。<value> が省略された場合\n"
"エイリアスは解除され、他の場合には値 <value> が使われます。\n"
"もし引数が指定されなければ、現在のエイリアスが列挙されます。\n"

#: src/commands.cc:125
msgid "anon - login anonymously (by default)\n"
msgstr "anon - 匿名でログインする (デフォルト)\n"

#: src/commands.cc:127
msgid "bookmark [SUBCMD]"
msgstr "bookmark [SUBCMD]"

#: src/commands.cc:128
msgid ""
"bookmark command controls bookmarks\n"
"\n"
"The following subcommands are recognized:\n"
"  add <name> [<loc>] - add current place or given location to bookmarks\n"
"                       and bind to given name\n"
"  del <name>         - remove bookmark with the name\n"
"  edit               - start editor on bookmarks file\n"
"  import <type>      - import foreign bookmarks\n"
"  list               - list bookmarks (default)\n"
msgstr ""
"bookmark コマンドはブックマークを制御します\n"
"\n"
"以下のサブコマンドが利用できます:\n"
"  add <name> [<loc>] - 現在の場所か指定されたロケーションをブックマークに加"
"え\n"
"                       指定の名前と結びつける\n"
"  del <name>         - 指定された名前のブックマークを削除する\n"
"  edit               - ブックマークファイルのエディタで編集する\n"
"  import <type>      - 他形式のブックマークを取り込む\n"
"  list               - ブックマークを列挙する (デフォルト)\n"

#: src/commands.cc:137
msgid "cache [SUBCMD]"
msgstr "cache [SUBCMD]"

#: src/commands.cc:138
#, fuzzy
msgid ""
"cache command controls local memory cache\n"
"\n"
"The following subcommands are recognized:\n"
"  stat        - print cache status (default)\n"
"  on|off      - turn on/off caching\n"
"  flush       - flush cache\n"
"  size <lim>  - set memory limit\n"
"  expire <Nx> - set cache expiration time to N seconds (x=s)\n"
"                minutes (x=m) hours (x=h) or days (x=d)\n"
msgstr ""
"cache コマンドはローカルのメモリキャッシュを制御します\n"
"\n"
"以下のサブコマンドが利用できます:\n"
"  stat        - キャッシュのステータスを表示 (デフォルト)\n"
"  on|off      - キャッシュの有効/無効\n"
"  flush       - キャッシュをフラッシュ\n"
"  size <lim>  - メモリ制限をセット、-1 で無制限\n"
"  expire <Nx> - キャッシュの期限を N 秒 (x=s)、N 分 (x=m)、\n"
"                N 時間 (x=h)、N 日 (x=d) にセット\n"

#: src/commands.cc:146
msgid "cat [-b] <files>"
msgstr "cat [-b] <files>"

#: src/commands.cc:147
msgid ""
"cat - output remote files to stdout (can be redirected)\n"
" -b  use binary mode (ascii is the default)\n"
msgstr ""
"cat - リモートファイルを stdout に出力します(リダイレクト可能)\n"
" -b  バイナリモードを使う (デフォルトはアスキー)\n"

#: src/commands.cc:149
msgid "cd <rdir>"
msgstr "cd <rdir>"

#: src/commands.cc:150
msgid ""
"Change current remote directory to <rdir>. The previous remote directory\n"
"is stored as `-'. You can do `cd -' to change the directory back.\n"
"The previous directory for each site is also stored on disk, so you can\n"
"do `open site; cd -' even after lftp restart.\n"
msgstr ""
"現在のリモートディレクトリを <rdir> に変更します。以前いたリモートディレクト"
"リ\n"
"は `-' として記憶され、`cd -' で以前のディレクトリに戻ることができます。\n"
"以前のディレクトリはそれぞれのサイトごとにディスクに記憶されるので、\n"
"lftp を再起動したあとでも `open site; cd -'とすることが可能です。\n"

#: src/commands.cc:154
#, fuzzy
msgid "chmod [OPTS] mode file..."
msgstr "chmod mode file..."

#: src/commands.cc:155
msgid ""
"Change the mode of each FILE to MODE.\n"
"\n"
" -c, --changes        - like verbose but report only when a change is made\n"
" -f, --quiet          - suppress most error messages\n"
" -v, --verbose        - output a diagnostic for every file processed\n"
" -R, --recursive      - change files and directories recursively\n"
"\n"
"MODE can be an octal number or symbolic mode (see chmod(1))\n"
msgstr ""

#: src/commands.cc:164
msgid ""
"Close idle connections. By default only with current server.\n"
" -a  close idle connections with all servers\n"
msgstr ""
"アイドル状態の接続を閉じます。デフォルトでは現在のサーバにのみ有効です。\n"
" -a  すべてのサーバについてアイドル状態の接続を閉じる\n"

#: src/commands.cc:166
msgid "[re]cls [opts] [path/][pattern]"
msgstr ""

#: src/commands.cc:167
msgid ""
"List remote files. You can redirect output of this command to file\n"
"or via pipe to external command.\n"
"\n"
" -1                   - single-column output\n"
" -a, --all            - show dot files\n"
" -B, --basename       - show basename of files only\n"
"     --block-size=SIZ - use SIZ-byte blocks\n"
" -d, --directory      - list directory entries instead of contents\n"
" -F, --classify       - append indicator (one of /@) to entries\n"
" -h, --human-readable - print sizes in human readable format (e.g., 1K)\n"
"     --si             - likewise, but use powers of 1000 not 1024\n"
" -k, --kilobytes      - like --block-size=1024\n"
" -l, --long           - use a long listing format\n"
" -q, --quiet          - don't show status\n"
" -s, --size           - print size of each file\n"
"     --filesize       - if printing size, only print size for files\n"
" -i, --nocase         - case-insensitive pattern matching\n"
" -I, --sortnocase     - sort names case-insensitively\n"
" -D, --dirsfirst      - list directories first\n"
"     --sort=OPT       - \"name\", \"size\", \"date\"\n"
" -S                   - sort by file size\n"
" --user, --group, --perms, --date, --linkcount, --links\n"
"                      - show individual fields\n"
" --time-style=STYLE   - use specified time format\n"
"\n"
"By default, cls output is cached, to see new listing use `recls' or\n"
"`cache flush'.\n"
"\n"
"The variables cls-default and cls-completion-default can be used to\n"
"specify defaults for cls listings and completion listings, respectively.\n"
"For example, to make completion listings show file sizes, set\n"
"cls-completion-default to \"-s\".\n"
"\n"
"Tips: Use --filesize with -D to pack the listing better.  If you don't\n"
"always want to see file sizes, --filesize in cls-default will affect the\n"
"-s flag on the commandline as well.  Add `-i' to cls-completion-default\n"
"to make filename completion case-insensitive.\n"
msgstr ""

#: src/commands.cc:217
#, fuzzy
msgid "debug [OPTS] [<level>|off]"
msgstr "debug [<level>|off] [-o <file>]"

#: src/commands.cc:218
#, fuzzy
msgid ""
"Set debug level to given value or turn debug off completely.\n"
" -o <file>  redirect debug output to the file\n"
" -c  show message context\n"
" -p  show PID\n"
" -t  show timestamps\n"
msgstr ""
"デバッグレベルを指定の値にセットするか、デバッグを完全にオフにします。\n"
" -o <file> デバッグ出力を指定のファイルにリダイレクトする。\n"

#: src/commands.cc:223
#, fuzzy
msgid "du [options] <dirs>"
msgstr "mkdir [-p] <dirs>"

#: src/commands.cc:224
msgid ""
"Summarize disk usage.\n"
" -a, --all             write counts for all files, not just directories\n"
"     --block-size=SIZ  use SIZ-byte blocks\n"
" -b, --bytes           print size in bytes\n"
" -c, --total           produce a grand total\n"
" -d, --max-depth=N     print the total for a directory (or file, with --"
"all)\n"
"                       only if it is N or fewer levels below the command\n"
"                       line argument;  --max-depth=0 is the same as\n"
"                       --summarize\n"
" -F, --files           print number of files instead of sizes\n"
" -h, --human-readable  print sizes in human readable format (e.g., 1K 234M "
"2G)\n"
" -H, --si              likewise, but use powers of 1000 not 1024\n"
" -k, --kilobytes       like --block-size=1024\n"
" -m, --megabytes       like --block-size=1048576\n"
" -S, --separate-dirs   do not include size of subdirectories\n"
" -s, --summarize       display only a total for each argument\n"
"     --exclude=PAT     exclude files that match PAT\n"
msgstr ""

#: src/commands.cc:242
#, fuzzy
msgid "edit [OPTS] <file>"
msgstr "mget [OPTS] <files>"

#: src/commands.cc:243
msgid ""
"Retrieve remote file to a temporary location, run a local editor on it\n"
"and upload the file back if changed.\n"
" -k  keep the temporary file\n"
" -o <temp>  explicit temporary file location\n"
msgstr ""

#: src/commands.cc:248
msgid "exit [<code>|bg]"
msgstr "exit [<code>|bg]"

#: src/commands.cc:249
msgid ""
"exit - exit from lftp or move to background if jobs are active\n"
"\n"
"If no jobs active, the code is passed to operating system as lftp\n"
"termination status. If omitted, exit code of last command is used.\n"
"`bg' forces moving to background if cmd:move-background is false.\n"
msgstr ""
"exit - lftp を終了させるか、ジョブがアクティブならばそれらをバックグラウンド"
"に移行させます\n"
"\n"
"もしアクティブなジョブが無ければ、lftp の終了ステータスとして指定のコードが\n"
"オペレーティングシステムに渡されます。省略された場合、最後のコマンドの終了"
"コードが使われます。\n"
"`bg'を指定すると、cmd:move-background が偽の時でも強制的にバックグラウンドに"
"移行させます。\n"

#: src/commands.cc:255
#, fuzzy
msgid ""
"Usage: find [OPTS] [directory]\n"
"Print contents of specified directory or current directory recursively.\n"
"Directories in the list are marked with trailing slash.\n"
"You can redirect output of this command.\n"
" -d, --maxdepth=LEVELS  Descend at most LEVELS of directories.\n"
msgstr ""
"使い方: find [OPTS] [directory]\n"
"現在のディレクトリ内にある指定のディレクトリの内容を再帰的に表示します。\n"
"リストにあるディレクトリには後ろにスラッシュが付けられて表示されます。\n"
"このコマンドの出力をリダイレクトすることが可能です。\n"
" -d, --maxdepth=LEVELS 最深で深さLEVELSのディレクトリまで降下します。\n"

#: src/commands.cc:260
msgid "get [OPTS] <rfile> [-o <lfile>]"
msgstr "get [OPTS] <rfile> [-o <lfile>]"

#: src/commands.cc:261
#, fuzzy
msgid ""
"Retrieve remote file <rfile> and store it to local file <lfile>.\n"
" -o <lfile> specifies local file name (default - basename of rfile)\n"
" -c  continue, resume transfer\n"
" -E  delete remote files after successful transfer\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"リモートファイル <rfile> を取得し、それをローカルファイル <lfile> に保存しま"
"す。\n"
" -o <lfile> ローカルでのファイル名を指定 (デフォルト - rfile のベースネー"
"ム)\n"
" -c 継続してダウンロード、reget と同じ\n"
" -E 転送が成功したらリモートファイルを削除する\n"
" -a アスキーモードを使う (デフォルトではバイナリ)\n"
" -O <base> ファイルが置かれるベースディレクトリか URL を指定\n"

#: src/commands.cc:268
msgid "glob [OPTS] <cmd> <args>"
msgstr ""

#: src/commands.cc:270
#, fuzzy
msgid ""
"Expand wildcards and run specified command.\n"
"Options can be used to expand wildcards to list of files, directories,\n"
"or both types. Type selection is not very reliable and depends on server.\n"
"If entry type cannot be determined, it will be included in the list.\n"
" -f  plain files (default)\n"
" -d  directories\n"
" -a  all types\n"
" --exist      return zero exit code when the patterns expand to non-empty "
"list\n"
" --not-exist  return zero exit code when the patterns expand to an empty "
"list\n"
msgstr ""
"使い方: glob [OPTS] command args...\n"
"ワイルドカードを展開して指定のコマンドを実行します。\n"
"ファイル、ディレクトリ、あるいは両方のどのタイプについてワイルドカードを展開"
"するか\n"
"オプションで指定できます。タイプ選択はそれほど信頼できるものではなく、サーバ"
"に依ります。\n"
"そのエントリのタイプが決定できない場合には、リストに含められます。\n"
" -f プレーンファイル (デフォルト)\n"
" -d ディレクトリ\n"
" -a 全タイプ\n"

#: src/commands.cc:279
msgid "help [<cmd>]"
msgstr "help [<cmd>]"

#: src/commands.cc:280
msgid "Print help for command <cmd>, or list of available commands\n"
msgstr ""
"コマンド <cmd> のヘルプを表示するか、利用可能なコマンドの一覧を表示します\n"

#: src/commands.cc:282
#, fuzzy
msgid ""
"List running jobs. -v means verbose, several -v can be specified.\n"
"If <job_no> is specified, only list a job with that number.\n"
msgstr ""
"実行中のジョブを列挙します。-v は冗長を意味し、-v をいくつか指定することもで"
"きます。\n"

#: src/commands.cc:284
msgid "kill all|<job_no>"
msgstr "kill all|<job_no>"

#: src/commands.cc:285
msgid "Delete specified job with <job_no> or all jobs\n"
msgstr "<job_no> で指定されたジョブかすべてのジョブを削除します\n"

#: src/commands.cc:286
msgid "lcd <ldir>"
msgstr "lcd <ldir>"

#: src/commands.cc:287
msgid ""
"Change current local directory to <ldir>. The previous local directory\n"
"is stored as `-'. You can do `lcd -' to change the directory back.\n"
msgstr ""
"現在のローカルディレクトリを <ldir> に変更します。以前いたローカルディレクト"
"リ\n"
"は `-' として保存されますので、`lcd -' とすれば元いたディレクトリに戻ることが"
"できます\n"

#: src/commands.cc:289
msgid "lftp [OPTS] <site>"
msgstr "lftp [OPTS] <site>"

#: src/commands.cc:290
#, fuzzy
msgid ""
"`lftp' is the first command executed by lftp after rc files\n"
" -f <file>           execute commands from the file and exit\n"
" -c <cmd>            execute the commands and exit\n"
" --norc              don't execute rc files from the home directory\n"
" --help              print this help and exit\n"
" --version           print lftp version and exit\n"
"Other options are the same as in `open' command:\n"
" -e <cmd>            execute the command just after selecting\n"
" -u <user>[,<pass>]  use the user/password for authentication\n"
" -p <port>           use the port for connection\n"
" -s <slot>           assign the connection to this slot\n"
" -d                  switch on debugging mode\n"
" <site>              host name, URL or bookmark name\n"
msgstr ""
"`lftp' は rc ファイルを読み込んだあと最初に実行されるコマンドです\n"
" -f <file>           ファイルで指定されたコマンドを実行し終了する\n"
" -c <cmd>            コマンドを実行して終了する\n"
" --help              このヘルプを表示して終了する\n"
" --version           lftp のバージョンを表示して終了する\n"
"他のオプションは `open' コマンドのものと同じです\n"
" -e <cmd>            選択のすぐあとに指定のコマンドを実行する\n"
" -u <user>[,<pass>]  認証で指定のユーザ/パスワードを使う\n"
" -p <port>           接続に指定のポートを使う\n"
" <site>              ホスト名、URL あるいはブックマーク名\n"

#: src/commands.cc:303
#, fuzzy
msgid "ln [-s] <file1> <file2>"
msgstr "mv <file1> <file2>"

#: src/commands.cc:304
#, fuzzy
msgid "Link <file1> to <file2>\n"
msgstr "<file1> を <file2> にリネームします\n"

#: src/commands.cc:308
msgid "ls [<args>]"
msgstr "ls [<args>]"

#: src/commands.cc:309
#, fuzzy
msgid ""
"List remote files. You can redirect output of this command to file\n"
"or via pipe to external command.\n"
"By default, ls output is cached, to see new listing use `rels' or\n"
"`cache flush'.\n"
"See also `help cls'.\n"
msgstr ""
"リモートファイルの一覧を表示します。このコマンドの出力はファイル、あるいは\n"
"パイプを通すことで外部コマンドにリダイレクトすることができます。\n"
" デフォルトでは ls 出力はキャッシュされますので、新しい一覧を見るには\n"
"`rels' を使うか `cache flush' を実行してください。\n"

#: src/commands.cc:314
msgid "mget [OPTS] <files>"
msgstr "mget [OPTS] <files>"

#: src/commands.cc:315
#, fuzzy
msgid ""
"Gets selected files with expanded wildcards\n"
" -c  continue, resume transfer\n"
" -d  create directories the same as in file names and get the\n"
"     files into them instead of current directory\n"
" -E  delete remote files after successful transfer\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"ワイルドカード展開によって選択された複数のファイルを取得します\n"
" -c  継続してダウンロード、reget と同じ\n"
" -d  ファイル名と同じ名前のディレクトリを作成して、取得したファイルを\n"
"     現在のディレクトリではなくそこに送る\n"
" -E  転送が成功したらリモートファイルを削除する\n"
" -a  アスキーモードを使う (デフォルトではバイナリ)\n"
" -O <base> ファイルが置かれるベースディレクトリか URL を指定する\n"

#: src/commands.cc:322
msgid "mirror [OPTS] [remote [local]]"
msgstr "mirror [OPTS] [remote [local]]"

#: src/commands.cc:323
#, fuzzy
msgid "mkdir [OPTS] <dirs>"
msgstr "mkdir [-p] <dirs>"

#: src/commands.cc:324
#, fuzzy
msgid ""
"Make remote directories\n"
" -p  make all levels of path\n"
" -f  be quiet, suppress messages\n"
msgstr ""
"リモートディレクトリを作成します\n"
" -p  パスの全レベルを作成する\n"

#: src/commands.cc:327
msgid "module name [args]"
msgstr "module name [args]"

#: src/commands.cc:328
msgid ""
"Load module (shared object). The module should contain function\n"
"   void module_init(int argc,const char *const *argv)\n"
"If name contains a slash, then the module is searched in current\n"
"directory, otherwise in directories specified by setting module:path.\n"
msgstr ""
"モジュール (共有オブジェクト) を読み込みます。モジュールには\n"
"   void module_init(int argc,const char *const *argv)\n"
"という関数が含まれている必要があります。名前にスラッシュが含まれていた場"
"合、\n"
"現在のディレクトリから探します。そうでなければ module:path で設定されたディレ"
"クトリから探します。\n"

#: src/commands.cc:332
msgid "more <files>"
msgstr "more <files>"

#: src/commands.cc:333
msgid "Same as `cat <files> | more'. if PAGER is set, it is used as filter\n"
msgstr ""
"`cat <files> | more' と同じです。もし PAGER がセットされていれば、それをフィ"
"ルタとして使います\n"

#: src/commands.cc:334
msgid "mput [OPTS] <files>"
msgstr "mput [OPTS] <files>"

#: src/commands.cc:335
msgid ""
"Upload files with wildcard expansion\n"
" -c  continue, reput\n"
" -d  create directories the same as in file names and put the\n"
"     files into them instead of current directory\n"
" -E  delete local files after successful transfer (dangerous)\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"ワイルドカード展開で指定された複数のファイルをアップロードします\n"
" -c  継続してアップロード、reput と同じ\n"
" -d  ファイル名と同じ名前のディレクトリを作成し、ファイルを現在の\n"
"     ディレクトリではなくそのディレクトリにアップロードする\n"
" -E  転送が成功したらローカルファイルを削除する (危険)\n"
" -a  アスキーモードを使う (デフォルトはバイナリ)\n"
" -O <base> ファイルが置かれるベースディレクトリか URL を指定\n"

#: src/commands.cc:342
msgid "mrm <files>"
msgstr "mrm <files>"

#: src/commands.cc:343
msgid "Removes specified files with wildcard expansion\n"
msgstr "ワイルドカード展開で指定されたファイルを削除します\n"

#: src/commands.cc:344
msgid "mv <file1> <file2>"
msgstr "mv <file1> <file2>"

#: src/commands.cc:345
msgid "Rename <file1> to <file2>\n"
msgstr "<file1> を <file2> にリネームします\n"

#: src/commands.cc:346
#, fuzzy
msgid "mmv [OPTS] <files> <target-dir>"
msgstr "mget [OPTS] <files>"

#: src/commands.cc:347
msgid ""
"Move <files> to <target-directory> with wildcard expansion\n"
" -O <dir>  specifies the target directory (alternative way)\n"
msgstr ""

#: src/commands.cc:349
#, fuzzy
msgid "[re]nlist [<args>]"
msgstr "renlist [<args>]"

#: src/commands.cc:350
#, fuzzy
msgid ""
"List remote file names.\n"
"By default, nlist output is cached, to see new listing use `renlist' or\n"
"`cache flush'.\n"
msgstr ""
"リモートファイルの一覧を表示します。このコマンドの出力はファイル、あるいは\n"
"パイプを通すことで外部コマンドにリダイレクトすることができます。\n"
" デフォルトでは ls 出力はキャッシュされますので、新しい一覧を見るには\n"
"`rels' を使うか `cache flush' を実行してください。\n"

#: src/commands.cc:353
msgid "open [OPTS] <site>"
msgstr "open [OPTS] <site>"

#: src/commands.cc:354
#, fuzzy
msgid ""
"Select a server, URL or bookmark\n"
" -e <cmd>            execute the command just after selecting\n"
" -u <user>[,<pass>]  use the user/password for authentication\n"
" -p <port>           use the port for connection\n"
" -s <slot>           assign the connection to this slot\n"
" -d                  switch on debugging mode\n"
" <site>              host name, URL or bookmark name\n"
msgstr ""
"サーバ、URL、ブックマークを選択する\n"
" -e <cmd>            選択のすぐあとに指定のコマンドを実行する\n"
" -u <user>[,<pass>]  認証に指定のユーザ/パスワードを使う\n"
" -p <port>           接続に指定のポートを使う\n"
" <site>              ホスト名、URL、ブックマーク名\n"

#: src/commands.cc:361
msgid "pget [OPTS] <rfile> [-o <lfile>]"
msgstr "pget [OPTS] <rfile> [-o <lfile>]"

#: src/commands.cc:362
#, fuzzy
msgid ""
"Gets the specified file using several connections. This can speed up "
"transfer,\n"
"but loads the net heavily impacting other users. Use only if you really\n"
"have to transfer the file ASAP.\n"
"\n"
"Options:\n"
" -c  continue transfer. Requires <lfile>.lftp-pget-status file.\n"
" -n <maxconn>  set maximum number of connections (default is is taken from\n"
"     pget:default-n setting)\n"
" -O <base> specifies base directory where files should be placed\n"
msgstr ""
"指定されたファイルを複数の接続を使って取得します。スピードアップが図れます"
"が、\n"
"ネットに大きな負荷をかけ、他のユーザに迷惑をかけます。そのファイルを本当に\n"
"一刻も早く転送しなければならない場合のみ使ってください。さもないと\n"
"他のユーザがぶちきれるかもしれません :)\n"
"\n"
"オプション:\n"
" -n <maxconn>  接続の最大数をセット (デフォルトは 5)\n"

#: src/commands.cc:370
msgid "put [OPTS] <lfile> [-o <rfile>]"
msgstr "put [OPTS] <lfile> [-o <rfile>]"

#: src/commands.cc:371
msgid ""
"Upload <lfile> with remote name <rfile>.\n"
" -o <rfile> specifies remote file name (default - basename of lfile)\n"
" -c  continue, reput\n"
"     it requires permission to overwrite remote files\n"
" -E  delete local files after successful transfer (dangerous)\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"<lfile> をリモートでの名前 <rfile> としてアップロードします。\n"
" -o <rfile> リモートでのファイル名を指定 (デフォルト - lfile のベースネー"
"ム)\n"
" -c 継続してアップロード、reput と同じ\n"
"    リモートファイルを上書きする権限が必要です\n"
" -E 転送が成功したらローカルファイルを削除する (危険)\n"
" -a アスキーモードを使う (デフォルトはバイナリ)\n"
" -O <base> ファイルが置かれるベースディレクトリか URL を指定\n"

#: src/commands.cc:379
msgid ""
"Print current remote URL.\n"
" -p  show password\n"
msgstr ""
"現在のリモート URL を表示します。\n"
" -p パスワードを見せる\n"

#: src/commands.cc:381
msgid "queue [OPTS] [<cmd>]"
msgstr ""

#: src/commands.cc:382
#, fuzzy
msgid ""
"\n"
"       queue [-n num] <command>\n"
"\n"
"Add the command to queue for current site. Each site has its own command\n"
"queue. `-n' adds the command before the given item in the queue. It is\n"
"possible to queue up a running job by using command `queue wait <jobno>'.\n"
"\n"
"       queue --delete|-d [index or wildcard expression]\n"
"\n"
"Delete one or more items from the queue. If no argument is given, the last\n"
"entry in the queue is deleted.\n"
"\n"
"       queue --move|-m <index or wildcard expression> [index]\n"
"\n"
"Move the given items before the given queue index, or to the end if no\n"
"destination is given.\n"
"\n"
"Options:\n"
" -q                  Be quiet.\n"
" -v                  Be verbose.\n"
" -Q                  Output in a format that can be used to re-queue.\n"
"                     Useful with --delete.\n"
msgstr ""
"使い方:\n"
"       queue [-n num] <command>\n"
"\n"
"コマンドを現在のサイトのキューに追加します。サイトはそれぞれ自分のコマンド"
"キューを持っています。\n"
"`-n' はコマンドをキューの指定アイテムの前に追加します。キューを実行中のジョブ"
"の後に実行させるには\n"
"コマンド `queue wait <jobno>' を使います。\n"
"\n"
"       queue --delete|-d [index or wildcard expression]\n"
"\n"
"キューからアイテムを削除します。引数が指定されなかった場合、\n"
"キューの最後のエントリを削除します。\n"
"\n"
"       queue --move|-m <index or wildcard expression> [index]\n"
"\n"
"指定のアイテムを指定のキューインデックスの前に移動します。\n"
"移動先が指定されていなかった場合最後に移動します。\n"

#: src/commands.cc:403
msgid "quote <cmd>"
msgstr "quote <cmd>"

#: src/commands.cc:404
msgid ""
"Send the command uninterpreted. Use with caution - it can lead to\n"
"unknown remote state and thus will cause reconnect. You cannot\n"
"be sure that any change of remote state because of quoted command\n"
"is solid - it can be reset by reconnect at any time.\n"
msgstr ""
"コマンドを解釈せずに送信します。注意して使ってください - このコマンドによっ"
"て\n"
"リモートの状態が不明になり、再接続を生じることがあるかもしれません。クォート"
"されたコマンドによる\n"
"リモート状態の変更が信頼できるとは限りません - いつ再接続によってリセットされ"
"るか分からないのです。\n"

#: src/commands.cc:409
#, fuzzy
msgid ""
"recls [<args>]\n"
"Same as `cls', but don't look in cache\n"
msgstr "`ls' と同じですが、キャッシュを参照しません\n"

#: src/commands.cc:412
#, fuzzy
msgid ""
"Usage: reget [OPTS] <rfile> [-o <lfile>]\n"
"Same as `get -c'\n"
msgstr "reget [OPTS] <rfile> [-o <lfile>]"

#: src/commands.cc:415
#, fuzzy
msgid ""
"Usage: rels [<args>]\n"
"Same as `ls', but don't look in cache\n"
msgstr "`ls' と同じですが、キャッシュを参照しません\n"

#: src/commands.cc:418
#, fuzzy
msgid ""
"Usage: renlist [<args>]\n"
"Same as `nlist', but don't look in cache\n"
msgstr "`nlist' と同じですが、キャッシュを参照しません\n"

#: src/commands.cc:420
msgid "repeat [OPTS] [delay] [command]"
msgstr ""

#: src/commands.cc:422
#, fuzzy
msgid ""
"Usage: reput <lfile> [-o <rfile>]\n"
"Same as `put -c'\n"
msgstr "reput <lfile> [-o <rfile>]"

#: src/commands.cc:424
msgid "rm [-r] [-f] <files>"
msgstr "rm [-r] [-f] <files>"

#: src/commands.cc:425
msgid ""
"Remove remote files\n"
" -r  recursive directory removal, be careful\n"
" -f  work quietly\n"
msgstr ""
"リモートファイルを削除します\n"
" -r  再帰的なディレクトリ削除、使用注意\n"
" -f  静かに作業\n"

#: src/commands.cc:428
msgid "rmdir [-f] <dirs>"
msgstr "rmdir [-f] <dirs>"

#: src/commands.cc:429
msgid "Remove remote directories\n"
msgstr "リモートディレクトリを削除します\n"

#: src/commands.cc:430
msgid "scache [<session_no>]"
msgstr "scache [<session_no>]"

#: src/commands.cc:431
msgid "List cached sessions or switch to specified session number\n"
msgstr ""
"キャッシュされたセッションを列挙するか、指定されたセッション番号に切り替えま"
"す\n"

#: src/commands.cc:432
msgid "set [OPT] [<var> [<val>]]"
msgstr "set [OPT] [<var> [<var>]]"

#: src/commands.cc:433
msgid ""
"Set variable to given value. If the value is omitted, unset the variable.\n"
"Variable name has format ``name/closure'', where closure can specify\n"
"exact application of the setting. See lftp(1) for details.\n"
"If set is called with no variable then only altered settings are listed.\n"
"It can be changed by options:\n"
" -a  list all settings, including default values\n"
" -d  list only default values, not necessary current ones\n"
msgstr ""
"変数を与えられた値にセットします。値が省かれた場合、変数をアンセットしま"
"す。\n"
"変数名は ``名前/クロージャ'' というフォーマットであり、クロージャで設定の\n"
"正確なアプリケーションを指定できます。詳しくは lftp(1) を参照してください。\n"
"変数の指定無く set が呼ばれた場合、変更された設定のみ列挙されます。\n"
"この動作はオプションで変更可能です:\n"
" -a すべての設定を列挙、デフォルト値も含む\n"
" -d デフォルト値のみ列挙、現在のものであるとは限らない\n"

#: src/commands.cc:441
#, fuzzy
msgid "site <site-cmd>"
msgstr "site <site_cmd>"

#: src/commands.cc:442
msgid ""
"Execute site command <site_cmd> and output the result\n"
"You can redirect its output\n"
msgstr ""
"サイトのコマンド <site_cmd> を実行し、結果を出力します\n"
"出力はリダイレクトできます\n"

#: src/commands.cc:446
msgid ""
"Usage: slot [<label>]\n"
"List assigned slots.\n"
"If <label> is specified, switch to the slot named <label>.\n"
msgstr ""

#: src/commands.cc:449
msgid "source <file>"
msgstr "source <file>"

#: src/commands.cc:450
msgid "Execute commands recorded in file <file>\n"
msgstr "ファイル <file> に記録されたコマンドを実行します\n"

#: src/commands.cc:452
#, fuzzy
msgid "torrent [OPTS] <file|URL>..."
msgstr "mget [OPTS] <files>"

#: src/commands.cc:453
msgid "user <user|URL> [<pass>]"
msgstr "user <user|URL> [<pass>]"

#: src/commands.cc:454
msgid ""
"Use specified info for remote login. If you specify URL, the password\n"
"will be cached for future usage.\n"
msgstr ""
"リモートログインの際指定された情報を使います。URL を指定した場合、\n"
"パスワードは将来の利用のためにキャッシュされます。\n"

#: src/commands.cc:457
msgid "Shows lftp version\n"
msgstr "lftp のバージョンを表示します\n"

#: src/commands.cc:458
msgid "wait [<jobno>]"
msgstr "wait [<jobno>]"

#: src/commands.cc:459
msgid ""
"Wait for specified job to terminate. If jobno is omitted, wait\n"
"for last backgrounded job.\n"
msgstr ""
"指定されたジョブが終了するまで待ちます。jobno が省略された場合、\n"
"最後のバックグラウンド化されたジョブを待ちます。\n"

#: src/commands.cc:461
msgid "zcat <files>"
msgstr "zcat <files>"

#: src/commands.cc:462
msgid "Same as cat, but filter each file through zcat\n"
msgstr "cat と同じですが、それぞれのファイルを zcat にかけます\n"

#: src/commands.cc:463
msgid "zmore <files>"
msgstr "zmore <files>"

#: src/commands.cc:464
msgid "Same as more, but filter each file through zcat\n"
msgstr "more と同じですが、それぞれのファイルを zcat にかけます\n"

#: src/commands.cc:466
msgid "Same as cat, but filter each file through bzcat\n"
msgstr "cat と同じですが、それぞれのファイルを bzcat にかけます\n"

#: src/commands.cc:468
msgid "Same as more, but filter each file through bzcat\n"
msgstr "more と同じですが、それぞれのファイルを bzcat にかけます\n"

#: src/commands.cc:524
#, c-format
msgid "Usage: %s local-dir\n"
msgstr "使い方: %s local-dir\n"

#: src/commands.cc:560
#, c-format
msgid "lcd ok, local cwd=%s\n"
msgstr "lcd 成功、ローカルの cwd=%s\n"

#: src/commands.cc:576
#, c-format
msgid "Usage: cd remote-dir\n"
msgstr "使い方: cd remote-dir\n"

#: src/commands.cc:588
#, c-format
msgid "%s: no old directory for this site\n"
msgstr "%s: このサイトの古いディレクトリはありません\n"

#: src/commands.cc:677
#, c-format
msgid "Usage: %s [<exit_code>]\n"
msgstr "使い方: %s [<exit_code>]\n"

#: src/commands.cc:686
msgid ""
"There are running jobs and `cmd:move-background' is not set.\n"
"Use `exit bg' to force moving to background or `kill all' to terminate "
"jobs.\n"
msgstr ""
"実行中のジョブがあるのに、`cmd:move-background' がセットされていません。\n"
"`exit bg' を使って強制的にバックグラウンドに移行させるか、`kill all'でジョブ"
"を終了させてください。\n"

#: src/commands.cc:703
msgid ""
"\n"
"lftp now tricks the shell to move it to background process group.\n"
"lftp continues to run in the background despite the `Stopped' message.\n"
"lftp will automatically terminate when all jobs are finished.\n"
"Use `fg' shell command to return to lftp if it is still running.\n"
msgstr ""

#: src/commands.cc:837 src/commands.cc:3616
#, c-format
msgid "Try `%s --help' for more information\n"
msgstr "`%s --help' でより詳しい情報が得られます\n"

#: src/commands.cc:856
#, c-format
msgid "%s: -c, -f, -v, -h conflict with other `open' options and arguments\n"
msgstr ""

#: src/commands.cc:956
#, c-format
msgid "Usage: %s [-e cmd] [-p port] [-u user[,pass]] <host|url>\n"
msgstr "使い方: %s [-e cmd] [-p port] [-u user[,pass]] <host|url>\n"

#: src/commands.cc:1046 src/commands.cc:2276 src/DummyProto.cc:65
#: src/MirrorJob.cc:2172 src/MirrorJob.cc:2194
msgid " - not supported protocol"
msgstr " - はサポートされていないプロトコルです"

#: src/commands.cc:1078 src/commands.cc:2260
msgid "Password: "
msgstr "パスワード: "

#: src/commands.cc:1080
#, c-format
msgid "%s: GetPass() failed -- assume anonymous login\n"
msgstr "%s: GetPass() が失敗しました -- 匿名ログインと仮定します\n"

#: src/commands.cc:1191 src/commands.cc:1284 src/commands.cc:1660
#: src/commands.cc:1691 src/commands.cc:1829 src/commands.cc:1914
#: src/commands.cc:2187 src/commands.cc:2381 src/commands.cc:2543
#: src/commands.cc:2588 src/commands.cc:2616 src/commands.cc:2623
#: src/commands.cc:2930 src/commands.cc:2957 src/commands.cc:2964
#: src/commands.cc:3293 src/lftp.cc:267 src/MirrorJob.cc:2136
#: src/SleepJob.cc:144 src/SleepJob.cc:200 src/Torrent.cc:4169
#, c-format
msgid "Try `help %s' for more information.\n"
msgstr "`help %s' でより詳しい情報が得られます。\n"

#: src/commands.cc:1201
#, c-format
msgid "Usage: %s [OPTS] command args...\n"
msgstr "使い方: %s [OPTS] command args...\n"

#: src/commands.cc:1254
#, fuzzy, c-format
msgid "%s: -n: positive number expected. "
msgstr "%s: -n: 数字が必要です。"

#: src/commands.cc:1306
#, c-format
msgid "Created a stopped queue.\n"
msgstr ""

#: src/commands.cc:1349 src/commands.cc:1377
#, c-format
msgid "%s: No queue is active.\n"
msgstr "%s: アクティブなキューはありません。\n"

#: src/commands.cc:1369
#, c-format
msgid "%s: -m: Number expected as second argument. "
msgstr "%s: -m: 第二の引数として数字が必要です。"

#: src/commands.cc:1421
#, c-format
msgid "Usage: %s <cmd>\n"
msgstr "使い方: %s <cmd>\n"

#: src/commands.cc:1527
msgid "invalid argument for `--sort'"
msgstr ""

#: src/commands.cc:1557
#, fuzzy
msgid "invalid block size"
msgstr "不正な数"

#: src/commands.cc:1700
#, c-format
msgid "Usage: %s [OPTS] files...\n"
msgstr "使い方: %s [OPTS] files...\n"

#: src/commands.cc:1785 src/commands.cc:1814
#, fuzzy, c-format
msgid "%s: %s: Number expected. "
msgstr "%s: -n: 数字が必要です。"

#: src/commands.cc:1834
#, c-format
msgid "%s: --continue conflicts with --remove-target.\n"
msgstr ""

#: src/commands.cc:1844 src/commands.cc:1920
#, c-format
msgid "File name missed. "
msgstr "ファイル名がありません。"

#: src/commands.cc:1989
#, fuzzy, c-format
msgid "Usage: %s %s[-f] files...\n"
msgstr "使い方: %s [-r] [-f] files...\n"

#: src/commands.cc:2032
#, fuzzy, c-format
msgid "Usage: %s [-e] <file|command>\n"
msgstr "使い方: %s <file>\n"

#: src/commands.cc:2079
#, c-format
msgid "Usage: %s [-v] [-v] ...\n"
msgstr "使い方: %s [-v] [-v] ...\n"

#: src/commands.cc:2093 src/commands.cc:2347 src/commands.cc:2469
#: src/commands.cc:2685 src/commands.cc:3101 src/commands.cc:3182
#: src/lftp.cc:279
#, c-format
msgid "%s: %s - not a number\n"
msgstr "%s: %s - 数ではありません\n"

#: src/commands.cc:2100 src/commands.cc:2326 src/commands.cc:2356
#: src/commands.cc:2487
#, c-format
msgid "%s: %d - no such job\n"
msgstr "%s: %d - そのようなジョブはありません\n"

#: src/commands.cc:2135
#, c-format
msgid "Usage: %s [-p]\n"
msgstr "使い方: %s [-p]\n"

#: src/commands.cc:2227
#, c-format
msgid "debug level is %d, output goes to %s\n"
msgstr "デバッグレベルは %d、出力は %s へ\n"

#: src/commands.cc:2230
#, c-format
msgid "debug is off\n"
msgstr "デバッグはオフ\n"

#: src/commands.cc:2241
#, fuzzy, c-format
msgid "Usage: %s <user|URL> [<pass>]\n"
msgstr "user <user|URL> [<pass>]"

#: src/commands.cc:2316 src/commands.cc:2479
#, c-format
msgid "%s: no current job\n"
msgstr "%s: 現在ジョブはありません\n"

#: src/commands.cc:2328
#, c-format
msgid "Usage: %s <jobno> ... | all\n"
msgstr "使い方: %s <jobno> ... | all\n"

#: src/commands.cc:2409
#, c-format
msgid "%s: %s. Use `set -a' to look at all variables.\n"
msgstr "%s: %s です。すべての変数を見るには `set -a' を使いましょう。\n"

#: src/commands.cc:2453
#, c-format
msgid "Usage: %s [<jobno>]\n"
msgstr "使い方: %s [<jobno>]\n"

#: src/commands.cc:2492
#, c-format
msgid "%s: some other job waits for job %d\n"
msgstr "%s: いくつかの他のジョブがジョブ %d を待っています\n"

#: src/commands.cc:2497
#, fuzzy, c-format
msgid "%s: wait loop detected\n"
msgstr "%s: バグ - デッドロックが検出されました\n"

#: src/commands.cc:2553
#, fuzzy, c-format
msgid "Usage: %s [OPTS] <files> <target-dir>\n"
msgstr "使い方: %s [OPTS] file\n"

#: src/commands.cc:2615 src/commands.cc:2956
#, c-format
msgid "Invalid command. "
msgstr "不正なコマンドです。 "

#: src/commands.cc:2622 src/commands.cc:2963
#, c-format
msgid "Ambiguous command. "
msgstr "あいまいなコマンドです。 "

#: src/commands.cc:2641
#, c-format
msgid "%s: Operand missed for size\n"
msgstr "%s: サイズのためのオペランドがありません\n"

#: src/commands.cc:2658
#, c-format
msgid "%s: Operand missed for `expire'\n"
msgstr "%s: `expire' のためのオペランドがありません\n"

#: src/commands.cc:2691
#, c-format
msgid ""
"%s: %s - no such cached session. Use `scache' to look at session list.\n"
msgstr ""
"%s: %s - そのようなキャッシュされたセッションはありません。`scache' でセッ"
"ションリストを見てください。\n"

#: src/commands.cc:2715
#, c-format
msgid "Sorry, no help for %s\n"
msgstr "残念ながら、%s のヘルプはありません\n"

#: src/commands.cc:2720
#, c-format
msgid "%s is a built-in alias for %s\n"
msgstr "%s は %s の組み込みエイリアスです\n"

#: src/commands.cc:2725
#, c-format
msgid "Usage: %s\n"
msgstr "使い方: %s\n"

#: src/commands.cc:2733
#, c-format
msgid "%s is an alias to `%s'\n"
msgstr "%s は `%s' のエイリアスです\n"

#: src/commands.cc:2737
#, c-format
msgid "No such command `%s'. Use `help' to see available commands.\n"
msgstr ""
"`%s'というコマンドはありません。利用可能なコマンドは `help' で調べられま"
"す。\n"

#: src/commands.cc:2739
#, c-format
msgid "Ambiguous command `%s'. Use `help' to see available commands.\n"
msgstr ""
"`%s' ではコマンドを特定できません。利用可能なコマンドは `help' で調べられま"
"す。\n"

#: src/commands.cc:2805
#, fuzzy, c-format
msgid "LFTP | Version %s | Copyright (c) 1996-%d Alexander V. Lukyanov\n"
msgstr "Lftp | バージョン %s | Copyright (c) 1996-2001 Alexander V. Lukyanov\n"

#: src/commands.cc:2808
#, c-format
msgid ""
"LFTP is free software: you can redistribute it and/or modify\n"
"it under the terms of the GNU General Public License as published by\n"
"the Free Software Foundation, either version 3 of the License, or\n"
"(at your option) any later version.\n"
"\n"
"This program is distributed in the hope that it will be useful,\n"
"but WITHOUT ANY WARRANTY; without even the implied warranty of\n"
"MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n"
"GNU General Public License for more details.\n"
"\n"
"You should have received a copy of the GNU General Public License\n"
"along with LFTP.  If not, see <http://www.gnu.org/licenses/>.\n"
msgstr ""

#: src/commands.cc:2822
#, fuzzy, c-format
msgid "Send bug reports and questions to the mailing list <%s>.\n"
msgstr "バグ報告や質問は <%s> まで送ってください。\n"

#: src/commands.cc:2828
msgid "Libraries used: "
msgstr ""

#: src/commands.cc:2979 src/commands.cc:3007
#, c-format
msgid "%s: bookmark name required\n"
msgstr "%s: ブックマーク名が必要です\n"

#: src/commands.cc:2996
#, c-format
msgid "%s: spaces in bookmark name are not allowed\n"
msgstr "%s: ブックマーク名にスペースを入れてはいけません\n"

#: src/commands.cc:3009
#, c-format
msgid "%s: no such bookmark `%s'\n"
msgstr "%s: `%s' というブックマークはありません\n"

#: src/commands.cc:3032
#, c-format
msgid "%s: import type required (netscape,ncftp)\n"
msgstr "%s: インポートタイプ(netscape,ncftp)を指定してください\n"

#: src/commands.cc:3110
#, c-format
msgid "Usage: %s [-d #] dir\n"
msgstr "使い方: %s [-d #] dir\n"

#: src/commands.cc:3215
#, fuzzy, c-format
msgid "%s: invalid block size `%s'\n"
msgstr "不正な数"

#: src/commands.cc:3226
#, fuzzy, c-format
msgid "Usage: %s [options] <dirs>\n"
msgstr "使い方: %s [-d #] dir\n"

#: src/commands.cc:3232
#, c-format
msgid "%s: warning: summarizing is the same as using --max-depth=0\n"
msgstr ""

#: src/commands.cc:3236
#, c-format
msgid "%s: summarizing conflicts with --max-depth=%i\n"
msgstr ""

#: src/commands.cc:3280
#, c-format
msgid "Usage: %s command args...\n"
msgstr "使い方: %s command args...\n"

#: src/commands.cc:3292
#, c-format
msgid "Usage: %s module [args...]\n"
msgstr "使い方: %s module [args...]\n"

#: src/commands.cc:3310 src/LocalAccess.cc:642
msgid "cannot get current directory"
msgstr "現在のディレクトリが取得できません"

#: src/commands.cc:3374
#, fuzzy, c-format
msgid "Usage: %s [OPTS] mode file...\n"
msgstr "使い方: %s [OPTS] files...\n"

#: src/commands.cc:3394
#, fuzzy, c-format
msgid "invalid mode string: %s\n"
msgstr "不正な数"

#: src/commands.cc:3457 src/commands.cc:3466
msgid "Invalid range format. Format is min-max, e.g. 10-20."
msgstr "範囲フォーマットが不正です。正しいフォーマットは 最小-最大 例) 10-20"

#: src/commands.cc:3478
#, c-format
msgid "Usage: %s [OPTS] file\n"
msgstr "使い方: %s [OPTS] file\n"

#: src/CopyJob.cc:82
#, c-format
msgid "`%s' at %lld %s%s%s%s"
msgstr "`%s' (現在 %lld バイト) %s%s%s%s"

#: src/CopyJob.cc:159
#, c-format
msgid "%lld $#ll#byte|bytes$ transferred in %ld $#l#second|seconds$"
msgstr "%lld バイト転送済、%ld 秒経過"

#: src/CopyJob.cc:167
#, fuzzy, c-format
msgid "%lld $#ll#byte|bytes$ transferred"
msgstr "%lld バイト転送済\n"

#: src/CopyJob.cc:283
#, c-format
msgid "Transfer of %d of %d $file|files$ failed\n"
msgstr "%d 個(%d ファイル中)の転送が失敗\n"

#: src/CopyJob.cc:289
#, c-format
msgid "Total %d $file|files$ transferred\n"
msgstr "計 %d ファイル転送済\n"

#: src/FileAccess.cc:160
#, fuzzy
msgid "Access failed: "
msgstr "アクセス失敗: %s"

#: src/FileAccess.cc:161
msgid "File cannot be accessed"
msgstr "ファイルにアクセスできません"

#: src/FileAccess.cc:163 src/Fish.cc:982 src/ftpclass.cc:4599
#: src/ftpclass.cc:4608 src/SFtp.cc:1339 src/Torrent.cc:3533
msgid "Not connected"
msgstr "接続されていません"

#: src/FileAccess.cc:166 src/FileAccess.cc:167
msgid "Fatal error"
msgstr "致命的エラー"

#: src/FileAccess.cc:169
msgid "Store failed - you have to reput"
msgstr "保存失敗 - reput する必要があります"

#: src/FileAccess.cc:172 src/FileAccess.cc:173
msgid "Login failed"
msgstr "ログインに失敗しました"

#: src/FileAccess.cc:176 src/FileAccess.cc:177
msgid "Operation not supported"
msgstr "その操作はサポートされていません"

#: src/FileAccess.cc:180
#, fuzzy
msgid "File moved"
msgstr "移動されたファイル: %s"

#: src/FileAccess.cc:182
#, fuzzy
msgid "File moved to `"
msgstr "ファイルを `%s' へ移動しました"

#: src/FileCopy.cc:141
msgid "copy: destination file is already complete\n"
msgstr ""

#: src/FileCopy.cc:182
msgid "copy: put is broken\n"
msgstr "copy: put が壊れています\n"

#: src/FileCopy.cc:201
msgid "seek failed"
msgstr "seek 失敗"

#: src/FileCopy.cc:207
msgid "no progress timeout"
msgstr ""

#: src/FileCopy.cc:235
msgid "cannot seek on data source"
msgstr "データソースには seek できません"

#: src/FileCopy.cc:238
#, c-format
msgid "copy: put rolled back to %lld, seeking get accordingly\n"
msgstr "copy: put は %lld に戻りました。適宜 get しています\n"

#: src/FileCopy.cc:251
msgid "copy: all data received, but get rolled back\n"
msgstr "copy: データをすべて受信しましたが、get は戻りました\n"

#: src/FileCopy.cc:267
#, c-format
msgid "copy: get rolled back to %lld, seeking put accordingly\n"
msgstr "copy: get は %lld に戻りました。適宜 put しています\n"

#: src/FileCopy.cc:364
msgid "file size decreased during transfer"
msgstr ""

#: src/FileCopy.cc:1227
#, c-format
msgid "copy: received redirection to `%s'\n"
msgstr "copy: `%s' へのリダイレクションを受け取りました\n"

#: src/FileCopy.cc:1290
#, fuzzy
#| msgid "saw file size in response"
msgid "file size increased during transfer"
msgstr "レスポンス中にファイルサイズがありました"

#: src/FileCopy.cc:1390
msgid "file name missed in URL"
msgstr "URL 中にファイル名がありません"

#: src/FileCopy.cc:2086
msgid "Verify command failed without a message"
msgstr ""

#: src/FileCopyFtp.cc:95
msgid "**** FXP: trying to reverse ftp:fxp-passive-source\n"
msgstr "**** FXP: ftp:fxp-passive-source を置き換えようとしています\n"

#: src/FileCopyFtp.cc:102
#, fuzzy
msgid "**** FXP: trying to reverse ftp:fxp-passive-sscn\n"
msgstr "**** FXP: ftp:fxp-passive-source を置き換えようとしています\n"

#: src/FileCopyFtp.cc:110
#, fuzzy
msgid "**** FXP: trying to reverse ftp:ssl-protect-fxp\n"
msgstr "**** FXP: ftp:fxp-passive-source を置き換えようとしています\n"

#: src/FileCopyFtp.cc:116
msgid "**** FXP: giving up, reverting to plain copy\n"
msgstr "**** FXP: あきらめてプレーンコピーに戻ります\n"

#: src/FileCopyFtp.cc:125
msgid "ftp:fxp-force is set but FXP is not available"
msgstr ""

#: src/FileCopy.h:285
msgid "Verifying..."
msgstr ""

#: src/FileSetOutput.cc:230
msgid "non-option arguments found"
msgstr ""

#: src/Filter.cc:166
#, fuzzy
msgid "pipe() failed: "
msgstr "pipe() が失敗しました: %s"

#: src/Filter.cc:200 src/PtyShell.cc:135
#, c-format
msgid "chdir(%s) failed: %s\n"
msgstr "chdir(%s) が失敗しました: %s\n"

#: src/Filter.cc:208
#, fuzzy, c-format
msgid "execvp(%s) failed: %s\n"
msgstr "execlp(%s) に失敗しました: %s\n"

#: src/Filter.cc:213 src/PtyShell.cc:147
#, c-format
msgid "execl(/bin/sh) failed: %s\n"
msgstr "execl (/bin/sh) が失敗しました: %s\n"

#: src/Filter.cc:415
#, fuzzy
msgid "file already exists and xfer:clobber is unset"
msgstr ""
"%s: %s: ファイルはすでに存在し、また xfer:clobber がセットされていません\n"

#: src/FindJobDu.cc:101
msgid "total"
msgstr ""

#: src/Fish.cc:75 src/ftpclass.cc:1292 src/Http.cc:1232 src/SFtp.cc:77
#, c-format
msgid "Closing idle connection"
msgstr "アイドル状態の接続を閉じます"

#: src/Fish.cc:162 src/SFtp.cc:177
msgid "Running connect program"
msgstr ""

#: src/Fish.cc:588 src/ftpclass.cc:2887 src/ftpclass.cc:3107 src/Http.cc:1510
#: src/SFtp.cc:742 src/SFtp.cc:1083 src/SFtp.cc:1084 src/SSH_Access.cc:167
#, c-format
msgid "Peer closed connection"
msgstr "ピアが接続を閉じました"

#: src/Fish.cc:634 src/ftpclass.cc:3037 src/ftpclass.cc:4219 src/SFtp.cc:1108
#, c-format
msgid "extra server response"
msgstr "余分なサーバレスポンスです"

#: src/Fish.cc:987 src/ftpclass.cc:4611 src/Http.cc:2329 src/Http.cc:2336
#: src/SFtp.cc:1345 src/Torrent.cc:3536 src/TorrentTracker.cc:624
msgid "Connecting..."
msgstr "接続中..."

#: src/Fish.cc:989 src/ftpclass.cc:4617 src/SFtp.cc:1347
msgid "Connected"
msgstr "接続しました"

#: src/Fish.cc:991 src/ftpclass.cc:4594 src/ftpclass.cc:4622
#: src/ftpclass.cc:4636 src/Http.cc:2338 src/SFtp.cc:1349
#: src/TorrentTracker.cc:626
msgid "Waiting for response..."
msgstr "応答を待っています..."

#: src/Fish.cc:993 src/ftpclass.cc:4656 src/Http.cc:2341 src/SFtp.cc:1351
msgid "Receiving data"
msgstr "データ受信中"

#: src/Fish.cc:995 src/ftpclass.cc:4654 src/Http.cc:2334 src/SFtp.cc:1353
msgid "Sending data"
msgstr "データ送信中"

#: src/Fish.cc:997 src/SFtp.cc:1355
msgid "Done"
msgstr ""

#: src/Fish.cc:1136 src/FtpDirList.cc:123 src/HttpDir.cc:1384 src/SFtp.cc:2200
#: src/SFtp.cc:2320
#, c-format
msgid "Getting file list (%lld) [%s]"
msgstr "ファイルリスト (%lld) [%s] を取得中"

#: src/ftpclass.cc:197
#, c-format
msgid "Data connection peer has wrong port number"
msgstr "データ接続ピアのポート番号が間違っています"

#: src/ftpclass.cc:203
#, c-format
msgid "Data connection peer has mismatching address"
msgstr "データ接続ピアのアドレスが一致しません"

#: src/ftpclass.cc:225 src/ftpclass.cc:250
#, c-format
msgid "Switching to NOREST mode"
msgstr "NOREST モードに切り替えます"

#: src/ftpclass.cc:425
#, c-format
msgid "Server reply matched ftp:retry-530, retrying"
msgstr "サーバの返答が ftp:retry-530 とマッチしました。再試行中"

#: src/ftpclass.cc:433
#, c-format
msgid "Server reply matched ftp:retry-530-anonymous, retrying"
msgstr "サーバの返答が ftp:retry-530-anonymous とマッチしました。再試行中"

#: src/ftpclass.cc:466
msgid "Account is required, set ftp:acct variable"
msgstr "アカウントが必要です。ftp:acct 変数を設定してください"

#: src/ftpclass.cc:483
msgid "ftp:skey-force is set and server does not support OPIE nor S/KEY"
msgstr ""
"ftp:skey-forceがセットされていますが、サーバはOPIEもS/KEYもサポートしていませ"
"ん"

#: src/ftpclass.cc:501
#, c-format
msgid "assuming failed host name lookup"
msgstr "ホスト名検索に失敗したと仮定します"

#: src/ftpclass.cc:809 src/ftpclass.cc:810
#, c-format
msgid "cannot parse EPSV response"
msgstr "EPSV レスポンスを解析できません"

#: src/ftpclass.cc:837 src/ftpclass.cc:838
#, fuzzy, c-format
#| msgid "cannot parse EPSV response"
msgid "cannot parse custom EPSV response"
msgstr "EPSV レスポンスを解析できません"

#: src/ftpclass.cc:1122
#, c-format
msgid "Closing control socket"
msgstr "コントロールソケットを閉じています"

#: src/ftpclass.cc:1341
#, fuzzy
msgid "MLSD is disabled by ftp:use-mlsd"
msgstr "SITE CHMOD はこのサイトではサポートされていません"

#: src/ftpclass.cc:1411 src/Http.cc:1431 src/Torrent.cc:3204
#: src/Torrent.cc:3743 src/TorrentTracker.cc:395
#, c-format
msgid "cannot create socket of address family %d"
msgstr "アドレスファミリ %d のソケットを作れません"

#: src/ftpclass.cc:1442 src/Http.cc:1457
#, c-format
msgid "Socket error (%s) - reconnecting"
msgstr "ソケットエラー (%s) - 再接続中"

#: src/ftpclass.cc:1715
#, fuzzy
msgid "MFF and SITE CHMOD are not supported by this site"
msgstr "SITE CHMOD はこのサイトではサポートされていません"

#: src/ftpclass.cc:1720
#, fuzzy
msgid "MLST and MLSD are not supported by this site"
msgstr "SITE CHMOD はこのサイトではサポートされていません"

#: src/ftpclass.cc:2031
#, fuzzy
msgid "SITE SYMLINK is not supported by the server"
msgstr "SITE CHMOD はこのサイトではサポートされていません"

#: src/ftpclass.cc:2204
msgid "unsupported network protocol"
msgstr "サポートされていないネットワークプロトコルです"

#: src/ftpclass.cc:2253 src/ftpclass.cc:2355
#, fuzzy, c-format
msgid "Data socket error (%s) - reconnecting"
msgstr "ソケットエラー (%s) - 再接続中"

#: src/ftpclass.cc:2281
#, fuzzy, c-format
msgid "Accepted data connection from (%s) port %u"
msgstr "データソケットを (%s) のポート %u に接続中"

#: src/ftpclass.cc:2330
#, c-format
msgid "Connecting data socket to (%s) port %u"
msgstr "データソケットを (%s) のポート %u に接続中"

#: src/ftpclass.cc:2336
#, fuzzy, c-format
msgid "Connecting data socket to proxy %s (%s) port %u"
msgstr "データソケットを (%s) のポート %u に接続中"

#: src/ftpclass.cc:2358 src/ftpclass.cc:4414
#, c-format
msgid "Switching passive mode off"
msgstr "パッシヴモードをオフに切り替えました"

#: src/ftpclass.cc:2366
#, fuzzy, c-format
msgid "Data connection established"
msgstr "データ接続ピアのアドレスが一致しません"

#: src/ftpclass.cc:2688 src/ftpclass.cc:4548
msgid "ftp:ssl-force is set and server does not support or allow SSL"
msgstr ""
"ftp:ssl-force がセットされていますが、サーバは SSL をサポートあるいは許可して"
"いません"

#: src/ftpclass.cc:3053
#, c-format
msgid "Persist and retry"
msgstr "持続的に再試行します"

#: src/ftpclass.cc:3321
#, c-format
msgid "Closing data socket"
msgstr "データソケットを閉じています"

#: src/ftpclass.cc:3343
#, fuzzy, c-format
msgid "Closing aborted data socket"
msgstr "データソケットを閉じています"

#: src/ftpclass.cc:4193
#, c-format
msgid "saw file size in response"
msgstr "レスポンス中にファイルサイズがありました"

#: src/ftpclass.cc:4233 src/ftpclass.cc:4260
#, c-format
msgid "Turning on sync-mode"
msgstr "sync モードを有効にしました"

#: src/ftpclass.cc:4434
#, c-format
msgid "Switching passive mode on"
msgstr "パッシヴモードをオンに切り替えました"

#: src/ftpclass.cc:4585
#, fuzzy
msgid "FEAT negotiation..."
msgstr "TLS ネゴシエーション..."

#: src/ftpclass.cc:4592
msgid "Sending commands..."
msgstr "コマンドを送信中..."

#: src/ftpclass.cc:4596
#, fuzzy
msgid "Delaying before retry"
msgstr "再接続を遅延しています"

#: src/ftpclass.cc:4597 src/Http.cc:2331
msgid "Connection idle"
msgstr "接続アイドル"

#: src/ftpclass.cc:4604 src/Http.cc:2323 src/Resolver.cc:172
#: src/Resolver.cc:217 src/TorrentTracker.cc:622
#, c-format
msgid "Resolving host address..."
msgstr "ホストアドレスを解決しています..."

#: src/ftpclass.cc:4615
msgid "TLS negotiation..."
msgstr "TLS ネゴシエーション..."

#: src/ftpclass.cc:4619
msgid "Logging in..."
msgstr "ログインしています..."

#: src/ftpclass.cc:4623
msgid "Making data connection..."
msgstr "データ接続を確立中..."

#: src/ftpclass.cc:4626
msgid "Changing remote directory..."
msgstr "リモートディレクトリを変更中..."

#: src/ftpclass.cc:4632
msgid "Waiting for other copy peer..."
msgstr "他のコピー相手を待っています..."

#: src/ftpclass.cc:4634 src/ftpclass.cc:4658
msgid "Waiting for transfer to complete"
msgstr "送受信の完了を待っています"

#: src/ftpclass.cc:4638
#, fuzzy
msgid "Waiting for TLS shutdown..."
msgstr "応答を待っています..."

#: src/ftpclass.cc:4640
msgid "Waiting for data connection..."
msgstr "データ接続を待っています..."

#: src/ftpclass.cc:4646
#, fuzzy
msgid "Sending data/TLS"
msgstr "データ送信中"

#: src/ftpclass.cc:4648
#, fuzzy
msgid "Receiving data/TLS"
msgstr "データ受信中"

#: src/Http.cc:230
#, c-format
msgid "Closing HTTP connection"
msgstr "HTTP 接続を閉じています"

#: src/Http.cc:240
msgid "POST method failed"
msgstr "POST メソッド失敗"

#: src/Http.cc:1381
msgid "ftp over http cannot work without proxy, set hftp:proxy."
msgstr ""
"ftp over http はプロキシ無しでは利用できません。hftp:proxy をセットしてくださ"
"い。"

#: src/Http.cc:1537
#, c-format
msgid "Sending request..."
msgstr "リクエストを送信中..."

#: src/Http.cc:1575
#, c-format
msgid "Hit EOF while fetching headers"
msgstr "ヘッダの取得中に EOF に達しました"

#: src/Http.cc:1695
#, c-format
msgid "Could not parse HTTP status line"
msgstr "HTTP ステータスラインを解析できませんでした"

#: src/Http.cc:1726
msgid "Object is not cached and http:cache-control has only-if-cached"
msgstr ""

#: src/Http.cc:1891
#, c-format
msgid "Receiving body..."
msgstr "ボディを受信中..."

#: src/Http.cc:2092
#, c-format
msgid "Hit EOF"
msgstr "EOF に達しました"

#: src/Http.cc:2095
#, c-format
msgid "Received not enough data, retrying"
msgstr "十分なデータを受信できなかったので、再試行します"

#: src/Http.cc:2106
#, c-format
msgid "Received all"
msgstr "すべて受信しました"

#: src/Http.cc:2111
#, c-format
msgid "Received all (total)"
msgstr "すべて受信しました (合計)"

#: src/Http.cc:2135 src/Http.cc:2159
msgid "chunked format violated"
msgstr "chunked フォーマットに違反しています"

#: src/Http.cc:2145
#, fuzzy, c-format
msgid "Received last chunk"
msgstr "すべて受信しました"

#: src/Http.cc:2339
msgid "Fetching headers..."
msgstr "ヘッダを取得中..."

#: src/Job.cc:293
#, c-format
msgid "[%d] Done (%s)"
msgstr "[%d] 完了 (%s)"

#: src/lftp.cc:370
#, fuzzy, c-format
msgid "[%u] Terminated by signal %d. %s\n"
msgstr "[%lu] シグナル %d で中断しました。 %s"

#: src/lftp.cc:445
#, fuzzy, c-format
msgid "[%u] Started.  %s\n"
msgstr "[%lu] 開始。 %s"

#: src/lftp.cc:463
#, fuzzy, c-format
msgid "[%u] Detaching from the terminal to complete transfers...\n"
msgstr "[%lu] 転送を完了するためバックグラウンドに移行中です...\n"

#: src/lftp.cc:465
#, c-format
msgid "[%u] Exiting and detaching from the terminal.\n"
msgstr ""

#: src/lftp.cc:470
#, c-format
msgid "[%u] Detached from terminal. %s\n"
msgstr ""

#: src/lftp.cc:480
#, fuzzy, c-format
msgid "[%u] Finished. %s\n"
msgstr "[%lu] 完了。 %s"

#: src/lftp.cc:484
#, fuzzy, c-format
msgid "[%u] Moving to background to complete transfers...\n"
msgstr "[%lu] 転送を完了するためバックグラウンドに移行中です...\n"

#: src/lftp.cc:553
msgid "history -w file|-r file|-c|-l [cnt]"
msgstr "history -w file|-r file|-c|-l [cnt]"

#: src/lftp.cc:554
msgid ""
" -w <file> Write history to file.\n"
" -r <file> Read history from file; appends to current history.\n"
" -c  Clear the history.\n"
" -l  List the history (default).\n"
"Optional argument cnt specifies the number of history lines to list,\n"
"or \"all\" to list all entries.\n"
msgstr ""
" -w <file> 履歴をファイルに書き込みます。\n"
" -r <file> 履歴をファイルから読み込み、現在の履歴に追加します。\n"
" -c 履歴を消去します。\n"
" -l 履歴を列挙します(デフォルト)。\n"
" オプション引数 cnt では列挙する履歴の行数を指定します。\"all\"を指定すると全"
"てのエントリが列挙されます。\n"

#: src/lftp.cc:561
msgid "Attach the terminal to specified backgrounded lftp process.\n"
msgstr ""

#: src/lftp_ssl.cc:1291
#, c-format
msgid "No certificate presented by %s.\n"
msgstr ""

#: src/LocalAccess.cc:604 src/NetAccess.cc:686
#, fuzzy
msgid "Getting directory contents"
msgstr "ディレクトリ内容 (%lld) を取得中"

#: src/LocalAccess.cc:606 src/NetAccess.cc:690
#, fuzzy
msgid "Getting files information"
msgstr "ファイル情報 (%d%%)を取得中"

#: src/LsCache.cc:190
#, c-format
msgid "%ld $#l#byte|bytes$ cached"
msgstr "%ld バイトキャッシュ済"

#: src/LsCache.cc:194
msgid ", no size limit"
msgstr "、サイズ制限無し"

#: src/LsCache.cc:196
#, c-format
msgid ", maximum size %ld\n"
msgstr "、最大サイズは %ld バイト\n"

#: src/mgetJob.cc:78
#, fuzzy, c-format
msgid "%s: %s: no files found\n"
msgstr "%s: ファイルがありません\n"

#: src/MirrorJob.cc:100
#, c-format
msgid "%sTotal: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
msgstr ""
"%s合計: %d 個のディレクトリ、%d 個のファイル、%d 個のシンボリックリンク\n"

#: src/MirrorJob.cc:104
#, c-format
msgid "%sNew: %d file$|s$, %d symlink$|s$\n"
msgstr "%s新規: %d 個のファイル、%d 個のシンボリックリンク\n"

#: src/MirrorJob.cc:108
#, c-format
msgid "%sModified: %d file$|s$, %d symlink$|s$\n"
msgstr "%s変更有り: %d 個のファイル、%d 個のシンボリックリンク\n"

#: src/MirrorJob.cc:115
#, c-format
msgid "%sRemoved: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
msgstr ""
"%s削除: %d 個のディレクトリ、%d 個のファイル、%d 個のシンボリックリンク\n"

#: src/MirrorJob.cc:120
#, fuzzy, c-format
msgid "%s%d error$|s$ detected\n"
msgstr "%s 成功、%d 個のディレクトリを作成しました\n"

#: src/MirrorJob.cc:231
#, fuzzy, c-format
msgid "Finished %s"
msgstr "[%lu] 完了。 %s"

#: src/MirrorJob.cc:344 src/MirrorJob.cc:519 src/MirrorJob.cc:1218
#, fuzzy, c-format
msgid "Removing old file `%s'"
msgstr "古いローカルファイル `%s' を削除しています"

#: src/MirrorJob.cc:346
#, fuzzy, c-format
msgid "Overwriting old file `%s'"
msgstr "古いローカルファイル `%s' を削除しています"

#: src/MirrorJob.cc:355
#, c-format
msgid "Skipping file `%s' (only-existing)"
msgstr ""

#: src/MirrorJob.cc:361
#, fuzzy, c-format
msgid "Transferring file `%s'"
msgstr "ローカルファイル `%s' を送信しています"

#: src/MirrorJob.cc:439
#, fuzzy, c-format
msgid "Skipping directory `%s' (only-existing)"
msgstr "リモートディレクトリ `%s' を作成しています"

#: src/MirrorJob.cc:461 src/MirrorJob.cc:550 src/MirrorJob.cc:902
#, c-format
msgid "Removing old local file `%s'"
msgstr "古いローカルファイル `%s' を削除しています"

#: src/MirrorJob.cc:487
#, fuzzy, c-format
msgid "Scanning directory `%s'"
msgstr "リモートディレクトリ `%s' を作成しています"

#: src/MirrorJob.cc:489
#, c-format
msgid "Mirroring directory `%s'"
msgstr "ディレクトリ `%s' をミラーしています"

#: src/MirrorJob.cc:525 src/MirrorJob.cc:567
#, c-format
msgid "Making symbolic link `%s' to `%s'"
msgstr "`%s' から `%s' へのシンボリックリンクを張っています"

#: src/MirrorJob.cc:562
#, c-format
msgid "Skipping symlink `%s' (only-existing)"
msgstr ""

#: src/MirrorJob.cc:807
#, c-format
msgid "mirror: protocol `%s' is not suitable for mirror\n"
msgstr "mirror: プロトコル `%s' はミラーに適しません\n"

#: src/MirrorJob.cc:923
#, fuzzy, c-format
msgid "Making directory `%s'"
msgstr "リモートディレクトリ `%s' を作成しています"

#: src/MirrorJob.cc:1181
#, fuzzy, c-format
msgid "Old directory `%s' is not removed"
msgstr "古いローカルファイル `%s' は削除されません"

#: src/MirrorJob.cc:1183
#, fuzzy, c-format
msgid "Old file `%s' is not removed"
msgstr "古いローカルファイル `%s' は削除されません"

#: src/MirrorJob.cc:1216
#, fuzzy, c-format
msgid "Removing old directory `%s'"
msgstr "古いリモートディレクトリ `%s' を削除しています"

#: src/MirrorJob.cc:1339
#, fuzzy, c-format
msgid "Removing source directory `%s'"
msgstr "古いリモートディレクトリ `%s' を削除しています"

#: src/MirrorJob.cc:1405
#, fuzzy, c-format
msgid "Removing source file `%s'"
msgstr "古いローカルファイル `%s' を削除しています"

#: src/MirrorJob.cc:1425
#, c-format
msgid "Retrying mirror...\n"
msgstr ""

#: src/MirrorJob.cc:1694
msgid "pattern is empty"
msgstr ""

#: src/MirrorJob.cc:1779
#, c-format
msgid "%s must be one of: %s"
msgstr ""

#: src/MirrorJob.cc:2019
#, c-format
msgid ""
"%s: multiple --file or --directory options must have the same base "
"directory\n"
msgstr ""

#: src/MirrorJob.cc:2160
#, c-format
msgid "%s: ambiguous source directory (`%s' or `%s'?)\n"
msgstr ""

#: src/MirrorJob.cc:2182
#, c-format
msgid "%s: ambiguous target directory (`%s' or `%s'?)\n"
msgstr ""

#: src/MirrorJob.cc:2223
#, c-format
msgid "%s: source directory is required (mirror:require-source is set)\n"
msgstr ""

#: src/MirrorJob.cc:2343
msgid ""
"\n"
"Mirror specified remote directory to local directory\n"
"\n"
" -R, --reverse          reverse mirror (put files)\n"
"Lots of other options are documented in the man page lftp(1).\n"
"\n"
"When using -R, the first directory is local and the second is remote.\n"
"If the second directory is omitted, basename of the first directory is "
"used.\n"
"If both directories are omitted, current local and remote directories are "
"used.\n"
"\n"
"See the man page lftp(1) for a complete documentation.\n"
msgstr ""

#: src/mkdirJob.cc:128
#, c-format
msgid "%s ok, `%s' created\n"
msgstr "%s 成功、`%s' を作成しました\n"

#: src/mkdirJob.cc:130 src/rmJob.cc:51
#, c-format
msgid "%s failed for %d of %d director$y|ies$\n"
msgstr "%s を %d 個のディレクトリ(合計 %d 個のうち)に行えませんでした\n"

#: src/mkdirJob.cc:133
#, c-format
msgid "%s ok, %d director$y|ies$ created\n"
msgstr "%s 成功、%d 個のディレクトリを作成しました\n"

#: src/module.cc:190
#, c-format
msgid "depend module `%s': %s\n"
msgstr "モジュール `%s' に依存しています: %s\n"

#: src/module.cc:210
msgid "modules are not supported on this system"
msgstr "モジュールはこのシステムではサポートされていません"

#: src/mvJob.cc:92
#, c-format
msgid "rename successful\n"
msgstr "リネームに成功しました\n"

#: src/NetAccess.cc:168
#, c-format
msgid "Connecting to %s%s (%s) port %u"
msgstr "%s%s (%s) ポート %u に接続中"

#: src/NetAccess.cc:224 src/Torrent.cc:3326
#, c-format
msgid "Timeout - reconnecting"
msgstr "タイムアウト - 再接続中"

#: src/NetAccess.cc:323
#, fuzzy
msgid "Connection limit reached"
msgstr "接続数制限に達しました"

#: src/NetAccess.cc:330
msgid "Delaying before reconnect"
msgstr "再接続を遅延しています"

#: src/NetAccess.cc:354 src/NetAccess.cc:356
msgid "max-retries exceeded"
msgstr "最大再試行回数を超えました"

#: src/OutputJob.cc:145
#, c-format
msgid "%s (filter)"
msgstr ""

#: src/parsecmd.cc:290
msgid "parse: missing filter command\n"
msgstr "parse: フィルタコマンドがありません\n"

#: src/parsecmd.cc:292
msgid "parse: missing redirection filename\n"
msgstr "parse: リダイレクションのファイル名がありません\n"

#: src/PatternSet.cc:110
#, fuzzy, c-format
msgid "regular expression `%s': %s"
msgstr "%s: %s の書き込みでエラー: %s\n"

#: src/pgetJob.cc:127
msgid "pget: falling back to plain get"
msgstr ""

#: src/pgetJob.cc:131
#, fuzzy
msgid "the target file is remote"
msgstr "古いリモートファイル `%s' は削除されません"

#: src/pgetJob.cc:136
msgid "the source file size is unknown"
msgstr ""

#: src/pgetJob.cc:173
#, c-format
msgid "pget: warning: space allocation for %s (%lld bytes) failed: %s\n"
msgstr ""

#: src/pgetJob.cc:234
#, c-format
msgid "`%s', got %lld of %lld (%d%%) %s%s"
msgstr "`%s'、%lld (%lld 中、%d%%)を取得 %s%s"

#: src/plural.c:96
msgid "=1 =0|>1"
msgstr "=1 =0|>1"

#: src/PollVec.cc:69
#, c-format
msgid "%s: BUG - deadlock detected\n"
msgstr "%s: バグ - デッドロックが検出されました\n"

#: src/PtyShell.cc:68
msgid "pseudo-tty allocation failed: "
msgstr ""

#: src/QueueFeeder.cc:68
msgid "Added job$|s$"
msgstr ""

#: src/QueueFeeder.cc:164 src/QueueFeeder.cc:185
#, c-format
msgid "No queued jobs.\n"
msgstr ""

#: src/QueueFeeder.cc:166
#, fuzzy, c-format
msgid "No queued job #%i.\n"
msgstr "%s: アクティブなキューはありません。\n"

#: src/QueueFeeder.cc:171 src/QueueFeeder.cc:192
msgid "Deleted job$|s$"
msgstr ""

#: src/QueueFeeder.cc:187
#, c-format
msgid "No queued jobs match \"%s\".\n"
msgstr ""

#: src/QueueFeeder.cc:212 src/QueueFeeder.cc:230
msgid "Moved job$|s$"
msgstr ""

#: src/QueueFeeder.cc:354
msgid "Commands queued:"
msgstr ""

#: src/ResMgr.cc:123
msgid "no such variable"
msgstr "そのような変数はありません"

#: src/ResMgr.cc:127
msgid "ambiguous variable name"
msgstr "変数名が特定できません"

#: src/ResMgr.cc:335
msgid "invalid boolean value"
msgstr "不正なブーリアン値"

#: src/ResMgr.cc:357
#, fuzzy
msgid "invalid boolean/auto value"
msgstr "不正なブーリアン値"

#: src/ResMgr.cc:402 src/ResMgr.cc:713
msgid "invalid number"
msgstr "不正な数"

#: src/ResMgr.cc:415
msgid "invalid floating point number"
msgstr "不正な浮動小数点数"

#: src/ResMgr.cc:429
#, fuzzy
msgid "invalid unsigned number"
msgstr "不正な数"

#: src/ResMgr.cc:678
msgid "Invalid time unit letter, only [smhd] are allowed."
msgstr "時間単位を示す文字として不正です。[smhd] のみ許されます。"

#: src/ResMgr.cc:686
msgid "Invalid time format. Format is <time><unit>, e.g. 2h30m."
msgstr ""
"時間フォーマットが不正です。正しいフォーマットは <時間><単位> 例) 2h30m"

#: src/ResMgr.cc:718
msgid "integer overflow"
msgstr ""

#: src/ResMgr.cc:804
msgid "Invalid IPv4 numeric address"
msgstr "不正なIPv4番号アドレス"

#: src/ResMgr.cc:814
#, fuzzy
msgid "Invalid IPv6 numeric address"
msgstr "不正なIPv4番号アドレス"

#: src/ResMgr.cc:884 src/ResMgr.cc:888
#, fuzzy
msgid "this encoding is not supported"
msgstr "その操作はサポートされていません"

#: src/ResMgr.cc:894
msgid "no closure defined for this setting"
msgstr "この設定では終止が定義されていません"

#: src/ResMgr.cc:900
#, fuzzy
msgid "a closure is required for this setting"
msgstr "この設定では終止が定義されていません"

#: src/Resolver.cc:236
msgid "host name resolve timeout"
msgstr "ホスト名解決がタイムアウトしました"

#: src/Resolver.cc:282
#, fuzzy, c-format
msgid "%d address$|es$ found"
msgstr "---- %d 個のアドレスが見つかりました\n"

#: src/Resolver.cc:327
msgid "Link-local IPv6 address should have a scope"
msgstr ""

#: src/Resolver.cc:765
msgid "DNS resolution not trusted."
msgstr ""

#: src/Resolver.cc:871
msgid "Host name lookup failure"
msgstr "ホスト名検索に失敗しました"

#: src/Resolver.cc:903
#, c-format
msgid "no such %s service"
msgstr "%s などというサービスはありません"

#: src/Resolver.cc:930
msgid "No address found"
msgstr "アドレスが見つかりません"

#: src/resource.cc:50 src/resource.cc:108
msgid "Proxy protocol unsupported"
msgstr "プロキシプロトコルはサポートされていません"

#: src/resource.cc:54
msgid "ftp:proxy password: "
msgstr "ftp:proxy パスワード: "

#: src/resource.cc:70
#, c-format
msgid "%s must be one of: "
msgstr ""

#: src/resource.cc:72
msgid "must be one of: "
msgstr ""

#: src/resource.cc:84
msgid ", or empty"
msgstr ""

#: src/resource.cc:116
msgid "only PUT and POST values allowed"
msgstr "PUT か POST 値のみ許可されます"

#: src/resource.cc:148
#, c-format
msgid "unknown address family `%s'"
msgstr "アドレスファミリー `%s' は不明です"

#: src/rmJob.cc:47
#, c-format
msgid "%s ok, `%s' removed\n"
msgstr "%s 成功、`%s' を削除しました\n"

#: src/rmJob.cc:54
#, c-format
msgid "%s failed for %d of %d file$|s$\n"
msgstr "%s を %d 個のファイル(%d ファイル中)に行えませんでした\n"

#: src/rmJob.cc:60
#, c-format
msgid "%s ok, %d director$y|ies$ removed\n"
msgstr "%s 成功、%d 個のディレクトリを削除しました\n"

#: src/rmJob.cc:63
#, c-format
msgid "%s ok, %d file$|s$ removed\n"
msgstr "%s 成功、%d ファイルを削除しました\n"

#: src/SFtp.cc:1099 src/SFtp.cc:1100
#, fuzzy, c-format
msgid "invalid server response format"
msgstr "余分なサーバレスポンスです"

#: src/SleepJob.cc:99
msgid "Sleeping forever"
msgstr ""

#: src/SleepJob.cc:100
msgid "Sleep time left: "
msgstr ""

#: src/SleepJob.cc:108
#, c-format
msgid "\tRepeat count: %d\n"
msgstr "\tリピート回数: %d\n"

#: src/SleepJob.cc:142
#, c-format
msgid "%s: argument required. "
msgstr "%s: 引数が必要です。"

#: src/SleepJob.cc:262
#, c-format
msgid "%s: date-time specification missed\n"
msgstr ""

#: src/SleepJob.cc:269
#, c-format
msgid "%s: date-time parse error\n"
msgstr ""

#: src/SleepJob.cc:303
msgid ""
"Usage: sleep <time>[unit]\n"
"Sleep for given amount of time. The time argument can be optionally\n"
"followed by unit specifier: d - days, h - hours, m - minutes, s - seconds.\n"
"By default time is assumed to be seconds.\n"
msgstr ""
"使い方: sleep <time>[unit]\n"
"指定された時間だけスリープします。時間を指定する引数に続けて、単位を指定す"
"る\n"
"文字をつけることも可能です: d - 日、h - 時間、m - 分、s - 秒。\n"
"デフォルトでは時間は秒であると仮定されます。\n"

#: src/SleepJob.cc:310
#, fuzzy
msgid ""
"Repeat specified command with a delay between iterations.\n"
"Default delay is one second, default command is empty.\n"
" -c <count>  maximum number of iterations\n"
" -d <delay>  delay between iterations\n"
" --while-ok  stop when command exits with non-zero code\n"
" --until-ok  stop when command exits with zero code\n"
" --weak      stop when lftp moves to background.\n"
msgstr ""
"使い方: repeat [delay] [command]\n"
"指定されたコマンドを、ディレイを挟んで繰り返します。デフォルトのディレイは 1 "
"秒で、デフォルトのコマンドは空です。\n"

#: src/Speedometer.cc:90
#, c-format
msgid "%.0fb/s"
msgstr "%.0fB/s"

#: src/Speedometer.cc:93
#, c-format
msgid "%.1fK/s"
msgstr "%.1fKB/s"

#: src/Speedometer.cc:96
#, c-format
msgid "%.2fM/s"
msgstr "%.2fMB/s"

#: src/Speedometer.cc:103
#, fuzzy, c-format
msgid "%.0f B/s"
msgstr "%.0fB/s"

#: src/Speedometer.cc:105
#, fuzzy, c-format
msgid "%.1f KiB/s"
msgstr "%.1fKB/s"

#: src/Speedometer.cc:107
#, fuzzy, c-format
msgid "%.2f MiB/s"
msgstr "%.2fMB/s"

#: src/Speedometer.cc:129
msgid "eta:"
msgstr "eta:"

#: src/SSH_Access.cc:97
#, fuzzy
msgid "Password required"
msgstr "パスワード: "

#: src/SSH_Access.cc:102
msgid "Login incorrect"
msgstr ""

#: src/SSH_Access.cc:196
#, fuzzy, c-format
msgid "Disconnecting"
msgstr "接続中..."

#: src/SysCmdJob.cc:73
#, c-format
msgid "execlp(%s) failed: %s\n"
msgstr "execlp(%s) に失敗しました: %s\n"

#
# 注意
#     day,hour,minute,secondには日本語を用いないでください。
#     Speedometer.ccで先頭1バイトのみが抽出されるため、
#     2バイト文字を用いると表示が文字化けします。
# <AUTHOR> <EMAIL> 2002-04-12
#
#: src/TimeDate.cc:155
msgid "day"
msgstr "day"

#: src/TimeDate.cc:156
msgid "hour"
msgstr "hour"

#: src/TimeDate.cc:157
msgid "minute"
msgstr "minute"

#: src/TimeDate.cc:158
msgid "second"
msgstr "second"

#: src/Torrent.cc:585
msgid "announced via "
msgstr ""

#: src/Torrent.cc:599
#, c-format
msgid "next announce in %s"
msgstr ""

#: src/Torrent.cc:1142
#, c-format
msgid "%d file$|s$ found, now scanning %s"
msgstr ""

#: src/Torrent.cc:1143
#, fuzzy, c-format
msgid "%d file$|s$ found"
msgstr "---- %d 個のアドレスが見つかりました\n"

#: src/Torrent.cc:2075 src/Torrent.cc:2085
#, c-format
msgid "Getting meta-data: %s"
msgstr ""

#: src/Torrent.cc:2077
#, c-format
msgid "Validation: %u/%u (%u%%) %s%s"
msgstr ""

#: src/Torrent.cc:2088
#, fuzzy
msgid "Waiting for meta-data..."
msgstr "\tコマンドを待っています\n"

#: src/Torrent.cc:2096
msgid "Shutting down: "
msgstr ""

#: src/Torrent.cc:3207
#, fuzzy, c-format
msgid "Connecting to peer %s port %u"
msgstr "%s%s (%s) ポート %u に接続中"

#: src/Torrent.cc:3258 src/Torrent.cc:3375
#, fuzzy, c-format
msgid "peer unexpectedly closed connection after %s"
msgstr "リモートエンドが接続を閉じました"

#: src/Torrent.cc:3259 src/Torrent.cc:3376
#, fuzzy
msgid "peer unexpectedly closed connection"
msgstr "リモートエンドが接続を閉じました"

#: src/Torrent.cc:3261 src/Torrent.cc:3262
#, fuzzy, c-format
msgid "peer closed connection (before handshake)"
msgstr "ピアが接続を閉じました"

#: src/Torrent.cc:3265 src/Torrent.cc:3378 src/Torrent.cc:3379
#, fuzzy, c-format
msgid "invalid peer response format"
msgstr "余分なサーバレスポンスです"

#: src/Torrent.cc:3360 src/Torrent.cc:3361
#, fuzzy, c-format
msgid "peer closed connection"
msgstr "ピアが接続を閉じました"

#: src/Torrent.cc:3538
msgid "Handshaking..."
msgstr ""

#: src/Torrent.cc:3798
msgid "Cannot bind a socket for torrent:port-range"
msgstr ""

#: src/Torrent.cc:3858
#, fuzzy, c-format
msgid "Accepted connection from [%s]:%d"
msgstr "データソケットを (%s) のポート %u に接続中"

#: src/Torrent.cc:3924
#, c-format
msgid "peer sent unknown info_hash=%s in handshake"
msgstr ""

#: src/Torrent.cc:3948
#, c-format
msgid "peer handshake timeout"
msgstr ""

#: src/Torrent.cc:3960
#, c-format
msgid "peer short handshake"
msgstr ""

#: src/Torrent.cc:3962
#, fuzzy, c-format
msgid "peer closed just accepted connection"
msgstr "ピアが接続を閉じました"

#: src/Torrent.cc:4013
#, fuzzy, c-format
msgid "Seeding in background...\n"
msgstr "コマンドを送信中..."

#: src/Torrent.cc:4176
#, c-format
msgid "%s: --share conflicts with --output-directory.\n"
msgstr ""

#: src/Torrent.cc:4180
#, c-format
msgid "%s: --share conflicts with --only-new.\n"
msgstr ""

#: src/Torrent.cc:4184
#, c-format
msgid "%s: --share conflicts with --only-incomplete.\n"
msgstr ""

#: src/Torrent.cc:4226
#, c-format
msgid "%s: Please specify a file or directory to share.\n"
msgstr ""

#: src/Torrent.cc:4228
#, c-format
msgid "%s: Please specify meta-info file or URL.\n"
msgstr ""

#: src/Torrent.cc:4258
msgid ""
"Start BitTorrent job for the given torrent-files, which can be a local "
"file,\n"
"URL, magnet link or plain info_hash written in hex or base32. Local "
"wildcards\n"
"are expanded. Options:\n"
" -O <base>      specifies base directory where files should be placed\n"
" --force-valid  skip file validation\n"
" --dht-bootstrap=<node>  bootstrap DHT by sending a query to the node\n"
" --share        share specified file or directory\n"
msgstr ""

#: src/TorrentTracker.cc:178
msgid "not started"
msgstr ""

#: src/TorrentTracker.cc:181
#, c-format
msgid "next request in %s"
msgstr ""

#: src/TorrentTracker.cc:284 src/TorrentTracker.cc:501
#, c-format
msgid "Received valid info about %d peer$|s$"
msgstr ""

#: src/TorrentTracker.cc:298
#, c-format
msgid "Received valid info about %d IPv6 peer$|s$"
msgstr ""

#, fuzzy
#~ msgid "%s: unrecognized option '--%s'\n"
#~ msgstr "%s: `%s'へのリダイレクションを受け取りました\n"

#~ msgid "Usage: mv <file1> <file2>\n"
#~ msgstr "使い方: mv <file1> <file2>\n"

#, fuzzy
#~ msgid "number"
#~ msgstr "不正な数"

#, fuzzy
#~ msgid "error: %s:%d\n"
#~ msgstr "致命的エラー: %s"

#, fuzzy
#~ msgid ""
#~ "\n"
#~ "Mirror specified remote directory to local directory\n"
#~ "\n"
#~ " -c, --continue         continue a mirror job if possible\n"
#~ " -e, --delete           delete files not present at remote site\n"
#~ "     --delete-first     delete old files before transferring new ones\n"
#~ " -s, --allow-suid       set suid/sgid bits according to remote site\n"
#~ "     --allow-chown      try to set owner and group on files\n"
#~ "     --ignore-time      ignore time when deciding whether to download\n"
#~ " -n, --only-newer       download only newer files (-c won't work)\n"
#~ " -r, --no-recursion     don't go to subdirectories\n"
#~ " -p, --no-perms         don't set file permissions\n"
#~ "     --no-umask         don't apply umask to file modes\n"
#~ " -R, --reverse          reverse mirror (put files)\n"
#~ " -L, --dereference      download symbolic links as files\n"
#~ " -N, --newer-than=SPEC  download only files newer than specified time\n"
#~ " -P, --parallel[=N]     download N files in parallel\n"
#~ " -i RX, --include RX    include matching files\n"
#~ " -x RX, --exclude RX    exclude matching files\n"
#~ "                        RX is extended regular expression\n"
#~ " -v, --verbose[=N]      verbose operation\n"
#~ "     --log=FILE         write lftp commands being executed to FILE\n"
#~ "     --script=FILE      write lftp commands to FILE, but don't execute "
#~ "them\n"
#~ "     --just-print, --dry-run    same as --script=-\n"
#~ "\n"
#~ "When using -R, the first directory is local and the second is remote.\n"
#~ "If the second directory is omitted, basename of first directory is used.\n"
#~ "If both directories are omitted, current local and remote directories are "
#~ "used.\n"
#~ "See lftp(1) for a complete list of options.\n"
#~ msgstr ""
#~ "\n"
#~ "指定されたリモートディレクトリをローカルディレクトリへミラーします\n"
#~ "\n"
#~ " -c, --continue         可能ならばミラーのジョブを継続する\n"
#~ " -e, --delete           リモートサイトに存在しないファイルを削除する\n"
#~ " -s, --allow-suid       リモートサイトに従って suid/sgid ビットを立てる\n"
#~ " -n, --only-newer       更新されたファイルのみダウンロードする (-c は使え"
#~ "ません)\n"
#~ " -r, --no-recursion     サブディレクトリまでミラーしない\n"
#~ " -p, --no-perms         ファイルのパーミッションをセットしない\n"
#~ "     --no-umask         ファイルのモードに umask を適用しない\n"
#~ " -R, --reverse          逆ミラーを行う (ファイルを送信する)\n"
#~ " -L, --dereference      シンボリックリンクをファイルとしてダウンロードす"
#~ "る\n"
#~ " -N, --newer-than FILE  指定のファイルより新しいものだけダウンロードする\n"
#~ " -P, --parallel[=N]     N ファイル並行してダウンロードする\n"
#~ " -i RX, --include RX    マッチするファイルを含める\n"
#~ " -x RX, --exclude RX    マッチするファイルを除外する\n"
#~ "                        RX は展開された正規表現\n"
#~ " -t Nx, --time-prec Nx  時間の精度を N 秒 (x=s)、N 分 (x=m)、\n"
#~ "                        N 時間 (x=h)、N 日 (x=d) にセット\n"
#~ "                        デフォルト - mirror:time-precision の設定\n"
#~ " -T Nx, --loose-time-prec  不正確な時間のための時間の精度をセット\n"
#~ "                        デフォルト - mirror:loose-time-precision\n"
#~ " -v, --verbose[=N]      冗長な操作\n"
#~ "     --use-cache        キャッシュされたディレクトリの一覧を使う\n"
#~ "\n"
#~ "-R を使う際、最初のディレクトリはローカルで次のディレクトリがリモートにな"
#~ "ります。\n"
#~ "第二のディレクトリが省略された場合には、最初のディレクトリのベースネームが"
#~ "使われます。\n"
#~ "両方のディレクトリが省略された場合、現在のローカルおよびリモートディレクト"
#~ "リが\n"
#~ "使われます。\n"

#, fuzzy
#~ msgid "SITE CHMOD is disabled by ftp:use-site-chmod"
#~ msgstr "SITE CHMOD はこのサイトではサポートされていません"

#~ msgid "ftp:ssl-auth must be one of: SSL, TLS, TLS-P, TLS-C"
#~ msgstr ""
#~ "ftp:ssl-auth は以下のどれかでなければなりません: SSL、TLS、TLS-P、TLS-C"

#, fuzzy
#~ msgid "invalid pair of numbers"
#~ msgstr "不正な数"

#~ msgid "Saw `unknown', assume failed login"
#~ msgstr "`unknown' を検出しましたので、ログインに失敗したと仮定します"

#~ msgid "Can only edit plain queues.\n"
#~ msgstr "プレーンなキューのみ編集できます。\n"

#~ msgid "Couldn't create temporary file `%s': %s.\n"
#~ msgstr "一時ファイル `%s' が作成できませんでした: %s。\n"

#~ msgid "%s: error writing %s: %s\n"
#~ msgstr "%s: %s の書き込みでエラー: %s\n"

#, fuzzy
#~ msgid "block size"
#~ msgstr "不正な数"

#~ msgid "%sTo be removed: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
#~ msgstr ""
#~ "%s削除予定: %d 個のディレクトリ、%d 個のファイル、%d 個のシンボリックリン"
#~ "ク\n"

#~ msgid "Usage: %s userid [pass]\n"
#~ msgstr "使い方: %s userid [pass]\n"

#~ msgid "Cache is on"
#~ msgstr "キャッシュは有効"

#~ msgid "Cache is off"
#~ msgstr "キャッシュは無効"

#~ msgid "Cache entries do not expire"
#~ msgstr "キャッシュエントリは失効しません"

#~ msgid "Cache entries expire in %ld $#l#second|seconds$\n"
#~ msgstr "キャッシュエントリは %ld 秒で無効になります\n"

#~ msgid "Cache entries expire in %ld $#l#minute|minutes$\n"
#~ msgstr "キャッシュエントリは %ld 分で無効になります\n"

#~ msgid ""
#~ "This is free software with ABSOLUTELY NO WARRANTY. See COPYING for "
#~ "details.\n"
#~ msgstr ""
#~ "lftp はフリーソフトウェアであり、*全くの無保証*です。詳しくは COPYING を参"
#~ "照。\n"

#~ msgid "Success, but did nothing??"
#~ msgstr "成功しましたが、何もしなかったのかな??"

#, fuzzy
#~ msgid "%s: %s: %s\n"
#~ msgstr "%s: %s: %s\n"

#~ msgid "Query of variable `%s' failed: %s\n"
#~ msgstr "変数 `%s' の問い合わせに失敗しました: %s\n"

#~ msgid ""
#~ "Enter date, or blank line to exit.\n"
#~ "\t> "
#~ msgstr ""
#~ "日付を入力してください。空行で終了します。\n"
#~ "\t> "

#~ msgid "Bad format - couldn't convert.\n"
#~ msgstr "不正な形式 -  変換できません。\n"

#~ msgid "%s: Invalid number for size\n"
#~ msgstr "%s: サイズとしては不正な数です\n"

#~ msgid "Warning: getcwd() failed: %s\n"
#~ msgstr "警告: getcwd() が失敗しました: %s\n"

#~ msgid "No directory to execute commands in - terminating\n"
#~ msgstr "コマンドを実行するディレクトリが存在しません - 終了します\n"

#~ msgid "chain output error"
#~ msgstr "チェーン出力エラー"

#~ msgid "Usage: %s mode file...\n"
#~ msgstr "使い方: %s mode file...\n"

#~ msgid "%s: %s - not an octal number\n"
#~ msgstr "%s: %s - 8進数ではありません\n"

#~ msgid "Removing old remote file `%s'"
#~ msgstr "古いリモートファイル `%s' を削除しています"

#~ msgid "Retrieving remote file `%s'"
#~ msgstr "リモートファイル `%s' を取得しています"

#~ msgid "mirror: cannot create `file:' access object, installation error?\n"
#~ msgstr ""
#~ "mirror: `file:' アクセスオブジェクトを作成できません。インストール時のエ"
#~ "ラーかも?\n"

#~ msgid "copy: get position was %lld\n"
#~ msgstr "copy: get の位置は %lld でした\n"

#~ msgid "copy: get hit eof\n"
#~ msgstr "copy: get が eof に達しました\n"

#~ msgid "copy: get reached range limit\n"
#~ msgstr "copy: get が範囲制限に達しました\n"

#~ msgid "copy: put confirmed store\n"
#~ msgstr "copy: put が store を確認しました\n"

#~ msgid "copy: get is finished - all done\n"
#~ msgstr "copy: get が終了しました - 全て完了\n"

#~ msgid "copy src: seek past eof (seek_pos=%lld, size=%lld)\n"
#~ msgstr "copy src: 過去の eof を探索 (seek_pos=%lld, size=%lld)\n"

#~ msgid "nlist [<args>]"
#~ msgstr "nlist [<args>]"

#~ msgid "List remote file names\n"
#~ msgstr "リモートファイルの名前を列挙します\n"

#~ msgid "Same as `get -c'\n"
#~ msgstr "`get -c' と同じ\n"

#~ msgid "rels [<args>]"
#~ msgstr "rels [<args>]"

#~ msgid "Same as `put -c'\n"
#~ msgstr "`put -c' と同じです\n"

#~ msgid "bzcat <files>"
#~ msgstr "bzcat <files>"

#~ msgid "bzmore <files>"
#~ msgstr "bzmore <files>"
