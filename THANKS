The following people contributed to lftp in various ways: testing,
suggesting useful improvements, sending patches. Listed in abc order.

<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>> for testing and suggestions.
<PERSON> <<EMAIL>>
<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
<PERSON><PERSON> <Ark<PERSON>.<PERSON>@ksu.ru>
<PERSON><PERSON> <<EMAIL>>
<PERSON><PERSON> <<EMAIL>> for testing with an ftp proxy, patch.
<PERSON> <<EMAIL>>
<PERSON><PERSON><PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>> for initial man pages
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <david<PERSON>@wolfeden.org>
<PERSON><PERSON> <<EMAIL>>
<PERSON> <<EMAIL>> for testing and suggestions.
<PERSON> Exarevsky <<EMAIL>>
Fernando Gutierrez <<EMAIL>>
Ganael LAPLANCHE <<EMAIL>>
Gabriele Balducci <<EMAIL>>
gARetH baBB <<EMAIL>>
Glenn F. Maynard <<EMAIL>> for du, cls, major code contributions.
Henry Hu <<EMAIL>>
Hugo Van den Berg <<EMAIL>> for tip about quote command, patch.
Igor Vinokurov <<EMAIL>> for suggestions.
Iida <<EMAIL>>
J Kalevi Suominen <<EMAIL>>
James Troup <<EMAIL>> for documentation update, other patches.
Jason Vas Dias <<EMAIL>> for Red Hat package maintenance; patches.
Jim Pick <<EMAIL>>
Jiri Skala <<EMAIL>>
Jonas Jensen <<EMAIL>>
Julien Oster <<EMAIL>>
Justin Piszcz <<EMAIL>>
Laurent MONIN <<EMAIL>>
Ludwig Nussel <<EMAIL>>
Manoj Kasichainula <<EMAIL>>
Marc Bevand <<EMAIL>>
Marco Calmar <<EMAIL>>
Mark S Bilk <<EMAIL>>
Mark Pulford <<EMAIL>>
Matt E Richards <<EMAIL>> for a fix.
Matthias Andree <<EMAIL>>
Matthias Fetzer <<EMAIL>>
Michael Handler <<EMAIL>>
Michael Ross <<EMAIL>>
Miroslav Lichvar <<EMAIL>>
Naofumi Yasufuku <<EMAIL>>
Nate Sutton <<EMAIL>>
Nicolás Lichtmaier <<EMAIL>> for i18n, Debian package maintenance.
Nicolas Noble <<EMAIL>>
Nix <<EMAIL>>
OGAWA Hirofumi <<EMAIL>>
Pascal Bleser <<EMAIL>>
Robert de Bath <<EMAIL>>
Robert Story <<EMAIL>>
Roger Pixley <<EMAIL>>
Ryan Thomas <<EMAIL>>
Sam Steingold <<EMAIL>>
Sami Farin <<EMAIL>>
Serge Bezzubov <<EMAIL>> for extensive testing.
Simon Ruderich <<EMAIL>>
Slama YA <<EMAIL>>
Solar Designer <<EMAIL>>
Thomas Glanzmann <<EMAIL>>
Tillmann Steinbrecher <<EMAIL>>
Timur Sufiev <<EMAIL>>
Uhus <<EMAIL>> for testing and suggestions.
Victor Sudakov <<EMAIL>>
Yann Rouillard <<EMAIL>>

bash developers for pieces of code and example of how to use readline.

Thanks to all who wrote to me about lftp.
