## some useful aliases
alias dir ls
alias less more
alias z<PERSON> <PERSON><PERSON>
alias b<PERSON><PERSON> b<PERSON><PERSON>
alias reconnect "close; cache flush; cd ."

## make prompt look better
set prompt "lftp \S\? \u\@\h:\w> "
## some may prefer colors (contributed by <PERSON> <m<PERSON><PERSON>@optusnet.com.au>)
#set prompt "\[\e[1;30m\][\[\e[0;34m\]f\[\e[1m\]t\[\e[37m\]p\[\e[30m\]] \[\e[34m\]\u\[\e[0;34m\]\@\[\e[1m\]\h\[\e[1;30m\]:\[\e[1;34m\]\w\[\e[1;30m\]>\[\e[0m\] "
## Uncomment the following two lines to make switch cls and ls, making
## cls the default.
#alias ls command cls
#alias hostls command ls

## default protocol selection
#set default-protocol/ftp.*	ftp
#set default-protocol/www.*	http
#set default-protocol/localhost	file

## this makes lftp faster but doesn't work with some sites/routers
#set ftp:sync-mode off

## synchronous mode for broken servers and/or routers
set sync-mode/ftp.idsoftware.com on
set sync-mode/ftp.microsoft.com on
set sync-mode/sunsolve.sun.com on
## extended regex to match first server message for automatic sync-mode.
set auto-sync-mode "icrosoft FTP Service|MadGoat|MikroTik"

## if default ftp passive mode does not work, try this:
# set ftp:passive-mode off

## Set this to follow http redirections
set xfer:max-redirections 10

## Proxy can help to pass a firewall
## Environment variables ftp_proxy, http_proxy and no_proxy are used to
## initialize the below variables automatically. You can set them here too.
##
## ftp:proxy must communicate with client over ftp protocol, squid won't do.
## This can be e.g. TIS-FWTK or rftpd. User and password are optional.
# set ftp:proxy ftp://[user:pass@]your_ftp_proxy:port
## ...but squid still can be used to access ftp servers, using hftp protocol:
# set ftp:proxy http://your.squid.address:port
## ...if squid allows CONNECT to arbitrary ports, then you can use CONNECT
## instead of hftp:
# set ftp:use-hftp no
##
## no proxy for host
# set ftp:proxy/local_host ""
## or domain
# set ftp:proxy/*.domain.com ...
##
## http:proxy must communicate with client over http protocol, e.g. squid.
## Default port is 3128.
# set http:proxy your_http_proxy[:port]
## hftp:proxy must also be an http proxy. It is used for FTP over HTTP access.
# set hftp:proxy your_http_proxy[:port]
##
## net:no-proxy disables proxy usage for list of domains.
# set net:no-proxy .domain.com,.otherdom.net

## If you don't have direct ftp access, this setting can be useful to select
## hftp instead of ftp automatically.
# set ftp:proxy http://your.http.proxy:port

## This can be used for automatic saving of configuration
# set at-exit "set > ~/.lftp/settings"
# source ~/.lftp/settings

## and this is for remembring last site
## (combine with previous rule if you want)
# set at-exit "bo a last"
# open last

## Terminal strings to set titlebars for terminals that don't
## properly specify tsl and fsl capabilities.
## Use cmd:set-term-status to enable this.
set cmd:term-status/*screen* "\e_\T\e\\"
set cmd:term-status/*xterm* "\e[11;0]\e]2;\T\007\e[11]"
set cmd:term-status/*rxvt* "\e[11;0]\e]2;\T\007\e[11]"
# set cmd:set-term-status on

## If you don't like advertising lftp or servers hate it, set this:
# set ftp:anon-pass "mozilla@"
# set ftp:client ""
# set http:user-agent "Mozilla/4.7 [en] (WinNT; I)"
