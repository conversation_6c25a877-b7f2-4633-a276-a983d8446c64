/00gnulib.m4
/alloca.m4
/argmatch.m4
/arpa_inet_h.m4
/bison.m4
/btowc.m4
/chown.m4
/clock_time.m4
/codeset.m4
/configmake.m4
/dirent_h.m4
/dirfd.m4
/dos.m4
/d-type.m4
/dup2.m4
/environ.m4
/eoverflow.m4
/errno_h.m4
/error.m4
/exitfail.m4
/exponentd.m4
/exponentf.m4
/exponentl.m4
/extensions.m4
/fcntl_h.m4
/fcntl.m4
/fcntl-o.m4
/filemode.m4
/float_h.m4
/fnmatch.m4
/fpieee.m4
/frexpl.m4
/frexp.m4
/ftruncate.m4
/getdate.m4
/getdelim.m4
/getdtablesize.m4
/getline.m4
/getlogin_r.m4
/getopt.m4
/getpagesize.m4
/gettime.m4
/gettimeofday.m4
/glibc21.m4
/glob.m4
/gnulib-common.m4
/gnulib-comp.m4
/gnulib-tool.m4
/human.m4
/iconv_h.m4
/iconv.m4
/iconv_open.m4
/include_next.m4
/inet_ntop.m4
/inet_pton.m4
/inline.m4
/intmax_t.m4
/inttypes_h.m4
/inttypes-h.m4
/inttypes.m4
/isc-posix.m4
/isnand.m4
/isnanf.m4
/isnanl.m4
/langinfo_h.m4
/largefile.m4
/lchown.m4
/ldexpl.m4
/lib-ld.m4
/lib-link.m4
/lib-prefix.m4
/libtool.m4
/libunistring-base.m4
/localcharset.m4
/locale-fr.m4
/locale-ja.m4
/locale-zh.m4
/lock.m4
/longdouble.m4
/lstat.m4
/lt~obsolete.m4
/ltoptions.m4
/ltsugar.m4
/ltversion.m4
/malloca.m4
/malloc.m4
/math_h.m4
/mbrlen.m4
/mbrtowc.m4
/mbsinit.m4
/mbsrtowcs.m4
/mbstate_t.m4
/mbswidth.m4
/mbtowc.m4
/md5.m4
/memchr.m4
/memcmp.m4
/memmem.m4
/memmove.m4
/mempcpy.m4
/mktime.m4
/mmap-anon.m4
/modechange.m4
/mode_t.m4
/multiarch.m4
/netinet_in_h.m4
/nl_langinfo.m4
/nocrash.m4
/open.m4
/parse-datetime.m4
/passfd.m4
/pathmax.m4
/poll_h.m4
/poll.m4
/printf-frexpl.m4
/printf-frexp.m4
/printf.m4
/quotearg.m4
/quote.m4
/readlink.m4
/realloc.m4
/regex.m4
/select.m4
/setenv.m4
/sha1.m4
/signal_h.m4
/signbit.m4
/signed.m4
/size_max.m4
/socketlib.m4
/sockets.m4
/socklen.m4
/sockpfaf.m4
/ssize_t.m4
/stat.m4
/stddef_h.m4
/stdint_h.m4
/stdint.m4
/stdio_h.m4
/stdlib_h.m4
/st_dm_mode.m4
/strcase.m4
/strdup.m4
/strerror.m4
/string_h.m4
/strings_h.m4
/strptime.m4
/strstr.m4
/strtoimax.m4
/strtok_r.m4
/strtoll.m4
/strtol.m4
/strtoull.m4
/strtoul.m4
/strtoumax.m4
/sys_select_h.m4
/sys_socket_h.m4
/sys_stat_h.m4
/sys_time_h.m4
/sys_types_h.m4
/sys_uio_h.m4
/threadlib.m4
/time_h.m4
/time_r.m4
/timespec.m4
/tm_gmtoff.m4
/ulonglong.m4
/unistd_h.m4
/vasnprintf.m4
/vsnprintf.m4
/vsnprintf-posix.m4
/warn-on-use.m4
/wchar_h.m4
/wchar.m4
/wcrtomb.m4
/wctype_h.m4
/wctype.m4
/wcwidth.m4
/wint_t.m4
/xalloc.m4
/xsize.m4
/xstrtol.m4
/closedir.m4
/fstat.m4
/msvc-inval.m4
/msvc-nothrow.m4
/opendir.m4
/readdir.m4
/stdalign.m4
/close.m4
/locale_h.m4
/localeconv.m4
/off_t.m4
/fdopen.m4
/getcwd.m4
/getgroups.m4
/getugroups.m4
/ioctl.m4
/locale-tr.m4
/localename.m4
/mgetgroups.m4
/perror.m4
/putenv.m4
/setlocale.m4
/sleep.m4
/stat-time.m4
/strerror_r.m4
/symlink.m4
/sys_ioctl_h.m4
/sys_wait_h.m4
/thread.m4
/usleep.m4
/wctob.m4
/wctomb.m4
/yield.m4
/extern-inline.m4
/absolute-header.m4
/gl-openssl.m4
/flexmember.m4
/time_rz.m4
/timegm.m4
/memcasecmp.m4
/double-slash-root.m4
/getprogname.m4
/limits-h.m4
/builtin-expect.m4
/getlogin.m4
/minmax.m4
/pthread_rwlock_rdlock.m4
/tzset.m4
/__inline.m4
/nstrftime.m4
/open-cloexec.m4
/std-gnu11.m4
/host-cpu-c-abi.m4
/af_alg.m4
/byteswap.m4
/fflush.m4
/fpurge.m4
/freading.m4
/fseek.m4
/fseeko.m4
/ftell.m4
/ftello.m4
/lseek.m4
/fnmatch_h.m4
/glob_h.m4
/open-slash.m4
/setlocale_null.m4
/visibility.m4
/ctype.m4
/isblank.m4
/strnlen.m4
/wmemchr.m4
/wmempcpy.m4
/zzgnulib.m4
/calloc.m4
/chdir-long.m4
/ctype_h.m4
/fchdir.m4
/filenamecat.m4
/free.m4
/fstatat.m4
/memrchr.m4
/openat.m4
/pid_t.m4
/reallocarray.m4
/save-cwd.m4
/ungetc.m4
/vararrays.m4
/assert_h.m4
/c-bool.m4
/build-to-host.m4
/c32rtomb.m4
/error_h.m4
/extensions-aix.m4
/intl-thread-locale.m4
/iswblank.m4
/iswctype.m4
/iswdigit.m4
/iswpunct.m4
/iswxdigit.m4
/lcmessage.m4
/locale-en.m4
/mbrtoc32.m4
/musl.m4
/off64_t.m4
/once.m4
/pthread-once.m4
/pthread-spin.m4
/pthread_h.m4
/sched_h.m4
/sys_cdefs_h.m4
/uchar_h.m4
/unicase_h.m4
/unictype_h.m4
/uninorm_h.m4
