# Polish translation of lftp.
# Copyright (C) 1998-2000 Free Software Foundation, Inc.
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 1998-2004
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2002-2016
# some updates by <PERSON><PERSON> <<EMAIL>>, 2003
# <PERSON> <<EMAIL>>, 2004-2006
#
msgid ""
msgstr ""
"Project-Id-Version: lftp 4.7.2\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2024-11-08 13:05+0300\n"
"PO-Revision-Date: 2016-05-20 15:00+0200\n"
"Last-Translator: J<PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Polish <<EMAIL>>\n"
"Language: pl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: lib/argmatch.c:145
#, c-format
msgid "invalid argument %s for %s"
msgstr "błędny argument %s opcji %s"

#: lib/argmatch.c:146
#, c-format
msgid "ambiguous argument %s for %s"
msgstr "niejednoznaczny argument %s opcji %s"

#: lib/argmatch.c:165
msgid "Valid arguments are:"
msgstr "Prawidłowe argumenty to:"

#: lib/error.c:208
msgid "Unknown system error"
msgstr "Nieznany błąd systemowy"

#: lib/getopt.c:282
#, fuzzy, c-format
msgid "%s: option '%s%s' is ambiguous\n"
msgstr "%s: opcja '%s' jest niejednoznaczna\n"

#: lib/getopt.c:288
#, fuzzy, c-format
msgid "%s: option '%s%s' is ambiguous; possibilities:"
msgstr "%s: opcja '%s' jest niejednoznaczna; możliwości:"

#: lib/getopt.c:322
#, fuzzy, c-format
msgid "%s: unrecognized option '%s%s'\n"
msgstr "%s: nieznana opcja '%c%s'\n"

#: lib/getopt.c:348
#, fuzzy, c-format
msgid "%s: option '%s%s' doesn't allow an argument\n"
msgstr "%s: opcja '%c%s' nie może mieć argumentów\n"

#: lib/getopt.c:363
#, fuzzy, c-format
msgid "%s: option '%s%s' requires an argument\n"
msgstr "%s: opcja '--%s' musi mieć argument\n"

#: lib/getopt.c:624
#, c-format
msgid "%s: invalid option -- '%c'\n"
msgstr "%s: błędna opcja -- '%c'\n"

#: lib/getopt.c:639 lib/getopt.c:685
#, c-format
msgid "%s: option requires an argument -- '%c'\n"
msgstr "%s: opcja musi mieć argument -- '%c'\n"

#. TRANSLATORS:
#. Get translations for open and closing quotation marks.
#. The message catalog should translate "`" to a left
#. quotation mark suitable for the locale, and similarly for
#. "'".  For example, a French Unicode local should translate
#. these to U+00AB (LEFT-POINTING DOUBLE ANGLE
#. QUOTATION MARK), and U+00BB (RIGHT-POINTING DOUBLE ANGLE
#. QUOTATION MARK), respectively.
#.
#. If the catalog has no translation, we will try to
#. use Unicode U+2018 (LEFT SINGLE QUOTATION MARK) and
#. Unicode U+2019 (RIGHT SINGLE QUOTATION MARK).  If the
#. current locale is not Unicode, locale_quoting_style
#. will quote 'like this', and clocale_quoting_style will
#. quote "like this".  You should always include translations
#. for "`" and "'" even if U+2018 and U+2019 are appropriate
#. for your locale.
#.
#. If you don't know what to put here, please see
#. <https://en.wikipedia.org/wiki/Quotation_marks_in_other_languages>
#. and use glyphs suitable for your language.
#: lib/quotearg.c:354
msgid "`"
msgstr "`"

#: lib/quotearg.c:355
msgid "'"
msgstr "'"

#: lib/xalloc-die.c:34
msgid "memory exhausted"
msgstr "pamięć wyczerpana"

#: src/ArgV.cc:107
#, fuzzy
msgid "option requires an argument"
msgstr "%s: opcja '--%s' musi mieć argument\n"

#: src/ArgV.cc:109 src/ArgV.cc:118
#, fuzzy
msgid "invalid option"
msgstr "%s: błędna opcja -- '%c'\n"

#: src/ArgV.cc:114
#, fuzzy, c-format
msgid "option `%s' requires an argument"
msgstr "%s: opcja '--%s' musi mieć argument\n"

#: src/ArgV.cc:116
#, fuzzy, c-format
msgid "unrecognized option `%s'"
msgstr "%s: nieznana opcja '--%s'\n"

#: src/attach.h:138
#, c-format
msgid "[%u] Attached to terminal %s. %s\n"
msgstr "[%u] Dołączono do terminala %s. %s\n"

#: src/attach.h:150
#, c-format
msgid "[%u] Attached to terminal.\n"
msgstr "[%u] Dołączono do terminala.\n"

#: src/buffer.cc:253 src/FileAccess.cc:866
msgid " [cached]"
msgstr " [zapamiętane]"

#: src/ChmodJob.cc:80
#, c-format
msgid "Failed to change mode of `%s' to %04o (%s).\n"
msgstr "Zmiana uprawnień dla `%s' na %04o (%s) nie powiodła się.\n"

#: src/ChmodJob.cc:83
#, c-format
msgid "Mode of `%s' changed to %04o (%s).\n"
msgstr "Uprawnienia `%s' zmienione na %04o (%s).\n"

#: src/ChmodJob.cc:88
#, c-format
msgid "Failed to change mode of `%s' because no old mode is available.\n"
msgstr ""
"Zmiana uprawnień dla `%s' nie powiodła się ponieważ brak\n"
"informacji o starych uprawnieniach.\n"

#: src/CmdExec.cc:105
#, c-format
msgid "Warning: chdir(%s) failed: %s\n"
msgstr "Uwaga: chdir(%s) nie powiodło się: %s\n"

#: src/CmdExec.cc:207
#, c-format
msgid "Unknown command `%s'.\n"
msgstr "Nieznana komenda `%s'.\n"

#: src/CmdExec.cc:209
#, c-format
msgid "Ambiguous command `%s'.\n"
msgstr "Dwuznaczne polecenie `%s'.\n"

#: src/CmdExec.cc:228
#, c-format
msgid "Module for command `%s' did not register the command.\n"
msgstr "Moduł dla komendy `%s' nie zarejestrował komendy.\n"

#: src/CmdExec.cc:384
#, c-format
msgid "cd ok, cwd=%s\n"
msgstr "cd ok, cwd=%s\n"

#: src/CmdExec.cc:405 src/MirrorJob.cc:736
#, c-format
msgid "%s: received redirection to `%s'\n"
msgstr "%s: otrzymano przekierowanie do `%s'\n"

#: src/CmdExec.cc:408 src/FileCopy.cc:1230
msgid "Too many redirections"
msgstr "Zbyt dużo przekierowań"

#: src/CmdExec.cc:517 src/CmdExec.cc:574
msgid "Interrupt"
msgstr "Przerwanie"

#: src/CmdExec.cc:639
#, c-format
msgid "Warning: discarding incomplete command\n"
msgstr "Uwaga: niekompletna komenda została odrzucona\n"

#: src/CmdExec.cc:737
#, c-format
msgid "\tExecuting builtin `%s' [%s]\n"
msgstr "\tUruchamianie wbudowanego `%s' [%s]\n"

#: src/CmdExec.cc:742
msgid "Queue is stopped."
msgstr "Kolejka jest zatrzymana."

#: src/CmdExec.cc:747
msgid "Now executing:"
msgstr "Aktualnie wykonuję:"

#: src/CmdExec.cc:758
#, c-format
msgid "\tWaiting for job [%d] to terminate\n"
msgstr "\tOczekiwanie na zakończenie zadania [%d]\n"

#: src/CmdExec.cc:761
#, c-format
msgid "\tWaiting for termination of jobs: "
msgstr "\tOczekiwanie na zakończenie prac: "

#: src/CmdExec.cc:770
msgid "\tRunning\n"
msgstr "\tUruchamianie\n"

#: src/CmdExec.cc:772
msgid "\tWaiting for command\n"
msgstr "\tOczekiwanie na komendę\n"

#: src/CmdExec.cc:1261
#, c-format
msgid "%s: command `%s' is not compiled in.\n"
msgstr "%s: komenda `%s' nie została wkompilowana.\n"

#: src/CmdExec.cc:1278
#, c-format
msgid "Usage: %s cmd [args...]\n"
msgstr "Użycie: %s polecenie [argumenty...]\n"

#: src/CmdExec.cc:1284
#, c-format
msgid "%s: cannot create local session\n"
msgstr "%s: nie można utworzyć sesji lokalnej\n"

#: src/commands.cc:114
msgid "!<shell-command>"
msgstr "!<komenda_powłoki>"

#: src/commands.cc:115
msgid "Launch shell or shell command\n"
msgstr "Uruchom powłokę lub polecenie powłoki\n"

#: src/commands.cc:116
msgid "(commands)"
msgstr "(polecenia)"

#: src/commands.cc:117
msgid ""
"Group commands together to be executed as one command\n"
"You can launch such a group in background\n"
msgstr ""
"Grupa poleceń uruchamiana jako jedne polecenie\n"
"Możesz uruchomić taką grupę w tle\n"

#: src/commands.cc:120
msgid "alias [<name> [<value>]]"
msgstr "alias [<nazwa> [<wartość>]]"

#: src/commands.cc:121
msgid ""
"Define or undefine alias <name>. If <value> omitted,\n"
"the alias is undefined, else is takes the value <value>.\n"
"If no argument is given the current aliases are listed.\n"
msgstr ""
"Dodaj lub usuń alias <nazwa>. Jeżeli opuścisz <wartość>\n"
"definicja aliasu zostanie usunięta, w przeciwnym razie, przyjmie wartość\n"
"<wartość>. Jeżeli nie podasz argumentu, uzyskasz listę wszystkich aliasów.\n"

#: src/commands.cc:125
msgid "anon - login anonymously (by default)\n"
msgstr "anon - anonimowe logowanie (domyślnie)\n"

#: src/commands.cc:127
msgid "bookmark [SUBCMD]"
msgstr "bookmark [PARAM]"

#: src/commands.cc:128
msgid ""
"bookmark command controls bookmarks\n"
"\n"
"The following subcommands are recognized:\n"
"  add <name> [<loc>] - add current place or given location to bookmarks\n"
"                       and bind to given name\n"
"  del <name>         - remove bookmark with the name\n"
"  edit               - start editor on bookmarks file\n"
"  import <type>      - import foreign bookmarks\n"
"  list               - list bookmarks (default)\n"
msgstr ""
"polecenie bookmark zarządza zakładkami\n"
"\n"
"Następujące parametry są stosowane:\n"
"  add <nazwa> [<lok>] - dodaj aktualne miejsce lub podaną lokalizację do\n"
"                        zakładek i skojarz z daną nazwą\n"
"  del <nazwa>         - usuń zakładkę o podanej nazwie\n"
"  edit                - edytuj plik zakładek\n"
"  import <typ>        - importuj obce pliki zakładek (netscape/ncftp)\n"
"  list                - spis zakładek (domyślnie)\n"

#: src/commands.cc:137
msgid "cache [SUBCMD]"
msgstr "cache [PARAM]"

#: src/commands.cc:138
msgid ""
"cache command controls local memory cache\n"
"\n"
"The following subcommands are recognized:\n"
"  stat        - print cache status (default)\n"
"  on|off      - turn on/off caching\n"
"  flush       - flush cache\n"
"  size <lim>  - set memory limit\n"
"  expire <Nx> - set cache expiration time to N seconds (x=s)\n"
"                minutes (x=m) hours (x=h) or days (x=d)\n"
msgstr ""
"polecenie cache kontroluje lokalną pamięć podręczną\n"
"\n"
"Poniższe parametry są rozpoznawane:\n"
"  stat        - wyświetl status bufora (domyślnie)\n"
"  on|off      - włącz/wyłącz buforowanie\n"
"  flush       - wyczyść bufor\n"
"  size <lim>  - ustaw limit pamięci\n"
"  expire <Nx> - ustal czas po jakim kasują się wpisy w buforze na N sekund\n"
"                (x=s), minut (x=m), godzin (x=h) lub dni (x=d)\n"

#: src/commands.cc:146
msgid "cat [-b] <files>"
msgstr "cat [-b] <pliki>"

#: src/commands.cc:147
msgid ""
"cat - output remote files to stdout (can be redirected)\n"
" -b  use binary mode (ascii is the default)\n"
msgstr ""
"cat - wyświetla zdalne pliki do stdout (można przekierowywać)\n"
" -b  użyj trybu binarnego (domyślnie używa ascii)\n"

#: src/commands.cc:149
msgid "cd <rdir>"
msgstr "cd <zd_kat>"

#: src/commands.cc:150
msgid ""
"Change current remote directory to <rdir>. The previous remote directory\n"
"is stored as `-'. You can do `cd -' to change the directory back.\n"
"The previous directory for each site is also stored on disk, so you can\n"
"do `open site; cd -' even after lftp restart.\n"
msgstr ""
"Zmienia zdalny katalog na <zd_kat>. Poprzedni zdalny katalog jest "
"zapamiętany\n"
"jako `-'. Możesz napisać `cd -' by powrócić do poprzedniego katalogu. "
"Poprzedni\n"
"katalog każdego serwera jest także zapamiętywany na dysku, więc możesz "
"wykonać\n"
"`open site; cd -' nawet po restarcie lftp.\n"

#: src/commands.cc:154
msgid "chmod [OPTS] mode file..."
msgstr "chmod [OPCJE] tryb plik..."

#: src/commands.cc:155
msgid ""
"Change the mode of each FILE to MODE.\n"
"\n"
" -c, --changes        - like verbose but report only when a change is made\n"
" -f, --quiet          - suppress most error messages\n"
" -v, --verbose        - output a diagnostic for every file processed\n"
" -R, --recursive      - change files and directories recursively\n"
"\n"
"MODE can be an octal number or symbolic mode (see chmod(1))\n"
msgstr ""
"Zmieniaj tryb dla każdego PLIKU na TRB.\n"
"\n"
" -c, --changes        - jak verbose, ale informuj tylko gdy dokonano zmiany\n"
" -f, --quiet          - pomiń większość komunikatów o błędach\n"
" -v, --verbose        - wyświetlaj diagnostyczne informacja dla każdego "
"pliku\n"
" -R, --recursive      - zmieniaj pliki i katalogi rekursywnie\n"
"\n"
"TRYB może być numerem ósemkowym lub symbolicznym trybem (sprawdź chmod(1))\n"

#: src/commands.cc:164
msgid ""
"Close idle connections. By default only with current server.\n"
" -a  close idle connections with all servers\n"
msgstr ""
"Zamyka bezczynne połączenia. Domyślnie tylko z bieżącym serwerem.\n"
" -a  zamknij bezczynne połączenia ze wszystkimi serwerami\n"

#: src/commands.cc:166
msgid "[re]cls [opts] [path/][pattern]"
msgstr "[re]cls [opcje] [ścieżka/][maska]"

#: src/commands.cc:167
msgid ""
"List remote files. You can redirect output of this command to file\n"
"or via pipe to external command.\n"
"\n"
" -1                   - single-column output\n"
" -a, --all            - show dot files\n"
" -B, --basename       - show basename of files only\n"
"     --block-size=SIZ - use SIZ-byte blocks\n"
" -d, --directory      - list directory entries instead of contents\n"
" -F, --classify       - append indicator (one of /@) to entries\n"
" -h, --human-readable - print sizes in human readable format (e.g., 1K)\n"
"     --si             - likewise, but use powers of 1000 not 1024\n"
" -k, --kilobytes      - like --block-size=1024\n"
" -l, --long           - use a long listing format\n"
" -q, --quiet          - don't show status\n"
" -s, --size           - print size of each file\n"
"     --filesize       - if printing size, only print size for files\n"
" -i, --nocase         - case-insensitive pattern matching\n"
" -I, --sortnocase     - sort names case-insensitively\n"
" -D, --dirsfirst      - list directories first\n"
"     --sort=OPT       - \"name\", \"size\", \"date\"\n"
" -S                   - sort by file size\n"
" --user, --group, --perms, --date, --linkcount, --links\n"
"                      - show individual fields\n"
" --time-style=STYLE   - use specified time format\n"
"\n"
"By default, cls output is cached, to see new listing use `recls' or\n"
"`cache flush'.\n"
"\n"
"The variables cls-default and cls-completion-default can be used to\n"
"specify defaults for cls listings and completion listings, respectively.\n"
"For example, to make completion listings show file sizes, set\n"
"cls-completion-default to \"-s\".\n"
"\n"
"Tips: Use --filesize with -D to pack the listing better.  If you don't\n"
"always want to see file sizes, --filesize in cls-default will affect the\n"
"-s flag on the commandline as well.  Add `-i' to cls-completion-default\n"
"to make filename completion case-insensitive.\n"
msgstr ""
"Wyświetl zdalne pliki. Możesz przekierować wyjście tego polecenia do pliku\n"
"lub poprzez potok do zewnętrznej polecenia.\n"
"\n"
" -1                   - wyświetlaj w jednej kolumnie\n"
" -a, --all            - wyświetlaj pliki z kropką\n"
" -B, --basename       - wyświetlaj jedynie nazwę bazową plików\n"
"     --block-size=ROZ - użyj bloków o rozmiarze ROZ bajtów\n"
" -d, --directory      - wyświetlaj listę katalogów zamiast ich zawartości\n"
" -F, --classify       - dodaj wskaźnik (jeden z /@) do listy\n"
" -h, --human-readable - wyświetlaj rozmiary w formie czytelnej dla "
"człowieka\n"
"     --si             - jak wyżej używając potęg 1000 zamiast 1024\n"
" -k, --kilobytes      - jak --block-size=1024\n"
" -l, --long           - używaj długiego formatu listy\n"
" -q, --quiet          - nie wyświetlaj statusu\n"
" -s, --size           - wyświetlaj rozmiar każdego pliku\n"
"     --filesize       - wyświetlaj rozmiar jedynie dla plików\n"
" -i, --nocase         - sprawdzanie wzorca bez rozróżniania wielkości "
"znaków\n"
" -I, --sortnocase     - sortuj nazwy bez rozróżniania wielkości znaków\n"
" -D, --dirsfirst      - w pierwszej kolejności wyświetlaj katalogi\n"
"     --sort=OPC       - \"name\", \"size\", \"date\"\n"
" -S                   - sortuj wg rozmiaru pliku\n"
" --user, --group, --perms, --date, --linkcount, --links\n"
"                      - wyświetlaj poszczególne pola\n"
" --time-style=STYL    - użyj określonego formatu czasu\n"
"\n"
"Domyślnie, wyjście komendy cls jest zapamiętywane w pamięci podręcznej. By\n"
"zobaczyć nowe listy plików użyj komendy `recls' lub `cache flush'.\n"
"\n"
"Zmienne cls-default i cls-completion-default mogą być użyte do określenia\n"
"domyślnych parametrów dla listingów \"cls\" względnie listingów "
"\"completion\".\n"
"Na przykład, aby listing \"completion\" pokazywał rozmiary plików ustaw:\n"
"cls-completion-default na \"-s\".\n"
"\n"
"Porady: Użyj --filesize z opcją -D by lepiej upakować listing. Jeśli nie\n"
"zawsze chce się oglądać rozmiary plików, --filesize w cls-default wpłynie\n"
"także na flagę -s z linii poleceń. Dodanie `-i' do cls-completion-default\n"
"uczyni dopełnianie nazw plików nie rozpoznającym wielkości liter.\n"

#: src/commands.cc:217
msgid "debug [OPTS] [<level>|off]"
msgstr "debug [OPCJE] [<poziom>|off]"

#: src/commands.cc:218
msgid ""
"Set debug level to given value or turn debug off completely.\n"
" -o <file>  redirect debug output to the file\n"
" -c  show message context\n"
" -p  show PID\n"
" -t  show timestamps\n"
msgstr ""
"Ustaw poziom diagnostyki na daną wartość lub wyłącz zupełnie.\n"
" -o <plik>  przekieruj wyjście diagnostyki do pliku.\n"
" -c  wypisuj kontekst komunikatu\n"
" -p  wypisuj PID\n"
" -t  wypisuj znaczniki czasu\n"

#: src/commands.cc:223
msgid "du [options] <dirs>"
msgstr "du [opcje[ <katalogi>"

#: src/commands.cc:224
msgid ""
"Summarize disk usage.\n"
" -a, --all             write counts for all files, not just directories\n"
"     --block-size=SIZ  use SIZ-byte blocks\n"
" -b, --bytes           print size in bytes\n"
" -c, --total           produce a grand total\n"
" -d, --max-depth=N     print the total for a directory (or file, with --"
"all)\n"
"                       only if it is N or fewer levels below the command\n"
"                       line argument;  --max-depth=0 is the same as\n"
"                       --summarize\n"
" -F, --files           print number of files instead of sizes\n"
" -h, --human-readable  print sizes in human readable format (e.g., 1K 234M "
"2G)\n"
" -H, --si              likewise, but use powers of 1000 not 1024\n"
" -k, --kilobytes       like --block-size=1024\n"
" -m, --megabytes       like --block-size=1048576\n"
" -S, --separate-dirs   do not include size of subdirectories\n"
" -s, --summarize       display only a total for each argument\n"
"     --exclude=PAT     exclude files that match PAT\n"
msgstr ""
"Podsumuj zużycie dysku.\n"
" -a, --all             wyświetlaj informacje dla wszystkich plików, a nie\n"
"     --block-size=ROZ  użyj bloków o wielkości ROZ\n"
" -b, --bytes           wyświetlaj rozmiar w bajtach\n"
" -c, --total           wyświetlaj sumaryczne informacje\n"
" -d, --max-depth=N     wyświetlaj sumaryczny rozmiar dla katalogu (lub "
"pliku,\n"
"                       z --all) tylko jeśli umieszczony on jest nie głębiej "
"niż\n"
"                       N; --max-depth=0 daje ten same efekt jak --summarize\n"
" -F, --files           wyświetlaj ilość plików zamiast rozmiarów\n"
" -h, --human-readable  wyświetlaj rozmiary w formacie czytelnym dla "
"człowieka\n"
"                       (np. 1K 234M)\n"
" -H, --si              jak wyżej z tym, że używaj potęg 1000, a nie 1024\n"
" -k, --kilobytes       jak --block-size=1024\n"
" -m, --megabytes       jak --block-size=1048576\n"
" -S, --separate-dirs   nie włączaj rozmiarów podkatalogów\n"
" -s, --summarize       wyświetlaj jedynie sumaryczny rozmiar dla każdego\n"
"                       argumentu\n"
"     --exclude=WZÓR    nie uwzględniaj plików pasujących do WZÓR\n"

#: src/commands.cc:242
msgid "edit [OPTS] <file>"
msgstr "edit [OPCJE] <plik>"

#: src/commands.cc:243
msgid ""
"Retrieve remote file to a temporary location, run a local editor on it\n"
"and upload the file back if changed.\n"
" -k  keep the temporary file\n"
" -o <temp>  explicit temporary file location\n"
msgstr ""
"Pobranie pliku zdalnego w tymczasowe miejsce, uruchomienie na nim lokalnego\n"
"edytora i przesłanie pliku z powrotem, jeśli został zmieniony.\n"
" -k  zachowanie pliku tymczasowego -o <temp>  jawna lokalizacja pliku "
"tymczasowego\n"

#: src/commands.cc:248
msgid "exit [<code>|bg]"
msgstr "exit [<kod>|bg]"

#: src/commands.cc:249
msgid ""
"exit - exit from lftp or move to background if jobs are active\n"
"\n"
"If no jobs active, the code is passed to operating system as lftp\n"
"termination status. If omitted, exit code of last command is used.\n"
"`bg' forces moving to background if cmd:move-background is false.\n"
msgstr ""
"exit - wyjście z lftp lub przejście w tło jeżeli są aktywne zadania\n"
"\n"
"Jeżeli nie ma aktywnych zadań, kod jest przekazany systemowi jako status\n"
"zakończenia lftp. Jeżeli go pominięto, użyty jest kod wyjściowy ostatniego\n"
"polecenia. `bg' wymusza przejście w tło nawet gdy cmd:move-background jest\n"
"ustawione na fałsz.\n"

#: src/commands.cc:255
msgid ""
"Usage: find [OPTS] [directory]\n"
"Print contents of specified directory or current directory recursively.\n"
"Directories in the list are marked with trailing slash.\n"
"You can redirect output of this command.\n"
" -d, --maxdepth=LEVELS  Descend at most LEVELS of directories.\n"
msgstr ""
"Użycie: find [OPCJE] [katalog]\n"
"Wyświetl rekursywnie zawartość podanego katalogu lub katalogu bieżącego.\n"
"Katalogi znajdujące się na liście są zaznaczone przez slash. Można\n"
"przekierować wyjście tej komendy.\n"
" -d, --maxdepth=POZIOMY  Zagłębianie maksymalnie do tylu POZIOMÓW.\n"

#: src/commands.cc:260
msgid "get [OPTS] <rfile> [-o <lfile>]"
msgstr "get [OPCJE] <zd_plik> [-o <lo_plik>]"

#: src/commands.cc:261
msgid ""
"Retrieve remote file <rfile> and store it to local file <lfile>.\n"
" -o <lfile> specifies local file name (default - basename of rfile)\n"
" -c  continue, resume transfer\n"
" -E  delete remote files after successful transfer\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Uzyskanie zdalnego pliku <zd_plik> i zachowanie go lokalnie jako <lo_plik>.\n"
" -o <lo_plik> ustala nazwę lokalnego pliku (domyślnie - nazwa pliku "
"odległego)\n"
" -c  kontynuowanie, wznowienie przesyłania\n"
" -E  kasowanie zdalnego pliku po zakończonej pomyślnie operacji\n"
" -a  tryb ascii (domyślnym jest tryb binarny)\n"
" -O <base> podaje bazowy katalog lub adres URL gdzie należy umieszczać "
"pliki.\n"

#: src/commands.cc:268
msgid "glob [OPTS] <cmd> <args>"
msgstr "glob [OPCJE] <komenda> <argumenty>"

#: src/commands.cc:270
#, fuzzy
msgid ""
"Expand wildcards and run specified command.\n"
"Options can be used to expand wildcards to list of files, directories,\n"
"or both types. Type selection is not very reliable and depends on server.\n"
"If entry type cannot be determined, it will be included in the list.\n"
" -f  plain files (default)\n"
" -d  directories\n"
" -a  all types\n"
" --exist      return zero exit code when the patterns expand to non-empty "
"list\n"
" --not-exist  return zero exit code when the patterns expand to an empty "
"list\n"
msgstr ""
"Dopasuj maski i uruchom określone komendy.\n"
"Opcje mogą być użyte do dopasowywania masek do listy plików, katalogów,\n"
"lub obu typów. Wybór typu nie jest godny zaufania i zależy od serwera.\n"
"Jeśli typ nie został rozpoznany, to także zostanie on umieszczony na "
"liście.\n"
" -f  zwykłe pliki (standardowo)\n"
" -d  katalogi\n"
" -a  wszystkie pliki\n"

#: src/commands.cc:279
msgid "help [<cmd>]"
msgstr "help [<komenda>]"

#: src/commands.cc:280
msgid "Print help for command <cmd>, or list of available commands\n"
msgstr ""
"Wyświetla pomoc dla polecenia <komenda>, lub listę dostępnych poleceń\n"

#: src/commands.cc:282
msgid ""
"List running jobs. -v means verbose, several -v can be specified.\n"
"If <job_no> is specified, only list a job with that number.\n"
msgstr ""
"Wyświetla działające procesy. -v podaje dodatkowe informacje.\n"
"Może być podane kilka -v. Z podanym <nr_proc> wyświetla tylko\n"
"dany proces.\n"

#: src/commands.cc:284
msgid "kill all|<job_no>"
msgstr "kill all|<nr_proc>"

#: src/commands.cc:285
msgid "Delete specified job with <job_no> or all jobs\n"
msgstr "Kasuje wskazany przez <nr_proc> proces lub wszystkie procesy\n"

#: src/commands.cc:286
msgid "lcd <ldir>"
msgstr "lcd <lo_kat>"

#: src/commands.cc:287
msgid ""
"Change current local directory to <ldir>. The previous local directory\n"
"is stored as `-'. You can do `lcd -' to change the directory back.\n"
msgstr ""
"Zmień aktualny lokalny katalog na <lo_kat>. Poprzedni lokalny katalog jest\n"
"zachowany jako `-'. Możesz wykonać `lcd -' by do niego powrócić.\n"

#: src/commands.cc:289
msgid "lftp [OPTS] <site>"
msgstr "lftp [OPCJE] <site>"

#: src/commands.cc:290
msgid ""
"`lftp' is the first command executed by lftp after rc files\n"
" -f <file>           execute commands from the file and exit\n"
" -c <cmd>            execute the commands and exit\n"
" --norc              don't execute rc files from the home directory\n"
" --help              print this help and exit\n"
" --version           print lftp version and exit\n"
"Other options are the same as in `open' command:\n"
" -e <cmd>            execute the command just after selecting\n"
" -u <user>[,<pass>]  use the user/password for authentication\n"
" -p <port>           use the port for connection\n"
" -s <slot>           assign the connection to this slot\n"
" -d                  switch on debugging mode\n"
" <site>              host name, URL or bookmark name\n"
msgstr ""
"`lftp' jest pierwszą komendą uruchamianą przez lftp po plikach rc\n"
" -f <plik>           wykonanie poleceń z pliku <plik> i wyjście\n"
" -c <komenda>        wykonanie polecenia <komenda> i wyjście\n"
" --norc              bez wykonywania plików rc z katalogu domowego\n"
" --help              wyświetlenie pomocy i zakończenia działania\n"
" --version           wyświetlenie wersji lftp i zakończenie działania\n"
"Inne opcje są takie same jak dla polecenia `open'\n"
" -e <komenda>        wykonanie polecenia <komenda> od razu po wybieraniu\n"
" -u <user>[,<hasło>] użycie user/hasło do uwierzytelnienia\n"
" -p <port>           użycie portu <port> do połączenia\n"
" -s <slot>           przypisanie połączenia do przegródki <slot>\n"
" -d                  włączenie trybu diagnostycznego\n"
" <site>              nazwa hosta, URL lub nazwa zakładki\n"

#: src/commands.cc:303
msgid "ln [-s] <file1> <file2>"
msgstr "ln [-s] <plik1> <plik2>"

#: src/commands.cc:304
msgid "Link <file1> to <file2>\n"
msgstr "Dowiąż <plik1> do <plik2>\n"

#: src/commands.cc:308
msgid "ls [<args>]"
msgstr "ls [<argumenty>]"

#: src/commands.cc:309
msgid ""
"List remote files. You can redirect output of this command to file\n"
"or via pipe to external command.\n"
"By default, ls output is cached, to see new listing use `rels' or\n"
"`cache flush'.\n"
"See also `help cls'.\n"
msgstr ""
"Wypisz listę odległych plików. Możesz przekierować wyjście do pliku lub\n"
"przez potok do zewnętrznego polecenia.\n"
"Domyślnie, wyjście ls jest buforowane, by zobaczyć nowy spis użyj `rels' "
"lub\n"
"`cache flush'.\n"
"Zobacz także `help cls'.\n"

#: src/commands.cc:314
msgid "mget [OPTS] <files>"
msgstr "mget [OPCJE] <pliki>"

#: src/commands.cc:315
msgid ""
"Gets selected files with expanded wildcards\n"
" -c  continue, resume transfer\n"
" -d  create directories the same as in file names and get the\n"
"     files into them instead of current directory\n"
" -E  delete remote files after successful transfer\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Pobiera wskazane pliki pasujące do maski\n"
" -c  kontynuacja, wznowienie przesyłania\n"
" -d  utwórz katalogi o nazwie takiej samej jak pliki i pobierz pliki do\n"
"     nich zamiast do bieżącego katalogu\n"
" -E  kasuj odległe pliki po udanej operacji transferu\n"
" -a  użyj trybu ascii (domyślnie tryb binarny)\n"
" -O <base> podaje bazowy katalog lub adres URL gdzie należy umieszczać "
"pliki.\n"

#: src/commands.cc:322
msgid "mirror [OPTS] [remote [local]]"
msgstr "mirror [OPCJE] [zdalny [lokalny]]"

#: src/commands.cc:323
msgid "mkdir [OPTS] <dirs>"
msgstr "mkdir [OPCJE] <katalogi>"

#: src/commands.cc:324
msgid ""
"Make remote directories\n"
" -p  make all levels of path\n"
" -f  be quiet, suppress messages\n"
msgstr ""
"Utwórz zdalne katalogi\n"
" -p  tworzy wszystkie poziomy ścieżki\n"
" -f  ciche działanie, bez komunikatów\n"

#: src/commands.cc:327
msgid "module name [args]"
msgstr "nazwa modułu [argumenty]"

#: src/commands.cc:328
msgid ""
"Load module (shared object). The module should contain function\n"
"   void module_init(int argc,const char *const *argv)\n"
"If name contains a slash, then the module is searched in current\n"
"directory, otherwise in directories specified by setting module:path.\n"
msgstr ""
"Ładowanie modułu (współdzielony obiekt). Moduł powinien zawierać funkcję\n"
"   void module_init(int argc,const char *const *argv\n"
"Jeśli nazwa zawiera znak \"slash\" wtedy moduł szukany jest w aktualnym\n"
"katalogu, a następnie w katalogu podanym w module:path.\n"

#: src/commands.cc:332
msgid "more <files>"
msgstr "more <pliki>"

#: src/commands.cc:333
msgid "Same as `cat <files> | more'. if PAGER is set, it is used as filter\n"
msgstr ""
"Znaczy to samo, co `cat <pliki> | more'. Jeżeli zmienna PAGER jest "
"ustawiona,\n"
"to będzie używana jako filtr\n"

#: src/commands.cc:334
msgid "mput [OPTS] <files>"
msgstr "mput [OPCJE] <pliki>"

#: src/commands.cc:335
msgid ""
"Upload files with wildcard expansion\n"
" -c  continue, reput\n"
" -d  create directories the same as in file names and put the\n"
"     files into them instead of current directory\n"
" -E  delete local files after successful transfer (dangerous)\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Uploaduje wskazane pliki pasujące do maski\n"
" -c  kontynuacja, reget\n"
" -d  utwórz katalogi o nazwie takiej samej jak pliki i pobierz pliki do\n"
"     nich zamiast do bieżącego katalogu\n"
" -E  kasuj odległe pliki po udanej operacji\n"
" -a  użyj trybu ascii (domyślnie tryb binarny)\n"
" -O <base> podaje bazowy katalog lub adres URL gdzie należy umieszczać "
"pliki.\n"

#: src/commands.cc:342
msgid "mrm <files>"
msgstr "mrm <pliki>"

#: src/commands.cc:343
msgid "Removes specified files with wildcard expansion\n"
msgstr "Usuń pliki odpowiadające podanej masce\n"

#: src/commands.cc:344
msgid "mv <file1> <file2>"
msgstr "mv <plik1> <plik2>"

#: src/commands.cc:345
msgid "Rename <file1> to <file2>\n"
msgstr "Zmień nazwę z <plik1> na <plik2>\n"

#: src/commands.cc:346
#, fuzzy
msgid "mmv [OPTS] <files> <target-dir>"
msgstr "mget [OPCJE] <pliki>"

#: src/commands.cc:347
msgid ""
"Move <files> to <target-directory> with wildcard expansion\n"
" -O <dir>  specifies the target directory (alternative way)\n"
msgstr ""

#: src/commands.cc:349
msgid "[re]nlist [<args>]"
msgstr "[re]nlist [<argumenty>]"

#: src/commands.cc:350
msgid ""
"List remote file names.\n"
"By default, nlist output is cached, to see new listing use `renlist' or\n"
"`cache flush'.\n"
msgstr ""
"Wypisz listę odległych plików.\n"
"Domyślnie, wyjście nlist jest buforowane, by zobaczyć nowy spis użyj "
"`renlist'\n"
"lub `cache flush'.\n"

#: src/commands.cc:353
msgid "open [OPTS] <site>"
msgstr "open [OPCJE] <site>"

#: src/commands.cc:354
msgid ""
"Select a server, URL or bookmark\n"
" -e <cmd>            execute the command just after selecting\n"
" -u <user>[,<pass>]  use the user/password for authentication\n"
" -p <port>           use the port for connection\n"
" -s <slot>           assign the connection to this slot\n"
" -d                  switch on debugging mode\n"
" <site>              host name, URL or bookmark name\n"
msgstr ""
"Wybór serwera, URL-a lub zakładki\n"
" -e <cmd>            wykonanie polecenia zaraz po wyborze\n"
" -u <user>[,<hasło>] użycie user/hasło do uwierzytelnienia\n"
" -p <port>           użycie portu <port> do połączenia\n"
" -s <slot>           przypisanie połączenia do przegródki <slot>\n"
" -d                  włączenie trybu diagnostycznego\n"
" <site>              nazwa hosta, URL lub zakładka\n"

#: src/commands.cc:361
msgid "pget [OPTS] <rfile> [-o <lfile>]"
msgstr "pget [OPCJE] <z_plik> [-p <l_plik>]"

#: src/commands.cc:362
msgid ""
"Gets the specified file using several connections. This can speed up "
"transfer,\n"
"but loads the net heavily impacting other users. Use only if you really\n"
"have to transfer the file ASAP.\n"
"\n"
"Options:\n"
" -c  continue transfer. Requires <lfile>.lftp-pget-status file.\n"
" -n <maxconn>  set maximum number of connections (default is is taken from\n"
"     pget:default-n setting)\n"
" -O <base> specifies base directory where files should be placed\n"
msgstr ""
"Pobiera wybrany plik używając kilku połączeń. Może to przyspieszyć "
"transfer,\n"
"lecz bardziej obciąża sieć, uderzając w innych użytkowników. Używaj tylko\n"
"jeżeli naprawdę musisz szybko ściągnąć plik, bo inny użytkownik może "
"oszaleć :)\n"
"\n"
"Opcje:\n"
" -c  wznów transfer. Wymaga pliku <lfile>.lftp-pget-status.\n"
" -n <maxconn>  ustaw maksymalną liczbę połączeń (domyślna wartość wzięta\n"
"    ze zmiennej pget:default-n)\n"
" -O <base> określa katalog, w którym umieszczone będą pobrane pliki\n"

#: src/commands.cc:370
msgid "put [OPTS] <lfile> [-o <rfile>]"
msgstr "put [OPCJE] <lplik> [-o <zplik>]"

#: src/commands.cc:371
msgid ""
"Upload <lfile> with remote name <rfile>.\n"
" -o <rfile> specifies remote file name (default - basename of lfile)\n"
" -c  continue, reput\n"
"     it requires permission to overwrite remote files\n"
" -E  delete local files after successful transfer (dangerous)\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"Umieść plik <lplik> ze zdalną nazwą <zplik>.\n"
" -o <zplik> precyzuje nazwę pliku (domyślnie - nazwa pliku źródłowego)\n"
" -c  kontynuuj, reput\n"
"     wymaga prawa do nadpisywania odległych plików\n"
" -E  kasowanie zdalnego pliku po zakończonej pomyślnie operacji\n"
" -a  tryb ascii (domyślnym jest tryb binarny)\n"
" -O <base> podaje bazowy katalog lub adres URL gdzie należy umieszczać "
"pliki.\n"

#: src/commands.cc:379
msgid ""
"Print current remote URL.\n"
" -p  show password\n"
msgstr ""
"Wyświetl aktualny zdalny URL.\n"
" -p  pokaż hasło\n"

#: src/commands.cc:381
msgid "queue [OPTS] [<cmd>]"
msgstr "queue [OPCJE] [<komenda>]"

#: src/commands.cc:382
msgid ""
"\n"
"       queue [-n num] <command>\n"
"\n"
"Add the command to queue for current site. Each site has its own command\n"
"queue. `-n' adds the command before the given item in the queue. It is\n"
"possible to queue up a running job by using command `queue wait <jobno>'.\n"
"\n"
"       queue --delete|-d [index or wildcard expression]\n"
"\n"
"Delete one or more items from the queue. If no argument is given, the last\n"
"entry in the queue is deleted.\n"
"\n"
"       queue --move|-m <index or wildcard expression> [index]\n"
"\n"
"Move the given items before the given queue index, or to the end if no\n"
"destination is given.\n"
"\n"
"Options:\n"
" -q                  Be quiet.\n"
" -v                  Be verbose.\n"
" -Q                  Output in a format that can be used to re-queue.\n"
"                     Useful with --delete.\n"
msgstr ""
"\n"
"\tqueue [-n num] <komenda>\n"
"\n"
"Dodaj komendę do kolejki dla aktualnego serwera. Każdy serwer ma swoją\n"
"kolejkę. `-n' dodaje komendę przed podanym elementem w kolejce. Można prze-\n"
"sunąc wyżej w kolejce zadanie komendą `queue wait <nrpracy>'.\n"
"\n"
"\tqueue --delete|-d [indeks lub wyrażenie wieloznaczne]\n"
"\n"
"Usuń jeden lub więcej elementów z kolejki. Jeśli nie podano argumentu, "
"zostanie\n"
"usunięty ostatni wpis w kolejce.\n"
"\n"
"\tqueue --move|-m <indeks lub wyrażenie wieloznaczne> [indeks]\n"
"\n"
"Przesuwa podane elementy przed podany indeks kolejki, lub na koniec\n"
"jeśli nie podano celu (indeksu).\n"
"\n"
"Opcje:\n"
" -q                  Wyświetlaj minimalną ilość komunikatów.\n"
" -v                  Wyświetlaj dodatkowe komunikaty.\n"
" -Q                  Wyjście w formacie, który może być użyty\n"
"                     do ponownego zakolejkowania. Użyteczne z --delete.\n"

#: src/commands.cc:403
msgid "quote <cmd>"
msgstr "quote <komenda>"

#: src/commands.cc:404
msgid ""
"Send the command uninterpreted. Use with caution - it can lead to\n"
"unknown remote state and thus will cause reconnect. You cannot\n"
"be sure that any change of remote state because of quoted command\n"
"is solid - it can be reset by reconnect at any time.\n"
msgstr ""
"Wyślij polecenie bez interpretowania. Używaj ostrożnie - może spowodować\n"
"nieoczekiwany stan komputera po drugiej stronie i wymusić ponowne "
"połączenie.\n"
"Nie możesz być pewny, że jakakolwiek zmiana stanu poprzez polecenie quote\n"
"będzie wykonana - może ona być wykasowana przez ponowne połączenie w "
"dowolnym\n"
"momencie.\n"

#: src/commands.cc:409
msgid ""
"recls [<args>]\n"
"Same as `cls', but don't look in cache\n"
msgstr ""
"recls [<argumenty>]\n"
"Podobnie jak `cls', ale nie zagląda do pamięci podręcznej\n"

#: src/commands.cc:412
msgid ""
"Usage: reget [OPTS] <rfile> [-o <lfile>]\n"
"Same as `get -c'\n"
msgstr ""
"Użycie: reget [OPCJE] <z_plik> [-o <l_plik>]\n"
"Podobnie jak `get -c'\n"

#: src/commands.cc:415
msgid ""
"Usage: rels [<args>]\n"
"Same as `ls', but don't look in cache\n"
msgstr ""
"Użycie: rels [<argumenty>]\n"
"Podobnie jak `ls', ale nie zagląda do pamięci podręcznej\n"

#: src/commands.cc:418
msgid ""
"Usage: renlist [<args>]\n"
"Same as `nlist', but don't look in cache\n"
msgstr ""
"Użycie: renlist [<argumenty>]\n"
"Podobnie jak `nlist', lecz nie zagląda do pamięci podręcznej\n"

#: src/commands.cc:420
msgid "repeat [OPTS] [delay] [command]"
msgstr "repeat [OPCJE] [opóźnienie] [komenda]"

#: src/commands.cc:422
msgid ""
"Usage: reput <lfile> [-o <rfile>]\n"
"Same as `put -c'\n"
msgstr ""
"Użycie: reput <l_plik> [-o <z_plik>]\n"
"Podobnie jak `put -c'\n"

#: src/commands.cc:424
msgid "rm [-r] [-f] <files>"
msgstr "rm [-r] [-r] <pliki>"

#: src/commands.cc:425
msgid ""
"Remove remote files\n"
" -r  recursive directory removal, be careful\n"
" -f  work quietly\n"
msgstr ""
"Usuń zdalne pliki\n"
" -r  usuń katalog z podkatalogami - ostrożnie!\n"
" -f  pracuj cicho\n"

#: src/commands.cc:428
msgid "rmdir [-f] <dirs>"
msgstr "rmdir [-f] <katalogi>"

#: src/commands.cc:429
msgid "Remove remote directories\n"
msgstr "Usuń zdalne katalogi\n"

#: src/commands.cc:430
msgid "scache [<session_no>]"
msgstr "scache [<nr_sesji>]"

#: src/commands.cc:431
msgid "List cached sessions or switch to specified session number\n"
msgstr ""
"Pokazuje listę buforowanych sesji lub zmienia na wskazany numer sesji\n"

#: src/commands.cc:432
msgid "set [OPT] [<var> [<val>]]"
msgstr "set [OPCJ] [<zmienna> [<wartość>]]"

#: src/commands.cc:433
msgid ""
"Set variable to given value. If the value is omitted, unset the variable.\n"
"Variable name has format ``name/closure'', where closure can specify\n"
"exact application of the setting. See lftp(1) for details.\n"
"If set is called with no variable then only altered settings are listed.\n"
"It can be changed by options:\n"
" -a  list all settings, including default values\n"
" -d  list only default values, not necessary current ones\n"
msgstr ""
"Ustaw zmienną na podaną wartość. Jeśli nie podano wartości usuń zmienną.\n"
"Nazwa zmiennej występuje w formacie ``nazwa/element_zamykający'', gdzie\n"
"elem_zamykający może być dokładnym ustawieniem aplikacji. Szczegóły w "
"lftp(1).\n"
"Jeśli komenda wywoływana jest bez podawania nazwy zmiennej wtedy tylko\n"
"zmienione ustawienia są wyświetlane. Jako opcje można podać:\n"
" -a  wyświetl wszystkie ustawienia włączając ustawienia standardowe\n"
" -d  wyświetl tylko standardowe ustawienia, niekoniecznie aktualne\n"

#: src/commands.cc:441
msgid "site <site-cmd>"
msgstr "site <polecenie-zd>"

#: src/commands.cc:442
msgid ""
"Execute site command <site_cmd> and output the result\n"
"You can redirect its output\n"
msgstr ""
"Wykonuje na zdalnym serwerze polecenie <polecenie-zd> i wyświetla wyniki\n"
"Możesz przekierować te wyniki\n"

#: src/commands.cc:446
msgid ""
"Usage: slot [<label>]\n"
"List assigned slots.\n"
"If <label> is specified, switch to the slot named <label>.\n"
msgstr ""
"Użycie: slot [<etykieta>]\n"
"Wypisz przypisane sloty.\n"
"Jeśli podano <etykietę>, przełącz na slot o tej <etykiecie>.\n"

#: src/commands.cc:449
msgid "source <file>"
msgstr "source <plik>"

#: src/commands.cc:450
msgid "Execute commands recorded in file <file>\n"
msgstr "Wykonuje polecenia zawarte w pliku <plik>\n"

#: src/commands.cc:452
msgid "torrent [OPTS] <file|URL>..."
msgstr "torrent [OPCJE] <plik|URL>..."

#: src/commands.cc:453
msgid "user <user|URL> [<pass>]"
msgstr "user <użytkownik|URL> [<hasło>]"

#: src/commands.cc:454
msgid ""
"Use specified info for remote login. If you specify URL, the password\n"
"will be cached for future usage.\n"
msgstr ""
"Użyj podanej informacji przy zdalnym logowaniu. Jeśli podasz URL, hasło\n"
"będzie zapamiętane w pamięci podręcznej w celu przyszłego użycia.\n"

#: src/commands.cc:457
msgid "Shows lftp version\n"
msgstr "Pokazuje wersję lftp\n"

#: src/commands.cc:458
msgid "wait [<jobno>]"
msgstr "wait [<nr_proc>]"

#: src/commands.cc:459
msgid ""
"Wait for specified job to terminate. If jobno is omitted, wait\n"
"for last backgrounded job.\n"
msgstr ""
"Czekaj na zakończenie pracy i wyjdź. Jeśli pominięto numer pracy,\n"
"czekaj na ostatnią pracę uruchomioną w tle.\n"

#: src/commands.cc:461
msgid "zcat <files>"
msgstr "zcat <pliki>"

#: src/commands.cc:462
msgid "Same as cat, but filter each file through zcat\n"
msgstr "Działa tak samo jak cat, lecz filtruje każdy plik przez zcat\n"

#: src/commands.cc:463
msgid "zmore <files>"
msgstr "zmore <pliki>"

#: src/commands.cc:464
msgid "Same as more, but filter each file through zcat\n"
msgstr "To samo co more, ale filtruj każdy plik przez zcat\n"

#: src/commands.cc:466
msgid "Same as cat, but filter each file through bzcat\n"
msgstr "Działa tak samo jak cat, lecz filtruje każdy plik przez bzcat\n"

#: src/commands.cc:468
msgid "Same as more, but filter each file through bzcat\n"
msgstr "To samo co more, ale filtruj każdy plik przez bzcat\n"

#: src/commands.cc:524
#, c-format
msgid "Usage: %s local-dir\n"
msgstr "Użycie: %s lokalny_katalog\n"

#: src/commands.cc:560
#, c-format
msgid "lcd ok, local cwd=%s\n"
msgstr "lcd ok, lokalne cwd=%s\n"

#: src/commands.cc:576
#, c-format
msgid "Usage: cd remote-dir\n"
msgstr "Użycie: cd zdalny_katalog\n"

#: src/commands.cc:588
#, c-format
msgid "%s: no old directory for this site\n"
msgstr "%s: brak starego katalogu dla tego serwera\n"

#: src/commands.cc:677
#, c-format
msgid "Usage: %s [<exit_code>]\n"
msgstr "Użycie: %s [<kod_wyjścia]\n"

#: src/commands.cc:686
msgid ""
"There are running jobs and `cmd:move-background' is not set.\n"
"Use `exit bg' to force moving to background or `kill all' to terminate "
"jobs.\n"
msgstr ""
"Aktualnie wykonywane są prace i `cmd:move-background' nie jest ustawiona.\n"
"Użyj `exit bg' by wymusić przejście w tło lub `kill all' by wyłączyć "
"zadania.\n"

#: src/commands.cc:703
msgid ""
"\n"
"lftp now tricks the shell to move it to background process group.\n"
"lftp continues to run in the background despite the `Stopped' message.\n"
"lftp will automatically terminate when all jobs are finished.\n"
"Use `fg' shell command to return to lftp if it is still running.\n"
msgstr ""
"\n"
"lftp teraz oszukuje powłokę, żeby przenieść się do grupy procesów w tle.\n"
"lftp będzie działał w tle pomimo komunikatu `Zatrzymany'.\n"
"lftp wyłączy się automatycznie po zakończeniu wszystkich zadań.\n"
"Dopóki lftp działa, można go przywrócić w normalnym trybie poleceniem `fg'.\n"

#: src/commands.cc:837 src/commands.cc:3616
#, c-format
msgid "Try `%s --help' for more information\n"
msgstr "Spróbuj `%s --help' by uzyskać więcej informacji\n"

#: src/commands.cc:856
#, c-format
msgid "%s: -c, -f, -v, -h conflict with other `open' options and arguments\n"
msgstr ""
"%s: -c, -f, -v i -h są w konflikcie z pozostałymi opcjami i argumentami "
"`open'\n"

#: src/commands.cc:956
#, c-format
msgid "Usage: %s [-e cmd] [-p port] [-u user[,pass]] <host|url>\n"
msgstr ""
"Użycie: %s [-e polecenie] [-p port] [-u użytkownik[,hasło]] <host|url>\n"

#: src/commands.cc:1046 src/commands.cc:2276 src/DummyProto.cc:65
#: src/MirrorJob.cc:2172 src/MirrorJob.cc:2194
msgid " - not supported protocol"
msgstr " - protokół nie jest wspierany"

#: src/commands.cc:1078 src/commands.cc:2260
msgid "Password: "
msgstr "Hasło: "

#: src/commands.cc:1080
#, c-format
msgid "%s: GetPass() failed -- assume anonymous login\n"
msgstr "%s: GetPass() nie powiodło się -- logowanie anonimowe\n"

#: src/commands.cc:1191 src/commands.cc:1284 src/commands.cc:1660
#: src/commands.cc:1691 src/commands.cc:1829 src/commands.cc:1914
#: src/commands.cc:2187 src/commands.cc:2381 src/commands.cc:2543
#: src/commands.cc:2588 src/commands.cc:2616 src/commands.cc:2623
#: src/commands.cc:2930 src/commands.cc:2957 src/commands.cc:2964
#: src/commands.cc:3293 src/lftp.cc:267 src/MirrorJob.cc:2136
#: src/SleepJob.cc:144 src/SleepJob.cc:200 src/Torrent.cc:4169
#, c-format
msgid "Try `help %s' for more information.\n"
msgstr "W celu uzyskania większej ilości informacji spróbuj `help %s'.\n"

#: src/commands.cc:1201
#, c-format
msgid "Usage: %s [OPTS] command args...\n"
msgstr "Użycie: %s [OPCJE] komenda argumenty...\n"

#: src/commands.cc:1254
#, c-format
msgid "%s: -n: positive number expected. "
msgstr "%s: -n: Oczekiwano liczby dodatniej. "

#: src/commands.cc:1306
#, c-format
msgid "Created a stopped queue.\n"
msgstr "Utworzono zatrzymaną kolejkę.\n"

#: src/commands.cc:1349 src/commands.cc:1377
#, c-format
msgid "%s: No queue is active.\n"
msgstr "%s: Nie ma aktywnej kolejki.\n"

#: src/commands.cc:1369
#, c-format
msgid "%s: -m: Number expected as second argument. "
msgstr "%s: -m: Oczekiwano liczby jako drugiego argumentu. "

#: src/commands.cc:1421
#, c-format
msgid "Usage: %s <cmd>\n"
msgstr "Użycie: %s <komenda>\n"

#: src/commands.cc:1527
msgid "invalid argument for `--sort'"
msgstr "nieprawidłowy argument dla `--sort'"

#: src/commands.cc:1557
msgid "invalid block size"
msgstr "nieprawidłowy rozmiar bloku"

#: src/commands.cc:1700
#, c-format
msgid "Usage: %s [OPTS] files...\n"
msgstr "Użycie: %s [OPCJE] pliki...\n"

#: src/commands.cc:1785 src/commands.cc:1814
#, fuzzy, c-format
msgid "%s: %s: Number expected. "
msgstr "%s: -n: Oczekiwano liczby. "

#: src/commands.cc:1834
#, fuzzy, c-format
msgid "%s: --continue conflicts with --remove-target.\n"
msgstr "%s: --share jest w konflikcie z --only-new.\n"

#: src/commands.cc:1844 src/commands.cc:1920
#, c-format
msgid "File name missed. "
msgstr "Brak nazwy pliku. "

#: src/commands.cc:1989
#, c-format
msgid "Usage: %s %s[-f] files...\n"
msgstr "Użycie: %s %s[-f] pliki...\n"

#: src/commands.cc:2032
#, c-format
msgid "Usage: %s [-e] <file|command>\n"
msgstr "Użycie: %s [-e] <plik|polecenie>\n"

#: src/commands.cc:2079
#, c-format
msgid "Usage: %s [-v] [-v] ...\n"
msgstr "Użycie: %s [-v] [-v] ...\n"

#: src/commands.cc:2093 src/commands.cc:2347 src/commands.cc:2469
#: src/commands.cc:2685 src/commands.cc:3101 src/commands.cc:3182
#: src/lftp.cc:279
#, c-format
msgid "%s: %s - not a number\n"
msgstr "%s: %s - nie jest numerem\n"

#: src/commands.cc:2100 src/commands.cc:2326 src/commands.cc:2356
#: src/commands.cc:2487
#, c-format
msgid "%s: %d - no such job\n"
msgstr "%s: %d - nie ma takiego zadania\n"

#: src/commands.cc:2135
#, c-format
msgid "Usage: %s [-p]\n"
msgstr "Użycie: %s [-p]\n"

#: src/commands.cc:2227
#, c-format
msgid "debug level is %d, output goes to %s\n"
msgstr "poziom debugowania wynosi %d, wyniki przekierowywane do %s\n"

#: src/commands.cc:2230
#, c-format
msgid "debug is off\n"
msgstr "debugowanie jest wyłączone\n"

#: src/commands.cc:2241
#, c-format
msgid "Usage: %s <user|URL> [<pass>]\n"
msgstr "Użycie: %s <użytkownik|URL> [<hasło>]\n"

#: src/commands.cc:2316 src/commands.cc:2479
#, c-format
msgid "%s: no current job\n"
msgstr "%s: nie ma takiego zadania\n"

#: src/commands.cc:2328
#, c-format
msgid "Usage: %s <jobno> ... | all\n"
msgstr "Użycie: %s <nr_proc> ... | all\n"

#: src/commands.cc:2409
#, c-format
msgid "%s: %s. Use `set -a' to look at all variables.\n"
msgstr "%s: %s. Użyj `set -a' by sprawdzić wszystkie zmienne.\n"

#: src/commands.cc:2453
#, c-format
msgid "Usage: %s [<jobno>]\n"
msgstr "Użycie: %s [<nr_proc>]\n"

#: src/commands.cc:2492
#, c-format
msgid "%s: some other job waits for job %d\n"
msgstr "%s: jakiś inny proces czeka na proces %d\n"

#: src/commands.cc:2497
#, c-format
msgid "%s: wait loop detected\n"
msgstr "%s: wykryto pętle zastoju\n"

#: src/commands.cc:2553
#, fuzzy, c-format
msgid "Usage: %s [OPTS] <files> <target-dir>\n"
msgstr "Użycie: %s [OPCJE] plik\n"

#: src/commands.cc:2615 src/commands.cc:2956
#, c-format
msgid "Invalid command. "
msgstr "Nieprawidłowe polecenie. "

#: src/commands.cc:2622 src/commands.cc:2963
#, c-format
msgid "Ambiguous command. "
msgstr "Dwuznaczne polecenie. "

#: src/commands.cc:2641
#, c-format
msgid "%s: Operand missed for size\n"
msgstr "%s: Operandowi brakuje rozmiaru\n"

#: src/commands.cc:2658
#, c-format
msgid "%s: Operand missed for `expire'\n"
msgstr "%s: Brakuje operandu dla `expire'\n"

#: src/commands.cc:2691
#, c-format
msgid ""
"%s: %s - no such cached session. Use `scache' to look at session list.\n"
msgstr ""
"%s: %s - brak takiej sesji w buforze. Użyj `scache' by poszukać w liście.\n"

#: src/commands.cc:2715
#, c-format
msgid "Sorry, no help for %s\n"
msgstr "Niestety, brak pomocy dla %s\n"

#: src/commands.cc:2720
#, c-format
msgid "%s is a built-in alias for %s\n"
msgstr "%s jest wbudowanym aliasem do %s\n"

#: src/commands.cc:2725
#, c-format
msgid "Usage: %s\n"
msgstr "Użycie: %s\n"

#: src/commands.cc:2733
#, c-format
msgid "%s is an alias to `%s'\n"
msgstr "%s jest aliasem do `%s'\n"

#: src/commands.cc:2737
#, c-format
msgid "No such command `%s'. Use `help' to see available commands.\n"
msgstr "Nie ma takiego polecenia `%s'. Użyj `help' by uzyskać spis komend.\n"

#: src/commands.cc:2739
#, c-format
msgid "Ambiguous command `%s'. Use `help' to see available commands.\n"
msgstr "Dwuznaczne polecenie `%s'. Użyj `help' by uzyskać spis komend.\n"

#: src/commands.cc:2805
#, c-format
msgid "LFTP | Version %s | Copyright (c) 1996-%d Alexander V. Lukyanov\n"
msgstr "LFTP | Wersja %s | Copyright (c) 1996-%d Alexander V. Lukyanov\n"

#: src/commands.cc:2808
#, c-format
msgid ""
"LFTP is free software: you can redistribute it and/or modify\n"
"it under the terms of the GNU General Public License as published by\n"
"the Free Software Foundation, either version 3 of the License, or\n"
"(at your option) any later version.\n"
"\n"
"This program is distributed in the hope that it will be useful,\n"
"but WITHOUT ANY WARRANTY; without even the implied warranty of\n"
"MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n"
"GNU General Public License for more details.\n"
"\n"
"You should have received a copy of the GNU General Public License\n"
"along with LFTP.  If not, see <http://www.gnu.org/licenses/>.\n"
msgstr ""
"LFTP to oprogramowanie wolnodostępne - można je rozprowadzać i/lub "
"modyfikować\n"
"na warunkach Powszechnej Licencji Publicznej GNU (General Public License),\n"
"opublikowanej przez Free Software Foundation, w wersji 3 lub nowszej.\n"
"\n"
"Ten program jest rozprowadzany w nadziei, że będzie przydatny, ale BEZ\n"
"JAKIEJKOLWIEK GWARANCJI, nawet domyślnej gwarancji SPRZEDAWALNOŚCI lub\n"
"PRZYDATNOŚCI DO KONKRETNEGO CELU. Więcej szczegółów w treści Powszechnej\n"
"Licencji Publicznej GNU.\n"
"\n"
"Kopia Powszechnej Licencji Publicznej GNU powinna być dołączona do LFTP.\n"
"Jeśli nie, można ją zobaczyć pod <http://www.gnu.org/licenses/>.\n"

#: src/commands.cc:2822
#, c-format
msgid "Send bug reports and questions to the mailing list <%s>.\n"
msgstr "Raporty o błędach oraz pytania proszę przesyłać na listę <%s>.\n"

#: src/commands.cc:2828
msgid "Libraries used: "
msgstr "Użyte biblioteki: "

#: src/commands.cc:2979 src/commands.cc:3007
#, c-format
msgid "%s: bookmark name required\n"
msgstr "%s: wymagana jest nazwa zakładki\n"

#: src/commands.cc:2996
#, c-format
msgid "%s: spaces in bookmark name are not allowed\n"
msgstr "%s: spacje w nazwie pliku zakładki są niedozwolone\n"

#: src/commands.cc:3009
#, c-format
msgid "%s: no such bookmark `%s'\n"
msgstr "%s: nie ma takiej zakładki `%s'\n"

#: src/commands.cc:3032
#, c-format
msgid "%s: import type required (netscape,ncftp)\n"
msgstr "%s: wymagany jest typ importowanych danych (netscape,ncftp)\n"

#: src/commands.cc:3110
#, c-format
msgid "Usage: %s [-d #] dir\n"
msgstr "Użycie: %s [-d $] katalog\n"

#: src/commands.cc:3215
#, c-format
msgid "%s: invalid block size `%s'\n"
msgstr "%s: nieprawidłowy rozmiar bloku `%s'\n"

#: src/commands.cc:3226
#, c-format
msgid "Usage: %s [options] <dirs>\n"
msgstr "Użycie: %s [opcje] <katalogi>\n"

#: src/commands.cc:3232
#, c-format
msgid "%s: warning: summarizing is the same as using --max-depth=0\n"
msgstr "%s: uwaga: podsumowanie jest takie samo jak przy --max-depth=0\n"

#: src/commands.cc:3236
#, c-format
msgid "%s: summarizing conflicts with --max-depth=%i\n"
msgstr "%s: podsumowanie konfliktów z --max-depth=%i\n"

#: src/commands.cc:3280
#, c-format
msgid "Usage: %s command args...\n"
msgstr "Użycie: %s module argumenty...\n"

#: src/commands.cc:3292
#, c-format
msgid "Usage: %s module [args...]\n"
msgstr "Użycie: %s module [argumenty...]\n"

#: src/commands.cc:3310 src/LocalAccess.cc:642
msgid "cannot get current directory"
msgstr "nie można uzyskać aktualnego katalogu"

#: src/commands.cc:3374
#, c-format
msgid "Usage: %s [OPTS] mode file...\n"
msgstr "Użycie: %s [OPCJE] tryb pliki...\n"

#: src/commands.cc:3394
#, c-format
msgid "invalid mode string: %s\n"
msgstr "nieprawidłowy tryb: %s\n"

#: src/commands.cc:3457 src/commands.cc:3466
msgid "Invalid range format. Format is min-max, e.g. 10-20."
msgstr "Nieprawidłowy format zakresu. Format to min-maks, np. 10-20."

#: src/commands.cc:3478
#, c-format
msgid "Usage: %s [OPTS] file\n"
msgstr "Użycie: %s [OPCJE] plik\n"

#: src/CopyJob.cc:82
#, c-format
msgid "`%s' at %lld %s%s%s%s"
msgstr "`%s' w %lld %s%s%s%s"

#: src/CopyJob.cc:159
#, c-format
msgid "%lld $#ll#byte|bytes$ transferred in %ld $#l#second|seconds$"
msgstr "%lld bajt$#ll#|y|ów$ przesłan$y|e|ych$ w ciągu %ld sekund$#l#y||$"

#: src/CopyJob.cc:167
#, c-format
msgid "%lld $#ll#byte|bytes$ transferred"
msgstr "%lld bajt$#ll#|y|ów$ przesłan$y|e|ych$"

#: src/CopyJob.cc:283
#, c-format
msgid "Transfer of %d of %d $file|files$ failed\n"
msgstr "Transfer %d z %d plik$u|ów|ów$ nie powiódł się\n"

#: src/CopyJob.cc:289
#, c-format
msgid "Total %d $file|files$ transferred\n"
msgstr "Podsumowując: przesłano %d plik$|i|ów$\n"

#: src/FileAccess.cc:160
msgid "Access failed: "
msgstr "Brak dostępu: "

#: src/FileAccess.cc:161
msgid "File cannot be accessed"
msgstr "Brak dostępu do pliku"

#: src/FileAccess.cc:163 src/Fish.cc:982 src/ftpclass.cc:4599
#: src/ftpclass.cc:4608 src/SFtp.cc:1339 src/Torrent.cc:3533
msgid "Not connected"
msgstr "Nie połączony"

#: src/FileAccess.cc:166 src/FileAccess.cc:167
msgid "Fatal error"
msgstr "Błąd krytyczny"

#: src/FileAccess.cc:169
msgid "Store failed - you have to reput"
msgstr "Zapis się nie powiódł - trzeba użyć reput"

#: src/FileAccess.cc:172 src/FileAccess.cc:173
msgid "Login failed"
msgstr "Logowanie nie udało się"

#: src/FileAccess.cc:176 src/FileAccess.cc:177
msgid "Operation not supported"
msgstr "Operacja jest niedostępna"

#: src/FileAccess.cc:180
msgid "File moved"
msgstr "Plik przeniesiony"

#: src/FileAccess.cc:182
msgid "File moved to `"
msgstr "Plik przeniesiony do `"

#: src/FileCopy.cc:141
msgid "copy: destination file is already complete\n"
msgstr "kopiowanie: plik docelowy jest już kompletny\n"

#: src/FileCopy.cc:182
msgid "copy: put is broken\n"
msgstr "kopiowanie: put jest uszkodzony\n"

#: src/FileCopy.cc:201
msgid "seek failed"
msgstr "wyszukiwanie nie powiodło się"

#: src/FileCopy.cc:207
msgid "no progress timeout"
msgstr "bez limitu czasu postępu"

#: src/FileCopy.cc:235
msgid "cannot seek on data source"
msgstr "nie można wyszukiwać na źródle danych"

#: src/FileCopy.cc:238
#, c-format
msgid "copy: put rolled back to %lld, seeking get accordingly\n"
msgstr "kopiowanie: put powróciło do %lld, przeszukiwanie get\n"

#: src/FileCopy.cc:251
msgid "copy: all data received, but get rolled back\n"
msgstr ""
"kopiowanie: wszystkie dane otrzymano, ale powrócono do poprzedniego stanu\n"

#: src/FileCopy.cc:267
#, c-format
msgid "copy: get rolled back to %lld, seeking put accordingly\n"
msgstr "kopiowanie: get powróciło do %lld, przeszukiwanie put\n"

#: src/FileCopy.cc:364
msgid "file size decreased during transfer"
msgstr "rozmiar pliku zmniejszył się w trakcie przesyłania"

#: src/FileCopy.cc:1227
#, c-format
msgid "copy: received redirection to `%s'\n"
msgstr "kopiowanie: otrzymano przekierowanie do `%s'\n"

#: src/FileCopy.cc:1290
#, fuzzy
#| msgid "file size decreased during transfer"
msgid "file size increased during transfer"
msgstr "rozmiar pliku zmniejszył się w trakcie przesyłania"

#: src/FileCopy.cc:1390
msgid "file name missed in URL"
msgstr "brak nazwy pliku w URL"

#: src/FileCopy.cc:2086
msgid "Verify command failed without a message"
msgstr "Polecenie weryfikacji nie powiodło się bez komunikatu"

#: src/FileCopyFtp.cc:95
msgid "**** FXP: trying to reverse ftp:fxp-passive-source\n"
msgstr "**** FXP: próba odwrócenia ftp:fxp-passive-source\n"

#: src/FileCopyFtp.cc:102
msgid "**** FXP: trying to reverse ftp:fxp-passive-sscn\n"
msgstr "**** FXP: próba odwrócenia ftp:fxp-passive-sscn\n"

#: src/FileCopyFtp.cc:110
msgid "**** FXP: trying to reverse ftp:ssl-protect-fxp\n"
msgstr "**** FXP: próba odwrócenia ftp:ssl-protect-fxp\n"

#: src/FileCopyFtp.cc:116
msgid "**** FXP: giving up, reverting to plain copy\n"
msgstr "**** FXP: poddawanie się, powracanie do prostego kopiowania\n"

#: src/FileCopyFtp.cc:125
msgid "ftp:fxp-force is set but FXP is not available"
msgstr "ftp:fxp-force jest ustawione, ale FXP jest niedostępne"

#: src/FileCopy.h:285
msgid "Verifying..."
msgstr "Weryfikacja..."

#: src/FileSetOutput.cc:230
msgid "non-option arguments found"
msgstr "znaleziono argumenty nie będące opcjami"

#: src/Filter.cc:166
msgid "pipe() failed: "
msgstr "pipe() nie powiodło się: "

#: src/Filter.cc:200 src/PtyShell.cc:135
#, c-format
msgid "chdir(%s) failed: %s\n"
msgstr "chdir(%s) nie powiodło się: %s\n"

#: src/Filter.cc:208
#, c-format
msgid "execvp(%s) failed: %s\n"
msgstr "execvp(%s) nie powiodło się: %s\n"

#: src/Filter.cc:213 src/PtyShell.cc:147
#, c-format
msgid "execl(/bin/sh) failed: %s\n"
msgstr "execlp(/bin/sh) nieudane: %s\n"

#: src/Filter.cc:415
#, fuzzy
msgid "file already exists and xfer:clobber is unset"
msgstr "%s: %s: plik aktualnie istnieje i xfer:clobber jest nie ustawione\n"

#: src/FindJobDu.cc:101
msgid "total"
msgstr "razem"

#: src/Fish.cc:75 src/ftpclass.cc:1292 src/Http.cc:1232 src/SFtp.cc:77
#, c-format
msgid "Closing idle connection"
msgstr "Zamykanie bezczynnego połączenia"

#: src/Fish.cc:162 src/SFtp.cc:177
msgid "Running connect program"
msgstr "Uruchamianie programu łączącego"

#: src/Fish.cc:588 src/ftpclass.cc:2887 src/ftpclass.cc:3107 src/Http.cc:1510
#: src/SFtp.cc:742 src/SFtp.cc:1083 src/SFtp.cc:1084 src/SSH_Access.cc:167
#, c-format
msgid "Peer closed connection"
msgstr "Komputer po drugiej stronie zamknął połączenie"

#: src/Fish.cc:634 src/ftpclass.cc:3037 src/ftpclass.cc:4219 src/SFtp.cc:1108
#, c-format
msgid "extra server response"
msgstr "dodatkowa odpowiedź serwera"

#: src/Fish.cc:987 src/ftpclass.cc:4611 src/Http.cc:2329 src/Http.cc:2336
#: src/SFtp.cc:1345 src/Torrent.cc:3536 src/TorrentTracker.cc:624
msgid "Connecting..."
msgstr "Łączenie się..."

#: src/Fish.cc:989 src/ftpclass.cc:4617 src/SFtp.cc:1347
msgid "Connected"
msgstr "Połączono"

#: src/Fish.cc:991 src/ftpclass.cc:4594 src/ftpclass.cc:4622
#: src/ftpclass.cc:4636 src/Http.cc:2338 src/SFtp.cc:1349
#: src/TorrentTracker.cc:626
msgid "Waiting for response..."
msgstr "Czekanie na odpowiedź..."

#: src/Fish.cc:993 src/ftpclass.cc:4656 src/Http.cc:2341 src/SFtp.cc:1351
msgid "Receiving data"
msgstr "Otrzymuję dane"

#: src/Fish.cc:995 src/ftpclass.cc:4654 src/Http.cc:2334 src/SFtp.cc:1353
msgid "Sending data"
msgstr "Wysyłam dane"

#: src/Fish.cc:997 src/SFtp.cc:1355
msgid "Done"
msgstr "Gotowe"

#: src/Fish.cc:1136 src/FtpDirList.cc:123 src/HttpDir.cc:1384 src/SFtp.cc:2200
#: src/SFtp.cc:2320
#, c-format
msgid "Getting file list (%lld) [%s]"
msgstr "Pobieranie listy plików (%lld) [%s]"

#: src/ftpclass.cc:197
#, c-format
msgid "Data connection peer has wrong port number"
msgstr "Nieprawidłowy port komputera po drugiej stronie"

#: src/ftpclass.cc:203
#, c-format
msgid "Data connection peer has mismatching address"
msgstr "Połączenie dla danych ma nieprawidłowy adres"

#: src/ftpclass.cc:225 src/ftpclass.cc:250
#, c-format
msgid "Switching to NOREST mode"
msgstr "Przełączam się w tryb NOREST"

#: src/ftpclass.cc:425
#, c-format
msgid "Server reply matched ftp:retry-530, retrying"
msgstr "Odpowiedź serwera pasowała do ftp:retry-530, ponawiam próbę"

#: src/ftpclass.cc:433
#, c-format
msgid "Server reply matched ftp:retry-530-anonymous, retrying"
msgstr "Odpowiedź serwera pasowała do ftp:retry-530-anonymous, ponawiam próbę"

#: src/ftpclass.cc:466
msgid "Account is required, set ftp:acct variable"
msgstr "Konto jest wymagane, ustaw zmienną ftp:acct"

#: src/ftpclass.cc:483
msgid "ftp:skey-force is set and server does not support OPIE nor S/KEY"
msgstr "ftp:skey-force jest ustawione i serwer nie obsługuje OPIE ani S/KEY"

#: src/ftpclass.cc:501
#, c-format
msgid "assuming failed host name lookup"
msgstr "przypuszczalnie nie powiodło się wyszukiwanie IP hosta"

#: src/ftpclass.cc:809 src/ftpclass.cc:810
#, c-format
msgid "cannot parse EPSV response"
msgstr "nie można przeanalizować odpowiedzi EPSV"

#: src/ftpclass.cc:837 src/ftpclass.cc:838
#, fuzzy, c-format
#| msgid "cannot parse EPSV response"
msgid "cannot parse custom EPSV response"
msgstr "nie można przeanalizować odpowiedzi EPSV"

#: src/ftpclass.cc:1122
#, c-format
msgid "Closing control socket"
msgstr "Zamykanie gniazda sterującego"

#: src/ftpclass.cc:1341
msgid "MLSD is disabled by ftp:use-mlsd"
msgstr "MLSD jest zablokowane przez ftp:use-mlsd"

#: src/ftpclass.cc:1411 src/Http.cc:1431 src/Torrent.cc:3204
#: src/Torrent.cc:3743 src/TorrentTracker.cc:395
#, c-format
msgid "cannot create socket of address family %d"
msgstr "nie można utworzyć gniazda dla rodziny adresu %d"

#: src/ftpclass.cc:1442 src/Http.cc:1457
#, c-format
msgid "Socket error (%s) - reconnecting"
msgstr "Błąd połączenia (%s) - powtórne łączenie"

#: src/ftpclass.cc:1715
msgid "MFF and SITE CHMOD are not supported by this site"
msgstr "MFF i SITE CHMOD nie są obsługiwane przez ten serwer"

#: src/ftpclass.cc:1720
msgid "MLST and MLSD are not supported by this site"
msgstr "MLST i MLSD nie są obsługiwane przez ten serwer"

#: src/ftpclass.cc:2031
#, fuzzy
msgid "SITE SYMLINK is not supported by the server"
msgstr "MLST i MLSD nie są obsługiwane przez ten serwer"

#: src/ftpclass.cc:2204
msgid "unsupported network protocol"
msgstr "protokół nie jest obsługiwany"

#: src/ftpclass.cc:2253 src/ftpclass.cc:2355
#, c-format
msgid "Data socket error (%s) - reconnecting"
msgstr "Błąd gniazda danych (%s) - powtórne łączenie"

#: src/ftpclass.cc:2281
#, c-format
msgid "Accepted data connection from (%s) port %u"
msgstr "Przyjmowanie połączenia danych z (%s) portu %u"

#: src/ftpclass.cc:2330
#, c-format
msgid "Connecting data socket to (%s) port %u"
msgstr "Łączenie się (gniazdo danych) z (%s) port %u"

#: src/ftpclass.cc:2336
#, c-format
msgid "Connecting data socket to proxy %s (%s) port %u"
msgstr "Łączenie się (gniazdo danych) z proxy %s (%s) port %u"

#: src/ftpclass.cc:2358 src/ftpclass.cc:4414
#, c-format
msgid "Switching passive mode off"
msgstr "Wyłączam tryb pasywny"

#: src/ftpclass.cc:2366
#, c-format
msgid "Data connection established"
msgstr "Ustanowiono połączenie dla danych"

#: src/ftpclass.cc:2688 src/ftpclass.cc:4548
msgid "ftp:ssl-force is set and server does not support or allow SSL"
msgstr ""
"ftp:ssl-force jest ustawione i serwer nie wspiera lub nie zezwala\n"
"na połączenia SSL"

#: src/ftpclass.cc:3053
#, c-format
msgid "Persist and retry"
msgstr "Ponowne wznawianie"

#: src/ftpclass.cc:3321
#, c-format
msgid "Closing data socket"
msgstr "Zamykanie gniazda danych"

#: src/ftpclass.cc:3343
#, c-format
msgid "Closing aborted data socket"
msgstr "Zamykanie gniazda danych po przerwaniu"

#: src/ftpclass.cc:4193
#, c-format
msgid "saw file size in response"
msgstr "w odpowiedzi pojawił się rozmiar pliku"

#: src/ftpclass.cc:4233 src/ftpclass.cc:4260
#, c-format
msgid "Turning on sync-mode"
msgstr "Włączanie trybu sync"

#: src/ftpclass.cc:4434
#, c-format
msgid "Switching passive mode on"
msgstr "Włączam tryb pasywny"

#: src/ftpclass.cc:4585
msgid "FEAT negotiation..."
msgstr "Negocjacja FEAT..."

#: src/ftpclass.cc:4592
msgid "Sending commands..."
msgstr "Wysyłanie komend..."

#: src/ftpclass.cc:4596
msgid "Delaying before retry"
msgstr "Opóźnienie przed ponownym połączeniem"

#: src/ftpclass.cc:4597 src/Http.cc:2331
msgid "Connection idle"
msgstr "Połączenie bezczynne"

#: src/ftpclass.cc:4604 src/Http.cc:2323 src/Resolver.cc:172
#: src/Resolver.cc:217 src/TorrentTracker.cc:622
#, c-format
msgid "Resolving host address..."
msgstr "Uzyskiwanie adresu hosta..."

#: src/ftpclass.cc:4615
msgid "TLS negotiation..."
msgstr "Negocjacja TLS..."

#: src/ftpclass.cc:4619
msgid "Logging in..."
msgstr "Logowanie się..."

#: src/ftpclass.cc:4623
msgid "Making data connection..."
msgstr "Ustanawianie połączenia dla danych..."

#: src/ftpclass.cc:4626
msgid "Changing remote directory..."
msgstr "Zmiana zdalnego katalogu..."

#: src/ftpclass.cc:4632
msgid "Waiting for other copy peer..."
msgstr "Oczekiwanie na inne kopiowanie..."

#: src/ftpclass.cc:4634 src/ftpclass.cc:4658
msgid "Waiting for transfer to complete"
msgstr "Oczekiwanie na zakończenie transferu"

#: src/ftpclass.cc:4638
msgid "Waiting for TLS shutdown..."
msgstr "Oczekiwanie na zakończenie TLS..."

#: src/ftpclass.cc:4640
msgid "Waiting for data connection..."
msgstr "Oczekiwanie na połączenie dla danych..."

#: src/ftpclass.cc:4646
msgid "Sending data/TLS"
msgstr "Wysyłam dane/TLS"

#: src/ftpclass.cc:4648
msgid "Receiving data/TLS"
msgstr "Otrzymuję dane/TLS"

#: src/Http.cc:230
#, c-format
msgid "Closing HTTP connection"
msgstr "Zamykanie połączenia HTTP"

#: src/Http.cc:240
msgid "POST method failed"
msgstr "nie powiodła się metoda POST"

#: src/Http.cc:1381
msgid "ftp over http cannot work without proxy, set hftp:proxy."
msgstr "ftp poprzez http może nie działać bez proxy, ustaw hftp:proxy"

#: src/Http.cc:1537
#, c-format
msgid "Sending request..."
msgstr "Wysyłanie żądania..."

#: src/Http.cc:1575
#, c-format
msgid "Hit EOF while fetching headers"
msgstr "Natrafiono na EOF podczas przesyłania nagłówków"

#: src/Http.cc:1695
#, c-format
msgid "Could not parse HTTP status line"
msgstr "Nie można zanalizować linii statusu HTTP"

#: src/Http.cc:1726
msgid "Object is not cached and http:cache-control has only-if-cached"
msgstr "Obiekt nie jest zapamiętany a http:cache-control ma only-if-cache"

#: src/Http.cc:1891
#, c-format
msgid "Receiving body..."
msgstr "Otrzymuję dane..."

#: src/Http.cc:2092
#, c-format
msgid "Hit EOF"
msgstr "Natrafiono na EOF"

#: src/Http.cc:2095
#, c-format
msgid "Received not enough data, retrying"
msgstr "Przesłanie nie wystarczającą ilość danych, próbuję ponownie"

#: src/Http.cc:2106
#, c-format
msgid "Received all"
msgstr "Przesłano wszystko"

#: src/Http.cc:2111
#, c-format
msgid "Received all (total)"
msgstr "Przesłano wszystko (całość)"

#: src/Http.cc:2135 src/Http.cc:2159
msgid "chunked format violated"
msgstr "naruszony format"

#: src/Http.cc:2145
#, c-format
msgid "Received last chunk"
msgstr "Przesłano ostatni fragment"

#: src/Http.cc:2339
msgid "Fetching headers..."
msgstr "Przesyłam nagłówki..."

#: src/Job.cc:293
#, c-format
msgid "[%d] Done (%s)"
msgstr "[%d] Zakończono (%s)"

#: src/lftp.cc:370
#, c-format
msgid "[%u] Terminated by signal %d. %s\n"
msgstr "[%u] Przerwany przez sygnał %d. %s\n"

#: src/lftp.cc:445
#, c-format
msgid "[%u] Started.  %s\n"
msgstr "[%u] Został uruchomiony.  %s\n"

#: src/lftp.cc:463
#, c-format
msgid "[%u] Detaching from the terminal to complete transfers...\n"
msgstr "[%u] Odłączanie od terminala w celu dokończenia transferów...\n"

#: src/lftp.cc:465
#, c-format
msgid "[%u] Exiting and detaching from the terminal.\n"
msgstr "[%u] Kończenie i odłączanie od terminala.\n"

#: src/lftp.cc:470
#, c-format
msgid "[%u] Detached from terminal. %s\n"
msgstr "[%u] Odłączono od terminala. %s\n"

#: src/lftp.cc:480
#, c-format
msgid "[%u] Finished. %s\n"
msgstr "[%u] Zakończono. %s\n"

#: src/lftp.cc:484
#, c-format
msgid "[%u] Moving to background to complete transfers...\n"
msgstr "[%u] Przechodzenie w tło w celu dokończenia transferów...\n"

#: src/lftp.cc:553
msgid "history -w file|-r file|-c|-l [cnt]"
msgstr "history -w plik|-r plik|-c|-l [licznik]"

#: src/lftp.cc:554
msgid ""
" -w <file> Write history to file.\n"
" -r <file> Read history from file; appends to current history.\n"
" -c  Clear the history.\n"
" -l  List the history (default).\n"
"Optional argument cnt specifies the number of history lines to list,\n"
"or \"all\" to list all entries.\n"
msgstr ""
" -w <plik> Zapisz historię do pliku.\n"
" -r <plik> Wczytaj historię z pliku; dołącza do aktualnej historii.\n"
" -c Wyczyść historię.\n"
" -l Wyświetl historię (domyślnie).\n"
"Opcjonalny argument licznik podaje liczbę linii z historii, które\n"
"mają być wyświetlone lub \"all\" by wyświetlić wszystkie wpisy.\n"

#: src/lftp.cc:561
msgid "Attach the terminal to specified backgrounded lftp process.\n"
msgstr "Dołączenie terminala do podanego procesu lftp działającego w tle.\n"

#: src/lftp_ssl.cc:1291
#, c-format
msgid "No certificate presented by %s.\n"
msgstr ""

#: src/LocalAccess.cc:604 src/NetAccess.cc:686
msgid "Getting directory contents"
msgstr "Pobieranie zawartości katalogu"

#: src/LocalAccess.cc:606 src/NetAccess.cc:690
msgid "Getting files information"
msgstr "Pobieranie informacji o plikach"

#: src/LsCache.cc:190
#, c-format
msgid "%ld $#l#byte|bytes$ cached"
msgstr "%ld bajt$#l#|y|ów$ w pamięci podręcznej"

#: src/LsCache.cc:194
msgid ", no size limit"
msgstr ", bez limitu rozmiaru"

#: src/LsCache.cc:196
#, c-format
msgid ", maximum size %ld\n"
msgstr ", maksymalny rozmiar %ld\n"

#: src/mgetJob.cc:78
#, c-format
msgid "%s: %s: no files found\n"
msgstr "%s: %s: nie znaleziono plików\n"

#: src/MirrorJob.cc:100
#, c-format
msgid "%sTotal: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
msgstr ""
"%sSumując: %d katalog$|i|ów$, %d plik$|i|ów$, %d $dowiązanie symboliczne|"
"dowiązania symboliczne|dowiązań symbolicznych$.\n"

#: src/MirrorJob.cc:104
#, c-format
msgid "%sNew: %d file$|s$, %d symlink$|s$\n"
msgstr ""
"%sNowych: %d plik$|i|ów$, %d $dowiązanie symboliczne|dowiązania symboliczne|"
"dowiązań symbolicznych$.\n"

#: src/MirrorJob.cc:108
#, c-format
msgid "%sModified: %d file$|s$, %d symlink$|s$\n"
msgstr ""
"%sZmodyfikowano: %d plik$|i|ów$, %d $dowiązanie symboliczne|dowiązania "
"symboliczne|dowiązań symbolicznych$.\n"

#: src/MirrorJob.cc:115
#, c-format
msgid "%sRemoved: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
msgstr ""
"%sUsunięto: %d katalog$|i|ów$, %d plik$|i|ów$, %d $dowiązanie symboliczne|"
"dowiązania symboliczne|dowiązań symbolicznych$.\n"

#: src/MirrorJob.cc:120
#, c-format
msgid "%s%d error$|s$ detected\n"
msgstr "%sWykryto %d bł$ąd|ędy|ędów$\n"

#: src/MirrorJob.cc:231
#, c-format
msgid "Finished %s"
msgstr "Zakończono %s"

#: src/MirrorJob.cc:344 src/MirrorJob.cc:519 src/MirrorJob.cc:1218
#, c-format
msgid "Removing old file `%s'"
msgstr "Usuwanie starego pliku `%s'"

#: src/MirrorJob.cc:346
#, c-format
msgid "Overwriting old file `%s'"
msgstr "Nadpisywanie starego pliku `%s'"

#: src/MirrorJob.cc:355
#, c-format
msgid "Skipping file `%s' (only-existing)"
msgstr "Pominięcie pliku `%s' (tylko istniejące)"

#: src/MirrorJob.cc:361
#, c-format
msgid "Transferring file `%s'"
msgstr "Przesyłanie pliku `%s'"

#: src/MirrorJob.cc:439
#, c-format
msgid "Skipping directory `%s' (only-existing)"
msgstr "Pominięcie katalogu `%s' (tylko istniejące)"

#: src/MirrorJob.cc:461 src/MirrorJob.cc:550 src/MirrorJob.cc:902
#, c-format
msgid "Removing old local file `%s'"
msgstr "Usuwanie starego lokalnego pliku `%s'"

#: src/MirrorJob.cc:487
#, c-format
msgid "Scanning directory `%s'"
msgstr "Przeszukiwanie katalogu `%s'"

#: src/MirrorJob.cc:489
#, c-format
msgid "Mirroring directory `%s'"
msgstr "Mirrorowanie katalogu `%s'"

#: src/MirrorJob.cc:525 src/MirrorJob.cc:567
#, c-format
msgid "Making symbolic link `%s' to `%s'"
msgstr "Tworzenie dowiązania symbolicznego `%s' do `%s'"

#: src/MirrorJob.cc:562
#, c-format
msgid "Skipping symlink `%s' (only-existing)"
msgstr "Pomijanie dowiązania symbolicznego `%s' (tylko istniejące)"

#: src/MirrorJob.cc:807
#, c-format
msgid "mirror: protocol `%s' is not suitable for mirror\n"
msgstr "mirror: protokół `%s' nieodpowiedni dla mirrora\n"

#: src/MirrorJob.cc:923
#, c-format
msgid "Making directory `%s'"
msgstr "Tworzenie katalogu `%s'"

#: src/MirrorJob.cc:1181
#, c-format
msgid "Old directory `%s' is not removed"
msgstr "Stary katalog `%s' nie został usunięty"

#: src/MirrorJob.cc:1183
#, c-format
msgid "Old file `%s' is not removed"
msgstr "Stary plik `%s' nie został usunięty"

#: src/MirrorJob.cc:1216
#, c-format
msgid "Removing old directory `%s'"
msgstr "Usuwanie starego katalogu `%s'"

#: src/MirrorJob.cc:1339
#, c-format
msgid "Removing source directory `%s'"
msgstr "Usuwanie katalogu źródłowego `%s'"

#: src/MirrorJob.cc:1405
#, c-format
msgid "Removing source file `%s'"
msgstr "Usuwanie pliku źródłowego `%s'"

#: src/MirrorJob.cc:1425
#, c-format
msgid "Retrying mirror...\n"
msgstr "Ponowne próbowanie mirrora...\n"

#: src/MirrorJob.cc:1694
msgid "pattern is empty"
msgstr ""

#: src/MirrorJob.cc:1779
#, c-format
msgid "%s must be one of: %s"
msgstr "%s musi być jednym z: %s"

#: src/MirrorJob.cc:2019
#, c-format
msgid ""
"%s: multiple --file or --directory options must have the same base "
"directory\n"
msgstr ""

#: src/MirrorJob.cc:2160
#, c-format
msgid "%s: ambiguous source directory (`%s' or `%s'?)\n"
msgstr "%s: niejednoznaczny katalog źródłowy (`%s' czy `%s'?)\n"

#: src/MirrorJob.cc:2182
#, c-format
msgid "%s: ambiguous target directory (`%s' or `%s'?)\n"
msgstr "%s: niejednoznaczny katalog docelowy (`%s' czy `%s'?)\n"

#: src/MirrorJob.cc:2223
#, c-format
msgid "%s: source directory is required (mirror:require-source is set)\n"
msgstr ""
"%s: katalog źródłowy jest wymagany (mirror:require-source jest ustawione)\n"

#: src/MirrorJob.cc:2343
msgid ""
"\n"
"Mirror specified remote directory to local directory\n"
"\n"
" -R, --reverse          reverse mirror (put files)\n"
"Lots of other options are documented in the man page lftp(1).\n"
"\n"
"When using -R, the first directory is local and the second is remote.\n"
"If the second directory is omitted, basename of the first directory is "
"used.\n"
"If both directories are omitted, current local and remote directories are "
"used.\n"
"\n"
"See the man page lftp(1) for a complete documentation.\n"
msgstr ""

#: src/mkdirJob.cc:128
#, c-format
msgid "%s ok, `%s' created\n"
msgstr "%s ok, `%s' utworzono\n"

#: src/mkdirJob.cc:130 src/rmJob.cc:51
#, c-format
msgid "%s failed for %d of %d director$y|ies$\n"
msgstr "%s nie udało się %d z %d katalog$|i|ów$\n"

#: src/mkdirJob.cc:133
#, c-format
msgid "%s ok, %d director$y|ies$ created\n"
msgstr "%s ok, utworzono %d katalog$|i|ów$\n"

#: src/module.cc:190
#, c-format
msgid "depend module `%s': %s\n"
msgstr "zależności modułu `%s': %s\n"

#: src/module.cc:210
msgid "modules are not supported on this system"
msgstr "moduły nie są wspierane na tym systemie"

#: src/mvJob.cc:92
#, c-format
msgid "rename successful\n"
msgstr "zmiana nazwy powiodła się\n"

#: src/NetAccess.cc:168
#, c-format
msgid "Connecting to %s%s (%s) port %u"
msgstr "Łączenie się z %s%s (%s) port %u"

#: src/NetAccess.cc:224 src/Torrent.cc:3326
#, c-format
msgid "Timeout - reconnecting"
msgstr "Przekroczony limit czasu - łączę się ponownie"

#: src/NetAccess.cc:323
msgid "Connection limit reached"
msgstr "Osiągnięto limit połączeń"

#: src/NetAccess.cc:330
msgid "Delaying before reconnect"
msgstr "Przerwa przed ponownym połączeniem"

#: src/NetAccess.cc:354 src/NetAccess.cc:356
msgid "max-retries exceeded"
msgstr "przekroczona maksymalna liczba prób"

#: src/OutputJob.cc:145
#, c-format
msgid "%s (filter)"
msgstr "%s (filtr)"

#: src/parsecmd.cc:290
msgid "parse: missing filter command\n"
msgstr "analizowanie: brakuje komendy filtra\n"

#: src/parsecmd.cc:292
msgid "parse: missing redirection filename\n"
msgstr "analizowanie: brakuje pliku docelowego dla przekierowania\n"

#: src/PatternSet.cc:110
#, c-format
msgid "regular expression `%s': %s"
msgstr ""

#: src/pgetJob.cc:127
msgid "pget: falling back to plain get"
msgstr "pget: degradacja do zwykłego get"

#: src/pgetJob.cc:131
msgid "the target file is remote"
msgstr "plik docelowy jest zdalny"

#: src/pgetJob.cc:136
msgid "the source file size is unknown"
msgstr "nieznany rozmiar pliku źródłowego"

#: src/pgetJob.cc:173
#, c-format
msgid "pget: warning: space allocation for %s (%lld bytes) failed: %s\n"
msgstr ""
"pget: uwaga: przydzielenie miejsca dla %s (bajtów: %lld) nie powiodło się: "
"%s\n"

#: src/pgetJob.cc:234
#, c-format
msgid "`%s', got %lld of %lld (%d%%) %s%s"
msgstr "`%s', mam %lld z %lld (%d%%) %s%s"

#: src/plural.c:96
msgid "=1 =0|>1"
msgstr "=1 >1<5|>20%10>1<5"

#: src/PollVec.cc:69
#, c-format
msgid "%s: BUG - deadlock detected\n"
msgstr "%s: BŁĄD - wykryto zastój\n"

#: src/PtyShell.cc:68
msgid "pseudo-tty allocation failed: "
msgstr "przydzielenie pseudo-tty nie powiodło się: "

#: src/QueueFeeder.cc:68
msgid "Added job$|s$"
msgstr "Dodano zadani$e|a|a$"

#: src/QueueFeeder.cc:164 src/QueueFeeder.cc:185
#, c-format
msgid "No queued jobs.\n"
msgstr "Nie ma żadnych skolejkowanych zadań.\n"

#: src/QueueFeeder.cc:166
#, c-format
msgid "No queued job #%i.\n"
msgstr "Nie ma skolejkowanego zadania %i.\n"

#: src/QueueFeeder.cc:171 src/QueueFeeder.cc:192
msgid "Deleted job$|s$"
msgstr "Usunięto zadani$e|a|a$"

#: src/QueueFeeder.cc:187
#, c-format
msgid "No queued jobs match \"%s\".\n"
msgstr "Nie ma skolejkowanego zadania pasującego do \"%s\".\n"

#: src/QueueFeeder.cc:212 src/QueueFeeder.cc:230
msgid "Moved job$|s$"
msgstr "Przesunięto zadani$e|a|a$"

#: src/QueueFeeder.cc:354
msgid "Commands queued:"
msgstr "Skolejkowane polecenia:"

#: src/ResMgr.cc:123
msgid "no such variable"
msgstr "nie ma takiej zmiennej"

#: src/ResMgr.cc:127
msgid "ambiguous variable name"
msgstr "dwuznaczna nazwa zmiennej"

#: src/ResMgr.cc:335
msgid "invalid boolean value"
msgstr "nieprawidłowa wartość zmiennej logicznej (bool)"

#: src/ResMgr.cc:357
msgid "invalid boolean/auto value"
msgstr "nieprawidłowa wartość zmiennej bool/auto"

#: src/ResMgr.cc:402 src/ResMgr.cc:713
msgid "invalid number"
msgstr "nieprawidłowy numer"

#: src/ResMgr.cc:415
msgid "invalid floating point number"
msgstr "nieprawidłowa liczba zmiennoprzecinkowa"

#: src/ResMgr.cc:429
msgid "invalid unsigned number"
msgstr "nieprawidłowa liczba bez znaku"

#: src/ResMgr.cc:678
msgid "Invalid time unit letter, only [smhd] are allowed."
msgstr "Nieprawidłowa litera-jednostka czasu, wyłącznie [shmd] są dozwolone."

#: src/ResMgr.cc:686
msgid "Invalid time format. Format is <time><unit>, e.g. 2h30m."
msgstr "Nieprawidłowy format czasu. Format to <czas><jednostka>, np. 2h30m."

#: src/ResMgr.cc:718
msgid "integer overflow"
msgstr "przepełnienie liczby całkowitej"

#: src/ResMgr.cc:804
msgid "Invalid IPv4 numeric address"
msgstr "Nieprawidłowy numeryczny adres IPv4"

#: src/ResMgr.cc:814
msgid "Invalid IPv6 numeric address"
msgstr "Nieprawidłowy numeryczny adres IPv6"

#: src/ResMgr.cc:884 src/ResMgr.cc:888
msgid "this encoding is not supported"
msgstr "to kodowanie nie jest wspierane"

#: src/ResMgr.cc:894
msgid "no closure defined for this setting"
msgstr "nie zdefiniowano zamknięcia dla tego ustawienia"

#: src/ResMgr.cc:900
#, fuzzy
msgid "a closure is required for this setting"
msgstr "nie zdefiniowano zamknięcia dla tego ustawienia"

#: src/Resolver.cc:236
msgid "host name resolve timeout"
msgstr "przekroczony czas rozwiązywania nazwy hosta"

#: src/Resolver.cc:282
#, c-format
msgid "%d address$|es$ found"
msgstr "Znaleziono %d adres$|y|ów$"

#: src/Resolver.cc:327
msgid "Link-local IPv6 address should have a scope"
msgstr ""

#: src/Resolver.cc:765
msgid "DNS resolution not trusted."
msgstr "Niezaufane rozwiązanie nazwy przez DNS."

#: src/Resolver.cc:871
msgid "Host name lookup failure"
msgstr "Rozwiązywanie nazwy hosta nie powiodło się"

#: src/Resolver.cc:903
#, c-format
msgid "no such %s service"
msgstr "nie ma takiej usługi %s"

#: src/Resolver.cc:930
msgid "No address found"
msgstr "Nie znaleziono adresu"

#: src/resource.cc:50 src/resource.cc:108
msgid "Proxy protocol unsupported"
msgstr "Brak wsparcia dla protokołu proxy"

#: src/resource.cc:54
msgid "ftp:proxy password: "
msgstr "ftp:proxy hasło: "

#: src/resource.cc:70
#, c-format
msgid "%s must be one of: "
msgstr "%s musi być jednym z: "

#: src/resource.cc:72
msgid "must be one of: "
msgstr "musi być jednym z: "

#: src/resource.cc:84
msgid ", or empty"
msgstr " lub puste"

#: src/resource.cc:116
msgid "only PUT and POST values allowed"
msgstr "tylko wartości PUT i POST są dozwolone"

#: src/resource.cc:148
#, c-format
msgid "unknown address family `%s'"
msgstr "nieznana rodzina adresu `%s'"

#: src/rmJob.cc:47
#, c-format
msgid "%s ok, `%s' removed\n"
msgstr "%s ok, `%s' usunięto\n"

#: src/rmJob.cc:54
#, c-format
msgid "%s failed for %d of %d file$|s$\n"
msgstr "%s nie powiodło się dla %d z %d plik$|i|ów$\n"

#: src/rmJob.cc:60
#, c-format
msgid "%s ok, %d director$y|ies$ removed\n"
msgstr "%s ok, %d katalog$|i|ów$ usunięto\n"

#: src/rmJob.cc:63
#, c-format
msgid "%s ok, %d file$|s$ removed\n"
msgstr "%s ok, %d plik$|i|ów$ usunięto\n"

#: src/SFtp.cc:1099 src/SFtp.cc:1100
#, c-format
msgid "invalid server response format"
msgstr "nieprawidłowy format odpowiedzi serwera"

#: src/SleepJob.cc:99
msgid "Sleeping forever"
msgstr "Zasypianie na zawsze"

#: src/SleepJob.cc:100
msgid "Sleep time left: "
msgstr "Pozostały czas snu: "

#: src/SleepJob.cc:108
#, c-format
msgid "\tRepeat count: %d\n"
msgstr "\tLicznik powtórek: %d\n"

#: src/SleepJob.cc:142
#, c-format
msgid "%s: argument required. "
msgstr "%s: wymagany jest argument. "

#: src/SleepJob.cc:262
#, c-format
msgid "%s: date-time specification missed\n"
msgstr "%s: brak określenia daty-czasu\n"

#: src/SleepJob.cc:269
#, c-format
msgid "%s: date-time parse error\n"
msgstr "%s: błąd składni daty-czasu\n"

#: src/SleepJob.cc:303
msgid ""
"Usage: sleep <time>[unit]\n"
"Sleep for given amount of time. The time argument can be optionally\n"
"followed by unit specifier: d - days, h - hours, m - minutes, s - seconds.\n"
"By default time is assumed to be seconds.\n"
msgstr ""
"Użycie: sleep <czas>[jednostka]\n"
"Śpij przez określony okres czasu. Argument czasu jest opcjonalny,\n"
"następnie litera-jednostka: d - dni, h - godziny, m - minuty, s - sekundy.\n"
"Domyślna jednostka czasu to sekundy.\n"

#: src/SleepJob.cc:310
msgid ""
"Repeat specified command with a delay between iterations.\n"
"Default delay is one second, default command is empty.\n"
" -c <count>  maximum number of iterations\n"
" -d <delay>  delay between iterations\n"
" --while-ok  stop when command exits with non-zero code\n"
" --until-ok  stop when command exits with zero code\n"
" --weak      stop when lftp moves to background.\n"
msgstr ""
"Powtarzaj podaną komendę z opóźnieniem pomiędzy iteracjami.\n"
"Domyślne opóźnienie to jedna sekunda, domyślna komenda jest pusta.\n"
" -c <liczba>      liczba powtórzeń\n"
" -d <opóźnienie>  opóźnienie między powtórzeniami\n"
" --while-ok       zatrzymanie kiedy polecenie zakończy się kodem niezerowym\n"
" --until-ok       zatrzymanie kiedy polecenie zakończy się kodem zerowym\n"
" --weak           zatrzymanie kiedy lftp przejdzie w tło.\n"

#: src/Speedometer.cc:90
#, c-format
msgid "%.0fb/s"
msgstr "%.0fb/s"

#: src/Speedometer.cc:93
#, c-format
msgid "%.1fK/s"
msgstr "%.1fK/s"

#: src/Speedometer.cc:96
#, c-format
msgid "%.2fM/s"
msgstr "%.2fM/s"

#: src/Speedometer.cc:103
#, c-format
msgid "%.0f B/s"
msgstr "%.0f B/s"

#: src/Speedometer.cc:105
#, c-format
msgid "%.1f KiB/s"
msgstr "%.1f KiB/s"

#: src/Speedometer.cc:107
#, c-format
msgid "%.2f MiB/s"
msgstr "%.2f MiB/s"

#: src/Speedometer.cc:129
msgid "eta:"
msgstr "pozostało: "

#: src/SSH_Access.cc:97
#, fuzzy
msgid "Password required"
msgstr "Hasło: "

#: src/SSH_Access.cc:102
msgid "Login incorrect"
msgstr ""

#: src/SSH_Access.cc:196
#, fuzzy, c-format
msgid "Disconnecting"
msgstr "Łączenie się..."

#: src/SysCmdJob.cc:73
#, c-format
msgid "execlp(%s) failed: %s\n"
msgstr "execlp(%s) nieudane: %s\n"

#: src/TimeDate.cc:155
msgid "day"
msgstr ""

#: src/TimeDate.cc:156
msgid "hour"
msgstr ""

#: src/TimeDate.cc:157
msgid "minute"
msgstr ""

#: src/TimeDate.cc:158
msgid "second"
msgstr ""

#: src/Torrent.cc:585
msgid "announced via "
msgstr "rozgłoszony poprzez "

#: src/Torrent.cc:599
#, c-format
msgid "next announce in %s"
msgstr "następne rozgłoszenie w %s"

#: src/Torrent.cc:1142
#, c-format
msgid "%d file$|s$ found, now scanning %s"
msgstr "Znaleziono %d plik$|i|ów$, teraz przeszukiwanie %s"

#: src/Torrent.cc:1143
#, c-format
msgid "%d file$|s$ found"
msgstr "Znaleziono %d plik$|i|ów$"

#: src/Torrent.cc:2075 src/Torrent.cc:2085
#, c-format
msgid "Getting meta-data: %s"
msgstr "Pobieranie metadanych: %s"

#: src/Torrent.cc:2077
#, c-format
msgid "Validation: %u/%u (%u%%) %s%s"
msgstr "Sprawdzanie poprawności: %u/%u (%u%%) %s%s"

#: src/Torrent.cc:2088
msgid "Waiting for meta-data..."
msgstr "Oczekiwanie na metadane..."

#: src/Torrent.cc:2096
msgid "Shutting down: "
msgstr "Zamykanie: "

#: src/Torrent.cc:3207
#, c-format
msgid "Connecting to peer %s port %u"
msgstr "Łączenie się z użytkownikiem %s na porcie %u"

#: src/Torrent.cc:3258 src/Torrent.cc:3375
#, c-format
msgid "peer unexpectedly closed connection after %s"
msgstr "druga strona nieoczekiwanie zamknęła połączenie po %s"

#: src/Torrent.cc:3259 src/Torrent.cc:3376
msgid "peer unexpectedly closed connection"
msgstr "druga strona nieoczekiwanie zamknęła połączenie"

#: src/Torrent.cc:3261 src/Torrent.cc:3262
#, c-format
msgid "peer closed connection (before handshake)"
msgstr "druga strona zamknęła połączenie (przed przywitaniem)"

#: src/Torrent.cc:3265 src/Torrent.cc:3378 src/Torrent.cc:3379
#, c-format
msgid "invalid peer response format"
msgstr "nieprawidłowy format odpowiedzi użytkownika"

#: src/Torrent.cc:3360 src/Torrent.cc:3361
#, c-format
msgid "peer closed connection"
msgstr "druga strona zamknęła połączenie"

#: src/Torrent.cc:3538
msgid "Handshaking..."
msgstr "Łączenie..."

#: src/Torrent.cc:3798
msgid "Cannot bind a socket for torrent:port-range"
msgstr "Nie można dowiązać gniazda do torrent:zakres-portów"

#: src/Torrent.cc:3858
#, c-format
msgid "Accepted connection from [%s]:%d"
msgstr "Przyjęto połączenie od [%s]:%d"

#: src/Torrent.cc:3924
#, c-format
msgid "peer sent unknown info_hash=%s in handshake"
msgstr "druga strona wysłała nieznane info_hash=%s przy przywitaniu"

#: src/Torrent.cc:3948
#, c-format
msgid "peer handshake timeout"
msgstr "upłynął limit czasu przywitania z drugą stroną"

#: src/Torrent.cc:3960
#, c-format
msgid "peer short handshake"
msgstr "przywitanie z drugą stroną zostało ucięte"

#: src/Torrent.cc:3962
#, c-format
msgid "peer closed just accepted connection"
msgstr "druga strona zamknęła świeżo przyjęte połączenie"

#: src/Torrent.cc:4013
#, c-format
msgid "Seeding in background...\n"
msgstr "Karmienie w tle...\n"

#: src/Torrent.cc:4176
#, c-format
msgid "%s: --share conflicts with --output-directory.\n"
msgstr "%s: --share jest w konflikcie z --output-directory.\n"

#: src/Torrent.cc:4180
#, c-format
msgid "%s: --share conflicts with --only-new.\n"
msgstr "%s: --share jest w konflikcie z --only-new.\n"

#: src/Torrent.cc:4184
#, c-format
msgid "%s: --share conflicts with --only-incomplete.\n"
msgstr "%s: --share jest w konflikcie z --only-incomplete.\n"

#: src/Torrent.cc:4226
#, c-format
msgid "%s: Please specify a file or directory to share.\n"
msgstr "%s: Proszę podać plik lub katalog do współdzielenia.\n"

#: src/Torrent.cc:4228
#, c-format
msgid "%s: Please specify meta-info file or URL.\n"
msgstr "%s: Proszę podać plik z metadanymi lub URL.\n"

#: src/Torrent.cc:4258
msgid ""
"Start BitTorrent job for the given torrent-files, which can be a local "
"file,\n"
"URL, magnet link or plain info_hash written in hex or base32. Local "
"wildcards\n"
"are expanded. Options:\n"
" -O <base>      specifies base directory where files should be placed\n"
" --force-valid  skip file validation\n"
" --dht-bootstrap=<node>  bootstrap DHT by sending a query to the node\n"
" --share        share specified file or directory\n"
msgstr ""
"Rozpoczęcie zadania BitTorrent dla podanych plików torrent, które mogą być\n"
"plikami lokalnymi, URL-ami, odnośnikami magnet lub surowym info_hashem,\n"
"zapisanym szesnastkowo lub w base32. Lokalne znaki glob są rozwijane.\n"
"Opcje:\n"
" -O <katalog>   określenie katalogu bazowego dla umieszczanych plików\n"
" --force-valid  pominięcie kontroli poprawności plików\n"
" --dht-bootstrap=<węzeł>  rozruch DHT poprzez wysłanie zapytania do węzła\n"
" --share        współdzielenie podanego pliku lub katalogu\n"

#: src/TorrentTracker.cc:178
msgid "not started"
msgstr "nierozpoczęte"

#: src/TorrentTracker.cc:181
#, c-format
msgid "next request in %s"
msgstr "następne żądanie w %s"

#: src/TorrentTracker.cc:284 src/TorrentTracker.cc:501
#, c-format
msgid "Received valid info about %d peer$|s$"
msgstr "Otrzymano poprawne informacje o %d użytkownik$u|ach|ach$"

#: src/TorrentTracker.cc:298
#, c-format
msgid "Received valid info about %d IPv6 peer$|s$"
msgstr "Otrzymano poprawne informacje o %d użytkownik$u|ach|ach$ IPv6"

#~ msgid "%s: option '--%s' doesn't allow an argument\n"
#~ msgstr "%s: opcja '--%s' nie może mieć argumentów\n"

#~ msgid "%s: unrecognized option '--%s'\n"
#~ msgstr "%s: nieznana opcja '--%s'\n"

#~ msgid "%s: option '-W %s' is ambiguous\n"
#~ msgstr "%s: opcja '-W %s' jest niejednoznaczna\n"

#~ msgid "%s: option '-W %s' doesn't allow an argument\n"
#~ msgstr "%s: opcja '-W %s' nie może mieć argumentów\n"

#~ msgid "%s: option '-W %s' requires an argument\n"
#~ msgstr "%s: opcja '-W %s' musi mieć argument\n"

#~ msgid "Usage: mv <file1> <file2>\n"
#~ msgstr "Użycie: mv <plik1> <plik2>\n"

#, fuzzy
#~ msgid "number"
#~ msgstr "nieprawidłowy numer"

#, fuzzy
#~ msgid ""
#~ "\n"
#~ "Mirror specified remote directory to local directory\n"
#~ "\n"
#~ " -c, --continue         continue a mirror job if possible\n"
#~ " -e, --delete           delete files not present at remote site\n"
#~ "     --delete-first     delete old files before transferring new ones\n"
#~ " -s, --allow-suid       set suid/sgid bits according to remote site\n"
#~ "     --allow-chown      try to set owner and group on files\n"
#~ "     --ignore-time      ignore time when deciding whether to download\n"
#~ " -n, --only-newer       download only newer files (-c won't work)\n"
#~ " -r, --no-recursion     don't go to subdirectories\n"
#~ " -p, --no-perms         don't set file permissions\n"
#~ "     --no-umask         don't apply umask to file modes\n"
#~ " -R, --reverse          reverse mirror (put files)\n"
#~ " -L, --dereference      download symbolic links as files\n"
#~ " -N, --newer-than=SPEC  download only files newer than specified time\n"
#~ " -P, --parallel[=N]     download N files in parallel\n"
#~ " -i RX, --include RX    include matching files\n"
#~ " -x RX, --exclude RX    exclude matching files\n"
#~ "                        RX is extended regular expression\n"
#~ " -v, --verbose[=N]      verbose operation\n"
#~ "     --log=FILE         write lftp commands being executed to FILE\n"
#~ "     --script=FILE      write lftp commands to FILE, but don't execute "
#~ "them\n"
#~ "     --just-print, --dry-run    same as --script=-\n"
#~ "\n"
#~ "When using -R, the first directory is local and the second is remote.\n"
#~ "If the second directory is omitted, basename of first directory is used.\n"
#~ "If both directories are omitted, current local and remote directories are "
#~ "used.\n"
#~ "See lftp(1) for a complete list of options.\n"
#~ msgstr ""
#~ "\n"
#~ "Mirror wskazanego zdalnego katalogu do lokalnego katalogu\n"
#~ "\n"
#~ " -c, --continue         kontynuuj pracę mirrora o ile to możliwe\n"
#~ " -e, --delete           skasuj pliki nie występujące w zdalnym serwisie\n"
#~ "     --delete-first     skasuj stare pliki przed ściągnięciem nowych\n"
#~ " -s, --allow-suid       ustaw bity suid/sgid zgodnie ze zdalnymi\n"
#~ "     --allow-chown      próbuj ustawiać właściciela i grupę na plikach\n"
#~ "     --ignore-time      ignoruj czas przy decydowaniu czy ściągać\n"
#~ " -n, --only-newer       pozyskuj tylko nowsze pliki (-c nie działa)\n"
#~ " -r, --no-recursion     nie wchodź do podkatalogów\n"
#~ " -p, --no-perms         nie ustawiaj praw dostępu do plików\n"
#~ "     --no-umask         nie używaj umaski dla praw plików\n"
#~ " -R, --reverse          przeciwny mirror (umieszczaj pliki)\n"
#~ " -L, --dereference      pozyskuj dowiązania symboliczne jako pliki\n"
#~ " -N, --newer-than PLIK  pozyskuj pliki tylko nowsze niż PLIK\n"
#~ " -P, --parallel[=N]     pozyskuj N plików równolegle\n"
#~ " -i RX, --include RX    dołącz pasujące pliki\n"
#~ " -x RX, --exclude RX    wyłącz pasujące pliki\n"
#~ "                        RX jest rozszerzonym wyrażeniem regularnym\n"
#~ " -v, --verbose[=N]      dodatkowe komunikaty\n"
#~ "     --log=PLIK         zapisz wykonywane polecenia lftp do PLIK\n"
#~ "     --script=PLIK      zapisz polecenia lftp do PLIK ale nie wykonuj "
#~ "ich\n"
#~ "     --just-print       to samo co --script=-\n"
#~ "     --dry-run          to samo co --script=-\n"
#~ "\n"
#~ "Przy opcji -R, pierwszy katalog jest lokalnym, a drugi zdalnym. Jeżeli "
#~ "nie\n"
#~ "podano nazwy drugiego katalogu, używana jest nazwa pierwszego. Jeżeli "
#~ "nie\n"
#~ "podano nazw obu katalogów, to będą użyte bieżące katalogi lokalny i "
#~ "zdalny.\n"
