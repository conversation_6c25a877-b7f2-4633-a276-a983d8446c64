LFTP
====
Command line driven, shell-like, reliable file transfer program. It supports a number of protocols and even BitTorrent with DHT! IPv6 is fully supported too.

To build from GIT sources, run `autogen.sh` script to create Makefiles. You will need autoconf, libtool, gettext-devel, automake, gnulib.
You can get gnulib using `git clone git://git.savannah.gnu.org/gnulib`.

Also for compiling lftp you will need these libraries:
* readline-devel
* zlib-devel
* gnutls-devel or openssl-devel (optional)
* expat-devel (optional)

[![Flattr this git repo](http://api.flattr.com/button/flattr-badge-large.png)](https://flattr.com/submit/auto?user_id=lavv17&url=https://github.com/lavv17/lftp&title=LFTP+-+sophisticated+file+transfer+program&language=en_GB&tags=github&category=software)
