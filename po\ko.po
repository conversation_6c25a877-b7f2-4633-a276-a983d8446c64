# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR Free Software Foundation, Inc.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 1999-2001.
#
msgid ""
msgstr ""
"Project-Id-Version: lftp 2.1.1\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2024-11-08 13:05+0300\n"
"PO-Revision-Date: 2001-05-27 KST\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: ko\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8-bit\n"

#: lib/argmatch.c:145
#, fuzzy, c-format
msgid "invalid argument %s for %s"
msgstr "잘못된 수입니다"

#: lib/argmatch.c:146
#, c-format
msgid "ambiguous argument %s for %s"
msgstr ""

#: lib/argmatch.c:165
msgid "Valid arguments are:"
msgstr ""

#: lib/error.c:208
msgid "Unknown system error"
msgstr ""

#: lib/getopt.c:282
#, c-format
msgid "%s: option '%s%s' is ambiguous\n"
msgstr ""

#: lib/getopt.c:288
#, c-format
msgid "%s: option '%s%s' is ambiguous; possibilities:"
msgstr ""

#: lib/getopt.c:322
#, fuzzy, c-format
msgid "%s: unrecognized option '%s%s'\n"
msgstr "%s: `%s'으로 리다이렉션을 받음\n"

#: lib/getopt.c:348
#, c-format
msgid "%s: option '%s%s' doesn't allow an argument\n"
msgstr ""

#: lib/getopt.c:363
#, c-format
msgid "%s: option '%s%s' requires an argument\n"
msgstr ""

#: lib/getopt.c:624
#, fuzzy, c-format
msgid "%s: invalid option -- '%c'\n"
msgstr "잘못된 수입니다"

#: lib/getopt.c:639 lib/getopt.c:685
#, c-format
msgid "%s: option requires an argument -- '%c'\n"
msgstr ""

#. TRANSLATORS:
#. Get translations for open and closing quotation marks.
#. The message catalog should translate "`" to a left
#. quotation mark suitable for the locale, and similarly for
#. "'".  For example, a French Unicode local should translate
#. these to U+00AB (LEFT-POINTING DOUBLE ANGLE
#. QUOTATION MARK), and U+00BB (RIGHT-POINTING DOUBLE ANGLE
#. QUOTATION MARK), respectively.
#.
#. If the catalog has no translation, we will try to
#. use Unicode U+2018 (LEFT SINGLE QUOTATION MARK) and
#. Unicode U+2019 (RIGHT SINGLE QUOTATION MARK).  If the
#. current locale is not Unicode, locale_quoting_style
#. will quote 'like this', and clocale_quoting_style will
#. quote "like this".  You should always include translations
#. for "`" and "'" even if U+2018 and U+2019 are appropriate
#. for your locale.
#.
#. If you don't know what to put here, please see
#. <https://en.wikipedia.org/wiki/Quotation_marks_in_other_languages>
#. and use glyphs suitable for your language.
#: lib/quotearg.c:354
msgid "`"
msgstr ""

#: lib/quotearg.c:355
msgid "'"
msgstr ""

#: lib/xalloc-die.c:34
msgid "memory exhausted"
msgstr ""

#: src/ArgV.cc:107
msgid "option requires an argument"
msgstr ""

#: src/ArgV.cc:109 src/ArgV.cc:118
#, fuzzy
msgid "invalid option"
msgstr "잘못된 수입니다"

#: src/ArgV.cc:114
#, c-format
msgid "option `%s' requires an argument"
msgstr ""

#: src/ArgV.cc:116
#, fuzzy, c-format
msgid "unrecognized option `%s'"
msgstr "%s: `%s'으로 리다이렉션을 받음\n"

#: src/attach.h:138
#, fuzzy, c-format
msgid "[%u] Attached to terminal %s. %s\n"
msgstr "[%lu] 시그널 %d 로 종료되었습니다. %s"

#: src/attach.h:150
#, c-format
msgid "[%u] Attached to terminal.\n"
msgstr ""

#: src/buffer.cc:253 src/FileAccess.cc:866
msgid " [cached]"
msgstr ""

#: src/ChmodJob.cc:80
#, c-format
msgid "Failed to change mode of `%s' to %04o (%s).\n"
msgstr ""

#: src/ChmodJob.cc:83
#, c-format
msgid "Mode of `%s' changed to %04o (%s).\n"
msgstr ""

#: src/ChmodJob.cc:88
#, c-format
msgid "Failed to change mode of `%s' because no old mode is available.\n"
msgstr ""

#: src/CmdExec.cc:105
#, c-format
msgid "Warning: chdir(%s) failed: %s\n"
msgstr "경고: chdir(%s) 실패: %s\n"

#: src/CmdExec.cc:207
#, c-format
msgid "Unknown command `%s'.\n"
msgstr "`%s' 알수 없는 명령어.\n"

#: src/CmdExec.cc:209
#, c-format
msgid "Ambiguous command `%s'.\n"
msgstr "`%s' 모호한 명령어.\n"

#: src/CmdExec.cc:228
#, c-format
msgid "Module for command `%s' did not register the command.\n"
msgstr "`%s'명령에 대한 모듈이 등록되어 있지 않습니다.\n"

#: src/CmdExec.cc:384
#, c-format
msgid "cd ok, cwd=%s\n"
msgstr "cd 성공, cwd=%s\n"

#: src/CmdExec.cc:405 src/MirrorJob.cc:736
#, c-format
msgid "%s: received redirection to `%s'\n"
msgstr "%s: `%s'으로 리다이렉션을 받음\n"

#: src/CmdExec.cc:408 src/FileCopy.cc:1230
msgid "Too many redirections"
msgstr "리다이렉션이 너무 많습니다"

#: src/CmdExec.cc:517 src/CmdExec.cc:574
msgid "Interrupt"
msgstr "인터럽트"

#: src/CmdExec.cc:639
#, c-format
msgid "Warning: discarding incomplete command\n"
msgstr "경고: 불완전한 명령어 무시\n"

#: src/CmdExec.cc:737
#, c-format
msgid "\tExecuting builtin `%s' [%s]\n"
msgstr "\t내장 실행 `%s' [%s]\n"

#: src/CmdExec.cc:742
msgid "Queue is stopped."
msgstr ""

#: src/CmdExec.cc:747
msgid "Now executing:"
msgstr "현재 실행중:"

#: src/CmdExec.cc:758
#, c-format
msgid "\tWaiting for job [%d] to terminate\n"
msgstr "\t작업 [%d]의 종료를 기다리는 중\n"

#: src/CmdExec.cc:761
#, c-format
msgid "\tWaiting for termination of jobs: "
msgstr "\t작업 종료를 위하여 기다리는 중: "

#: src/CmdExec.cc:770
msgid "\tRunning\n"
msgstr "\t실행중\n"

#: src/CmdExec.cc:772
msgid "\tWaiting for command\n"
msgstr "\t명령을 기다리는 중\n"

#: src/CmdExec.cc:1261
#, c-format
msgid "%s: command `%s' is not compiled in.\n"
msgstr "%s: `%s'명령은 컴파일되지 않았습니다.\n"

#: src/CmdExec.cc:1278
#, fuzzy, c-format
msgid "Usage: %s cmd [args...]\n"
msgstr "사용법: %s module [args...]\n"

#: src/CmdExec.cc:1284
#, c-format
msgid "%s: cannot create local session\n"
msgstr ""

#: src/commands.cc:114
msgid "!<shell-command>"
msgstr ""

#: src/commands.cc:115
msgid "Launch shell or shell command\n"
msgstr "쉘 나들이 또는 쉘 명령어\n"

#: src/commands.cc:116
msgid "(commands)"
msgstr ""

#: src/commands.cc:117
msgid ""
"Group commands together to be executed as one command\n"
"You can launch such a group in background\n"
msgstr ""
"하나의 명령어로써 실행할 수 있도록 명령어를 그룹으로 지정\n"
"백그라운드에서 하나의 그룹과 같이 실행할 수 있습니다\n"

#: src/commands.cc:120
msgid "alias [<name> [<value>]]"
msgstr ""

#: src/commands.cc:121
msgid ""
"Define or undefine alias <name>. If <value> omitted,\n"
"the alias is undefined, else is takes the value <value>.\n"
"If no argument is given the current aliases are listed.\n"
msgstr ""
"alias <name> 를 정의 또는 해제. <value>를 생략했다면,\n"
"alias는 해제되며, 반대인 경우는 <value> 값을 갖습니다.\n"
"어떤 인수도 주지 않으면 현재 aliase들을 보여줍니다.\n"

#: src/commands.cc:125
msgid "anon - login anonymously (by default)\n"
msgstr "anon - 익명으로 로그인 (기본)\n"

#: src/commands.cc:127
msgid "bookmark [SUBCMD]"
msgstr ""

#: src/commands.cc:128
msgid ""
"bookmark command controls bookmarks\n"
"\n"
"The following subcommands are recognized:\n"
"  add <name> [<loc>] - add current place or given location to bookmarks\n"
"                       and bind to given name\n"
"  del <name>         - remove bookmark with the name\n"
"  edit               - start editor on bookmarks file\n"
"  import <type>      - import foreign bookmarks\n"
"  list               - list bookmarks (default)\n"
msgstr ""
"bookmark 명령어는 bookmark를 제어합니다\n"
"\n"
"서브명령어는 다음과 같습니다:\n"
"  add <name> [<loc>] - 현재 위치하는 곳 또는 북마크(bookmark)에 주어진\n"
"                       곳을 추가합니다. 그리고 주어진 이름으로 표시합니다\n"
"  del <name>         - 이름을 가지는 북마크를 지웁니다\n"
"  edit               - 북마크 파일을 편집합니다\n"
"  import <type>      - 외부 북마크를 가져옵니다\n"
"  list               - 북마크의 목록을 보여줍니다(기본)\n"

#: src/commands.cc:137
msgid "cache [SUBCMD]"
msgstr ""

#: src/commands.cc:138
#, fuzzy
msgid ""
"cache command controls local memory cache\n"
"\n"
"The following subcommands are recognized:\n"
"  stat        - print cache status (default)\n"
"  on|off      - turn on/off caching\n"
"  flush       - flush cache\n"
"  size <lim>  - set memory limit\n"
"  expire <Nx> - set cache expiration time to N seconds (x=s)\n"
"                minutes (x=m) hours (x=h) or days (x=d)\n"
msgstr ""
"cache 명령어는 지역 메모리 캐시를 제어합니다\n"
"\n"
"서브 명령어는 다음과 같습니다:\n"
"  stat        - 캐시 상태를 보여줍니다(기본)\n"
"  on|off      - 캐시 껴고/끕니다\n"
"  flush       - 캐시를 비웁니다\n"
"  size <lim>  - 메모리 한계를 설정합니다, -1은 무제한\n"
"  expire <Nx> - 캐시의 만기를 N초(x=s)/분(x=m)/시(x=h)\n"
"                또는 날(x=d)로 설정합니다\n"

#: src/commands.cc:146
msgid "cat [-b] <files>"
msgstr ""

#: src/commands.cc:147
msgid ""
"cat - output remote files to stdout (can be redirected)\n"
" -b  use binary mode (ascii is the default)\n"
msgstr ""
"cat - 원거리 파일을 표준출력(또는 리다이렉션)으로 보냅니다\n"
" -b   이진 모드 사용 (기본설정은 아스키)\n"

#: src/commands.cc:149
msgid "cd <rdir>"
msgstr ""

#: src/commands.cc:150
msgid ""
"Change current remote directory to <rdir>. The previous remote directory\n"
"is stored as `-'. You can do `cd -' to change the directory back.\n"
"The previous directory for each site is also stored on disk, so you can\n"
"do `open site; cd -' even after lftp restart.\n"
msgstr ""
"<rdir>로 현재 원거리 디렉토리를 바꿉니다. 이전 원거리 디렉토리는 -로 저장됩니"
"다.\n"
"'cd -'로 원래의 디렉토리로 돌아갈 수 있습니다. 각 사이트별로 이전 디렉토리"
"가 \n"
"디스크에 저장됩니다. 즉, lftp를 재시작 후에 'open site; cd -'를 이용할 수 있"
"습니다.\n"

#: src/commands.cc:154
msgid "chmod [OPTS] mode file..."
msgstr ""

#: src/commands.cc:155
msgid ""
"Change the mode of each FILE to MODE.\n"
"\n"
" -c, --changes        - like verbose but report only when a change is made\n"
" -f, --quiet          - suppress most error messages\n"
" -v, --verbose        - output a diagnostic for every file processed\n"
" -R, --recursive      - change files and directories recursively\n"
"\n"
"MODE can be an octal number or symbolic mode (see chmod(1))\n"
msgstr ""

#: src/commands.cc:164
msgid ""
"Close idle connections. By default only with current server.\n"
" -a  close idle connections with all servers\n"
msgstr ""
"놀고 접속을 닫는다. 기본적으로 현재 서버에 적용됩니다.\n"
" -a  모든 서버의 놀고 있는 접속을 닫습니다\n"

#: src/commands.cc:166
msgid "[re]cls [opts] [path/][pattern]"
msgstr ""

#: src/commands.cc:167
msgid ""
"List remote files. You can redirect output of this command to file\n"
"or via pipe to external command.\n"
"\n"
" -1                   - single-column output\n"
" -a, --all            - show dot files\n"
" -B, --basename       - show basename of files only\n"
"     --block-size=SIZ - use SIZ-byte blocks\n"
" -d, --directory      - list directory entries instead of contents\n"
" -F, --classify       - append indicator (one of /@) to entries\n"
" -h, --human-readable - print sizes in human readable format (e.g., 1K)\n"
"     --si             - likewise, but use powers of 1000 not 1024\n"
" -k, --kilobytes      - like --block-size=1024\n"
" -l, --long           - use a long listing format\n"
" -q, --quiet          - don't show status\n"
" -s, --size           - print size of each file\n"
"     --filesize       - if printing size, only print size for files\n"
" -i, --nocase         - case-insensitive pattern matching\n"
" -I, --sortnocase     - sort names case-insensitively\n"
" -D, --dirsfirst      - list directories first\n"
"     --sort=OPT       - \"name\", \"size\", \"date\"\n"
" -S                   - sort by file size\n"
" --user, --group, --perms, --date, --linkcount, --links\n"
"                      - show individual fields\n"
" --time-style=STYLE   - use specified time format\n"
"\n"
"By default, cls output is cached, to see new listing use `recls' or\n"
"`cache flush'.\n"
"\n"
"The variables cls-default and cls-completion-default can be used to\n"
"specify defaults for cls listings and completion listings, respectively.\n"
"For example, to make completion listings show file sizes, set\n"
"cls-completion-default to \"-s\".\n"
"\n"
"Tips: Use --filesize with -D to pack the listing better.  If you don't\n"
"always want to see file sizes, --filesize in cls-default will affect the\n"
"-s flag on the commandline as well.  Add `-i' to cls-completion-default\n"
"to make filename completion case-insensitive.\n"
msgstr ""

#: src/commands.cc:217
msgid "debug [OPTS] [<level>|off]"
msgstr ""

#: src/commands.cc:218
#, fuzzy
msgid ""
"Set debug level to given value or turn debug off completely.\n"
" -o <file>  redirect debug output to the file\n"
" -c  show message context\n"
" -p  show PID\n"
" -t  show timestamps\n"
msgstr ""
"주어진 값으로 디버그 레벨을 설정하거나 디버그 기능을 끕니다.\n"
" -o <file>  디버그 출력을 파일로 저장합니다.\n"

#: src/commands.cc:223
msgid "du [options] <dirs>"
msgstr ""

#: src/commands.cc:224
msgid ""
"Summarize disk usage.\n"
" -a, --all             write counts for all files, not just directories\n"
"     --block-size=SIZ  use SIZ-byte blocks\n"
" -b, --bytes           print size in bytes\n"
" -c, --total           produce a grand total\n"
" -d, --max-depth=N     print the total for a directory (or file, with --"
"all)\n"
"                       only if it is N or fewer levels below the command\n"
"                       line argument;  --max-depth=0 is the same as\n"
"                       --summarize\n"
" -F, --files           print number of files instead of sizes\n"
" -h, --human-readable  print sizes in human readable format (e.g., 1K 234M "
"2G)\n"
" -H, --si              likewise, but use powers of 1000 not 1024\n"
" -k, --kilobytes       like --block-size=1024\n"
" -m, --megabytes       like --block-size=1048576\n"
" -S, --separate-dirs   do not include size of subdirectories\n"
" -s, --summarize       display only a total for each argument\n"
"     --exclude=PAT     exclude files that match PAT\n"
msgstr ""

#: src/commands.cc:242
#, fuzzy
msgid "edit [OPTS] <file>"
msgstr "사용법: %s [OPTS] file\n"

#: src/commands.cc:243
msgid ""
"Retrieve remote file to a temporary location, run a local editor on it\n"
"and upload the file back if changed.\n"
" -k  keep the temporary file\n"
" -o <temp>  explicit temporary file location\n"
msgstr ""

#: src/commands.cc:248
msgid "exit [<code>|bg]"
msgstr ""

#: src/commands.cc:249
msgid ""
"exit - exit from lftp or move to background if jobs are active\n"
"\n"
"If no jobs active, the code is passed to operating system as lftp\n"
"termination status. If omitted, exit code of last command is used.\n"
"`bg' forces moving to background if cmd:move-background is false.\n"
msgstr ""
"exit - lftp를 빠져나가거나 지덩된 작업을 백그라운드로 돌립니다\n"
"\n"
"지정된 작업이 없다면, 코드는 lftp를 종료함을  OS 알립니다.\n"
"생략했다면, 마지막 명령어가 종료 됩니다. 만약 cmd:move-backgrond가\n"
"실패한다면 `bg'를 통해 강제로 백그라운드로 돌릴 수 있습니다.\n"

#: src/commands.cc:255
msgid ""
"Usage: find [OPTS] [directory]\n"
"Print contents of specified directory or current directory recursively.\n"
"Directories in the list are marked with trailing slash.\n"
"You can redirect output of this command.\n"
" -d, --maxdepth=LEVELS  Descend at most LEVELS of directories.\n"
msgstr ""

#: src/commands.cc:260
msgid "get [OPTS] <rfile> [-o <lfile>]"
msgstr ""

#: src/commands.cc:261
#, fuzzy
msgid ""
"Retrieve remote file <rfile> and store it to local file <lfile>.\n"
" -o <lfile> specifies local file name (default - basename of rfile)\n"
" -c  continue, resume transfer\n"
" -E  delete remote files after successful transfer\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"원거리 파일 <rfile>을 받아 지역 파일 <lfile>로 저장.\n"
" -o <lfile> 지역 파일명 지정 (기본 - rfile의 이름)\n"
" -c  이어받기, reget\n"
" -E  성공적으로 받은 후 원거리 파일 삭제\n"
" -a  아스키 모드 사용 (기본 - 바이너리) -O <base> 파일이 위치할 기본 디렉토"
"리 또는 URL을 지정\n"

#: src/commands.cc:268
msgid "glob [OPTS] <cmd> <args>"
msgstr ""

#: src/commands.cc:270
#, fuzzy
msgid ""
"Expand wildcards and run specified command.\n"
"Options can be used to expand wildcards to list of files, directories,\n"
"or both types. Type selection is not very reliable and depends on server.\n"
"If entry type cannot be determined, it will be included in the list.\n"
" -f  plain files (default)\n"
" -d  directories\n"
" -a  all types\n"
" --exist      return zero exit code when the patterns expand to non-empty "
"list\n"
" --not-exist  return zero exit code when the patterns expand to an empty "
"list\n"
msgstr ""
"사용법: glob [OPTS] command args...\n"
"와일드카드를 확장하고 지정한 명령을 실행.\n"
"옵션은 파일이나  디렉토리의 목록으로 와일드카드로 확장하는데\n"
"사용될 수 있거나, 동시에 사용될 수 있음. 타입의 선택은 확실히 신뢰할 \n"
"수는 없지만 서버에 따라 결정됩니다. 만약 타입을 결정할 수 없다면, 목록에\n"
"포함 될 겁니다.\n"
" -f  일반 파일 (기본)\n"
" -d  디렉토리\n"
" -a  모든 타입\n"

#: src/commands.cc:279
msgid "help [<cmd>]"
msgstr ""

#: src/commands.cc:280
msgid "Print help for command <cmd>, or list of available commands\n"
msgstr "명령어 <cmd> 의 도움말, 또는 이용 가능한 명령어의 목록을 보여줍니다\n"

#: src/commands.cc:282
#, fuzzy
msgid ""
"List running jobs. -v means verbose, several -v can be specified.\n"
"If <job_no> is specified, only list a job with that number.\n"
msgstr ""
"수행중인 작업의 목록를 보여줍니다. -v는 자세히, 일부 -v는 조건으로 지정될 수 "
"있습니다.\n"

#: src/commands.cc:284
msgid "kill all|<job_no>"
msgstr ""

#: src/commands.cc:285
msgid "Delete specified job with <job_no> or all jobs\n"
msgstr "<job_no>를 가진 특정 작업 또는 모든 작업을 지웁니다\n"

#: src/commands.cc:286
msgid "lcd <ldir>"
msgstr ""

#: src/commands.cc:287
msgid ""
"Change current local directory to <ldir>. The previous local directory\n"
"is stored as `-'. You can do `lcd -' to change the directory back.\n"
msgstr ""
"현재 지역 디렉토리를 <ldir>로 바꿉니다. 이전 지역 디렉토리는 `-'로써\n"
"저장됩니다. `lcd -'으로 이전 디렉토리로 되돌아갈 수 있습니다.\n"

#: src/commands.cc:289
msgid "lftp [OPTS] <site>"
msgstr ""

#: src/commands.cc:290
#, fuzzy
msgid ""
"`lftp' is the first command executed by lftp after rc files\n"
" -f <file>           execute commands from the file and exit\n"
" -c <cmd>            execute the commands and exit\n"
" --norc              don't execute rc files from the home directory\n"
" --help              print this help and exit\n"
" --version           print lftp version and exit\n"
"Other options are the same as in `open' command:\n"
" -e <cmd>            execute the command just after selecting\n"
" -u <user>[,<pass>]  use the user/password for authentication\n"
" -p <port>           use the port for connection\n"
" -s <slot>           assign the connection to this slot\n"
" -d                  switch on debugging mode\n"
" <site>              host name, URL or bookmark name\n"
msgstr ""
"`lftp'는 rc 파일 이후 lftp에 의해 실행되는 첫번째 명령어입니다\n"
" -f <file>           파일로부터 명령어를 실행하고 빠져나갑니다\n"
" -c <cmd>            명령어를 실행하고 빠져나갑니다\n"
" --help              이 도움말을 출력하고 빠져나갑니다\n"
" --version           lftp의 버전을 출력하고 빠져나갑니다\n"
"다른 옵션들은 `open' 명령어에 있는 것과 같습니다\n"
" -e <cmd>            지정된 명령어만 실행합니다\n"
" -u <user>[, <pass>] user/password로 인증받습니다\n"
" -p <port>           포트로 접속합니다\n"
" <site>              호스트 이름, URL 또는 북마크 이름\n"

#: src/commands.cc:303
#, fuzzy
msgid "ln [-s] <file1> <file2>"
msgstr "사용법: mv <file1> <file2>\n"

#: src/commands.cc:304
#, fuzzy
msgid "Link <file1> to <file2>\n"
msgstr "<file1>를 <file2>로 이름을 변경합니다\n"

#: src/commands.cc:308
msgid "ls [<args>]"
msgstr ""

#: src/commands.cc:309
#, fuzzy
msgid ""
"List remote files. You can redirect output of this command to file\n"
"or via pipe to external command.\n"
"By default, ls output is cached, to see new listing use `rels' or\n"
"`cache flush'.\n"
"See also `help cls'.\n"
msgstr ""
"원거리 파일 목록입니다, 파일 또는 다른 명령어에 파이프를 통하여 출력\n"
"을 재지정할 수 있습니다.\n"
"기본값으로, ls 출력은 캐시되며, `rels' 또는 `cache flush'로 새 목록을\n"
"볼 수 있습니다.\n"

#: src/commands.cc:314
msgid "mget [OPTS] <files>"
msgstr ""

#: src/commands.cc:315
#, fuzzy
msgid ""
"Gets selected files with expanded wildcards\n"
" -c  continue, resume transfer\n"
" -d  create directories the same as in file names and get the\n"
"     files into them instead of current directory\n"
" -E  delete remote files after successful transfer\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"확장된 와일드카드를 가지고 지정한 파일 받음\n"
" -c  이어받기, reget\n"
" -d  현재의 디렉토리 대신에 받는 파일명과 같은 이름의 디렉토리를\n"
"     만들어 그곳에 파일을 저장\n"
" -E  파일을 성공적으로 전송한 후 원거리 파일을 삭제\n"
" -a  아스키 모드 사용 (기본 - 바이너리)\n"
" -O <base> 파일이 위치할 기본 디렉토리 또는 URL을 지정\n"

#: src/commands.cc:322
msgid "mirror [OPTS] [remote [local]]"
msgstr ""

#: src/commands.cc:323
msgid "mkdir [OPTS] <dirs>"
msgstr ""

#: src/commands.cc:324
#, fuzzy
msgid ""
"Make remote directories\n"
" -p  make all levels of path\n"
" -f  be quiet, suppress messages\n"
msgstr ""
"원거리 디렉토리를 만듭니다\n"
" -p  모든 레벨의 패스를 한번에 만듭니다\n"

#: src/commands.cc:327
msgid "module name [args]"
msgstr ""

#: src/commands.cc:328
msgid ""
"Load module (shared object). The module should contain function\n"
"   void module_init(int argc,const char *const *argv)\n"
"If name contains a slash, then the module is searched in current\n"
"directory, otherwise in directories specified by setting module:path.\n"
msgstr ""
"모듈(shared object)을 올립니다. 모듈은 함수\n"
"   void module_init(int argc,const char *const *argv)\n"
"를 포함해야 합니다. 만약 이름에 슬래시가 포함된다면\n"
"그 모듈을 현재 디렉토리에서 찾습니다\n"
"그렇지 않다면 module:path에 의해 지정된 디렉토리에서 찾습니다.\n"

#: src/commands.cc:332
msgid "more <files>"
msgstr ""

#: src/commands.cc:333
msgid "Same as `cat <files> | more'. if PAGER is set, it is used as filter\n"
msgstr ""
"`cat <files> | more'과 같습니다. 만약 PAGER가 지정되어 있다면, 필터로서\n"
"사용됩니다\n"

#: src/commands.cc:334
msgid "mput [OPTS] <files>"
msgstr ""

#: src/commands.cc:335
msgid ""
"Upload files with wildcard expansion\n"
" -c  continue, reput\n"
" -d  create directories the same as in file names and put the\n"
"     files into them instead of current directory\n"
" -E  delete local files after successful transfer (dangerous)\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"와일드카드 확장으로 해당되는 파일을 업로드\n"
" -c  이어서 업로드, reput\n"
" -d  현재의 디렉토리 대신에 받는 파일명과 같은 이름의 디렉토리를\n"
"     만들어 그곳에 파일을 저장\n"
" -E  파일을 성공적으로 전송한 후 원거리 파일을 삭제(주의)\n"
" -a  아스키 모드 사용 (기본 - 바이너리)\n"
" -O <base> 파일이 위치할 기본 디렉토리 또는 URL을 지정\n"

#: src/commands.cc:342
msgid "mrm <files>"
msgstr ""

#: src/commands.cc:343
msgid "Removes specified files with wildcard expansion\n"
msgstr "와일드카드 식으로 특정 파일들을 지웁니다\n"

#: src/commands.cc:344
msgid "mv <file1> <file2>"
msgstr ""

#: src/commands.cc:345
msgid "Rename <file1> to <file2>\n"
msgstr "<file1>를 <file2>로 이름을 변경합니다\n"

#: src/commands.cc:346
msgid "mmv [OPTS] <files> <target-dir>"
msgstr ""

#: src/commands.cc:347
msgid ""
"Move <files> to <target-directory> with wildcard expansion\n"
" -O <dir>  specifies the target directory (alternative way)\n"
msgstr ""

#: src/commands.cc:349
msgid "[re]nlist [<args>]"
msgstr ""

#: src/commands.cc:350
#, fuzzy
msgid ""
"List remote file names.\n"
"By default, nlist output is cached, to see new listing use `renlist' or\n"
"`cache flush'.\n"
msgstr ""
"원거리 파일 목록입니다, 파일 또는 다른 명령어에 파이프를 통하여 출력\n"
"을 재지정할 수 있습니다.\n"
"기본값으로, ls 출력은 캐시되며, `rels' 또는 `cache flush'로 새 목록을\n"
"볼 수 있습니다.\n"

#: src/commands.cc:353
msgid "open [OPTS] <site>"
msgstr ""

#: src/commands.cc:354
#, fuzzy
msgid ""
"Select a server, URL or bookmark\n"
" -e <cmd>            execute the command just after selecting\n"
" -u <user>[,<pass>]  use the user/password for authentication\n"
" -p <port>           use the port for connection\n"
" -s <slot>           assign the connection to this slot\n"
" -d                  switch on debugging mode\n"
" <site>              host name, URL or bookmark name\n"
msgstr ""
"서버의 URL 또는 북마크를 지정합니다\n"
" -e <cmd>            명령어(cmd)만 실행\n"
" -u <user>[,<pass>]  인증을 위하여 사용자/비밀번호 지정\n"
" -p <port>           접속 포트를 지정\n"
"<site>               호스트 이름, URL 또는 북마크 이름\n"

#: src/commands.cc:361
msgid "pget [OPTS] <rfile> [-o <lfile>]"
msgstr ""

#: src/commands.cc:362
#, fuzzy
msgid ""
"Gets the specified file using several connections. This can speed up "
"transfer,\n"
"but loads the net heavily impacting other users. Use only if you really\n"
"have to transfer the file ASAP.\n"
"\n"
"Options:\n"
" -c  continue transfer. Requires <lfile>.lftp-pget-status file.\n"
" -n <maxconn>  set maximum number of connections (default is is taken from\n"
"     pget:default-n setting)\n"
" -O <base> specifies base directory where files should be placed\n"
msgstr ""
"여러개의 접속을 이용하여 지정된 파일을 받습니다. 이는 전송를 빠르게\n"
"할 수 있습니다, 그러나 네트워크 부하를 많이 주어 다른 사용자에게\n"
"피해를 줄 수 있습니다. 정말로 최대한 빨리 전송해야할 파일일 때만\n"
"사용하십시오, 그렇지 않으면 다른 사용자를 미치게 만들겁니다. :)\n"
"\n"
"옵션:\n"
" -n <maxconn>  최대 접속수를 설정 (기본설정 5)\n"

#: src/commands.cc:370
msgid "put [OPTS] <lfile> [-o <rfile>]"
msgstr ""

#: src/commands.cc:371
msgid ""
"Upload <lfile> with remote name <rfile>.\n"
" -o <rfile> specifies remote file name (default - basename of lfile)\n"
" -c  continue, reput\n"
"     it requires permission to overwrite remote files\n"
" -E  delete local files after successful transfer (dangerous)\n"
" -a  use ascii mode (binary is the default)\n"
" -O <base> specifies base directory or URL where files should be placed\n"
msgstr ""
"<lfile>를 원거리 파일명 <rfile>로 업로드.\n"
" -o  <rfile> 지역 파일 이름을 지정 (기본 - lfile의 이름)\n"
" -c  이어올리기, reput\n"
" -E  파일을 성공적으로 전송한 후 원거리 파일을 삭제(주의)\n"
" -a  아스키 모드 사용 (기본 - 바이너리)\n"
" -O <base> 파일이 위치할 기본 디렉토리 또는 URL을 지정\n"

#: src/commands.cc:379
msgid ""
"Print current remote URL.\n"
" -p  show password\n"
msgstr ""
"현재의 원거리 URL을 출력.\n"
" -p  비밀번호를 표시\n"

#: src/commands.cc:381
msgid "queue [OPTS] [<cmd>]"
msgstr ""

#: src/commands.cc:382
msgid ""
"\n"
"       queue [-n num] <command>\n"
"\n"
"Add the command to queue for current site. Each site has its own command\n"
"queue. `-n' adds the command before the given item in the queue. It is\n"
"possible to queue up a running job by using command `queue wait <jobno>'.\n"
"\n"
"       queue --delete|-d [index or wildcard expression]\n"
"\n"
"Delete one or more items from the queue. If no argument is given, the last\n"
"entry in the queue is deleted.\n"
"\n"
"       queue --move|-m <index or wildcard expression> [index]\n"
"\n"
"Move the given items before the given queue index, or to the end if no\n"
"destination is given.\n"
"\n"
"Options:\n"
" -q                  Be quiet.\n"
" -v                  Be verbose.\n"
" -Q                  Output in a format that can be used to re-queue.\n"
"                     Useful with --delete.\n"
msgstr ""

#: src/commands.cc:403
msgid "quote <cmd>"
msgstr ""

#: src/commands.cc:404
msgid ""
"Send the command uninterpreted. Use with caution - it can lead to\n"
"unknown remote state and thus will cause reconnect. You cannot\n"
"be sure that any change of remote state because of quoted command\n"
"is solid - it can be reset by reconnect at any time.\n"
msgstr ""
"비실행 명령어를 보냅니다. 주의해서 사용하십시오 - 잘못된 연결 상태로\n"
"간주될 수 있어 재접속을 야기시킬 수도 있습니다. 인용한 명령어라고 해서\n"
"접속이 계속 유지된다고는 확신할 수 없습니다 - 어떤 경우엔 재접속으로\n"
"재설정되는 경우도 있습니다.\n"

#: src/commands.cc:409
#, fuzzy
msgid ""
"recls [<args>]\n"
"Same as `cls', but don't look in cache\n"
msgstr "`ls' 와 같지만 캐시를 이용하지 않습니다\n"

#: src/commands.cc:412
msgid ""
"Usage: reget [OPTS] <rfile> [-o <lfile>]\n"
"Same as `get -c'\n"
msgstr ""

#: src/commands.cc:415
#, fuzzy
msgid ""
"Usage: rels [<args>]\n"
"Same as `ls', but don't look in cache\n"
msgstr "`ls' 와 같지만 캐시를 이용하지 않습니다\n"

#: src/commands.cc:418
#, fuzzy
msgid ""
"Usage: renlist [<args>]\n"
"Same as `nlist', but don't look in cache\n"
msgstr "`nlist' 와 같지만 캐시를 이용하지 않습니다\n"

#: src/commands.cc:420
msgid "repeat [OPTS] [delay] [command]"
msgstr ""

#: src/commands.cc:422
msgid ""
"Usage: reput <lfile> [-o <rfile>]\n"
"Same as `put -c'\n"
msgstr ""

#: src/commands.cc:424
msgid "rm [-r] [-f] <files>"
msgstr ""

#: src/commands.cc:425
msgid ""
"Remove remote files\n"
" -r  recursive directory removal, be careful\n"
" -f  work quietly\n"
msgstr ""
"원거리 파일 삭제\n"
" -r  서브디렉토리를 포함하여 지웁니다, 주의하십시오\n"
" -f  조용히 실행\n"

#: src/commands.cc:428
msgid "rmdir [-f] <dirs>"
msgstr ""

#: src/commands.cc:429
msgid "Remove remote directories\n"
msgstr "원거리 디렉토리를 지웁니다\n"

#: src/commands.cc:430
msgid "scache [<session_no>]"
msgstr ""

#: src/commands.cc:431
msgid "List cached sessions or switch to specified session number\n"
msgstr "캐시된 세션 목록을 보여주거나 지정된 세션으로 전환합니다\n"

#: src/commands.cc:432
msgid "set [OPT] [<var> [<val>]]"
msgstr ""

#: src/commands.cc:433
msgid ""
"Set variable to given value. If the value is omitted, unset the variable.\n"
"Variable name has format ``name/closure'', where closure can specify\n"
"exact application of the setting. See lftp(1) for details.\n"
"If set is called with no variable then only altered settings are listed.\n"
"It can be changed by options:\n"
" -a  list all settings, including default values\n"
" -d  list only default values, not necessary current ones\n"
msgstr ""
"변수(var)를 주어진 값(val)으로 설정합니다. 만약 값을 생략하면,\n"
"변수를 해제합니다. 변수명은 ``name/closure'' 의 형식을 갖으며,\n"
"closure는 정확한 애플리케이션(application)으로\n"
"지정할 수 있습니다. 자세한 것은 lftp(1)를 보십시오.\n"
"만약 변수 없이 set를 이용하면 변경된 설정값을 표시합니다.\n"
"이는 다음 옵션으로 바꿀 수 있습니다:\n"
" -a  기본값을 포함한 모든 설정값을 표시합니다.\n"
" -d  현재 이용하는 것들을 제외한 기본값만을 표시합니다.\n"

#: src/commands.cc:441
#, fuzzy
msgid "site <site-cmd>"
msgstr "사용법: site <site_cmd>\n"

#: src/commands.cc:442
msgid ""
"Execute site command <site_cmd> and output the result\n"
"You can redirect its output\n"
msgstr ""
"<site_cmd> 명령을 실행하고 그 결과를 출력합니다.\n"
"그 결과를 재지정(redirect)할 수 있습니다\n"

#: src/commands.cc:446
msgid ""
"Usage: slot [<label>]\n"
"List assigned slots.\n"
"If <label> is specified, switch to the slot named <label>.\n"
msgstr ""

#: src/commands.cc:449
msgid "source <file>"
msgstr ""

#: src/commands.cc:450
msgid "Execute commands recorded in file <file>\n"
msgstr "파일 <file> 에 기록된 명령어들을 실행합니다\n"

#: src/commands.cc:452
#, fuzzy
msgid "torrent [OPTS] <file|URL>..."
msgstr "사용법: %s [OPTS] file\n"

#: src/commands.cc:453
msgid "user <user|URL> [<pass>]"
msgstr ""

#: src/commands.cc:454
msgid ""
"Use specified info for remote login. If you specify URL, the password\n"
"will be cached for future usage.\n"
msgstr ""
"원거리 로긴을 위하여 사용. 만약 URL을 지정하면, 비밀번호는\n"
"앞으로의 사용을 위하여 저장될 겁니다.\n"

#: src/commands.cc:457
msgid "Shows lftp version\n"
msgstr "lftp 버전을 보여줍니다\n"

#: src/commands.cc:458
msgid "wait [<jobno>]"
msgstr "wait [<jobno>]"

#: src/commands.cc:459
msgid ""
"Wait for specified job to terminate. If jobno is omitted, wait\n"
"for last backgrounded job.\n"
msgstr ""
"지정한 작업을 종료하기 위하여 대기. 작업 번호를 생략하면,\n"
"마지막 백그라운드로 전환된 작업에 대해 대기합니다.\n"

#: src/commands.cc:461
msgid "zcat <files>"
msgstr ""

#: src/commands.cc:462
msgid "Same as cat, but filter each file through zcat\n"
msgstr "cat과 같으나, zcat으로 각 파일을 필터링합니다\n"

#: src/commands.cc:463
msgid "zmore <files>"
msgstr ""

#: src/commands.cc:464
msgid "Same as more, but filter each file through zcat\n"
msgstr "more과 같으나, zcat으로 각 파일을 필터링합니다\n"

#: src/commands.cc:466
msgid "Same as cat, but filter each file through bzcat\n"
msgstr "cat과 같으나, bzcat으로 각 파일을 필터링합니다\n"

#: src/commands.cc:468
msgid "Same as more, but filter each file through bzcat\n"
msgstr "more과 같으나, bzcat으로 각 파일을 필터링합니다\n"

#: src/commands.cc:524
#, c-format
msgid "Usage: %s local-dir\n"
msgstr "사용법: %s local-dir\n"

#: src/commands.cc:560
#, c-format
msgid "lcd ok, local cwd=%s\n"
msgstr "lcd 성공, 지역 cwd=%s\n"

#: src/commands.cc:576
#, c-format
msgid "Usage: cd remote-dir\n"
msgstr "사용법: cd remote-dir\n"

#: src/commands.cc:588
#, c-format
msgid "%s: no old directory for this site\n"
msgstr "%s: 이 사이트엔 옛 디렉토리가 존재하지 않습니다\n"

#: src/commands.cc:677
#, c-format
msgid "Usage: %s [<exit_code>]\n"
msgstr "사용법: %s [<exit_code>]\n"

#: src/commands.cc:686
msgid ""
"There are running jobs and `cmd:move-background' is not set.\n"
"Use `exit bg' to force moving to background or `kill all' to terminate "
"jobs.\n"
msgstr ""
"실행중인 작업들이 있고 'cmd:move-background'는 설정하지 않음.\n"
"`exit bg'는 강제로 백그라운드로 가기 위해 사용되거나 `kill all'는 \n"
"작업들을 종료하기 위하여 사용한다.\n"

#: src/commands.cc:703
msgid ""
"\n"
"lftp now tricks the shell to move it to background process group.\n"
"lftp continues to run in the background despite the `Stopped' message.\n"
"lftp will automatically terminate when all jobs are finished.\n"
"Use `fg' shell command to return to lftp if it is still running.\n"
msgstr ""

#: src/commands.cc:837 src/commands.cc:3616
#, c-format
msgid "Try `%s --help' for more information\n"
msgstr "`%s --help'로 더 많은 정보를 보십시오\n"

#: src/commands.cc:856
#, c-format
msgid "%s: -c, -f, -v, -h conflict with other `open' options and arguments\n"
msgstr ""

#: src/commands.cc:956
#, c-format
msgid "Usage: %s [-e cmd] [-p port] [-u user[,pass]] <host|url>\n"
msgstr "사용법: %s [-e cmd] [-p port] [-u user[,pass]] <host|url>\n"

#: src/commands.cc:1046 src/commands.cc:2276 src/DummyProto.cc:65
#: src/MirrorJob.cc:2172 src/MirrorJob.cc:2194
msgid " - not supported protocol"
msgstr " - 지원하지 않는 프로토콜입니다"

#: src/commands.cc:1078 src/commands.cc:2260
msgid "Password: "
msgstr "비밀번호: "

#: src/commands.cc:1080
#, c-format
msgid "%s: GetPass() failed -- assume anonymous login\n"
msgstr "%s: GetPass() 실패 -- 익명의 로그인으로 간주합니다\n"

#: src/commands.cc:1191 src/commands.cc:1284 src/commands.cc:1660
#: src/commands.cc:1691 src/commands.cc:1829 src/commands.cc:1914
#: src/commands.cc:2187 src/commands.cc:2381 src/commands.cc:2543
#: src/commands.cc:2588 src/commands.cc:2616 src/commands.cc:2623
#: src/commands.cc:2930 src/commands.cc:2957 src/commands.cc:2964
#: src/commands.cc:3293 src/lftp.cc:267 src/MirrorJob.cc:2136
#: src/SleepJob.cc:144 src/SleepJob.cc:200 src/Torrent.cc:4169
#, c-format
msgid "Try `help %s' for more information.\n"
msgstr "상세한 것은 `help %s'를 이용하십시오\n"

#: src/commands.cc:1201
#, c-format
msgid "Usage: %s [OPTS] command args...\n"
msgstr "사용법: %s [OPTS] command args...\n"

#: src/commands.cc:1254
#, fuzzy, c-format
msgid "%s: -n: positive number expected. "
msgstr "%s: -n: 숫자가 요구됩니다  "

#: src/commands.cc:1306
#, c-format
msgid "Created a stopped queue.\n"
msgstr ""

#: src/commands.cc:1349 src/commands.cc:1377
#, c-format
msgid "%s: No queue is active.\n"
msgstr ""

#: src/commands.cc:1369
#, fuzzy, c-format
msgid "%s: -m: Number expected as second argument. "
msgstr "%s: -n: 숫자가 요구됩니다  "

#: src/commands.cc:1421
#, c-format
msgid "Usage: %s <cmd>\n"
msgstr "사용법: %s <cmd>\n"

#: src/commands.cc:1527
msgid "invalid argument for `--sort'"
msgstr ""

#: src/commands.cc:1557
#, fuzzy
msgid "invalid block size"
msgstr "잘못된 수입니다"

#: src/commands.cc:1700
#, c-format
msgid "Usage: %s [OPTS] files...\n"
msgstr "사용법: %s [OPTS] files...\n"

#: src/commands.cc:1785 src/commands.cc:1814
#, fuzzy, c-format
msgid "%s: %s: Number expected. "
msgstr "%s: -n: 숫자가 요구됩니다  "

#: src/commands.cc:1834
#, c-format
msgid "%s: --continue conflicts with --remove-target.\n"
msgstr ""

#: src/commands.cc:1844 src/commands.cc:1920
#, c-format
msgid "File name missed. "
msgstr "파일명이 없습니다. "

#: src/commands.cc:1989
#, fuzzy, c-format
msgid "Usage: %s %s[-f] files...\n"
msgstr "사용법: %s [-r] [-f] files...\n"

#: src/commands.cc:2032
#, fuzzy, c-format
msgid "Usage: %s [-e] <file|command>\n"
msgstr "사용법: %s <file>\n"

#: src/commands.cc:2079
#, c-format
msgid "Usage: %s [-v] [-v] ...\n"
msgstr "사용법: %s [-v] [-v] ...\n"

#: src/commands.cc:2093 src/commands.cc:2347 src/commands.cc:2469
#: src/commands.cc:2685 src/commands.cc:3101 src/commands.cc:3182
#: src/lftp.cc:279
#, c-format
msgid "%s: %s - not a number\n"
msgstr "%s %s - 숫자가 아닙니다\n"

#: src/commands.cc:2100 src/commands.cc:2326 src/commands.cc:2356
#: src/commands.cc:2487
#, c-format
msgid "%s: %d - no such job\n"
msgstr "%s: %d - 작업 없음\n"

#: src/commands.cc:2135
#, c-format
msgid "Usage: %s [-p]\n"
msgstr "사용법: %s [-p]\n"

#: src/commands.cc:2227
#, c-format
msgid "debug level is %d, output goes to %s\n"
msgstr "디버그 레벨은 %d이고, 출력은 %s로 됩니다\n"

#: src/commands.cc:2230
#, c-format
msgid "debug is off\n"
msgstr "디버그 끔\n"

#: src/commands.cc:2241
#, fuzzy, c-format
msgid "Usage: %s <user|URL> [<pass>]\n"
msgstr "사용법: %s userid [pass]\n"

#: src/commands.cc:2316 src/commands.cc:2479
#, c-format
msgid "%s: no current job\n"
msgstr "%s: 현재 작업 없음\n"

#: src/commands.cc:2328
#, c-format
msgid "Usage: %s <jobno> ... | all\n"
msgstr "사용법: %s <jobno> ... | all\n"

#: src/commands.cc:2409
#, c-format
msgid "%s: %s. Use `set -a' to look at all variables.\n"
msgstr "%s: %s. `set -a'를 사용하여 모든 값들을 봅니다.\n"

#: src/commands.cc:2453
#, c-format
msgid "Usage: %s [<jobno>]\n"
msgstr "사용법: %s [<jobno>]\n"

#: src/commands.cc:2492
#, c-format
msgid "%s: some other job waits for job %d\n"
msgstr "%s: 일부 다른 작업이 작업 %d를 기다립니다\n"

#: src/commands.cc:2497
#, fuzzy, c-format
msgid "%s: wait loop detected\n"
msgstr "%s: 버그 - 데드락 발견\n"

#: src/commands.cc:2553
#, fuzzy, c-format
msgid "Usage: %s [OPTS] <files> <target-dir>\n"
msgstr "사용법: %s [OPTS] file\n"

#: src/commands.cc:2615 src/commands.cc:2956
#, c-format
msgid "Invalid command. "
msgstr "잘못된 명령어. "

#: src/commands.cc:2622 src/commands.cc:2963
#, c-format
msgid "Ambiguous command. "
msgstr "모호한 명령어. "

#: src/commands.cc:2641
#, c-format
msgid "%s: Operand missed for size\n"
msgstr "%s: 크기의 오퍼랜드가 빠졌습니다.\n"

#: src/commands.cc:2658
#, c-format
msgid "%s: Operand missed for `expire'\n"
msgstr "%s: `expire'의 오퍼랜드가 빠졌습니다.\n"

#: src/commands.cc:2691
#, c-format
msgid ""
"%s: %s - no such cached session. Use `scache' to look at session list.\n"
msgstr "%s: %s - 캐시 세션이 없습니다. `scache'로 세션 목록에서 찾으십시오.\n"

#: src/commands.cc:2715
#, c-format
msgid "Sorry, no help for %s\n"
msgstr "죄송합니다, %s 에 대한 도움말은 없습니다\n"

#: src/commands.cc:2720
#, c-format
msgid "%s is a built-in alias for %s\n"
msgstr "%s 는 %s을 기본 별명입니다\n"

#: src/commands.cc:2725
#, c-format
msgid "Usage: %s\n"
msgstr "사용법: %s\n"

#: src/commands.cc:2733
#, c-format
msgid "%s is an alias to `%s'\n"
msgstr "%s는 `%s'인 별명을 갖습니다\n"

#: src/commands.cc:2737
#, c-format
msgid "No such command `%s'. Use `help' to see available commands.\n"
msgstr ""
"명령어 `%s'는 없습니다. `help'를 사용하여 이용가능한 명령어를 확인하십시오.\n"

#: src/commands.cc:2739
#, c-format
msgid "Ambiguous command `%s'. Use `help' to see available commands.\n"
msgstr ""
"모호한 명령어 `%s'. `help'를 사용하여 이용가능한 명령어를 확인하십시오.\n"

#: src/commands.cc:2805
#, fuzzy, c-format
msgid "LFTP | Version %s | Copyright (c) 1996-%d Alexander V. Lukyanov\n"
msgstr "Lftp | 버전 %s | 저작권 (C) 1996-2001 Alexander V. Lukyanov\n"

#: src/commands.cc:2808
#, c-format
msgid ""
"LFTP is free software: you can redistribute it and/or modify\n"
"it under the terms of the GNU General Public License as published by\n"
"the Free Software Foundation, either version 3 of the License, or\n"
"(at your option) any later version.\n"
"\n"
"This program is distributed in the hope that it will be useful,\n"
"but WITHOUT ANY WARRANTY; without even the implied warranty of\n"
"MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n"
"GNU General Public License for more details.\n"
"\n"
"You should have received a copy of the GNU General Public License\n"
"along with LFTP.  If not, see <http://www.gnu.org/licenses/>.\n"
msgstr ""

#: src/commands.cc:2822
#, fuzzy, c-format
msgid "Send bug reports and questions to the mailing list <%s>.\n"
msgstr "<%s> 에게 버그 보고서나 질문을 보내십시오.\n"

#: src/commands.cc:2828
msgid "Libraries used: "
msgstr ""

#: src/commands.cc:2979 src/commands.cc:3007
#, c-format
msgid "%s: bookmark name required\n"
msgstr "%s: 북마크 이름이 필요합니다\n"

#: src/commands.cc:2996
#, c-format
msgid "%s: spaces in bookmark name are not allowed\n"
msgstr "%s: 북마크 이름에 공백문자를 사용할 수 없습니다\n"

#: src/commands.cc:3009
#, c-format
msgid "%s: no such bookmark `%s'\n"
msgstr "%s: 그와 같은 북마크 `%s'\n"

#: src/commands.cc:3032
#, c-format
msgid "%s: import type required (netscape,ncftp)\n"
msgstr "%s: (netscape,ncftp)에서 가져온 양식이 필요합니다\n"

#: src/commands.cc:3110
#, fuzzy, c-format
msgid "Usage: %s [-d #] dir\n"
msgstr "사용법: %s [-p]\n"

#: src/commands.cc:3215
#, fuzzy, c-format
msgid "%s: invalid block size `%s'\n"
msgstr "잘못된 수입니다"

#: src/commands.cc:3226
#, fuzzy, c-format
msgid "Usage: %s [options] <dirs>\n"
msgstr "사용법: %s [-p]\n"

#: src/commands.cc:3232
#, c-format
msgid "%s: warning: summarizing is the same as using --max-depth=0\n"
msgstr ""

#: src/commands.cc:3236
#, c-format
msgid "%s: summarizing conflicts with --max-depth=%i\n"
msgstr ""

#: src/commands.cc:3280
#, c-format
msgid "Usage: %s command args...\n"
msgstr "사용법: %s command args...\n"

#: src/commands.cc:3292
#, c-format
msgid "Usage: %s module [args...]\n"
msgstr "사용법: %s module [args...]\n"

#: src/commands.cc:3310 src/LocalAccess.cc:642
msgid "cannot get current directory"
msgstr "현재 디렉토리에 접근할 수 없습니다"

#: src/commands.cc:3374
#, fuzzy, c-format
msgid "Usage: %s [OPTS] mode file...\n"
msgstr "사용법: %s [OPTS] files...\n"

#: src/commands.cc:3394
#, fuzzy, c-format
msgid "invalid mode string: %s\n"
msgstr "잘못된 수입니다"

#: src/commands.cc:3457 src/commands.cc:3466
msgid "Invalid range format. Format is min-max, e.g. 10-20."
msgstr "범위를 벗어난 양식. 양식은 최소-최대 입니다, 예. 10-20."

#: src/commands.cc:3478
#, c-format
msgid "Usage: %s [OPTS] file\n"
msgstr "사용법: %s [OPTS] file\n"

#: src/CopyJob.cc:82
#, c-format
msgid "`%s' at %lld %s%s%s%s"
msgstr ""

#: src/CopyJob.cc:159
#, c-format
msgid "%lld $#ll#byte|bytes$ transferred in %ld $#l#second|seconds$"
msgstr "%lld $#l#byte|bytes$ 전송 in %ld $#l#second|seconds$"

#: src/CopyJob.cc:167
#, fuzzy, c-format
msgid "%lld $#ll#byte|bytes$ transferred"
msgstr "%lld $#ll#byte|bytes$ 전송\n"

#: src/CopyJob.cc:283
#, c-format
msgid "Transfer of %d of %d $file|files$ failed\n"
msgstr "%d of %d $file|files$ 전송 실패\n"

#: src/CopyJob.cc:289
#, c-format
msgid "Total %d $file|files$ transferred\n"
msgstr "총 %d $file|files$ 전송\n"

#: src/FileAccess.cc:160
#, fuzzy
msgid "Access failed: "
msgstr "접속 실패: %s"

#: src/FileAccess.cc:161
msgid "File cannot be accessed"
msgstr "파일을 읽을 수 없습니다"

#: src/FileAccess.cc:163 src/Fish.cc:982 src/ftpclass.cc:4599
#: src/ftpclass.cc:4608 src/SFtp.cc:1339 src/Torrent.cc:3533
msgid "Not connected"
msgstr "접속되지 않았습니다"

#: src/FileAccess.cc:166 src/FileAccess.cc:167
msgid "Fatal error"
msgstr "치명적 오류"

#: src/FileAccess.cc:169
msgid "Store failed - you have to reput"
msgstr "저장 실패 - 다시 입력하십시오"

#: src/FileAccess.cc:172 src/FileAccess.cc:173
msgid "Login failed"
msgstr "로그인 실패"

#: src/FileAccess.cc:176 src/FileAccess.cc:177
msgid "Operation not supported"
msgstr "지원되지 않는 오퍼레이션입니다"

#: src/FileAccess.cc:180
#, fuzzy
msgid "File moved"
msgstr "파일 이동: %s"

#: src/FileAccess.cc:182
#, fuzzy
msgid "File moved to `"
msgstr "`%s'로 파일 이동"

#: src/FileCopy.cc:141
msgid "copy: destination file is already complete\n"
msgstr ""

#: src/FileCopy.cc:182
msgid "copy: put is broken\n"
msgstr "copy: put은 깨짐\n"

#: src/FileCopy.cc:201
#, fuzzy
msgid "seek failed"
msgstr "로그인 실패"

#: src/FileCopy.cc:207
msgid "no progress timeout"
msgstr ""

#: src/FileCopy.cc:235
msgid "cannot seek on data source"
msgstr "소스 자료에서 찾을 수 없음"

#: src/FileCopy.cc:238
#, c-format
msgid "copy: put rolled back to %lld, seeking get accordingly\n"
msgstr ""

#: src/FileCopy.cc:251
msgid "copy: all data received, but get rolled back\n"
msgstr ""

#: src/FileCopy.cc:267
#, c-format
msgid "copy: get rolled back to %lld, seeking put accordingly\n"
msgstr ""

#: src/FileCopy.cc:364
msgid "file size decreased during transfer"
msgstr ""

#: src/FileCopy.cc:1227
#, c-format
msgid "copy: received redirection to `%s'\n"
msgstr "copy: `%s'로 재지정하여 받음\n"

#: src/FileCopy.cc:1290
#, fuzzy
#| msgid "saw file size in response"
msgid "file size increased during transfer"
msgstr "응답에서 파일 크기를 봅니다"

#: src/FileCopy.cc:1390
msgid "file name missed in URL"
msgstr "URL에 파일명이 빠졌습니다"

#: src/FileCopy.cc:2086
msgid "Verify command failed without a message"
msgstr ""

#: src/FileCopyFtp.cc:95
msgid "**** FXP: trying to reverse ftp:fxp-passive-source\n"
msgstr "**** FXP: 역 ftp:fxp-passive-source 시도\n"

#: src/FileCopyFtp.cc:102
#, fuzzy
msgid "**** FXP: trying to reverse ftp:fxp-passive-sscn\n"
msgstr "**** FXP: 역 ftp:fxp-passive-source 시도\n"

#: src/FileCopyFtp.cc:110
#, fuzzy
msgid "**** FXP: trying to reverse ftp:ssl-protect-fxp\n"
msgstr "**** FXP: 역 ftp:fxp-passive-source 시도\n"

#: src/FileCopyFtp.cc:116
msgid "**** FXP: giving up, reverting to plain copy\n"
msgstr "**** FXP: 포기하고 일반적인 복사로 바꿈\n"

#: src/FileCopyFtp.cc:125
msgid "ftp:fxp-force is set but FXP is not available"
msgstr ""

#: src/FileCopy.h:285
msgid "Verifying..."
msgstr ""

#: src/FileSetOutput.cc:230
msgid "non-option arguments found"
msgstr ""

#: src/Filter.cc:166
#, fuzzy
msgid "pipe() failed: "
msgstr "pipe() 실패: %s"

#: src/Filter.cc:200 src/PtyShell.cc:135
#, c-format
msgid "chdir(%s) failed: %s\n"
msgstr "chdir(%s) 실패: %s\n"

#: src/Filter.cc:208
#, fuzzy, c-format
msgid "execvp(%s) failed: %s\n"
msgstr "execlp(%s) 실패: %s\n"

#: src/Filter.cc:213 src/PtyShell.cc:147
#, c-format
msgid "execl(/bin/sh) failed: %s\n"
msgstr "execl(/bin/sh) 실패: %s\n"

#: src/Filter.cc:415
#, fuzzy
msgid "file already exists and xfer:clobber is unset"
msgstr "%s: %s: 파일이 이미 존재하며 xfer:clobber가 설정되어 있지 않습니다\n"

#: src/FindJobDu.cc:101
msgid "total"
msgstr ""

#: src/Fish.cc:75 src/ftpclass.cc:1292 src/Http.cc:1232 src/SFtp.cc:77
#, c-format
msgid "Closing idle connection"
msgstr "사용하지 않는 접속을 끊습니다"

#: src/Fish.cc:162 src/SFtp.cc:177
msgid "Running connect program"
msgstr ""

#: src/Fish.cc:588 src/ftpclass.cc:2887 src/ftpclass.cc:3107 src/Http.cc:1510
#: src/SFtp.cc:742 src/SFtp.cc:1083 src/SFtp.cc:1084 src/SSH_Access.cc:167
#, c-format
msgid "Peer closed connection"
msgstr "접속이 끊겼습니다"

#: src/Fish.cc:634 src/ftpclass.cc:3037 src/ftpclass.cc:4219 src/SFtp.cc:1108
#, c-format
msgid "extra server response"
msgstr "임시 서버 응답"

#: src/Fish.cc:987 src/ftpclass.cc:4611 src/Http.cc:2329 src/Http.cc:2336
#: src/SFtp.cc:1345 src/Torrent.cc:3536 src/TorrentTracker.cc:624
msgid "Connecting..."
msgstr "접속중..."

#: src/Fish.cc:989 src/ftpclass.cc:4617 src/SFtp.cc:1347
msgid "Connected"
msgstr "접속되었음"

#: src/Fish.cc:991 src/ftpclass.cc:4594 src/ftpclass.cc:4622
#: src/ftpclass.cc:4636 src/Http.cc:2338 src/SFtp.cc:1349
#: src/TorrentTracker.cc:626
msgid "Waiting for response..."
msgstr "응답을 기다립니다..."

#: src/Fish.cc:993 src/ftpclass.cc:4656 src/Http.cc:2341 src/SFtp.cc:1351
msgid "Receiving data"
msgstr "자료 받는 중"

#: src/Fish.cc:995 src/ftpclass.cc:4654 src/Http.cc:2334 src/SFtp.cc:1353
msgid "Sending data"
msgstr "자료 보내는 중"

#: src/Fish.cc:997 src/SFtp.cc:1355
#, fuzzy
msgid "Done"
msgstr "\t완료\n"

#: src/Fish.cc:1136 src/FtpDirList.cc:123 src/HttpDir.cc:1384 src/SFtp.cc:2200
#: src/SFtp.cc:2320
#, c-format
msgid "Getting file list (%lld) [%s]"
msgstr "파일 목록을 가져옵니다 (%lld) [%s]"

#: src/ftpclass.cc:197
#, c-format
msgid "Data connection peer has wrong port number"
msgstr "접속이 잘못된 포트로 된 듯 합니다"

#: src/ftpclass.cc:203
#, c-format
msgid "Data connection peer has mismatching address"
msgstr "접속이 잘못된 주소로 된 듯 합니다"

#: src/ftpclass.cc:225 src/ftpclass.cc:250
#, c-format
msgid "Switching to NOREST mode"
msgstr "NOREST 모드로 전환"

#: src/ftpclass.cc:425
#, c-format
msgid "Server reply matched ftp:retry-530, retrying"
msgstr ""

#: src/ftpclass.cc:433
#, c-format
msgid "Server reply matched ftp:retry-530-anonymous, retrying"
msgstr ""

#: src/ftpclass.cc:466
msgid "Account is required, set ftp:acct variable"
msgstr ""

#: src/ftpclass.cc:483
msgid "ftp:skey-force is set and server does not support OPIE nor S/KEY"
msgstr ""

#: src/ftpclass.cc:501
#, c-format
msgid "assuming failed host name lookup"
msgstr "호스트 이름을 찾는데 실패한 듯 합니다"

#: src/ftpclass.cc:809 src/ftpclass.cc:810
#, c-format
msgid "cannot parse EPSV response"
msgstr ""

#: src/ftpclass.cc:837 src/ftpclass.cc:838
#, c-format
msgid "cannot parse custom EPSV response"
msgstr ""

#: src/ftpclass.cc:1122
#, c-format
msgid "Closing control socket"
msgstr "제어 소켓 닫음"

#: src/ftpclass.cc:1341
msgid "MLSD is disabled by ftp:use-mlsd"
msgstr ""

#: src/ftpclass.cc:1411 src/Http.cc:1431 src/Torrent.cc:3204
#: src/Torrent.cc:3743 src/TorrentTracker.cc:395
#, c-format
msgid "cannot create socket of address family %d"
msgstr "주소 %d의 소켓을 만들 수 없습니다"

#: src/ftpclass.cc:1442 src/Http.cc:1457
#, c-format
msgid "Socket error (%s) - reconnecting"
msgstr "(%s) 소켓 오류 - 재접속중"

#: src/ftpclass.cc:1715
#, fuzzy
msgid "MFF and SITE CHMOD are not supported by this site"
msgstr "모듈은 이 시스템에서 지원되지 않음"

#: src/ftpclass.cc:1720
#, fuzzy
msgid "MLST and MLSD are not supported by this site"
msgstr "모듈은 이 시스템에서 지원되지 않음"

#: src/ftpclass.cc:2031
#, fuzzy
msgid "SITE SYMLINK is not supported by the server"
msgstr "모듈은 이 시스템에서 지원되지 않음"

#: src/ftpclass.cc:2204
msgid "unsupported network protocol"
msgstr "지원하지 않는 네트워크 프로토콜입니다"

#: src/ftpclass.cc:2253 src/ftpclass.cc:2355
#, fuzzy, c-format
msgid "Data socket error (%s) - reconnecting"
msgstr "(%s) 소켓 오류 - 재접속중"

#: src/ftpclass.cc:2281
#, fuzzy, c-format
msgid "Accepted data connection from (%s) port %u"
msgstr "(%s) 포트 %u에 자료 소켓을 연결합니다"

#: src/ftpclass.cc:2330
#, c-format
msgid "Connecting data socket to (%s) port %u"
msgstr "(%s) 포트 %u에 자료 소켓을 연결합니다"

#: src/ftpclass.cc:2336
#, fuzzy, c-format
msgid "Connecting data socket to proxy %s (%s) port %u"
msgstr "(%s) 포트 %u에 자료 소켓을 연결합니다"

#: src/ftpclass.cc:2358 src/ftpclass.cc:4414
#, c-format
msgid "Switching passive mode off"
msgstr "패시브(passive) 모드 끔"

#: src/ftpclass.cc:2366
#, fuzzy, c-format
msgid "Data connection established"
msgstr "접속이 잘못된 주소로 된 듯 합니다"

#: src/ftpclass.cc:2688 src/ftpclass.cc:4548
msgid "ftp:ssl-force is set and server does not support or allow SSL"
msgstr ""
"ftp:ssl-force가 설정되어있지만 서버가 지원하지 않거나 SSL을 허용하지 않음"

#: src/ftpclass.cc:3053
#, c-format
msgid "Persist and retry"
msgstr "계속 재시도함"

#: src/ftpclass.cc:3321
#, c-format
msgid "Closing data socket"
msgstr "데이터 소켓 닫음"

#: src/ftpclass.cc:3343
#, fuzzy, c-format
msgid "Closing aborted data socket"
msgstr "데이터 소켓 닫음"

#: src/ftpclass.cc:4193
#, c-format
msgid "saw file size in response"
msgstr "응답에서 파일 크기를 봅니다"

#: src/ftpclass.cc:4233 src/ftpclass.cc:4260
#, c-format
msgid "Turning on sync-mode"
msgstr "동기 모드로 전환"

#: src/ftpclass.cc:4434
#, c-format
msgid "Switching passive mode on"
msgstr "패시브(passive) 모드로 전환"

#: src/ftpclass.cc:4585
#, fuzzy
msgid "FEAT negotiation..."
msgstr "TLS 협상중..."

#: src/ftpclass.cc:4592
msgid "Sending commands..."
msgstr "명령어 전송중..."

#: src/ftpclass.cc:4596
#, fuzzy
msgid "Delaying before retry"
msgstr "재접속하기 위하여 기다립니다"

#: src/ftpclass.cc:4597 src/Http.cc:2331
msgid "Connection idle"
msgstr "사용하지 않는 접속"

#: src/ftpclass.cc:4604 src/Http.cc:2323 src/Resolver.cc:172
#: src/Resolver.cc:217 src/TorrentTracker.cc:622
#, c-format
msgid "Resolving host address..."
msgstr "호스트 주소를 찾습니다..."

#: src/ftpclass.cc:4615
msgid "TLS negotiation..."
msgstr "TLS 협상중..."

#: src/ftpclass.cc:4619
msgid "Logging in..."
msgstr "로그인 중..."

#: src/ftpclass.cc:4623
msgid "Making data connection..."
msgstr "접속 하는 중..."

#: src/ftpclass.cc:4626
msgid "Changing remote directory..."
msgstr "원거리 디렉토리 변경중..."

#: src/ftpclass.cc:4632
#, fuzzy
msgid "Waiting for other copy peer..."
msgstr "응답을 기다립니다..."

#: src/ftpclass.cc:4634 src/ftpclass.cc:4658
msgid "Waiting for transfer to complete"
msgstr "전송 완료를 기다립니다"

#: src/ftpclass.cc:4638
#, fuzzy
msgid "Waiting for TLS shutdown..."
msgstr "응답을 기다립니다..."

#: src/ftpclass.cc:4640
msgid "Waiting for data connection..."
msgstr "접속을 기다리는 중..."

#: src/ftpclass.cc:4646
#, fuzzy
msgid "Sending data/TLS"
msgstr "자료 보내는 중"

#: src/ftpclass.cc:4648
#, fuzzy
msgid "Receiving data/TLS"
msgstr "자료 받는 중"

#: src/Http.cc:230
#, c-format
msgid "Closing HTTP connection"
msgstr "HTTP 연결을 닫습니다"

#: src/Http.cc:240
msgid "POST method failed"
msgstr "POST 메소드 실패"

#: src/Http.cc:1381
msgid "ftp over http cannot work without proxy, set hftp:proxy."
msgstr ""
"http상의 ftp는 프락시 없이는 동작할 수 없습니다, http:proxy를 설정하세요"

#: src/Http.cc:1537
#, c-format
msgid "Sending request..."
msgstr "request 보내는 중..."

#: src/Http.cc:1575
#, c-format
msgid "Hit EOF while fetching headers"
msgstr "헤더를 읽어 오는 동안 EOF를 입력"

#: src/Http.cc:1695
#, c-format
msgid "Could not parse HTTP status line"
msgstr "HTTP 상태를 파싱할 수 없음"

#: src/Http.cc:1726
msgid "Object is not cached and http:cache-control has only-if-cached"
msgstr ""

#: src/Http.cc:1891
#, c-format
msgid "Receiving body..."
msgstr "body를 받은 중..."

#: src/Http.cc:2092
#, c-format
msgid "Hit EOF"
msgstr ""

#: src/Http.cc:2095
#, c-format
msgid "Received not enough data, retrying"
msgstr "충분한 자료를 받지 못해 다시 시도중"

#: src/Http.cc:2106
#, c-format
msgid "Received all"
msgstr "모두 받았음"

#: src/Http.cc:2111
#, c-format
msgid "Received all (total)"
msgstr "모두(전체) 받음"

#: src/Http.cc:2135 src/Http.cc:2159
msgid "chunked format violated"
msgstr "쓰지 않는 양식이 문제가 됩니다"

#: src/Http.cc:2145
#, fuzzy, c-format
msgid "Received last chunk"
msgstr "모두 받았음"

#: src/Http.cc:2339
msgid "Fetching headers..."
msgstr "헤더를 읽고 있는중.."

#: src/Job.cc:293
#, c-format
msgid "[%d] Done (%s)"
msgstr "[%d] 완료 (%s)"

#: src/lftp.cc:370
#, fuzzy, c-format
msgid "[%u] Terminated by signal %d. %s\n"
msgstr "[%lu] 시그널 %d 로 종료되었습니다. %s"

#: src/lftp.cc:445
#, fuzzy, c-format
msgid "[%u] Started.  %s\n"
msgstr "[%lu] 시작. %s"

#: src/lftp.cc:463
#, fuzzy, c-format
msgid "[%u] Detaching from the terminal to complete transfers...\n"
msgstr "[%lu] 전송을 완료하도록 백그라운드로 옮기는 중...\n"

#: src/lftp.cc:465
#, c-format
msgid "[%u] Exiting and detaching from the terminal.\n"
msgstr ""

#: src/lftp.cc:470
#, c-format
msgid "[%u] Detached from terminal. %s\n"
msgstr ""

#: src/lftp.cc:480
#, fuzzy, c-format
msgid "[%u] Finished. %s\n"
msgstr "[%lu] 종료. %s"

#: src/lftp.cc:484
#, fuzzy, c-format
msgid "[%u] Moving to background to complete transfers...\n"
msgstr "[%lu] 전송을 완료하도록 백그라운드로 옮기는 중...\n"

#: src/lftp.cc:553
msgid "history -w file|-r file|-c|-l [cnt]"
msgstr ""

#: src/lftp.cc:554
msgid ""
" -w <file> Write history to file.\n"
" -r <file> Read history from file; appends to current history.\n"
" -c  Clear the history.\n"
" -l  List the history (default).\n"
"Optional argument cnt specifies the number of history lines to list,\n"
"or \"all\" to list all entries.\n"
msgstr ""

#: src/lftp.cc:561
msgid "Attach the terminal to specified backgrounded lftp process.\n"
msgstr ""

#: src/lftp_ssl.cc:1291
#, c-format
msgid "No certificate presented by %s.\n"
msgstr ""

#: src/LocalAccess.cc:604 src/NetAccess.cc:686
#, fuzzy
msgid "Getting directory contents"
msgstr "디렉토리 정보를 가져오는 중 (%lld)"

#: src/LocalAccess.cc:606 src/NetAccess.cc:690
#, fuzzy
msgid "Getting files information"
msgstr "파일 정보를 가져오는 중 (%d%%)"

#: src/LsCache.cc:190
#, c-format
msgid "%ld $#l#byte|bytes$ cached"
msgstr "%ld $#l#byte|bytes$ 캐시"

#: src/LsCache.cc:194
msgid ", no size limit"
msgstr ", 크기 제한 없음"

#: src/LsCache.cc:196
#, c-format
msgid ", maximum size %ld\n"
msgstr ", 최대 크기 %ld\n"

#: src/mgetJob.cc:78
#, fuzzy, c-format
msgid "%s: %s: no files found\n"
msgstr "%s: 파일수 찾을 수 없습니다\n"

#: src/MirrorJob.cc:100
#, c-format
msgid "%sTotal: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
msgstr "%s전체: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"

#: src/MirrorJob.cc:104
#, c-format
msgid "%sNew: %d file$|s$, %d symlink$|s$\n"
msgstr "%s생성: %d file$|s$, %d symlink$|s$\n"

#: src/MirrorJob.cc:108
#, c-format
msgid "%sModified: %d file$|s$, %d symlink$|s$\n"
msgstr "%s수정: %d file$|s$, %d symlink$|s$\n"

#: src/MirrorJob.cc:115
#, c-format
msgid "%sRemoved: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
msgstr "%s삭제: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"

#: src/MirrorJob.cc:120
#, fuzzy, c-format
msgid "%s%d error$|s$ detected\n"
msgstr "%s 성공, %d director$y|ies$ 생성\n"

#: src/MirrorJob.cc:231
#, fuzzy, c-format
msgid "Finished %s"
msgstr "[%lu] 종료. %s"

#: src/MirrorJob.cc:344 src/MirrorJob.cc:519 src/MirrorJob.cc:1218
#, fuzzy, c-format
msgid "Removing old file `%s'"
msgstr "옛 지역 파일 `%s' 지움"

#: src/MirrorJob.cc:346
#, fuzzy, c-format
msgid "Overwriting old file `%s'"
msgstr "옛 지역 파일 `%s' 지움"

#: src/MirrorJob.cc:355
#, c-format
msgid "Skipping file `%s' (only-existing)"
msgstr ""

#: src/MirrorJob.cc:361
#, fuzzy, c-format
msgid "Transferring file `%s'"
msgstr "지역 파일 `%s'을 전송"

#: src/MirrorJob.cc:439
#, fuzzy, c-format
msgid "Skipping directory `%s' (only-existing)"
msgstr "원거리 디렉토리 `%s' 생성"

#: src/MirrorJob.cc:461 src/MirrorJob.cc:550 src/MirrorJob.cc:902
#, c-format
msgid "Removing old local file `%s'"
msgstr "옛 지역 파일 `%s' 지움"

#: src/MirrorJob.cc:487
#, fuzzy, c-format
msgid "Scanning directory `%s'"
msgstr "원거리 디렉토리 `%s' 생성"

#: src/MirrorJob.cc:489
#, c-format
msgid "Mirroring directory `%s'"
msgstr "디렉토리 `%s' 미러링"

#: src/MirrorJob.cc:525 src/MirrorJob.cc:567
#, c-format
msgid "Making symbolic link `%s' to `%s'"
msgstr "`%s'를 `%s'로 심볼 링크함"

#: src/MirrorJob.cc:562
#, c-format
msgid "Skipping symlink `%s' (only-existing)"
msgstr ""

#: src/MirrorJob.cc:807
#, c-format
msgid "mirror: protocol `%s' is not suitable for mirror\n"
msgstr "미러: 프로토콜 '%s'은 mirror에 적합하지 않습니다\n"

#: src/MirrorJob.cc:923
#, fuzzy, c-format
msgid "Making directory `%s'"
msgstr "원거리 디렉토리 `%s' 생성"

#: src/MirrorJob.cc:1181
#, fuzzy, c-format
msgid "Old directory `%s' is not removed"
msgstr "옛 지역 파일 `%s'이 삭제되지 않았습니다"

#: src/MirrorJob.cc:1183
#, fuzzy, c-format
msgid "Old file `%s' is not removed"
msgstr "옛 지역 파일 `%s'이 삭제되지 않았습니다"

#: src/MirrorJob.cc:1216
#, fuzzy, c-format
msgid "Removing old directory `%s'"
msgstr "옛 원거리 디렉토리 `%s' 삭제"

#: src/MirrorJob.cc:1339
#, fuzzy, c-format
msgid "Removing source directory `%s'"
msgstr "옛 원거리 디렉토리 `%s' 삭제"

#: src/MirrorJob.cc:1405
#, fuzzy, c-format
msgid "Removing source file `%s'"
msgstr "옛 지역 파일 `%s' 지움"

#: src/MirrorJob.cc:1425
#, c-format
msgid "Retrying mirror...\n"
msgstr ""

#: src/MirrorJob.cc:1694
msgid "pattern is empty"
msgstr ""

#: src/MirrorJob.cc:1779
#, c-format
msgid "%s must be one of: %s"
msgstr ""

#: src/MirrorJob.cc:2019
#, c-format
msgid ""
"%s: multiple --file or --directory options must have the same base "
"directory\n"
msgstr ""

#: src/MirrorJob.cc:2160
#, c-format
msgid "%s: ambiguous source directory (`%s' or `%s'?)\n"
msgstr ""

#: src/MirrorJob.cc:2182
#, c-format
msgid "%s: ambiguous target directory (`%s' or `%s'?)\n"
msgstr ""

#: src/MirrorJob.cc:2223
#, c-format
msgid "%s: source directory is required (mirror:require-source is set)\n"
msgstr ""

#: src/MirrorJob.cc:2343
msgid ""
"\n"
"Mirror specified remote directory to local directory\n"
"\n"
" -R, --reverse          reverse mirror (put files)\n"
"Lots of other options are documented in the man page lftp(1).\n"
"\n"
"When using -R, the first directory is local and the second is remote.\n"
"If the second directory is omitted, basename of the first directory is "
"used.\n"
"If both directories are omitted, current local and remote directories are "
"used.\n"
"\n"
"See the man page lftp(1) for a complete documentation.\n"
msgstr ""

#: src/mkdirJob.cc:128
#, c-format
msgid "%s ok, `%s' created\n"
msgstr "%s 성공, `%s' 만들었습니다\n"

#: src/mkdirJob.cc:130 src/rmJob.cc:51
#, c-format
msgid "%s failed for %d of %d director$y|ies$\n"
msgstr "%s 실패 for %d of %d director$y|ies$\n"

#: src/mkdirJob.cc:133
#, c-format
msgid "%s ok, %d director$y|ies$ created\n"
msgstr "%s 성공, %d director$y|ies$ 생성\n"

#: src/module.cc:190
#, c-format
msgid "depend module `%s': %s\n"
msgstr "`%s'모듈에 의존함: %s\n"

#: src/module.cc:210
msgid "modules are not supported on this system"
msgstr "모듈은 이 시스템에서 지원되지 않음"

#: src/mvJob.cc:92
#, c-format
msgid "rename successful\n"
msgstr "이름변경 성공\n"

#: src/NetAccess.cc:168
#, c-format
msgid "Connecting to %s%s (%s) port %u"
msgstr "%s%s (%s) 포트 %u에 연결 중"

#: src/NetAccess.cc:224 src/Torrent.cc:3326
#, c-format
msgid "Timeout - reconnecting"
msgstr "시간초과 - 재접속"

#: src/NetAccess.cc:323
#, fuzzy
msgid "Connection limit reached"
msgstr "접속 제한까지 갔습니다"

#: src/NetAccess.cc:330
msgid "Delaying before reconnect"
msgstr "재접속하기 위하여 기다립니다"

#: src/NetAccess.cc:354 src/NetAccess.cc:356
msgid "max-retries exceeded"
msgstr "최대-재시도를 초과했습니다"

#: src/OutputJob.cc:145
#, c-format
msgid "%s (filter)"
msgstr ""

#: src/parsecmd.cc:290
msgid "parse: missing filter command\n"
msgstr "parse: 필터 명령어가 빠졌습니다\n"

#: src/parsecmd.cc:292
msgid "parse: missing redirection filename\n"
msgstr "parse: 재지정 파일명이 빠졌습니다\n"

#: src/PatternSet.cc:110
#, c-format
msgid "regular expression `%s': %s"
msgstr ""

#: src/pgetJob.cc:127
msgid "pget: falling back to plain get"
msgstr ""

#: src/pgetJob.cc:131
#, fuzzy
msgid "the target file is remote"
msgstr "옛 원거리 파일 `%s'이 삭제되지 않았습니다"

#: src/pgetJob.cc:136
msgid "the source file size is unknown"
msgstr ""

#: src/pgetJob.cc:173
#, c-format
msgid "pget: warning: space allocation for %s (%lld bytes) failed: %s\n"
msgstr ""

#: src/pgetJob.cc:234
#, c-format
msgid "`%s', got %lld of %lld (%d%%) %s%s"
msgstr ""

#: src/plural.c:96
msgid "=1 =0|>1"
msgstr ""

#: src/PollVec.cc:69
#, c-format
msgid "%s: BUG - deadlock detected\n"
msgstr "%s: 버그 - 데드락 발견\n"

#: src/PtyShell.cc:68
msgid "pseudo-tty allocation failed: "
msgstr ""

#: src/QueueFeeder.cc:68
msgid "Added job$|s$"
msgstr ""

#: src/QueueFeeder.cc:164 src/QueueFeeder.cc:185
#, c-format
msgid "No queued jobs.\n"
msgstr ""

#: src/QueueFeeder.cc:166
#, c-format
msgid "No queued job #%i.\n"
msgstr ""

#: src/QueueFeeder.cc:171 src/QueueFeeder.cc:192
msgid "Deleted job$|s$"
msgstr ""

#: src/QueueFeeder.cc:187
#, c-format
msgid "No queued jobs match \"%s\".\n"
msgstr ""

#: src/QueueFeeder.cc:212 src/QueueFeeder.cc:230
msgid "Moved job$|s$"
msgstr ""

#: src/QueueFeeder.cc:354
#, fuzzy
msgid "Commands queued:"
msgstr "\t명령어 대기:\n"

#: src/ResMgr.cc:123
msgid "no such variable"
msgstr "그러한 변수는 없습니다"

#: src/ResMgr.cc:127
msgid "ambiguous variable name"
msgstr "모호한 변수명입니다"

#: src/ResMgr.cc:335
msgid "invalid boolean value"
msgstr "잘못된 boolean 변수입니다"

#: src/ResMgr.cc:357
#, fuzzy
msgid "invalid boolean/auto value"
msgstr "잘못된 boolean 변수입니다"

#: src/ResMgr.cc:402 src/ResMgr.cc:713
msgid "invalid number"
msgstr "잘못된 수입니다"

#: src/ResMgr.cc:415
msgid "invalid floating point number"
msgstr "잘못된 부동소수 점 수입니다"

#: src/ResMgr.cc:429
#, fuzzy
msgid "invalid unsigned number"
msgstr "잘못된 수입니다"

#: src/ResMgr.cc:678
msgid "Invalid time unit letter, only [smhd] are allowed."
msgstr "잘못된 시간 단위입니다, [smhd] 만 허용합니다."

#: src/ResMgr.cc:686
msgid "Invalid time format. Format is <time><unit>, e.g. 2h30m."
msgstr "잘못된 시간 양식입니다. 양식은 <time><unit> 입니다, 예. 2h30m."

#: src/ResMgr.cc:718
msgid "integer overflow"
msgstr ""

#: src/ResMgr.cc:804
msgid "Invalid IPv4 numeric address"
msgstr ""

#: src/ResMgr.cc:814
msgid "Invalid IPv6 numeric address"
msgstr ""

#: src/ResMgr.cc:884 src/ResMgr.cc:888
#, fuzzy
msgid "this encoding is not supported"
msgstr "지원되지 않는 오퍼레이션입니다"

#: src/ResMgr.cc:894
msgid "no closure defined for this setting"
msgstr "이 설정에 대한 종결이 정의되어 있지 않습니다"

#: src/ResMgr.cc:900
#, fuzzy
msgid "a closure is required for this setting"
msgstr "이 설정에 대한 종결이 정의되어 있지 않습니다"

#: src/Resolver.cc:236
msgid "host name resolve timeout"
msgstr "호스트 주소를 찾는데 시간이 초과되었습니다"

#: src/Resolver.cc:282
#, fuzzy, c-format
msgid "%d address$|es$ found"
msgstr "주소를 찾을 수 없습니다"

#: src/Resolver.cc:327
msgid "Link-local IPv6 address should have a scope"
msgstr ""

#: src/Resolver.cc:765
msgid "DNS resolution not trusted."
msgstr ""

#: src/Resolver.cc:871
msgid "Host name lookup failure"
msgstr "호스트명 찾기 실패"

#: src/Resolver.cc:903
#, c-format
msgid "no such %s service"
msgstr "그러한 %s는  없습니다"

#: src/Resolver.cc:930
msgid "No address found"
msgstr "주소를 찾을 수 없습니다"

#: src/resource.cc:50 src/resource.cc:108
msgid "Proxy protocol unsupported"
msgstr "프락시 프로토콜을 지원하지 않습니다"

#: src/resource.cc:54
msgid "ftp:proxy password: "
msgstr "ftp:proxy 비밀번호: "

#: src/resource.cc:70
#, c-format
msgid "%s must be one of: "
msgstr ""

#: src/resource.cc:72
msgid "must be one of: "
msgstr ""

#: src/resource.cc:84
msgid ", or empty"
msgstr ""

#: src/resource.cc:116
msgid "only PUT and POST values allowed"
msgstr "PUT과 POST 값만 허용"

#: src/resource.cc:148
#, c-format
msgid "unknown address family `%s'"
msgstr "`%s'는 알수 없는 주소입니다"

#: src/rmJob.cc:47
#, c-format
msgid "%s ok, `%s' removed\n"
msgstr "%s 성공, `%s' 삭제했습니다\n"

#: src/rmJob.cc:54
#, c-format
msgid "%s failed for %d of %d file$|s$\n"
msgstr "%s 실패 for %d of %d file$|s$\n"

#: src/rmJob.cc:60
#, c-format
msgid "%s ok, %d director$y|ies$ removed\n"
msgstr "%s 성공, %d director$y|ies$ 삭제했습니다\n"

#: src/rmJob.cc:63
#, c-format
msgid "%s ok, %d file$|s$ removed\n"
msgstr "%s 성공, %d file$|s$ 삭제했습니다\n"

#: src/SFtp.cc:1099 src/SFtp.cc:1100
#, fuzzy, c-format
msgid "invalid server response format"
msgstr "임시 서버 응답"

#: src/SleepJob.cc:99
msgid "Sleeping forever"
msgstr ""

#: src/SleepJob.cc:100
msgid "Sleep time left: "
msgstr ""

#: src/SleepJob.cc:108
#, c-format
msgid "\tRepeat count: %d\n"
msgstr "\t반복 횟수: %d\n"

#: src/SleepJob.cc:142
#, c-format
msgid "%s: argument required. "
msgstr "%s: 인자가 필요합니다. "

#: src/SleepJob.cc:262
#, c-format
msgid "%s: date-time specification missed\n"
msgstr ""

#: src/SleepJob.cc:269
#, c-format
msgid "%s: date-time parse error\n"
msgstr ""

#: src/SleepJob.cc:303
msgid ""
"Usage: sleep <time>[unit]\n"
"Sleep for given amount of time. The time argument can be optionally\n"
"followed by unit specifier: d - days, h - hours, m - minutes, s - seconds.\n"
"By default time is assumed to be seconds.\n"
msgstr ""
"사용법: sleep <time>[unit]\n"
"지정된 시간동안 동작하지 않습니다. 옵션으로 시간을 다음 구분자로\n"
"표현할 수 있습니다: d - 일, h - 시, m - 분, s - 초.\n"
"기본단위는 초로 간주합니다.\n"

#: src/SleepJob.cc:310
#, fuzzy
msgid ""
"Repeat specified command with a delay between iterations.\n"
"Default delay is one second, default command is empty.\n"
" -c <count>  maximum number of iterations\n"
" -d <delay>  delay between iterations\n"
" --while-ok  stop when command exits with non-zero code\n"
" --until-ok  stop when command exits with zero code\n"
" --weak      stop when lftp moves to background.\n"
msgstr ""
"사용법: repeat [delay] [command]\n"
"지연을 두어 반복적으로 명령을 실행함.\n"
"기본 지연은 1초, 기본 명령은 없음.\n"

#: src/Speedometer.cc:90
#, c-format
msgid "%.0fb/s"
msgstr "%.0fb/초"

#: src/Speedometer.cc:93
#, c-format
msgid "%.1fK/s"
msgstr "%.1fK/초"

#: src/Speedometer.cc:96
#, c-format
msgid "%.2fM/s"
msgstr "%.2fM/초"

#: src/Speedometer.cc:103
#, fuzzy, c-format
msgid "%.0f B/s"
msgstr "%.0fb/초"

#: src/Speedometer.cc:105
#, fuzzy, c-format
msgid "%.1f KiB/s"
msgstr "%.1fK/초"

#: src/Speedometer.cc:107
#, fuzzy, c-format
msgid "%.2f MiB/s"
msgstr "%.2fM/초"

#: src/Speedometer.cc:129
msgid "eta:"
msgstr "남은시간:"

#: src/SSH_Access.cc:97
#, fuzzy
msgid "Password required"
msgstr "비밀번호: "

#: src/SSH_Access.cc:102
msgid "Login incorrect"
msgstr ""

#: src/SSH_Access.cc:196
#, fuzzy, c-format
msgid "Disconnecting"
msgstr "접속중..."

#: src/SysCmdJob.cc:73
#, c-format
msgid "execlp(%s) failed: %s\n"
msgstr "execlp(%s) 실패: %s\n"

#: src/TimeDate.cc:155
msgid "day"
msgstr ""

#: src/TimeDate.cc:156
msgid "hour"
msgstr ""

#: src/TimeDate.cc:157
msgid "minute"
msgstr ""

#: src/TimeDate.cc:158
msgid "second"
msgstr ""

#: src/Torrent.cc:585
msgid "announced via "
msgstr ""

#: src/Torrent.cc:599
#, c-format
msgid "next announce in %s"
msgstr ""

#: src/Torrent.cc:1142
#, c-format
msgid "%d file$|s$ found, now scanning %s"
msgstr ""

#: src/Torrent.cc:1143
#, fuzzy, c-format
msgid "%d file$|s$ found"
msgstr "주소를 찾을 수 없습니다"

#: src/Torrent.cc:2075 src/Torrent.cc:2085
#, c-format
msgid "Getting meta-data: %s"
msgstr ""

#: src/Torrent.cc:2077
#, c-format
msgid "Validation: %u/%u (%u%%) %s%s"
msgstr ""

#: src/Torrent.cc:2088
#, fuzzy
msgid "Waiting for meta-data..."
msgstr "\t명령을 기다리는 중\n"

#: src/Torrent.cc:2096
msgid "Shutting down: "
msgstr ""

#: src/Torrent.cc:3207
#, fuzzy, c-format
msgid "Connecting to peer %s port %u"
msgstr "%s%s (%s) 포트 %u에 연결 중"

#: src/Torrent.cc:3258 src/Torrent.cc:3375
#, fuzzy, c-format
msgid "peer unexpectedly closed connection after %s"
msgstr "원거리 끝이 접속을 끊습니다"

#: src/Torrent.cc:3259 src/Torrent.cc:3376
#, fuzzy
msgid "peer unexpectedly closed connection"
msgstr "원거리 끝이 접속을 끊습니다"

#: src/Torrent.cc:3261 src/Torrent.cc:3262
#, fuzzy, c-format
msgid "peer closed connection (before handshake)"
msgstr "접속이 끊겼습니다"

#: src/Torrent.cc:3265 src/Torrent.cc:3378 src/Torrent.cc:3379
#, fuzzy, c-format
msgid "invalid peer response format"
msgstr "임시 서버 응답"

#: src/Torrent.cc:3360 src/Torrent.cc:3361
#, fuzzy, c-format
msgid "peer closed connection"
msgstr "접속이 끊겼습니다"

#: src/Torrent.cc:3538
msgid "Handshaking..."
msgstr ""

#: src/Torrent.cc:3798
msgid "Cannot bind a socket for torrent:port-range"
msgstr ""

#: src/Torrent.cc:3858
#, fuzzy, c-format
msgid "Accepted connection from [%s]:%d"
msgstr "(%s) 포트 %u에 자료 소켓을 연결합니다"

#: src/Torrent.cc:3924
#, c-format
msgid "peer sent unknown info_hash=%s in handshake"
msgstr ""

#: src/Torrent.cc:3948
#, c-format
msgid "peer handshake timeout"
msgstr ""

#: src/Torrent.cc:3960
#, c-format
msgid "peer short handshake"
msgstr ""

#: src/Torrent.cc:3962
#, fuzzy, c-format
msgid "peer closed just accepted connection"
msgstr "접속이 끊겼습니다"

#: src/Torrent.cc:4013
#, fuzzy, c-format
msgid "Seeding in background...\n"
msgstr "명령어 전송중..."

#: src/Torrent.cc:4176
#, c-format
msgid "%s: --share conflicts with --output-directory.\n"
msgstr ""

#: src/Torrent.cc:4180
#, c-format
msgid "%s: --share conflicts with --only-new.\n"
msgstr ""

#: src/Torrent.cc:4184
#, c-format
msgid "%s: --share conflicts with --only-incomplete.\n"
msgstr ""

#: src/Torrent.cc:4226
#, c-format
msgid "%s: Please specify a file or directory to share.\n"
msgstr ""

#: src/Torrent.cc:4228
#, c-format
msgid "%s: Please specify meta-info file or URL.\n"
msgstr ""

#: src/Torrent.cc:4258
msgid ""
"Start BitTorrent job for the given torrent-files, which can be a local "
"file,\n"
"URL, magnet link or plain info_hash written in hex or base32. Local "
"wildcards\n"
"are expanded. Options:\n"
" -O <base>      specifies base directory where files should be placed\n"
" --force-valid  skip file validation\n"
" --dht-bootstrap=<node>  bootstrap DHT by sending a query to the node\n"
" --share        share specified file or directory\n"
msgstr ""

#: src/TorrentTracker.cc:178
msgid "not started"
msgstr ""

#: src/TorrentTracker.cc:181
#, c-format
msgid "next request in %s"
msgstr ""

#: src/TorrentTracker.cc:284 src/TorrentTracker.cc:501
#, c-format
msgid "Received valid info about %d peer$|s$"
msgstr ""

#: src/TorrentTracker.cc:298
#, c-format
msgid "Received valid info about %d IPv6 peer$|s$"
msgstr ""

#, fuzzy
#~ msgid "%s: unrecognized option '--%s'\n"
#~ msgstr "%s: `%s'으로 리다이렉션을 받음\n"

#~ msgid "Usage: mv <file1> <file2>\n"
#~ msgstr "사용법: mv <file1> <file2>\n"

#, fuzzy
#~ msgid "number"
#~ msgstr "잘못된 수입니다"

#, fuzzy
#~ msgid "error: %s:%d\n"
#~ msgstr "치명적 오류: %s"

#, fuzzy
#~ msgid ""
#~ "\n"
#~ "Mirror specified remote directory to local directory\n"
#~ "\n"
#~ " -c, --continue         continue a mirror job if possible\n"
#~ " -e, --delete           delete files not present at remote site\n"
#~ "     --delete-first     delete old files before transferring new ones\n"
#~ " -s, --allow-suid       set suid/sgid bits according to remote site\n"
#~ "     --allow-chown      try to set owner and group on files\n"
#~ "     --ignore-time      ignore time when deciding whether to download\n"
#~ " -n, --only-newer       download only newer files (-c won't work)\n"
#~ " -r, --no-recursion     don't go to subdirectories\n"
#~ " -p, --no-perms         don't set file permissions\n"
#~ "     --no-umask         don't apply umask to file modes\n"
#~ " -R, --reverse          reverse mirror (put files)\n"
#~ " -L, --dereference      download symbolic links as files\n"
#~ " -N, --newer-than=SPEC  download only files newer than specified time\n"
#~ " -P, --parallel[=N]     download N files in parallel\n"
#~ " -i RX, --include RX    include matching files\n"
#~ " -x RX, --exclude RX    exclude matching files\n"
#~ "                        RX is extended regular expression\n"
#~ " -v, --verbose[=N]      verbose operation\n"
#~ "     --log=FILE         write lftp commands being executed to FILE\n"
#~ "     --script=FILE      write lftp commands to FILE, but don't execute "
#~ "them\n"
#~ "     --just-print, --dry-run    same as --script=-\n"
#~ "\n"
#~ "When using -R, the first directory is local and the second is remote.\n"
#~ "If the second directory is omitted, basename of first directory is used.\n"
#~ "If both directories are omitted, current local and remote directories are "
#~ "used.\n"
#~ "See lftp(1) for a complete list of options.\n"
#~ msgstr ""
#~ "\n"
#~ "지정된 원거리 디렉토리를 지역 디렉토리로 미러\n"
#~ "\n"
#~ " -c, --continue         가능하다면 미러 작업을 이어서 함\n"
#~ " -e, --delete           원거리 사이트에 존재하지 않는 파일을 삭제\n"
#~ " -s, --allow-suid       원거리 사이트에 따라 suid/sgid 비트를 설정\n"
#~ " -n, --only-newer       새로 갱신된 파일만 받음(-c옵션 사용 불가)\n"
#~ " -r, --no-recursion     서브디렉토리의 내용은 미러링 하지 않음\n"
#~ " -p, --no-perms         파일 퍼미션 무시\n"
#~ "     --no-umask         파일 모드에 umask를 적용하지 않음\n"
#~ " -R, --reverse          역 미러 (파일들을 업로드함)\n"
#~ " -L, --dereference      심볼릭 링크를 파일로써 받음\n"
#~ " -N, --newer-than FILE  지정된 파일 이후에 갱신된 것만 받음\n"
#~ " -P, --parallel[=N]     동시에 N개의 파일들을 받음\n"
#~ " -i RX, --include RX    일치하는 파일만 포함\n"
#~ " -x RX, --exclude RX    일치하는 파일만 제외\n"
#~ "                        RX는 확장된 정규 표현식\n"
#~ " -t Nx, --time-prec Nx  시간 정밀도 설정 \n"
#~ "                        초(x=s), 분(x=m), 시간(x=h), 일(x=d)\n"
#~ "                        기본 - mirror:time-precision 설정됨\n"
#~ " -T Nx, --loose-time-prec  느슨한 시간으로 시간 설정\n"
#~ "                        기본 - mirror:loose-time-precision\n"
#~ " -v, --verbose          verbose 동작\n"
#~ "     --use-cache        캐시된 디렉토리 목록을 사용\n"
#~ "\n"
#~ "-R을 사용할 때, 첫번째 디렉토리가 지역이고 두번째가 원거리입니다.\n"
#~ "만약 두번째 디렉토리를 생략하면, 첫번째 디렉토리명을 사용합니다.\n"
#~ "두 디렉토리 다 생략하면, 현재 지역과 원거리 디렉토리를 사용합니다.\n"

#, fuzzy
#~ msgid "invalid pair of numbers"
#~ msgstr "잘못된 수입니다"

#~ msgid "Saw `unknown', assume failed login"
#~ msgstr "`알수 없는' 보면, 로그인에 실패한 듯 합니다"

#, fuzzy
#~ msgid "block size"
#~ msgstr "잘못된 수입니다"

#~ msgid "%sTo be removed: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"
#~ msgstr "%s삭제: %d director$y|ies$, %d file$|s$, %d symlink$|s$\n"

#~ msgid "Cache is on"
#~ msgstr "캐시 동작"

#~ msgid "Cache is off"
#~ msgstr "캐시 동작하지 않음"

#~ msgid "Cache entries do not expire"
#~ msgstr "캐시 내용 만기되지 않았습니다"

#~ msgid "Cache entries expire in %ld $#l#second|seconds$\n"
#~ msgstr "%ld $#l#second|seconds$ 의 캐시 내용이 만기되었습니다\n"

#~ msgid "Cache entries expire in %ld $#l#minute|minutes$\n"
#~ msgstr "%ld $#l#minute|minutes$ 의 캐시 내용이 만기되었습니다\n"

#~ msgid ""
#~ "This is free software with ABSOLUTELY NO WARRANTY. See COPYING for "
#~ "details.\n"
#~ msgstr ""
#~ "이것은 ABSOLUTELY NO WARRANTY를 가지는 프리 소프트웨어입니다. 자세한 것은 "
#~ "COPYING을 보십시오.\n"

#~ msgid "Success, but did nothing??"
#~ msgstr "성공했지만, 아무 변화도 없다??"

#, fuzzy
#~ msgid "%s: %s: %s\n"
#~ msgstr "사용법: %s\n"

#~ msgid "Query of variable `%s' failed: %s\n"
#~ msgstr "`%s' 값을 가져오는데 실패: %s\n"

#~ msgid ""
#~ "Enter date, or blank line to exit.\n"
#~ "\t> "
#~ msgstr ""
#~ "자료 입력, 종료하기 위해선 공백라인을 입력.\n"
#~ "\t> "

#~ msgid "Bad format - couldn't convert.\n"
#~ msgstr "잘못도니 양식 - 변환할 수 없음.\n"

#~ msgid "%s: Invalid number for size\n"
#~ msgstr "%s: 크기로 사용할 수 없는 수입니다\n"

#~ msgid "Warning: getcwd() failed: %s\n"
#~ msgstr "경고: getcwd() 실패: %s\n"

#~ msgid "No directory to execute commands in - terminating\n"
#~ msgstr "디렉토리가 존재하지 않습니다 - 종료합니다\n"

#~ msgid "chain output error"
#~ msgstr "체인 출력 에러"

#~ msgid "Usage: %s mode file...\n"
#~ msgstr "사용법: %s mode file...\n"

#~ msgid "%s: %s - not an octal number\n"
#~ msgstr "%s: %s - 8진수가 아닙니다\n"

#~ msgid "Removing old remote file `%s'"
#~ msgstr "옛 원거리 파일 `%s' 지움"

#~ msgid "Retrieving remote file `%s'"
#~ msgstr "원거리 파일 `%s' 다시 받음"

#~ msgid "mirror: cannot create `file:' access object, installation error?\n"
#~ msgstr "미러: `file:' 접근 객체를 만들 수 없습니다, 설치 에러?\n"

#~ msgid "copy: get position was %lld\n"
#~ msgstr "copy: get 위치는 %lld임\n"

#~ msgid "copy: get hit eof\n"
#~ msgstr "copy: eof 받음\n"

#~ msgid "copy: get reached range limit\n"
#~ msgstr "copy: 범위 제한에 도달함\n"

#~ msgid "copy: get is finished - all done\n"
#~ msgstr "copy: 받기 끝남 - 모두 완료\n"

#~ msgid "List remote file names\n"
#~ msgstr "원거리 파일 이름의 목록입니다\n"

#~ msgid "Same as `get -c'\n"
#~ msgstr "`get -c'와 같습니다\n"

#~ msgid "Same as `put -c'\n"
#~ msgstr "`put -c' 와 같습니다\n"

#~ msgid "dns cache hit\n"
#~ msgstr "dns 캐시 일치\n"

#~ msgid "child returned invalid data"
#~ msgstr "잘못된 정보를 반환했습니다"

#, fuzzy
#~ msgid ""
#~ "Usage: queue -e|-s <file>|<command>\n"
#~ " -e|--edit    Edit the queue\n"
#~ " -s|--source <file> adds the contents of a file to the end of the queue\n"
#~ "<command>     Add the command to queue for current site. Each site has "
#~ "its\n"
#~ "own command queue. It is possible to queue up a running job by using\n"
#~ "command `queue wait <jobno>'.\n"
#~ msgstr ""
#~ "사용법: queue <command>\n"
#~ "현재 사이트에 대한  큐에 명령어를 추가함. 각 사이트는 그 자신의\n"
#~ "명령어 큐를 가지고 있습니다. `queue wait <jobno>' 명령을 사용하여\n"
#~ "실행중인 작업을 큐에 추가할 수 있습니다.\n"

#, fuzzy
#~ msgid "Usage: %s -e|-s|command args...\n"
#~ msgstr "사용법: %s command args...\n"

#~ msgid "apache listing matched"
#~ msgstr "아파치 리스팅 일치"

#~ msgid "unusual apache listing matched"
#~ msgstr "이상한 아파치 리스팅 일치"

#~ msgid "squid EPLF listing matched"
#~ msgstr "squid EPLF 리스팅 일치"

#~ msgid "Saw `Login incorrect', assume failed login"
#~ msgstr "`틀린 로그인' 보면, 로그인에 실해한 듯 합니다"

#~ msgid "%s: cannot add empty bookmark\n"
#~ msgstr "%s: 빈 북마크를 추가할 수 없습니다\n"

#~ msgid "%s: cannot get current directory\n"
#~ msgstr "%s: 현재 디렉토리에 접근할 수 없습니다\n"

#~ msgid "Fatal protocol error occured"
#~ msgstr "치명적인 프로토콜 오류가 발생했습니다"

#~ msgid "Usage: %s files...\n"
#~ msgstr "사용법: %s files...\n"

#~ msgid "remote rm(%s) - %s\n"
#~ msgstr "원거리 rm(%s) -%s\n"

#~ msgid "\tNo files transferred successfully :(\n"
#~ msgstr "\t파일 전송 실패 :(\n"

#~ msgid "Average transfer rate %g bytes/s\n"
#~ msgstr "평균 전송률은 %g 바이트/초 입니다\n"

#~ msgid "%s: cannot write -- disk full?\n"
#~ msgstr "%s: 저장할 수 없습니다 -- 디스크가 꽉 찬 듯?\n"

#~ msgid ""
#~ "Upload files with wildcard expansion\n"
#~ " -c  continue, reput\n"
#~ " -d  create directories the same as in file names and put the\n"
#~ "     files into them instead of current directory\n"
#~ msgstr ""
#~ "와일드카드(wildcard) 표현을 이용하여 파일들을 올립니다\n"
#~ " -c  계속, 이어받기\n"
#~ " -d  파일명과 동일한 디렉토리를 만들어 현재 디렉토리가 아닌\n"
#~ "     그 디렉토리에 파일을 올립니다\n"

#~ msgid "Use specified info for remote login\n"
#~ msgstr "원거리 로그인을 위하여 지정된 정보를 사용합니다\n"

#~ msgid "Wait for specified job to terminate.\n"
#~ msgstr "지정된 작업이 종료되기를 기다립니다.\n"

#~ msgid ""
#~ "FtpGet | Version %s | Copyright (C) 1996-1999 Alexander V. Lukyanov\n"
#~ msgstr "FtpGet | 버전 %s | 저작권(C) 1996-1999 Alexander V. Lukyanov\n"

#~ msgid ""
#~ "Usage: ftpget [OPTIONS] host filename [-o local] [filename...]\n"
#~ "\n"
#~ "-p  --port         set port number\n"
#~ "-u  --user         login as user using pass as password\n"
#~ "-l  --list         get listing of specified directory(ies)\n"
#~ "-c  --continue     reget specified file(s)\n"
#~ "-q  --quiet        quiet (no output)\n"
#~ "-v  --verbose      verbose (lots of output)\n"
#~ "    --async-mode   use asynchronous mode (faster)\n"
#~ "    --sync-mode    use synchronous mode (compatible with bugs)\n"
#~ "\n"
#~ "-o  output to local file `local' (default - base name of filename)\n"
#~ msgstr ""
#~ "사용법: ftpget [OPTIONS] host filename [-o local] [filename...]\n"
#~ "\n"
#~ "-p  --port         포트 번호 설정\n"
#~ "-u  --user         로긴명과 비밀번호 지정\n"
#~ "-l  --list         지정된 디렉토리의 목록을 가져옴\n"
#~ "-c  --contiue      이어받기\n"
#~ "-q  --quiet        출력 없음\n"
#~ "-v  --verbose      자세한 출력\n"
#~ "    --async-mode   비동기 모드 사용(빠름)\n"
#~ "    --sync-mode    동기 모드 사용(불완전한 호환성)\n"
#~ "\n"
#~ "-o  지역파일 `local'에 저장 (기본값 - 원 파일명)\n"

#~ msgid "Usage: %s [-c] [-d] [-e] pattern ...\n"
#~ msgstr "사용법: %s [-c] [-d] [-e] pattern ...\n"

#~ msgid "Sorry, %s can work with only ftp protocol\n"
#~ msgstr "죄송합니다, %s 는 ftp 프로토콜이어야 동작합니다\n"

#~ msgid "Usage: %s [-c] [-p] <source> <dest>\n"
#~ msgstr "사용법: %s [-c] [-p] <source> <dest>\n"

#~ msgid "Getting size of `%s' [%s]"
#~ msgstr "'%s'의 크기를 가져오고 있습니다 [%s]"

#~ msgid "Copying of `%s' in progress (%c)"
#~ msgstr "'%s'의 복사가 진행중입니다 (%c)"
